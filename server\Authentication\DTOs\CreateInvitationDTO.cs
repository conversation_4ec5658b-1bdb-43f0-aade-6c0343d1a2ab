using Final_E_Receipt.Authentication.Models;
using System.ComponentModel.DataAnnotations;

namespace Final_E_Receipt.Authentication.DTOs
{
    public class CreateInvitationDTO
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; }

        [Required]
        public string Role { get; set; }

        [Required]
        public string OrganizationId { get; set; }

        [Required]
        public AuthenticationType AuthType { get; set; } // Admin chooses auth method
    }
}

