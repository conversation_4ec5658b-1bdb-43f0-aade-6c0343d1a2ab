import { msalService } from './msalService';

export interface GraphUser {
  id: string;
  displayName: string;
  givenName: string;
  surname: string;
  mail: string;
  userPrincipalName: string;
}

class GraphService {
  private readonly graphBaseUrl = 'https://graph.microsoft.com/v1.0';

  /**
   * Get current user from Microsoft Graph
   */
  async getCurrentUser(): Promise<GraphUser> {
    try {
      const token = await msalService.getAccessToken();
      if (!token) {
        throw new Error('No access token available');
      }

      const response = await fetch(`${this.graphBaseUrl}/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Graph API error: ${response.status} ${response.statusText}`);
      }

      const user = await response.json();
      console.log('Graph user data:', user);
      
      return {
        id: user.id,
        displayName: user.displayName || '',
        givenName: user.givenName || '',
        surname: user.surname || '',
        mail: user.mail || user.userPrincipalName || '',
        userPrincipalName: user.userPrincipalName || ''
      };
    } catch (error) {
      console.error('Failed to get user from Graph:', error);
      throw error;
    }
  }

  /**
   * Get user photo from Microsoft Graph
   */
  async getUserPhoto(): Promise<string | null> {
    try {
      const token = await msalService.getAccessToken();
      if (!token) {
        return null;
      }

      const response = await fetch(`${this.graphBaseUrl}/me/photo/$value`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        return null;
      }

      const blob = await response.blob();
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error('Failed to get user photo:', error);
      return null;
    }
  }
}

// Export singleton instance
export const graphService = new GraphService();
