import { useState, useCallback } from "react";
import { apiService } from "../../services/apiService";

// Types for Receipt Templates API
export interface ReceiptTemplate {
  id: string;
  name: string;
  description: string;
  fileName: string;
  fileSize: number;
  uploadedDate: string;
  isActive: boolean;
  isDefault: boolean;
  previewUrl: string;
  organizationId?: string;
  organizationName?: string;
  createdBy: string;
  updatedAt?: string;
}

export interface CreateReceiptTemplateDTO {
  name: string;
  description: string;
  file: File;
  isDefault: boolean;
  organizationId?: string;
}

export interface UpdateReceiptTemplateDTO {
  name?: string;
  description?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

export interface ReceiptTemplateFilters {
  organizationId?: string;
  isActive?: boolean;
  isDefault?: boolean;
  searchTerm?: string;
}

// Custom hook for Receipt Templates API
export const useReceiptTemplatesApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(
    async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
      try {
        setLoading(true);
        setError(null);
        const result = await apiCall();
        return result;
      } catch (err: any) {
        const errorMessage =
          err.response?.data?.message || err.message || "An error occurred";
        setError(errorMessage);
        return null;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // Get all receipt templates
  const getReceiptTemplates = useCallback(
    (filters?: ReceiptTemplateFilters) =>
      handleApiCall(async () => {
        const queryParams = new URLSearchParams();
        if (filters?.organizationId)
          queryParams.append("organizationId", filters.organizationId);
        if (filters?.isActive !== undefined)
          queryParams.append("isActive", filters.isActive.toString());
        if (filters?.isDefault !== undefined)
          queryParams.append("isDefault", filters.isDefault.toString());
        if (filters?.searchTerm)
          queryParams.append("searchTerm", filters.searchTerm);

        const response = await apiService.get<ReceiptTemplate[]>(
          `/api/receipt-templates?${queryParams.toString()}`
        );
        return response;
      }),
    [handleApiCall]
  );

  // Get receipt template by ID
  const getReceiptTemplateById = useCallback(
    (id: string) =>
      handleApiCall(() =>
        apiService.get<ReceiptTemplate>(`/api/receipt-templates/${id}`)
      ),
    [handleApiCall]
  );

  // Create new receipt template
  const createReceiptTemplate = useCallback(
    (templateData: CreateReceiptTemplateDTO) =>
      handleApiCall(async () => {
        const formData = new FormData();
        formData.append("name", templateData.name);
        formData.append("description", templateData.description);
        formData.append("file", templateData.file);
        formData.append("isDefault", templateData.isDefault.toString());
        if (templateData.organizationId) {
          formData.append("organizationId", templateData.organizationId);
        }

        // Note: apiService.post might need to be updated to handle FormData properly
        // You may need to pass the FormData directly without wrapping
        const response = await apiService.post<ReceiptTemplate>(
          "/api/receipt-templates",
          formData
        );
        return response;
      }),
    [handleApiCall]
  );

  // Update receipt template
  const updateReceiptTemplate = useCallback(
    (id: string, updateData: UpdateReceiptTemplateDTO) =>
      handleApiCall(() =>
        apiService.put<ReceiptTemplate>(
          `/api/receipt-templates/${id}`,
          updateData
        )
      ),
    [handleApiCall]
  );

  // Delete receipt template
  const deleteReceiptTemplate = useCallback(
    (id: string) =>
      handleApiCall(() => apiService.delete(`/api/receipt-templates/${id}`)),
    [handleApiCall]
  );

  // Set template as default
  const setDefaultTemplate = useCallback(
    (id: string) =>
      handleApiCall(() =>
        apiService.post<ReceiptTemplate>(
          `/api/receipt-templates/${id}/set-default`
        )
      ),
    [handleApiCall]
  );

  // Toggle template active status
  const toggleTemplateStatus = useCallback(
    (id: string) =>
      handleApiCall(() =>
        apiService.post<ReceiptTemplate>(
          `/api/receipt-templates/${id}/toggle-status`
        )
      ),
    [handleApiCall]
  );

  // Download template file
  const downloadTemplate = useCallback(
    (id: string) =>
      handleApiCall(async () => {
        // For file downloads, you might need a different approach
        const response = await apiService.get(
          `/api/receipt-templates/${id}/download`
        );
        return response;
      }),
    [handleApiCall]
  );

  // Get template preview
  const getTemplatePreview = useCallback(
    (id: string) =>
      handleApiCall(async () => {
        const response = await apiService.get<{ previewUrl: string }>(
          `/api/receipt-templates/${id}/preview`
        );
        return response?.previewUrl || "";
      }),
    [handleApiCall]
  );

  return {
    loading,
    error,
    getReceiptTemplates,
    getReceiptTemplateById,
    createReceiptTemplate,
    updateReceiptTemplate,
    deleteReceiptTemplate,
    setDefaultTemplate,
    toggleTemplateStatus,
    downloadTemplate,
    getTemplatePreview,
  };
};

export default useReceiptTemplatesApi;

// import { useState, useCallback } from 'react';
// import { apiService } from '../../services/apiService';

// // Types for Receipt Templates API
// export interface ReceiptTemplate {
//   id: string;
//   name: string;
//   description: string;
//   fileName: string;
//   fileSize: number;
//   uploadedDate: string;
//   isActive: boolean;
//   isDefault: boolean;
//   previewUrl: string;
//   organizationId?: string;
//   organizationName?: string;
//   createdBy: string;
//   updatedAt?: string;
// }

// export interface CreateReceiptTemplateDTO {
//   name: string;
//   description: string;
//   file: File;
//   isDefault: boolean;
//   organizationId?: string;
// }

// export interface UpdateReceiptTemplateDTO {
//   name?: string;
//   description?: string;
//   isActive?: boolean;
//   isDefault?: boolean;
// }

// export interface ReceiptTemplateFilters {
//   organizationId?: string;
//   isActive?: boolean;
//   isDefault?: boolean;
//   searchTerm?: string;
// }

// // Custom hook for Receipt Templates API
// export const useReceiptTemplatesApi = () => {
//   const [loading, setLoading] = useState(false);
//   const [error, setError] = useState<string | null>(null);

//   // Get all receipt templates
//   const getReceiptTemplates = useCallback(async (filters?: ReceiptTemplateFilters): Promise<ReceiptTemplate[]> => {
//     setLoading(true);
//     setError(null);
//     try {
//       const queryParams = new URLSearchParams();
//       if (filters?.organizationId) queryParams.append('organizationId', filters.organizationId);
//       if (filters?.isActive !== undefined) queryParams.append('isActive', filters.isActive.toString());
//       if (filters?.isDefault !== undefined) queryParams.append('isDefault', filters.isDefault.toString());
//       if (filters?.searchTerm) queryParams.append('searchTerm', filters.searchTerm);

//       const response = await apiService.get(`/api/receipt-templates?${queryParams.toString()}`);
//       return response.data;
//     } catch (err: any) {
//       const errorMessage = err.response?.data?.message || 'Failed to fetch receipt templates';
//       setError(errorMessage);
//       throw new Error(errorMessage);
//     } finally {
//       setLoading(false);
//     }
//   }, []);

//   // Get receipt template by ID
//   const getReceiptTemplateById = useCallback(async (id: string): Promise<ReceiptTemplate> => {
//     setLoading(true);
//     setError(null);
//     try {
//       const response = await apiService.get(`/api/receipt-templates/${id}`);
//       return response.data;
//     } catch (err: any) {
//       const errorMessage = err.response?.data?.message || 'Failed to fetch receipt template';
//       setError(errorMessage);
//       throw new Error(errorMessage);
//     } finally {
//       setLoading(false);
//     }
//   }, []);

//   // Create new receipt template
//   const createReceiptTemplate = useCallback(async (templateData: CreateReceiptTemplateDTO): Promise<ReceiptTemplate> => {
//     setLoading(true);
//     setError(null);
//     try {
//       const formData = new FormData();
//       formData.append('name', templateData.name);
//       formData.append('description', templateData.description);
//       formData.append('file', templateData.file);
//       formData.append('isDefault', templateData.isDefault.toString());
//       if (templateData.organizationId) {
//         formData.append('organizationId', templateData.organizationId);
//       }

//       const response = await apiService.post('/api/receipt-templates', formData, {
//         headers: {
//           'Content-Type': 'multipart/form-data',
//         },
//       });
//       return response.data;
//     } catch (err: any) {
//       const errorMessage = err.response?.data?.message || 'Failed to create receipt template';
//       setError(errorMessage);
//       throw new Error(errorMessage);
//     } finally {
//       setLoading(false);
//     }
//   }, []);

//   // Update receipt template
//   const updateReceiptTemplate = useCallback(async (id: string, updateData: UpdateReceiptTemplateDTO): Promise<ReceiptTemplate> => {
//     setLoading(true);
//     setError(null);
//     try {
//       const response = await apiService.put(`/api/receipt-templates/${id}`, updateData);
//       return response.data;
//     } catch (err: any) {
//       const errorMessage = err.response?.data?.message || 'Failed to update receipt template';
//       setError(errorMessage);
//       throw new Error(errorMessage);
//     } finally {
//       setLoading(false);
//     }
//   }, []);

//   // Delete receipt template
//   const deleteReceiptTemplate = useCallback(async (id: string): Promise<void> => {
//     setLoading(true);
//     setError(null);
//     try {
//       await apiService.delete(`/api/receipt-templates/${id}`);
//     } catch (err: any) {
//       const errorMessage = err.response?.data?.message || 'Failed to delete receipt template';
//       setError(errorMessage);
//       throw new Error(errorMessage);
//     } finally {
//       setLoading(false);
//     }
//   }, []);

//   // Set template as default
//   const setDefaultTemplate = useCallback(async (id: string): Promise<ReceiptTemplate> => {
//     setLoading(true);
//     setError(null);
//     try {
//       const response = await apiService.post(`/api/receipt-templates/${id}/set-default`);
//       return response.data;
//     } catch (err: any) {
//       const errorMessage = err.response?.data?.message || 'Failed to set default template';
//       setError(errorMessage);
//       throw new Error(errorMessage);
//     } finally {
//       setLoading(false);
//     }
//   }, []);

//   // Toggle template active status
//   const toggleTemplateStatus = useCallback(async (id: string): Promise<ReceiptTemplate> => {
//     setLoading(true);
//     setError(null);
//     try {
//       const response = await apiService.post(`/api/receipt-templates/${id}/toggle-status`);
//       return response.data;
//     } catch (err: any) {
//       const errorMessage = err.response?.data?.message || 'Failed to toggle template status';
//       setError(errorMessage);
//       throw new Error(errorMessage);
//     } finally {
//       setLoading(false);
//     }
//   }, []);

//   // Download template file
//   const downloadTemplate = useCallback(async (id: string): Promise<Blob> => {
//     setLoading(true);
//     setError(null);
//     try {
//       const response = await apiService.get(`/api/receipt-templates/${id}/download`, {
//         responseType: 'blob',
//       });
//       return response.data;
//     } catch (err: any) {
//       const errorMessage = err.response?.data?.message || 'Failed to download template';
//       setError(errorMessage);
//       throw new Error(errorMessage);
//     } finally {
//       setLoading(false);
//     }
//   }, []);

//   // Get template preview
//   const getTemplatePreview = useCallback(async (id: string): Promise<string> => {
//     setLoading(true);
//     setError(null);
//     try {
//       const response = await apiService.get(`/api/receipt-templates/${id}/preview`);
//       return response.data.previewUrl;
//     } catch (err: any) {
//       const errorMessage = err.response?.data?.message || 'Failed to get template preview';
//       setError(errorMessage);
//       throw new Error(errorMessage);
//     } finally {
//       setLoading(false);
//     }
//   }, []);

//   return {
//     loading,
//     error,
//     getReceiptTemplates,
//     getReceiptTemplateById,
//     createReceiptTemplate,
//     updateReceiptTemplate,
//     deleteReceiptTemplate,
//     setDefaultTemplate,
//     toggleTemplateStatus,
//     downloadTemplate,
//     getTemplatePreview,
//   };
// };

// export default useReceiptTemplatesApi;
