﻿using Final_E_Receipt.Services;

namespace Final_E_Receipt.Payments.Services
{
    public class PaymentTypeService
    {
        private readonly IDatabaseService _dbService;

        public PaymentTypeService(IDatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<PaymentType> CreatePaymentType(string name, string description, string createdBy)
        {
            var parameters = new
            {
                Id = Guid.NewGuid().ToString(),
                Name = name,
                Description = description,
                CreatedBy = createdBy
            };

            return await _dbService.QueryFirstOrDefaultAsync<PaymentType>("CreatePaymentType", parameters);
        }

        public async Task<PaymentType> GetPaymentTypeById(string id)
        {
            var parameters = new { Id = id };
            return await _dbService.QueryFirstOrDefaultAsync<PaymentType>("GetPaymentTypeById", parameters);
        }

        public async Task<List<PaymentType>> GetAllPaymentTypes(bool? isActive = null)
        {
            var parameters = new { IsActive = isActive };
            var types = await _dbService.QueryAsync<PaymentType>("GetAllPaymentTypes", parameters);
            return types.ToList();
        }

        public async Task<PaymentType> UpdatePaymentType(string id, string name, string description, string updatedBy)
        {
            var parameters = new
            {
                Id = id,
                Name = name,
                Description = description,
                UpdatedBy = updatedBy
            };

            return await _dbService.QueryFirstOrDefaultAsync<PaymentType>("UpdatePaymentType", parameters);
        }

        public async Task<PaymentType> TogglePaymentTypeStatus(string id, bool isActive, string updatedBy)
        {
            var parameters = new
            {
                Id = id,
                IsActive = isActive,
                UpdatedBy = updatedBy
            };

            return await _dbService.QueryFirstOrDefaultAsync<PaymentType>("TogglePaymentTypeStatus", parameters);
        }

        public async Task<bool> DeletePaymentType(string id)
        {
            try
            {
                var parameters = new { Id = id };
                var rowsAffected = await _dbService.ExecuteAsync("DeletePaymentType", parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                // Log the error (implementation depends on your logging system)
                // _logger.LogError(ex, "Failed to delete payment type {Id}", id);
                return false;
            }
        }

        public async Task<List<PaymentTypeSummary>> GetPaymentTypeSummary(DateTime? startDate = null, DateTime? endDate = null, string organizationId = null)
        {
            var parameters = new
            {
                StartDate = startDate,
                EndDate = endDate,
                OrganizationId = organizationId
            };

            var summaries = await _dbService.QueryAsync<PaymentTypeSummary>("GetPaymentTypeSummary", parameters);
            return summaries.ToList();
        }

        public async Task<bool> ValidatePaymentType(string paymentTypeId)
        {
            var parameters = new { Id = paymentTypeId };
            var type = await _dbService.QueryFirstOrDefaultAsync<PaymentType>("GetPaymentTypeById", parameters);
            return type != null && type.IsActive;
        }
    }

    public class PaymentTypeSummary
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int TotalPayments { get; set; }
        public decimal TotalAmount { get; set; }
        public DateTime? FirstPaymentDate { get; set; }
        public DateTime? LastPaymentDate { get; set; }
    }
}

