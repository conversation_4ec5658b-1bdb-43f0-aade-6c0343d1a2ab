import React, { useState, useEffect, useRef } from 'react';
import { Bell, X } from 'lucide-react';
import { useNotificationsApi } from '../../hooks/api';
import { useAuth } from '../../hooks/useAuth';
import NotificationDropdown from './NotificationDropdown';
import type { Notification } from '../../hooks/api/useNotifications';

interface NotificationBellProps {
  className?: string;
  showBadge?: boolean;
  maxDropdownItems?: number;
}

const NotificationBell: React.FC<NotificationBellProps> = ({
  className = '',
  showBadge = true,
  maxDropdownItems = 5,
}) => {
  const { user } = useAuth();
  const { getUnreadNotifications, getNotificationStats } = useNotificationsApi();
  
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [recentNotifications, setRecentNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  
  const dropdownRef = useRef<HTMLDivElement>(null);
  const bellRef = useRef<HTMLButtonElement>(null);

  // Auto-refresh notifications every 30 seconds
  useEffect(() => {
    if (user?.id) {
      loadNotificationData();
      
      const interval = setInterval(() => {
        loadNotificationData();
      }, 30000); // 30 seconds

      return () => clearInterval(interval);
    }
  }, [user?.id]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        bellRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !bellRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  const loadNotificationData = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      
      // Load unread notifications for the dropdown
      const unreadResult = await getUnreadNotifications(user.id);
      if (unreadResult) {
        setRecentNotifications(unreadResult.slice(0, maxDropdownItems));
      }

      // Load stats for the badge count
      const statsResult = await getNotificationStats(user.id);
      if (statsResult) {
        setUnreadCount(statsResult.unreadCount);
      }
    } catch (error) {
      console.error('Failed to load notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBellClick = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      loadNotificationData(); // Refresh when opening
    }
  };

  const handleNotificationUpdate = () => {
    loadNotificationData(); // Refresh after any notification action
  };

  return (
    <div className="relative">
      {/* Notification Bell */}
      <button
        ref={bellRef}
        onClick={handleBellClick}
        className={`
          relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors
          ${isOpen ? 'bg-gray-100 text-gray-900' : ''}
          ${className}
        `}
        title="Notifications"
      >
        <Bell size={20} />
        
        {/* Notification Badge */}
        {showBadge && unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
        
        {/* Loading indicator */}
        {loading && (
          <div className="absolute -top-1 -right-1 w-3 h-3">
            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-[#2aa45c]"></div>
          </div>
        )}
      </button>

      {/* Notification Dropdown */}
      {isOpen && (
        <NotificationDropdown
          ref={dropdownRef}
          notifications={recentNotifications}
          unreadCount={unreadCount}
          onNotificationUpdate={handleNotificationUpdate}
          onClose={() => setIsOpen(false)}
          loading={loading}
        />
      )}
    </div>
  );
};

export default NotificationBell;
