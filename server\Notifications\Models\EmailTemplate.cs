using System;

namespace Final_E_Receipt.Notifications.Models
{
    public class EmailTemplate
    {
        public string Id { get; set; }
        public string OrganizationId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Subject { get; set; }
        public string BodyContent { get; set; }
        public string Type { get; set; } // RECEIPT_NOTIFICATION, PAYMENT_REMINDER, etc.
        public bool IsDefault { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
    }
}