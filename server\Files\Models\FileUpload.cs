using System;

namespace Final_E_Receipt.Files.Models
{
    public class FileUpload
    {
        public string Id { get; set; }
        public string FileName { get; set; }
        public string OriginalFileName { get; set; }
        public string FilePath { get; set; }
        public string ContentType { get; set; }
        public long FileSize { get; set; }
        public string FileHash { get; set; }
        public string UploadedBy { get; set; }
        public string OrganizationId { get; set; }
        public string RelatedEntityType { get; set; } // PAYMENT, RECEIPT, CERTIFICATE
        public string RelatedEntityId { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsScanned { get; set; } = false;
        public string ScanResult { get; set; }
        public DateTime? ScannedAt { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
    }
}


