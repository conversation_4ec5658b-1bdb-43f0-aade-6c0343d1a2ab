import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import CertificateTemplateSelector from './CertificateTemplateSelector';

interface CreateCertificateFormProps {
  organizationId: string;
  onSubmit: (data: any) => void;
  loading?: boolean;
}

export const CreateCertificateForm: React.FC<CreateCertificateFormProps> = ({
  organizationId,
  onSubmit,
  loading = false
}) => {
  const [formData, setFormData] = useState({
    organizationId,
    paymentProfileId: '',
    certificateType: '',
    totalAmount: '',
    currency: 'NGN',
    validFrom: '',
    validUntil: '',
    description: '',
    terms: '',
    complianceYear: new Date().getFullYear().toString(),
    compliancePeriod: 'ANNUAL',
    regulatoryBody: '',
    licenseCategory: 'GENERAL',
    notes: '',
    selectedTemplateId: '', // NEW: Template selection
    preferredOrientation: 'Landscape' // NEW: Orientation preference
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTemplateSelect = (templateId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedTemplateId: templateId
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Certificate Information */}
      <Card>
        <CardHeader>
          <CardTitle>Certificate Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="certificateType">Certificate Type</Label>
              <Select 
                value={formData.certificateType} 
                onValueChange={(value) => handleInputChange('certificateType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select certificate type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ANNUAL_LICENSE">Annual License</SelectItem>
                  <SelectItem value="TAX_CLEARANCE">Tax Clearance</SelectItem>
                  <SelectItem value="QUARTERLY_COMPLIANCE">Quarterly Compliance</SelectItem>
                  <SelectItem value="PAYMENT_COMPLIANCE">Payment Compliance</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="totalAmount">Total Amount</Label>
              <Input
                id="totalAmount"
                type="number"
                step="0.01"
                value={formData.totalAmount}
                onChange={(e) => handleInputChange('totalAmount', e.target.value)}
                placeholder="0.00"
                required
              />
            </div>

            <div>
              <Label htmlFor="validFrom">Valid From</Label>
              <Input
                id="validFrom"
                type="date"
                value={formData.validFrom}
                onChange={(e) => handleInputChange('validFrom', e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="validUntil">Valid Until</Label>
              <Input
                id="validUntil"
                type="date"
                value={formData.validUntil}
                onChange={(e) => handleInputChange('validUntil', e.target.value)}
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Certificate description"
            />
          </div>
        </CardContent>
      </Card>

      {/* Template Selection - Only show if certificate type is selected */}
      {formData.certificateType && (
        <CertificateTemplateSelector
          organizationId={organizationId}
          certificateType={formData.certificateType}
          selectedTemplateId={formData.selectedTemplateId}
          onTemplateSelect={handleTemplateSelect}
          disabled={loading}
        />
      )}

      {/* Additional Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="complianceYear">Compliance Year</Label>
              <Input
                id="complianceYear"
                value={formData.complianceYear}
                onChange={(e) => handleInputChange('complianceYear', e.target.value)}
                placeholder="2024"
              />
            </div>

            <div>
              <Label htmlFor="preferredOrientation">Preferred Orientation</Label>
              <Select 
                value={formData.preferredOrientation} 
                onValueChange={(value) => handleInputChange('preferredOrientation', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Portrait">Portrait</SelectItem>
                  <SelectItem value="Landscape">Landscape</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Input
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes"
            />
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline">
          Cancel
        </Button>
        <Button 
          type="submit" 
          disabled={loading || !formData.certificateType || !formData.selectedTemplateId}
        >
          {loading ? 'Creating Certificate...' : 'Create Certificate'}
        </Button>
      </div>

      {/* Selected Template Summary */}
      {formData.selectedTemplateId && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-800">
                  Template Selected: {formData.selectedTemplateId}
                </p>
                <p className="text-xs text-green-600">
                  Orientation: {formData.preferredOrientation}
                </p>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => handleTemplateSelect('')}
                className="text-green-700 hover:text-green-800"
              >
                Change Template
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </form>
  );
};

export default CreateCertificateForm;
