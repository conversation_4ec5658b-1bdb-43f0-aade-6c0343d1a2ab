
// import React, { useState } from 'react';
// import {  Clock, AlertCircle, DollarSign, Search, Filter} from 'lucide-react';

// interface Payment {
//   id: string;
//   payerName: string;
//   amount: number;
//   description: string;
//   requestDate: string;
//   priority: 'low' | 'medium' | 'high';
//   category: string;
//   requestedBy: string;
// }


// // Mock data
// const pendingPayments: Payment[] = [
//   {
//     id: 'PAY-001',
//     payerName: '<PERSON>',
//     amount: 1250.00,
//     description: 'Monthly service fee payment',
//     requestDate: '2025-06-28',
//     priority: 'high',
//     category: 'Service Fees',
//     requestedBy: 'Finance Officer A'
//   },
//   {
//     id: 'PAY-002',
//     payerName: 'ABC Corporation',
//     amount: 5000.00,
//     description: 'Quarterly subscription payment',
//     requestDate: '2025-06-27',
//     priority: 'medium',
//     category: 'Subscriptions',
//     requestedBy: 'Finance Officer B'
//   },
//   {
//     id: 'PAY-003',
//     payerName: '<PERSON>',
//     amount: 750.50,
//     description: 'Utility bill payment',
//     requestDate: '2025-06-29',
//     priority: 'low',
//     category: 'Utilities',
//     requestedBy: 'Finance Officer A'
//   }
// ];
// // Pending Approvals Dashboard Components
// const PendingApprovalsStats: React.FC = () => (
//   <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//     <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
//       <div className="flex items-center justify-between">
//         <div>
//           <p className="text-sm font-medium text-gray-600">Pending Approvals</p>
//           <p className="text-2xl font-bold" style={{ color: '#045024' }}>{pendingPayments.length}</p>
//         </div>
//         <Clock className="text-orange-500" size={24} />
//       </div>
//     </div>
//     <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
//       <div className="flex items-center justify-between">
//         <div>
//           <p className="text-sm font-medium text-gray-600">High Priority</p>
//           <p className="text-2xl font-bold text-red-600">
//             {pendingPayments.filter(p => p.priority === 'high').length}
//           </p>
//         </div>
//         <AlertCircle className="text-red-500" size={24} />
//       </div>
//     </div>
//     <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
//       <div className="flex items-center justify-between">
//         <div>
//           <p className="text-sm font-medium text-gray-600">Total Value</p>
//           <p className="text-2xl font-bold" style={{ color: '#045024' }}>
//             ₦{pendingPayments.reduce((sum, p) => sum + p.amount, 0).toLocaleString()}
//           </p>
//         </div>
//         <DollarSign className="text-green-500" size={24} />
//       </div>
//     </div>
//   </div>
// );

// interface PendingApprovalsFiltersProps {
//   searchTerm: string;
//   setSearchTerm: (term: string) => void;
//   priorityFilter: string;
//   setPriorityFilter: (priority: string) => void;
// }

// const PendingApprovalsFilters: React.FC<PendingApprovalsFiltersProps> = ({
//   searchTerm,
//   setSearchTerm,
//   priorityFilter,
//   setPriorityFilter
// }) => (
//   <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
//     <div className="flex flex-col md:flex-row gap-4">
//       <div className="flex-1">
//         <div className="relative">
//           <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
//           <input
//             type="text"
//             placeholder="Search by payer name or description..."
//             className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#045024] focus:border-[#045024]"
//             value={searchTerm}
//             onChange={(e) => setSearchTerm(e.target.value)}
//           />
//         </div>
//       </div>
//       <div className="flex items-center gap-2">
//         <Filter size={20} className="text-gray-400" />
//         <select
//           className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#045024] focus:border-[#045024]"
//           value={priorityFilter}
//           onChange={(e) => setPriorityFilter(e.target.value)}
//         >
//           <option value="all">All Priorities</option>
//           <option value="high">High Priority</option>
//           <option value="medium">Medium Priority</option>
//           <option value="low">Low Priority</option>
//         </select>
//       </div>
//     </div>
//   </div>
// );

// interface PendingPaymentsListProps {
//   payments: Payment[];
// }

// const PendingPaymentsList: React.FC<PendingPaymentsListProps> = ({ payments }) => {
//   const getPriorityColor = (priority: string) => {
//     switch (priority) {
//       case 'high': return 'bg-red-100 text-red-800';
//       case 'medium': return 'bg-yellow-100 text-yellow-800';
//       case 'low': return 'bg-green-100 text-green-800';
//       default: return 'bg-gray-100 text-gray-800';
//     }
//   };

//   return (
//     <div className="bg-white rounded-lg shadow-sm border border-gray-200">
//       <div className="p-6 border-b border-gray-200">
//         <h3 className="text-lg font-semibold" style={{ color: '#045024' }}>Pending Payment Approvals</h3>
//       </div>
//       <div className="divide-y divide-gray-200">
//         {payments.map((payment) => (
//           <div key={payment.id} className="p-6 hover:bg-gray-50">
//             <div className="flex items-center justify-between">
//               <div className="flex-1">
//                 <div className="flex items-center gap-3 mb-2">
//                   <h4 className="font-semibold text-gray-900">{payment.payerName}</h4>
//                   <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(payment.priority)}`}>
//                     {payment.priority.toUpperCase()}
//                   </span>
//                 </div>
//                 <p className="text-sm text-gray-600 mb-2">{payment.description}</p>
//                 <div className="flex items-center gap-4 text-sm text-gray-500">
//                   <span>ID: {payment.id}</span>
//                   <span>Category: {payment.category}</span>
//                   <span>Requested: {new Date(payment.requestDate).toLocaleDateString()}</span>
//                   <span>By: {payment.requestedBy}</span>
//                 </div>
//               </div>
//               <div className="flex items-center gap-4">
//                 <div className="text-right">
//                   <p className="text-lg font-bold" style={{ color: '#045024' }}>
//                     ₦{payment.amount.toLocaleString()}
//                   </p>
//                 </div>
//                 <button className="bg-[#045024] text-white px-4 py-2 rounded-lg hover:bg-[#2aa45c] transition-colors">
//                   Review
//                 </button>
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };

// const PendingApprovalsDashboard: React.FC = () => {
//   const [searchTerm, setSearchTerm] = useState('');
//   const [priorityFilter, setPriorityFilter] = useState<string>('all');

//   const filteredPayments = pendingPayments.filter(payment => {
//     const matchesSearch = payment.payerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
//                          payment.description.toLowerCase().includes(searchTerm.toLowerCase());
//     const matchesPriority = priorityFilter === 'all' || payment.priority === priorityFilter;
//     return matchesSearch && matchesPriority;
//   });

//   return (
//     <div className="space-y-6">
//       <PendingApprovalsStats />
//       <PendingApprovalsFilters
//         searchTerm={searchTerm}
//         setSearchTerm={setSearchTerm}
//         priorityFilter={priorityFilter}
//         setPriorityFilter={setPriorityFilter}
//       />
//       <PendingPaymentsList payments={filteredPayments} />
//     </div>
//   );
// };
// export default PendingApprovalsDashboard;

// import React, { useState } from 'react';
// import { Clock, AlertCircle, DollarSign, Search, Filter, CheckCircle, XCircle, FileText, ArrowLeft } from 'lucide-react';

// interface Payment {
//   id: string;
//   payerName: string;
//   amount: number;
//   description: string;
//   requestDate: string;
//   priority: 'low' | 'medium' | 'high';
//   category: string;
//   requestedBy: string;
// }

// // Mock data
// const pendingPayments: Payment[] = [
//   {
//     id: 'PAY-001',
//     payerName: 'John Smith',
//     amount: 1250.00,
//     description: 'Monthly service fee payment',
//     requestDate: '2025-06-28',
//     priority: 'high',
//     category: 'Service Fees',
//     requestedBy: 'Finance Officer A'
//   },
//   {
//     id: 'PAY-002',
//     payerName: 'ABC Corporation',
//     amount: 5000.00,
//     description: 'Quarterly subscription payment',
//     requestDate: '2025-06-27',
//     priority: 'medium',
//     category: 'Subscriptions',
//     requestedBy: 'Finance Officer B'
//   },
//   {
//     id: 'PAY-003',
//     payerName: 'Mary Johnson',
//     amount: 750.50,
//     description: 'Utility bill payment',
//     requestDate: '2025-06-29',
//     priority: 'low',
//     category: 'Utilities',
//     requestedBy: 'Finance Officer A'
//   }
// ];

// // Dashboard Components
// const PendingApprovalsStats: React.FC = () => (
//   <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//     <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
//       <div className="flex items-center justify-between">
//         <div>
//           <p className="text-sm font-medium text-gray-600">Pending Approvals</p>
//           <p className="text-2xl font-bold" style={{ color: '#045024' }}>{pendingPayments.length}</p>
//         </div>
//         <Clock className="text-orange-500" size={24} />
//       </div>
//     </div>
//     <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
//       <div className="flex items-center justify-between">
//         <div>
//           <p className="text-sm font-medium text-gray-600">High Priority</p>
//           <p className="text-2xl font-bold text-red-600">
//             {pendingPayments.filter(p => p.priority === 'high').length}
//           </p>
//         </div>
//         <AlertCircle className="text-red-500" size={24} />
//       </div>
//     </div>
//     <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
//       <div className="flex items-center justify-between">
//         <div>
//           <p className="text-sm font-medium text-gray-600">Total Value</p>
//           <p className="text-2xl font-bold" style={{ color: '#045024' }}>
//             ₦{pendingPayments.reduce((sum, p) => sum + p.amount, 0).toLocaleString()}
//           </p>
//         </div>
//         <DollarSign className="text-green-500" size={24} />
//       </div>
//     </div>
//   </div>
// );

// interface PendingApprovalsFiltersProps {
//   searchTerm: string;
//   setSearchTerm: (term: string) => void;
//   priorityFilter: string;
//   setPriorityFilter: (priority: string) => void;
// }

// const PendingApprovalsFilters: React.FC<PendingApprovalsFiltersProps> = ({
//   searchTerm,
//   setSearchTerm,
//   priorityFilter,
//   setPriorityFilter
// }) => (
//   <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
//     <div className="flex flex-col md:flex-row gap-4">
//       <div className="flex-1">
//         <div className="relative">
//           <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
//           <input
//             type="text"
//             placeholder="Search by payer name or description..."
//             className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#045024] focus:border-[#045024]"
//             value={searchTerm}
//             onChange={(e) => setSearchTerm(e.target.value)}
//           />
//         </div>
//       </div>
//       <div className="flex items-center gap-2">
//         <Filter size={20} className="text-gray-400" />
//         <select
//           className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#045024] focus:border-[#045024]"
//           value={priorityFilter}
//           onChange={(e) => setPriorityFilter(e.target.value)}
//         >
//           <option value="all">All Priorities</option>
//           <option value="high">High Priority</option>
//           <option value="medium">Medium Priority</option>
//           <option value="low">Low Priority</option>
//         </select>
//       </div>
//     </div>
//   </div>
// );

// interface PendingPaymentsListProps {
//   payments: Payment[];
//   onReviewPayment: (payment: Payment) => void;
// }

// const PendingPaymentsList: React.FC<PendingPaymentsListProps> = ({ payments, onReviewPayment }) => {
//   const getPriorityColor = (priority: string) => {
//     switch (priority) {
//       case 'high': return 'bg-red-100 text-red-800';
//       case 'medium': return 'bg-yellow-100 text-yellow-800';
//       case 'low': return 'bg-green-100 text-green-800';
//       default: return 'bg-gray-100 text-gray-800';
//     }
//   };

//   return (
//     <div className="bg-white rounded-lg shadow-sm border border-gray-200">
//       <div className="p-6 border-b border-gray-200">
//         <h3 className="text-lg font-semibold" style={{ color: '#045024' }}>Pending Payment Approvals</h3>
//       </div>
//       <div className="divide-y divide-gray-200">
//         {payments.map((payment) => (
//           <div key={payment.id} className="p-6 hover:bg-gray-50">
//             <div className="flex items-center justify-between">
//               <div className="flex-1">
//                 <div className="flex items-center gap-3 mb-2">
//                   <h4 className="font-semibold text-gray-900">{payment.payerName}</h4>
//                   <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(payment.priority)}`}>
//                     {payment.priority.toUpperCase()}
//                   </span>
//                 </div>
//                 <p className="text-sm text-gray-600 mb-2">{payment.description}</p>
//                 <div className="flex items-center gap-4 text-sm text-gray-500">
//                   <span>ID: {payment.id}</span>
//                   <span>Category: {payment.category}</span>
//                   <span>Requested: {new Date(payment.requestDate).toLocaleDateString()}</span>
//                   <span>By: {payment.requestedBy}</span>
//                 </div>
//               </div>
//               <div className="flex items-center gap-4">
//                 <div className="text-right">
//                   <p className="text-lg font-bold" style={{ color: '#045024' }}>
//                     ₦{payment.amount.toLocaleString()}
//                   </p>
//                 </div>
//                 <button 
//                   onClick={() => onReviewPayment(payment)}
//                   className="bg-[#045024] text-white px-4 py-2 rounded-lg hover:bg-[#2aa45c] transition-colors"
//                 >
//                   Review
//                 </button>
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };

// const PendingApprovalsDashboard: React.FC<{ onReviewPayment: (payment: Payment) => void }> = ({ onReviewPayment }) => {
//   const [searchTerm, setSearchTerm] = useState('');
//   const [priorityFilter, setPriorityFilter] = useState<string>('all');

//   const filteredPayments = pendingPayments.filter(payment => {
//     const matchesSearch = payment.payerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
//                          payment.description.toLowerCase().includes(searchTerm.toLowerCase());
//     const matchesPriority = priorityFilter === 'all' || payment.priority === priorityFilter;
//     return matchesSearch && matchesPriority;
//   });

//   return (
//     <div className="space-y-6">
//       <PendingApprovalsStats />
//       <PendingApprovalsFilters
//         searchTerm={searchTerm}
//         setSearchTerm={setSearchTerm}
//         priorityFilter={priorityFilter}
//         setPriorityFilter={setPriorityFilter}
//       />
//       <PendingPaymentsList 
//         payments={filteredPayments} 
//         onReviewPayment={onReviewPayment}
//       />
//     </div>
//   );
// };

// // Payment Approval Page Components
// interface PaymentListSidebarProps {
//   payments: Payment[];
//   selectedPayment: Payment | null;
//   onSelectPayment: (payment: Payment) => void;
// }

// const PaymentListSidebar: React.FC<PaymentListSidebarProps> = ({
//   payments,
//   selectedPayment,
//   onSelectPayment
// }) => (
//   <div className="bg-white rounded-lg shadow-sm border border-gray-200">
//     <div className="p-4 border-b border-gray-200">
//       <h3 className="font-semibold" style={{ color: '#045024' }}>Pending Payments</h3>
//     </div>
//     <div className="divide-y divide-gray-200">
//       {payments.map((payment) => (
//         <button
//           key={payment.id}
//           onClick={() => onSelectPayment(payment)}
//           className={`w-full p-4 text-left hover:bg-gray-50 ${
//             selectedPayment?.id === payment.id ? 'bg-[#045024] bg-opacity-5 border-r-4 border-[#045024]' : ''
//           }`}
//         >
//           <div className="flex justify-between items-start mb-2">
//             <h4 className="font-medium text-gray-900">{payment.payerName}</h4>
//             <span className="text-sm font-bold" style={{ color: '#045024' }}>
//               ₦{payment.amount.toLocaleString()}
//             </span>
//           </div>
//           <p className="text-sm text-gray-600 truncate">{payment.description}</p>
//           <p className="text-xs text-gray-500 mt-1">{payment.id}</p>
//         </button>
//       ))}
//     </div>
//   </div>
// );

// interface PaymentReviewDetailsProps {
//   payment: Payment;
//   approvalNotes: string;
//   setApprovalNotes: (notes: string) => void;
//   onApprove: () => void;
//   onReject: () => void;
// }

// const PaymentReviewDetails: React.FC<PaymentReviewDetailsProps> = ({
//   payment,
//   approvalNotes,
//   setApprovalNotes,
//   onApprove,
//   onReject
// }) => (
//   <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
//     <div className="flex items-center justify-between mb-6">
//       <h3 className="text-xl font-semibold" style={{ color: '#045024' }}>Payment Review</h3>
//       <span className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium">
//         Pending Approval
//       </span>
//     </div>

//     <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
//       <div>
//         <label className="block text-sm font-medium text-gray-700 mb-1">Payment ID</label>
//         <p className="text-gray-900">{payment.id}</p>
//       </div>
//       <div>
//         <label className="block text-sm font-medium text-gray-700 mb-1">Payer Name</label>
//         <p className="text-gray-900">{payment.payerName}</p>
//       </div>
//       <div>
//         <label className="block text-sm font-medium text-gray-700 mb-1">Amount</label>
//         <p className="text-2xl font-bold" style={{ color: '#045024' }}>
//           ₦{payment.amount.toLocaleString()}
//         </p>
//       </div>
//       <div>
//         <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
//         <span className={`px-2 py-1 rounded-full text-xs font-medium ${
//           payment.priority === 'high' ? 'bg-red-100 text-red-800' :
//           payment.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
//           'bg-green-100 text-green-800'
//         }`}>
//           {payment.priority.toUpperCase()}
//         </span>
//       </div>
//       <div>
//         <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
//         <p className="text-gray-900">{payment.category}</p>
//       </div>
//       <div>
//         <label className="block text-sm font-medium text-gray-700 mb-1">Request Date</label>
//         <p className="text-gray-900">{new Date(payment.requestDate).toLocaleDateString()}</p>
//       </div>
//       <div className="md:col-span-2">
//         <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
//         <p className="text-gray-900">{payment.description}</p>
//       </div>
//       <div>
//         <label className="block text-sm font-medium text-gray-700 mb-1">Requested By</label>
//         <p className="text-gray-900">{payment.requestedBy}</p>
//       </div>
//     </div>

//     <div className="mb-6">
//       <label className="block text-sm font-medium text-gray-700 mb-2">Approval Notes</label>
//       <textarea
//         className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#045024] focus:border-[#045024]"
//         rows={4}
//         placeholder="Add notes for approval or rejection..."
//         value={approvalNotes}
//         onChange={(e) => setApprovalNotes(e.target.value)}
//       />
//     </div>

//     <div className="flex gap-4">
//       <button
//         onClick={onApprove}
//         className="flex items-center gap-2 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
//       >
//         <CheckCircle size={20} />
//         Approve Payment
//       </button>
//       <button
//         onClick={onReject}
//         className="flex items-center gap-2 bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors"
//       >
//         <XCircle size={20} />
//         Reject Payment
//       </button>
//     </div>
//   </div>
// );

// const PaymentApprovalPage: React.FC<{ 
//   selectedPayment: Payment | null; 
//   onBackToDashboard: () => void;
// }> = ({ selectedPayment, onBackToDashboard }) => {
//   const [currentPayment, setCurrentPayment] = useState<Payment | null>(selectedPayment);
//   const [approvalNotes, setApprovalNotes] = useState('');

//   const handleApprove = () => {
//     if (currentPayment) {
//       alert(`Payment ${currentPayment.id} approved successfully!`);
//       setApprovalNotes('');
//       onBackToDashboard();
//     }
//   };

//   const handleReject = () => {
//     if (!currentPayment) return;
    
//     if (!approvalNotes.trim()) {
//       alert('Please provide rejection notes.');
//       return;
//     }
//     alert(`Payment ${currentPayment.id} rejected.`);
//     setApprovalNotes('');
//     onBackToDashboard();
//   };

//   if (!currentPayment) {
//     return (
//       <div className="bg-white rounded-lg p-8 text-center">
//         <FileText className="mx-auto mb-4 text-gray-400" size={48} />
//         <h3 className="text-lg font-semibold text-gray-900 mb-2">No Payment Selected</h3>
//         <p className="text-gray-600">Select a payment from the pending approvals to review.</p>
//       </div>
//     );
//   }

//   return (
//     <div className="space-y-6">
//       <div className="flex items-center gap-4">
//         <button
//           onClick={onBackToDashboard}
//           className="flex items-center gap-2 text-[#045024] hover:text-[#2aa45c] transition-colors"
//         >
//           <ArrowLeft size={20} />
//           Back to Dashboard
//         </button>
//       </div>
      
//       <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
//         <PaymentListSidebar
//           payments={pendingPayments}
//           selectedPayment={currentPayment}
//           onSelectPayment={setCurrentPayment}
//         />
//         <div className="lg:col-span-2">
//           <PaymentReviewDetails
//             payment={currentPayment}
//             approvalNotes={approvalNotes}
//             setApprovalNotes={setApprovalNotes}
//             onApprove={handleApprove}
//             onReject={handleReject}
//           />
//         </div>
//       </div>
//     </div>
//   );
// };

// // Main App Component
// const PaymentApprovalSystem: React.FC = () => {
//   const [currentView, setCurrentView] = useState<'dashboard' | 'approval'>('dashboard');
//   const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

//   const handleReviewPayment = (payment: Payment) => {
//     setSelectedPayment(payment);
//     setCurrentView('approval');
//   };

//   const handleBackToDashboard = () => {
//     setCurrentView('dashboard');
//     setSelectedPayment(null);
//   };

//   return (
//     <div className="min-h-screen bg-gray-50 p-6">
//       <div className="max-w-7xl mx-auto">
//         <div className="mb-8">
//           <h1 className="text-3xl font-bold" style={{ color: '#045024' }}>
//             Payment Approval System
//           </h1>
//           <p className="text-gray-600 mt-2">
//             {currentView === 'dashboard' ? 'Manage pending payment approvals' : 'Review payment details'}
//           </p>
//         </div>

//         {currentView === 'dashboard' ? (
//           <PendingApprovalsDashboard onReviewPayment={handleReviewPayment} />
//         ) : (
//           <PaymentApprovalPage 
//             selectedPayment={selectedPayment} 
//             onBackToDashboard={handleBackToDashboard}
//           />
//         )}
//       </div>
//     </div>
//   );
// };

// export default PaymentApprovalSystem;

import React, { useState } from 'react';
import { Clock, AlertCircle, DollarSign, Search, Filter, CheckCircle, XCircle, FileText, ArrowLeft } from 'lucide-react';

interface Payment {
  id: string;
  payerName: string;
  amount: number;
  description: string;
  requestDate: string;
  priority: 'low' | 'medium' | 'high';
  category: string;
  requestedBy: string;
}

// Mock data
const pendingPayments: Payment[] = [
  {
    id: 'PAY-001',
    payerName: 'John Smith',
    amount: 1250.00,
    description: 'Monthly service fee payment',
    requestDate: '2025-06-28',
    priority: 'high',
    category: 'Service Fees',
    requestedBy: 'Finance Officer A'
  },
  {
    id: 'PAY-002',
    payerName: 'ABC Corporation',
    amount: 5000.00,
    description: 'Quarterly subscription payment',
    requestDate: '2025-06-27',
    priority: 'medium',
    category: 'Subscriptions',
    requestedBy: 'Finance Officer B'
  },
  {
    id: 'PAY-003',
    payerName: 'Mary Johnson',
    amount: 750.50,
    description: 'Utility bill payment',
    requestDate: '2025-06-29',
    priority: 'low',
    category: 'Utilities',
    requestedBy: 'Finance Officer A'
  }
];

// Dashboard Components
const PendingApprovalsStats: React.FC = () => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">Pending Approvals</p>
          <p className="text-2xl font-bold" style={{ color: '#045024' }}>{pendingPayments.length}</p>
        </div>
        <Clock className="text-orange-500" size={24} />
      </div>
    </div>
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">High Priority</p>
          <p className="text-2xl font-bold text-red-600">
            {pendingPayments.filter(p => p.priority === 'high').length}
          </p>
        </div>
        <AlertCircle className="text-red-500" size={24} />
      </div>
    </div>
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">Total Value</p>
          <p className="text-2xl font-bold" style={{ color: '#045024' }}>
            ₦{pendingPayments.reduce((sum, p) => sum + p.amount, 0).toLocaleString()}
          </p>
        </div>
        <DollarSign className="text-green-500" size={24} />
      </div>
    </div>
  </div>
);

interface PendingApprovalsFiltersProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  priorityFilter: string;
  setPriorityFilter: (priority: string) => void;
}

const PendingApprovalsFilters: React.FC<PendingApprovalsFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  priorityFilter,
  setPriorityFilter
}) => (
  <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
    <div className="flex flex-col md:flex-row gap-4">
      <div className="flex-1">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search by payer name or description..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#045024] focus:border-[#045024]"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Filter size={20} className="text-gray-400" />
        <select
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#045024] focus:border-[#045024]"
          value={priorityFilter}
          onChange={(e) => setPriorityFilter(e.target.value)}
        >
          <option value="all">All Priorities</option>
          <option value="high">High Priority</option>
          <option value="medium">Medium Priority</option>
          <option value="low">Low Priority</option>
        </select>
      </div>
    </div>
  </div>
);

interface PendingPaymentsListProps {
  payments: Payment[];
  onReviewPayment: (payment: Payment) => void;
}

const PendingPaymentsList: React.FC<PendingPaymentsListProps> = ({ payments, onReviewPayment }) => {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-lg font-semibold" style={{ color: '#045024' }}>Pending Payment Approvals</h3>
      </div>
      <div className="divide-y divide-gray-200">
        {payments.map((payment) => (
          <div key={payment.id} className="p-6 hover:bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h4 className="font-semibold text-gray-900">{payment.payerName}</h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(payment.priority)}`}>
                    {payment.priority.toUpperCase()}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{payment.description}</p>
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <span>ID: {payment.id}</span>
                  <span>Category: {payment.category}</span>
                  <span>Requested: {new Date(payment.requestDate).toLocaleDateString()}</span>
                  <span>By: {payment.requestedBy}</span>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div className="text-right">
                  <p className="text-lg font-bold" style={{ color: '#045024' }}>
                    ₦{payment.amount.toLocaleString()}
                  </p>
                </div>
                <button 
                  onClick={() => onReviewPayment(payment)}
                  className="bg-[#045024] text-white px-4 py-2 rounded-lg hover:bg-[#2aa45c] transition-colors"
                >
                  Review
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const PendingApprovalsDashboard: React.FC<{ onReviewPayment: (payment: Payment) => void }> = ({ onReviewPayment }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  const filteredPayments = pendingPayments.filter(payment => {
    const matchesSearch = payment.payerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPriority = priorityFilter === 'all' || payment.priority === priorityFilter;
    return matchesSearch && matchesPriority;
  });

  return (
    <div className="space-y-6">
      <PendingApprovalsStats />
      <PendingApprovalsFilters
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        priorityFilter={priorityFilter}
        setPriorityFilter={setPriorityFilter}
      />
      <PendingPaymentsList 
        payments={filteredPayments} 
        onReviewPayment={onReviewPayment}
      />
    </div>
  );
};

// Payment Approval Page Components
interface PaymentListSidebarProps {
  payments: Payment[];
  selectedPayment: Payment | null;
  onSelectPayment: (payment: Payment) => void;
}

const PaymentListSidebar: React.FC<PaymentListSidebarProps> = ({
  payments,
  selectedPayment,
  onSelectPayment
}) => (
  <div className="bg-white rounded-lg shadow-sm border border-gray-200">
    <div className="p-4 border-b border-gray-200">
      <h3 className="font-semibold" style={{ color: '#045024' }}>Pending Payments</h3>
    </div>
    <div className="divide-y divide-gray-200">
      {payments.map((payment) => (
        <button
          key={payment.id}
          onClick={() => onSelectPayment(payment)}
          className={`w-full p-4 text-left hover:bg-gray-50 ${
            selectedPayment?.id === payment.id ? 'bg-[#045024] bg-opacity-5 border-r-4 border-[#045024]' : ''
          }`}
        >
          <div className="flex justify-between items-start mb-2">
            <h4 className="font-medium text-gray-900">{payment.payerName}</h4>
            <span className="text-sm font-bold" style={{ color: '#045024' }}>
              ₦{payment.amount.toLocaleString()}
            </span>
          </div>
          <p className="text-sm text-gray-600 truncate">{payment.description}</p>
          <p className="text-xs text-gray-500 mt-1">{payment.id}</p>
        </button>
      ))}
    </div>
  </div>
);

interface PaymentReviewDetailsProps {
  payment: Payment;
  approvalNotes: string;
  setApprovalNotes: (notes: string) => void;
  onApprove: () => void;
  onReject: () => void;
}

const PaymentReviewDetails: React.FC<PaymentReviewDetailsProps> = ({
  payment,
  approvalNotes,
  setApprovalNotes,
  onApprove,
  onReject
}) => (
  <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
    <div className="flex items-center justify-between mb-6">
      <h3 className="text-xl font-semibold" style={{ color: '#045024' }}>Payment Review</h3>
      <span className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium">
        Pending Approval
      </span>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Payment ID</label>
        <p className="text-gray-900">{payment.id}</p>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Payer Name</label>
        <p className="text-gray-900">{payment.payerName}</p>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Amount</label>
        <p className="text-2xl font-bold" style={{ color: '#045024' }}>
          ₦{payment.amount.toLocaleString()}
        </p>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          payment.priority === 'high' ? 'bg-red-100 text-red-800' :
          payment.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
          'bg-green-100 text-green-800'
        }`}>
          {payment.priority.toUpperCase()}
        </span>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
        <p className="text-gray-900">{payment.category}</p>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Request Date</label>
        <p className="text-gray-900">{new Date(payment.requestDate).toLocaleDateString()}</p>
      </div>
      <div className="md:col-span-2">
        <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
        <p className="text-gray-900">{payment.description}</p>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Requested By</label>
        <p className="text-gray-900">{payment.requestedBy}</p>
      </div>
    </div>

    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">Approval Notes</label>
      <textarea
        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#045024] focus:border-[#045024]"
        rows={4}
        placeholder="Add notes for approval or rejection..."
        value={approvalNotes}
        onChange={(e) => setApprovalNotes(e.target.value)}
      />
    </div>

    <div className="flex gap-4">
      <button
        onClick={onApprove}
        className="flex items-center gap-2 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
      >
        <CheckCircle size={20} />
        Approve Payment
      </button>
      <button
        onClick={onReject}
        className="flex items-center gap-2 bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors"
      >
        <XCircle size={20} />
        Reject Payment
      </button>
    </div>
  </div>
);

const PaymentApprovalPage: React.FC<{ 
  selectedPayment: Payment | null; 
  onBackToDashboard: () => void;
}> = ({ selectedPayment, onBackToDashboard }) => {
  const [approvalNotes, setApprovalNotes] = useState('');

  const handleApprove = () => {
    if (selectedPayment) {
      alert(`Payment ${selectedPayment.id} approved successfully!`);
      setApprovalNotes('');
      onBackToDashboard();
    }
  };

  const handleReject = () => {
    if (!selectedPayment) return;
    
    if (!approvalNotes.trim()) {
      alert('Please provide rejection notes.');
      return;
    }
    alert(`Payment ${selectedPayment.id} rejected.`);
    setApprovalNotes('');
    onBackToDashboard();
  };

  if (!selectedPayment) {
    return (
      <div className="bg-white rounded-lg p-8 text-center">
        <FileText className="mx-auto mb-4 text-gray-400" size={48} />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Payment Selected</h3>
        <p className="text-gray-600">Select a payment from the pending approvals to review.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <button
          onClick={onBackToDashboard}
          className="flex items-center gap-2 text-[#045024] hover:text-[#2aa45c] transition-colors"
        >
          <ArrowLeft size={20} />
          Back to Dashboard
        </button>
      </div>
      
      <div className="max-w-4xl mx-auto">
        <PaymentReviewDetails
          payment={selectedPayment}
          approvalNotes={approvalNotes}
          setApprovalNotes={setApprovalNotes}
          onApprove={handleApprove}
          onReject={handleReject}
        />
      </div>
    </div>
  );
};

// Main App Component
const PaymentApprovalSystem: React.FC = () => {
  const [currentView, setCurrentView] = useState<'dashboard' | 'approval'>('dashboard');
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

  const handleReviewPayment = (payment: Payment) => {
    setSelectedPayment(payment);
    setCurrentView('approval');
  };

  const handleBackToDashboard = () => {
    setCurrentView('dashboard');
    setSelectedPayment(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold" style={{ color: '#045024' }}>
            Payment Approval System
          </h1>
          <p className="text-gray-600 mt-2">
            {currentView === 'dashboard' ? 'Manage pending payment approvals' : 'Review payment details'}
          </p>
        </div>

        {currentView === 'dashboard' ? (
          <PendingApprovalsDashboard onReviewPayment={handleReviewPayment} />
        ) : (
          <PaymentApprovalPage 
            selectedPayment={selectedPayment} 
            onBackToDashboard={handleBackToDashboard}
          />
        )}
      </div>
    </div>
  );
};

export default PaymentApprovalSystem;