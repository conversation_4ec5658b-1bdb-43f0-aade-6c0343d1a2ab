# Document Templates

This directory contains branded templates for receipts and compliance certificates.

## Directory Structure for JRB Service

JRB provides tax services to multiple organizations and uses generalized templates:

```
templates/
├── receipts/
│   ├── jrb-receipt-template.png              # ONE template for all receipts
│   └── default-receipt-template.png          # Fallback
└── certificates/
    ├── jrb-certificate-landscape-template.png # Landscape certificates (Annual License)
    ├── jrb-certificate-portrait-template.png  # Portrait certificates (Tax Clearance, etc.)
    └── default-certificate-template.png       # Fallback
```

## JRB Template Usage:
```
JRB Receipt Template:
├── Used for ALL receipt types
├── Company A payment → jrb-receipt-template.png
├── Company B payment → jrb-receipt-template.png
└── Company C payment → jrb-receipt-template.png

JRB Certificate Templates:
├── Annual License → jrb-certificate-landscape-template.png
├── Tax Clearance → jrb-certificate-portrait-template.png
├── Quarterly Compliance → jrb-certificate-portrait-template.png
└── Payment Compliance → jrb-certificate-portrait-template.png
```

## Template Requirements

### Image Format
- **Format**: PNG or JPG
- **Resolution**: 300 DPI minimum
- **Size**: A4 dimensions (2480 x 3508 pixels at 300 DPI)

### Receipt Templates
- **Orientation**: Portrait
- **Organization branding**: Logo, letterhead, colors
- **Text areas**: Leave blank spaces for dynamic text overlay

### Certificate Templates
- **Orientation**: Landscape or Portrait (configurable per organization/type)
- **Official elements**: Seals, signatures, watermarks
- **Text areas**: Leave blank spaces for dynamic text overlay

## Text Positioning

Text positions are configured in code in the `BrandedDocumentService` class:
- Each organization has specific text positions
- Different positions for different document types
- Coordinates are in pixels from top-left corner

## Adding New Templates

1. Create branded template image with blank text areas
2. Save in appropriate directory with naming convention:
   - Receipts: `{organizationId}-receipt-template.png`
   - Certificates: `{organizationId}-{certificateType}-template.png`
3. Update text positions in `BrandedDocumentService.cs`
4. Test with sample data

## Template Naming Convention

- **Organization ID**: Use the organization's database ID
- **Certificate Types**: 
  - `annual_license` - Annual Business License
  - `tax_clearance` - Tax Clearance Certificate
  - `quarterly_compliance` - Quarterly Compliance
  - `payment_compliance` - Payment Compliance

## Examples

For Joint Revenue Board (organizationId: "jrb-001"):
- `jrb-001-receipt-template.png`
- `jrb-001-annual_license-template.png`
- `jrb-001-annual_license-landscape-template.png`
- `jrb-001-tax_clearance-template.png`
- `default-certificate-template.png`

For any other organization (organizationId: "org-123"):
- `org-123-receipt-template.png`
- `org-123-annual_license-template.png`
- `org-123-tax_clearance-template.png`
