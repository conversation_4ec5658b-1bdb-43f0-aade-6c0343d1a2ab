-- DUMMY TEST DATA FOR JRB PAYMENT MANAGEMENT SYSTEM
-- This file contains sample data for testing all system functionality
-- Run this AFTER deploying the complete database schema

-- ===== ORGANIZATIONS (3 sample organizations that pay taxes to JRB) =====

INSERT INTO Organizations (Id, Name, Email, PhoneNumber, Address, ContactPerson, IsActive, CreatedAt) VALUES
('org-001', 'ABC Manufacturing Ltd', '<EMAIL>', '+***********-5678', '123 Industrial Avenue, Lagos', '<PERSON>', 1, GETDATE()),
('org-002', 'XYZ Trading Company', '<EMAIL>', '+234-************', '456 Commercial Street, Abuja', 'Mary Okafor', 1, GETDATE()),
('org-003', 'Tech Solutions Nigeria', '<EMAIL>', '+***********-7890', '789 Technology Park, Port Harcourt', 'David Em<PERSON>', 1, GETDATE());

-- ===== USERS (JRB staff and organization contacts) =====

-- JRB Admin User
INSERT INTO Users (Id, FirstName, LastName, Email, PhoneNumber, Role, OrganizationId, IsActive, CreatedAt, AzureAdObjectId) VALUES
('user-admin-001', 'Sarah', 'Johnson', '<EMAIL>', '+***********-1111', 'ADMIN', NULL, 1, GETDATE(), 'azure-admin-001');

-- JRB Finance Officers
INSERT INTO Users (Id, FirstName, LastName, Email, PhoneNumber, Role, OrganizationId, IsActive, CreatedAt, AzureAdObjectId) VALUES
('user-fo-001', 'Michael', 'Okonkwo', '<EMAIL>', '+***********-2222', 'FINANCE_OFFICER', NULL, 1, GETDATE(), 'azure-fo-001'),
('user-fo-002', 'Grace', 'Adebola', '<EMAIL>', '+***********-3333', 'FINANCE_OFFICER', NULL, 1, GETDATE(), 'azure-fo-002');

-- JRB Senior Finance Officer
INSERT INTO Users (Id, FirstName, LastName, Email, PhoneNumber, Role, OrganizationId, IsActive, CreatedAt, AzureAdObjectId) VALUES
('user-sfo-001', 'Dr. Ahmed', 'Ibrahim', '<EMAIL>', '+234-************', 'SENIOR_FINANCE_OFFICER', NULL, 1, GETDATE(), 'azure-sfo-001');

-- Organization Payers (contacts from taxpaying organizations)
INSERT INTO Users (Id, FirstName, LastName, Email, PhoneNumber, Role, OrganizationId, IsActive, CreatedAt, AzureAdObjectId) VALUES
('user-payer-001', 'John', 'Adebayo', '<EMAIL>', '+***********-5678', 'PAYER', 'org-001', 1, GETDATE(), 'azure-payer-001'),
('user-payer-002', 'Mary', 'Okafor', '<EMAIL>', '+234-************', 'PAYER', 'org-002', 1, GETDATE(), 'azure-payer-002'),
('user-payer-003', 'David', 'Emeka', '<EMAIL>', '+***********-7890', 'PAYER', 'org-003', 1, GETDATE(), 'azure-payer-003');

-- ===== PAYMENT PROFILES (Tax services offered by JRB) =====

INSERT INTO PaymentProfiles (Id, Name, Description, Category, IsActive, CreatedAt, CreatedBy) VALUES
('profile-001', 'Annual Business License', 'Annual business registration and licensing fee for commercial operations', 'LICENSE', 1, GETDATE(), 'user-admin-001'),
('profile-002', 'Corporate Income Tax', 'Annual corporate income tax payment for registered businesses', 'TAX', 1, GETDATE(), 'user-admin-001'),
('profile-003', 'Quarterly VAT Returns', 'Quarterly Value Added Tax returns and payments', 'TAX', 1, GETDATE(), 'user-admin-001');

-- ===== PAYMENT SCHEDULES (What organizations need to pay) =====

-- ABC Manufacturing schedules
INSERT INTO PaymentSchedules (Id, OrganizationId, PaymentProfileId, Description, Amount, Currency, DueDate, Status, CreatedAt, CreatedBy) VALUES
('schedule-001', 'org-001', 'profile-001', 'ABC Manufacturing - Annual Business License 2024', 75000.00, 'NGN', '2024-03-31', 'PAID', GETDATE(), 'user-admin-001'),
('schedule-002', 'org-001', 'profile-002', 'ABC Manufacturing - Corporate Income Tax 2024', 250000.00, 'NGN', '2024-04-30', 'PENDING', GETDATE(), 'user-admin-001'),
('schedule-003', 'org-001', 'profile-003', 'ABC Manufacturing - Q1 VAT Returns 2024', 45000.00, 'NGN', '2024-04-21', 'PAID', GETDATE(), 'user-admin-001');

-- XYZ Trading schedules
INSERT INTO PaymentSchedules (Id, OrganizationId, PaymentProfileId, Description, Amount, Currency, DueDate, Status, CreatedAt, CreatedBy) VALUES
('schedule-004', 'org-002', 'profile-001', 'XYZ Trading - Annual Business License 2024', 50000.00, 'NGN', '2024-03-31', 'PAID', GETDATE(), 'user-admin-001'),
('schedule-005', 'org-002', 'profile-002', 'XYZ Trading - Corporate Income Tax 2024', 180000.00, 'NGN', '2024-04-30', 'PENDING', GETDATE(), 'user-admin-001'),
('schedule-006', 'org-002', 'profile-003', 'XYZ Trading - Q1 VAT Returns 2024', 32000.00, 'NGN', '2024-04-21', 'OVERDUE', GETDATE(), 'user-admin-001');

-- Tech Solutions schedules
INSERT INTO PaymentSchedules (Id, OrganizationId, PaymentProfileId, Description, Amount, Currency, DueDate, Status, CreatedAt, CreatedBy) VALUES
('schedule-007', 'org-003', 'profile-001', 'Tech Solutions - Annual Business License 2024', 60000.00, 'NGN', '2024-03-31', 'PAID', GETDATE(), 'user-admin-001'),
('schedule-008', 'org-003', 'profile-002', 'Tech Solutions - Corporate Income Tax 2024', 320000.00, 'NGN', '2024-04-30', 'PENDING', GETDATE(), 'user-admin-001'),
('schedule-009', 'org-003', 'profile-003', 'Tech Solutions - Q1 VAT Returns 2024', 58000.00, 'NGN', '2024-04-21', 'PAID', GETDATE(), 'user-admin-001');

-- ===== PAYMENTS (Actual payments made by organizations) =====

-- ABC Manufacturing payments
INSERT INTO Payments (Id, PayerId, PayerName, PayerEmail, Amount, Currency, PaymentMethod, TransactionReference, Status, Description, Category, CreatedAt, CompletedAt, OrganizationId, PaymentScheduleId, AcknowledgedBy, AcknowledgedDate, AcknowledgmentNotes, ApprovedBy, ApprovedDate, ApprovalNotes) VALUES
('payment-001', 'user-payer-001', 'ABC Manufacturing Ltd', '<EMAIL>', 75000.00, 'NGN', 'Bank Transfer', 'TXN-ABC-001-2024', 'Completed', 'Annual Business License Payment', 'LICENSE', DATEADD(day, -15, GETDATE()), DATEADD(day, -10, GETDATE()), 'org-001', 'schedule-001', 'user-fo-001', DATEADD(day, -12, GETDATE()), 'Payment proof verified and acknowledged', 'user-sfo-001', DATEADD(day, -10, GETDATE()), 'Payment approved and processed'),
('payment-002', 'user-payer-001', 'ABC Manufacturing Ltd', '<EMAIL>', 45000.00, 'NGN', 'Online Payment', 'TXN-ABC-002-2024', 'Completed', 'Q1 VAT Returns Payment', 'TAX', DATEADD(day, -8, GETDATE()), DATEADD(day, -5, GETDATE()), 'org-001', 'schedule-003', 'user-fo-002', DATEADD(day, -7, GETDATE()), 'Online payment confirmed', 'user-sfo-001', DATEADD(day, -5, GETDATE()), 'VAT payment approved');

-- XYZ Trading payments
INSERT INTO Payments (Id, PayerId, PayerName, PayerEmail, Amount, Currency, PaymentMethod, TransactionReference, Status, Description, Category, CreatedAt, CompletedAt, OrganizationId, PaymentScheduleId, AcknowledgedBy, AcknowledgedDate, AcknowledgmentNotes, ApprovedBy, ApprovedDate, ApprovalNotes) VALUES
('payment-003', 'user-payer-002', 'XYZ Trading Company', '<EMAIL>', 50000.00, 'NGN', 'Bank Transfer', 'TXN-XYZ-001-2024', 'Completed', 'Annual Business License Payment', 'LICENSE', DATEADD(day, -20, GETDATE()), DATEADD(day, -18, GETDATE()), 'org-002', 'schedule-004', 'user-fo-001', DATEADD(day, -19, GETDATE()), 'Bank transfer confirmed', 'user-sfo-001', DATEADD(day, -18, GETDATE()), 'License payment approved');

-- Tech Solutions payments
INSERT INTO Payments (Id, PayerId, PayerName, PayerEmail, Amount, Currency, PaymentMethod, TransactionReference, Status, Description, Category, CreatedAt, CompletedAt, OrganizationId, PaymentScheduleId, AcknowledgedBy, AcknowledgedDate, AcknowledgmentNotes, ApprovedBy, ApprovedDate, ApprovalNotes) VALUES
('payment-004', 'user-payer-003', 'Tech Solutions Nigeria', '<EMAIL>', 60000.00, 'NGN', 'Bank Transfer', 'TXN-TECH-001-2024', 'Completed', 'Annual Business License Payment', 'LICENSE', DATEADD(day, -12, GETDATE()), DATEADD(day, -8, GETDATE()), 'org-003', 'schedule-007', 'user-fo-002', DATEADD(day, -10, GETDATE()), 'Payment verified successfully', 'user-sfo-001', DATEADD(day, -8, GETDATE()), 'Tech company license approved'),
('payment-005', 'user-payer-003', 'Tech Solutions Nigeria', '<EMAIL>', 58000.00, 'NGN', 'Online Payment', 'TXN-TECH-002-2024', 'Completed', 'Q1 VAT Returns Payment', 'TAX', DATEADD(day, -6, GETDATE()), DATEADD(day, -3, GETDATE()), 'org-003', 'schedule-009', 'user-fo-001', DATEADD(day, -5, GETDATE()), 'VAT payment processed', 'user-sfo-001', DATEADD(day, -3, GETDATE()), 'Q1 VAT approved');

-- ===== RECEIPTS (Generated for completed payments) =====

INSERT INTO Receipts (Id, PayerId, PayerName, PayerEmail, ReceiptNumber, Amount, Currency, PaymentMethod, PaymentDate, Description, Category, CreatedAt, CreatedBy, OrganizationId, PaymentId, IsRevoked, NotificationSent, NotificationSentDate) VALUES
('receipt-001', 'user-payer-001', 'ABC Manufacturing Ltd', '<EMAIL>', 'JRB-RCP-2024-001', 75000.00, 'NGN', 'Bank Transfer', DATEADD(day, -10, GETDATE()), 'Annual Business License Payment', 'LICENSE', DATEADD(day, -10, GETDATE()), 'user-sfo-001', 'org-001', 'payment-001', 0, 1, DATEADD(day, -10, GETDATE())),
('receipt-002', 'user-payer-001', 'ABC Manufacturing Ltd', '<EMAIL>', 'JRB-RCP-2024-002', 45000.00, 'NGN', 'Online Payment', DATEADD(day, -5, GETDATE()), 'Q1 VAT Returns Payment', 'TAX', DATEADD(day, -5, GETDATE()), 'user-sfo-001', 'org-001', 'payment-002', 0, 1, DATEADD(day, -5, GETDATE())),
('receipt-003', 'user-payer-002', 'XYZ Trading Company', '<EMAIL>', 'JRB-RCP-2024-003', 50000.00, 'NGN', 'Bank Transfer', DATEADD(day, -18, GETDATE()), 'Annual Business License Payment', 'LICENSE', DATEADD(day, -18, GETDATE()), 'user-sfo-001', 'org-002', 'payment-003', 0, 1, DATEADD(day, -18, GETDATE())),
('receipt-004', 'user-payer-003', 'Tech Solutions Nigeria', '<EMAIL>', 'JRB-RCP-2024-004', 60000.00, 'NGN', 'Bank Transfer', DATEADD(day, -8, GETDATE()), 'Annual Business License Payment', 'LICENSE', DATEADD(day, -8, GETDATE()), 'user-sfo-001', 'org-003', 'payment-004', 0, 1, DATEADD(day, -8, GETDATE())),
('receipt-005', 'user-payer-003', 'Tech Solutions Nigeria', '<EMAIL>', 'JRB-RCP-2024-005', 58000.00, 'NGN', 'Online Payment', DATEADD(day, -3, GETDATE()), 'Q1 VAT Returns Payment', 'TAX', DATEADD(day, -3, GETDATE()), 'user-sfo-001', 'org-003', 'payment-005', 0, 1, DATEADD(day, -3, GETDATE()));

-- ===== COMPLIANCE CERTIFICATES (Generated for completed payment profiles) =====

INSERT INTO ComplianceCertificates (Id, CertificateNumber, OrganizationId, OrganizationName, PaymentProfileId, PaymentProfileName, CertificateType, Status, TotalAmount, Currency, ValidFrom, ValidUntil, IssuedDate, IssuedBy, Description, Terms, IsRevoked, CreatedAt, CreatedBy, ComplianceYear, CompliancePeriod, RegulatoryBody, LicenseCategory, Notes) VALUES
('cert-001', 'AL-2024-1001', 'org-001', 'ABC Manufacturing Ltd', 'profile-001', 'Annual Business License', 'ANNUAL_LICENSE', 'ISSUED', 75000.00, 'NGN', '2024-01-01', '2024-12-31', DATEADD(day, -10, GETDATE()), 'user-sfo-001', 'Annual Business License Certificate for ABC Manufacturing Ltd', 'This certificate is valid for the period specified and must be renewed annually. Certificate holder must comply with all applicable regulations.', 0, DATEADD(day, -10, GETDATE()), 'user-sfo-001', '2024', 'ANNUAL', 'Joint Revenue Board', 'GENERAL', 'Auto-generated certificate for completed annual license payment'),
('cert-002', 'AL-2024-1002', 'org-002', 'XYZ Trading Company', 'profile-001', 'Annual Business License', 'ANNUAL_LICENSE', 'ISSUED', 50000.00, 'NGN', '2024-01-01', '2024-12-31', DATEADD(day, -18, GETDATE()), 'user-sfo-001', 'Annual Business License Certificate for XYZ Trading Company', 'This certificate is valid for the period specified and must be renewed annually. Certificate holder must comply with all applicable regulations.', 0, DATEADD(day, -18, GETDATE()), 'user-sfo-001', '2024', 'ANNUAL', 'Joint Revenue Board', 'GENERAL', 'Auto-generated certificate for completed annual license payment'),
('cert-003', 'AL-2024-1003', 'org-003', 'Tech Solutions Nigeria', 'profile-001', 'Annual Business License', 'ANNUAL_LICENSE', 'ISSUED', 60000.00, 'NGN', '2024-01-01', '2024-12-31', DATEADD(day, -8, GETDATE()), 'user-sfo-001', 'Annual Business License Certificate for Tech Solutions Nigeria', 'This certificate is valid for the period specified and must be renewed annually. Certificate holder must comply with all applicable regulations.', 0, DATEADD(day, -8, GETDATE()), 'user-sfo-001', '2024', 'ANNUAL', 'Joint Revenue Board', 'GENERAL', 'Auto-generated certificate for completed annual license payment');

-- ===== FILE UPLOADS (Sample uploaded files) =====

INSERT INTO FileUploads (Id, FileName, OriginalFileName, FilePath, ContentType, FileSize, FileHash, UploadedBy, OrganizationId, RelatedEntityType, RelatedEntityId, CreatedAt, IsActive, IsScanned, ScanResult, ScannedAt, Description, Category) VALUES
('file-001', 'payment_proof_001.pdf', 'ABC_Bank_Transfer_Proof.pdf', '/uploads/2024/03/payment_proof_001.pdf', 'application/pdf', 245760, 'sha256hash001', 'user-payer-001', 'org-001', 'PAYMENT', 'payment-001', DATEADD(day, -15, GETDATE()), 1, 1, 'CLEAN', DATEADD(day, -15, GETDATE()), 'Bank transfer proof for annual license payment', 'PAYMENT_PROOF'),
('file-002', 'payment_proof_002.jpg', 'ABC_Online_Payment_Screenshot.jpg', '/uploads/2024/03/payment_proof_002.jpg', 'image/jpeg', 156432, 'sha256hash002', 'user-payer-001', 'org-001', 'PAYMENT', 'payment-002', DATEADD(day, -8, GETDATE()), 1, 1, 'CLEAN', DATEADD(day, -8, GETDATE()), 'Online payment screenshot for VAT payment', 'PAYMENT_PROOF'),
('file-003', 'payment_proof_003.pdf', 'XYZ_Bank_Statement.pdf', '/uploads/2024/03/payment_proof_003.pdf', 'application/pdf', 312456, 'sha256hash003', 'user-payer-002', 'org-002', 'PAYMENT', 'payment-003', DATEADD(day, -20, GETDATE()), 1, 1, 'CLEAN', DATEADD(day, -20, GETDATE()), 'Bank statement showing license payment', 'PAYMENT_PROOF'),
('file-004', 'receipt_001.pdf', 'JRB-RCP-2024-001.pdf', '/uploads/receipts/2024/03/receipt_001.pdf', 'application/pdf', 89234, 'sha256hash004', 'user-sfo-001', 'org-001', 'RECEIPT', 'receipt-001', DATEADD(day, -10, GETDATE()), 1, 0, NULL, NULL, 'Generated receipt PDF for ABC Manufacturing', 'RECEIPT_PDF'),
('file-005', 'certificate_001.pdf', 'AL-2024-1001.pdf', '/uploads/certificates/2024/03/certificate_001.pdf', 'application/pdf', 134567, 'sha256hash005', 'user-sfo-001', 'org-001', 'CERTIFICATE', 'cert-001', DATEADD(day, -10, GETDATE()), 1, 0, NULL, NULL, 'Generated certificate PDF for ABC Manufacturing', 'CERTIFICATE_PDF'),
('file-006', 'certificate_002.pdf', 'AL-2024-1002.pdf', '/uploads/certificates/2024/03/certificate_002.pdf', 'application/pdf', 128934, 'sha256hash006', 'user-sfo-001', 'org-002', 'CERTIFICATE', 'cert-002', DATEADD(day, -18, GETDATE()), 1, 0, NULL, NULL, 'Generated certificate PDF for XYZ Trading', 'CERTIFICATE_PDF');

-- ===== EMAIL TEMPLATES (JRB notification templates) =====

INSERT INTO EmailTemplates (Id, OrganizationId, Name, Description, Subject, BodyContent, TemplateContent, Type, IsDefault, CreatedAt, CreatedBy) VALUES
('template-001', NULL, 'Receipt Notification', 'Email template for receipt notifications', 'Payment Receipt - {{ReceiptNumber}}', 'Dear {{PayerName}}, Your payment receipt {{ReceiptNumber}} for {{Description}} has been generated.', '<html><body><h2>Joint Revenue Board</h2><p>Dear {{PayerName}},</p><p>Your payment receipt <strong>{{ReceiptNumber}}</strong> for {{Description}} has been generated.</p><p>Amount: {{Currency}} {{Amount}}</p><p>Payment Date: {{PaymentDate}}</p><p>Thank you for your compliance.</p></body></html>', 'RECEIPT_NOTIFICATION', 1, GETDATE(), 'user-admin-001'),
('template-002', NULL, 'Certificate Issued', 'Email template for certificate issuance', 'Compliance Certificate Issued - {{CertificateNumber}}', 'Dear {{OrganizationName}}, Your compliance certificate {{CertificateNumber}} has been issued.', '<html><body><h2>Joint Revenue Board</h2><p>Dear {{OrganizationName}},</p><p>Your compliance certificate <strong>{{CertificateNumber}}</strong> has been issued.</p><p>Certificate Type: {{CertificateType}}</p><p>Valid From: {{ValidFrom}} to {{ValidUntil}}</p><p>Please find the certificate attached.</p></body></html>', 'CERTIFICATE_ISSUED', 1, GETDATE(), 'user-admin-001'),
('template-003', NULL, 'Payment Reminder', 'Email template for payment reminders', 'Payment Reminder - {{Description}}', 'Dear {{OrganizationName}}, This is a reminder for your upcoming payment due on {{DueDate}}.', '<html><body><h2>Joint Revenue Board</h2><p>Dear {{OrganizationName}},</p><p>This is a reminder for your upcoming payment:</p><p><strong>{{Description}}</strong></p><p>Amount: {{Currency}} {{Amount}}</p><p>Due Date: {{DueDate}}</p><p>Please ensure timely payment to avoid penalties.</p></body></html>', 'PAYMENT_REMINDER', 1, GETDATE(), 'user-admin-001');

-- ===== EMAIL CONFIGURATIONS (JRB email settings) =====

INSERT INTO EmailConfigurations (Id, OrganizationId, SmtpServer, SmtpPort, SmtpUsername, SmtpPassword, EnableSsl, IsDefault, SenderName, FromEmail, CreatedAt, CreatedBy) VALUES
('email-config-001', NULL, 'smtp.jrb.gov.ng', 587, '<EMAIL>', 'encrypted_password_123', 1, 1, 'Joint Revenue Board', '<EMAIL>', GETDATE(), 'user-admin-001'),
('email-config-002', NULL, 'smtp.gmail.com', 587, '<EMAIL>', 'encrypted_backup_password', 1, 0, 'JRB Backup System', '<EMAIL>', GETDATE(), 'user-admin-001');

-- ===== USER INVITATIONS (Sample pending invitations) =====

INSERT INTO UserInvitations (Id, Email, Role, OrganizationId, InvitedDate, Status, InvitedBy, ExpiryDate) VALUES
('invite-001', '<EMAIL>', 'FINANCE_OFFICER', NULL, GETDATE(), 'Pending', 'user-admin-001', DATEADD(day, 7, GETDATE())),
('invite-002', '<EMAIL>', 'PAYER', 'org-001', DATEADD(day, -2, GETDATE()), 'Pending', 'user-admin-001', DATEADD(day, 5, GETDATE())),
('invite-003', '<EMAIL>', 'PAYER', 'org-002', DATEADD(day, -10, GETDATE()), 'Expired', 'user-admin-001', DATEADD(day, -3, GETDATE()));

-- ===== UPDATE PAYMENT SCHEDULES WITH PAYMENT IDs =====

-- Link completed payments to their schedules
UPDATE PaymentSchedules SET PaymentId = 'payment-001', PaidAmount = 75000.00, PaidDate = DATEADD(day, -10, GETDATE()) WHERE Id = 'schedule-001';
UPDATE PaymentSchedules SET PaymentId = 'payment-002', PaidAmount = 45000.00, PaidDate = DATEADD(day, -5, GETDATE()) WHERE Id = 'schedule-003';
UPDATE PaymentSchedules SET PaymentId = 'payment-003', PaidAmount = 50000.00, PaidDate = DATEADD(day, -18, GETDATE()) WHERE Id = 'schedule-004';
UPDATE PaymentSchedules SET PaymentId = 'payment-004', PaidAmount = 60000.00, PaidDate = DATEADD(day, -8, GETDATE()) WHERE Id = 'schedule-007';
UPDATE PaymentSchedules SET PaymentId = 'payment-005', PaidAmount = 58000.00, PaidDate = DATEADD(day, -3, GETDATE()) WHERE Id = 'schedule-009';

-- ===== UPDATE PAYMENTS WITH RECEIPT IDs =====

-- Link payments to their generated receipts
UPDATE Payments SET ReceiptId = 'receipt-001' WHERE Id = 'payment-001';
UPDATE Payments SET ReceiptId = 'receipt-002' WHERE Id = 'payment-002';
UPDATE Payments SET ReceiptId = 'receipt-003' WHERE Id = 'payment-003';
UPDATE Payments SET ReceiptId = 'receipt-004' WHERE Id = 'payment-004';
UPDATE Payments SET ReceiptId = 'receipt-005' WHERE Id = 'payment-005';

-- ===== UPDATE PAYMENTS WITH PROOF FILE IDs =====

-- Link payments to their proof files
UPDATE Payments SET ProofFileId = 'file-001' WHERE Id = 'payment-001';
UPDATE Payments SET ProofFileId = 'file-002' WHERE Id = 'payment-002';
UPDATE Payments SET ProofFileId = 'file-003' WHERE Id = 'payment-003';

-- ===== UPDATE CERTIFICATES WITH PDF FILE IDs =====

-- Link certificates to their generated PDF files
UPDATE ComplianceCertificates SET CertificatePdfFileId = 'file-005' WHERE Id = 'cert-001';
UPDATE ComplianceCertificates SET CertificatePdfFileId = 'file-006' WHERE Id = 'cert-002';

-- ===== VERIFICATION QUERIES (Run these to verify data was inserted correctly) =====

/*
-- Verify Organizations
SELECT COUNT(*) as OrganizationCount FROM Organizations;
SELECT Name, Email, ContactPerson FROM Organizations;

-- Verify Users by Role
SELECT Role, COUNT(*) as UserCount FROM Users GROUP BY Role;

-- Verify Payment Profiles
SELECT Name, Category, IsActive FROM PaymentProfiles;

-- Verify Payment Schedules by Status
SELECT Status, COUNT(*) as ScheduleCount FROM PaymentSchedules GROUP BY Status;

-- Verify Payments by Status
SELECT Status, COUNT(*) as PaymentCount FROM Payments GROUP BY Status;

-- Verify Receipts
SELECT COUNT(*) as ReceiptCount FROM Receipts;
SELECT ReceiptNumber, PayerName, Amount FROM Receipts;

-- Verify Certificates
SELECT COUNT(*) as CertificateCount FROM ComplianceCertificates;
SELECT CertificateNumber, OrganizationName, CertificateType, Status FROM ComplianceCertificates;

-- Verify File Uploads by Type
SELECT RelatedEntityType, COUNT(*) as FileCount FROM FileUploads GROUP BY RelatedEntityType;

-- Verify Email Templates
SELECT Name, Type, IsDefault FROM EmailTemplates;

-- Verify User Invitations by Status
SELECT Status, COUNT(*) as InvitationCount FROM UserInvitations GROUP BY Status;
*/
