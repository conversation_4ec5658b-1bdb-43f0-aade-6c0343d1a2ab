import React, { useState, useEffect } from 'react';
import { 
  Bell, 
  Mail, 
  Smartphone, 
  Save, 
  RefreshCw,
  DollarSign,
  Receipt,
  Shield,
  AlertTriangle,
  Clock,
  CheckCircle
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';

interface NotificationPreference {
  type: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  inApp: boolean;
  email: boolean;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
}

interface NotificationSettingsProps {
  className?: string;
}

const NotificationSettings: React.FC<NotificationSettingsProps> = ({
  className = '',
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [preferences, setPreferences] = useState<NotificationPreference[]>([
    {
      type: 'PAYMENT_DUE',
      label: 'Payment Due Reminders',
      description: 'Get notified when payments are due soon',
      icon: <DollarSign size={16} className="text-orange-500" />,
      inApp: true,
      email: true,
      priority: 'HIGH',
    },
    {
      type: 'PAYMENT_OVERDUE',
      label: 'Overdue Payment Alerts',
      description: 'Get notified when payments are overdue',
      icon: <AlertTriangle size={16} className="text-red-500" />,
      inApp: true,
      email: true,
      priority: 'URGENT',
    },
    {
      type: 'RECEIPT_GENERATED',
      label: 'Receipt Generated',
      description: 'Get notified when receipts are generated',
      icon: <Receipt size={16} className="text-green-500" />,
      inApp: true,
      email: false,
      priority: 'MEDIUM',
    },
    {
      type: 'PAYMENT_APPROVED',
      label: 'Payment Approved',
      description: 'Get notified when payments are approved',
      icon: <CheckCircle size={16} className="text-green-500" />,
      inApp: true,
      email: true,
      priority: 'MEDIUM',
    },
    {
      type: 'PAYMENT_SCHEDULE_CREATED',
      label: 'Payment Schedule Created',
      description: 'Get notified when new payment schedules are created for you',
      icon: <Clock size={16} className="text-blue-500" />,
      inApp: true,
      email: true,
      priority: 'MEDIUM',
    },
    {
      type: 'PAYMENT_SCHEDULE_UPDATED',
      label: 'Payment Schedule Updated',
      description: 'Get notified when your payment schedules are modified',
      icon: <Clock size={16} className="text-yellow-500" />,
      inApp: true,
      email: true,
      priority: 'MEDIUM',
    },
    {
      type: 'PAYMENT_SCHEDULE_DELETED',
      label: 'Payment Schedule Cancelled',
      description: 'Get notified when your payment schedules are cancelled',
      icon: <Clock size={16} className="text-red-500" />,
      inApp: true,
      email: true,
      priority: 'HIGH',
    },
    {
      type: 'BULK_SCHEDULES_IMPORTED',
      label: 'Bulk Schedules Imported',
      description: 'Get notified when payment schedules are imported in bulk',
      icon: <DollarSign size={16} className="text-green-500" />,
      inApp: true,
      email: false,
      priority: 'MEDIUM',
    },
    {
      type: 'CERTIFICATE_EXPIRING',
      label: 'Certificate Expiring',
      description: 'Get notified when compliance certificates are expiring',
      icon: <Shield size={16} className="text-yellow-500" />,
      inApp: true,
      email: true,
      priority: 'HIGH',
    },
    {
      type: 'CERTIFICATE_EXPIRED',
      label: 'Certificate Expired',
      description: 'Get notified when compliance certificates have expired',
      icon: <Shield size={16} className="text-red-500" />,
      inApp: true,
      email: true,
      priority: 'URGENT',
    },
    {
      type: 'SYSTEM_ALERT',
      label: 'System Alerts',
      description: 'Get notified about important system updates',
      icon: <Bell size={16} className="text-blue-500" />,
      inApp: true,
      email: false,
      priority: 'MEDIUM',
    },
  ]);

  const [globalSettings, setGlobalSettings] = useState({
    inAppEnabled: true,
    emailEnabled: true,
    quietHours: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00',
    },
    digestEmail: {
      enabled: true,
      frequency: 'DAILY', // DAILY, WEEKLY, NEVER
      time: '09:00',
    },
  });

  useEffect(() => {
    loadNotificationSettings();
  }, [user?.id]);

  const loadNotificationSettings = async () => {
    if (!user?.id) return;
    
    setLoading(true);
    try {
      // In a real implementation, you would load user's notification preferences from the backend
      // For now, we'll use the default preferences
      console.log('Loading notification settings for user:', user.id);
    } catch (error) {
      console.error('Failed to load notification settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePreferenceChange = (type: string, field: 'inApp' | 'email', value: boolean) => {
    setPreferences(prev => prev.map(pref => 
      pref.type === type ? { ...pref, [field]: value } : pref
    ));
  };

  const handleGlobalSettingChange = (setting: string, value: any) => {
    setGlobalSettings(prev => ({
      ...prev,
      [setting]: value,
    }));
  };

  const handleSaveSettings = async () => {
    setSaving(true);
    try {
      // In a real implementation, you would save the settings to the backend
      const settingsData = {
        preferences,
        globalSettings,
      };
      
      console.log('Saving notification settings:', settingsData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Show success message (you might want to use a toast notification)
      alert('Notification settings saved successfully!');
    } catch (error) {
      console.error('Failed to save notification settings:', error);
      alert('Failed to save settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const getPriorityBadge = (priority: string) => {
    const colors = {
      LOW: 'bg-gray-100 text-gray-800',
      MEDIUM: 'bg-yellow-100 text-yellow-800',
      HIGH: 'bg-orange-100 text-orange-800',
      URGENT: 'bg-red-100 text-red-800',
    };
    
    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${colors[priority as keyof typeof colors]}`}>
        {priority}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2aa45c]"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
            <Bell className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Notification Settings</h2>
            <p className="text-gray-600">Manage how you receive notifications</p>
          </div>
        </div>
        <button
          onClick={handleSaveSettings}
          disabled={saving}
          className="bg-[#2aa45c] text-white px-4 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2 disabled:opacity-50"
        >
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Saving...</span>
            </>
          ) : (
            <>
              <Save size={16} />
              <span>Save Settings</span>
            </>
          )}
        </button>
      </div>

      {/* Global Settings */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Global Settings</h3>
        
        <div className="space-y-4">
          {/* Master Toggles */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <Bell size={20} className="text-blue-500" />
                <div>
                  <p className="font-medium text-gray-900">In-App Notifications</p>
                  <p className="text-sm text-gray-600">Show notifications in the app</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={globalSettings.inAppEnabled}
                  onChange={(e) => handleGlobalSettingChange('inAppEnabled', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2aa45c]"></div>
              </label>
            </div>

            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <Mail size={20} className="text-green-500" />
                <div>
                  <p className="font-medium text-gray-900">Email Notifications</p>
                  <p className="text-sm text-gray-600">Send notifications via email</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={globalSettings.emailEnabled}
                  onChange={(e) => handleGlobalSettingChange('emailEnabled', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2aa45c]"></div>
              </label>
            </div>
          </div>

          {/* Quiet Hours */}
          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                <Clock size={20} className="text-purple-500" />
                <div>
                  <p className="font-medium text-gray-900">Quiet Hours</p>
                  <p className="text-sm text-gray-600">Disable notifications during specific hours</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={globalSettings.quietHours.enabled}
                  onChange={(e) => handleGlobalSettingChange('quietHours', {
                    ...globalSettings.quietHours,
                    enabled: e.target.checked
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2aa45c]"></div>
              </label>
            </div>
            
            {globalSettings.quietHours.enabled && (
              <div className="grid grid-cols-2 gap-4 mt-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                  <input
                    type="time"
                    value={globalSettings.quietHours.startTime}
                    onChange={(e) => handleGlobalSettingChange('quietHours', {
                      ...globalSettings.quietHours,
                      startTime: e.target.value
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                  <input
                    type="time"
                    value={globalSettings.quietHours.endTime}
                    onChange={(e) => handleGlobalSettingChange('quietHours', {
                      ...globalSettings.quietHours,
                      endTime: e.target.value
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Notification Preferences */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Types</h3>
        
        <div className="space-y-4">
          {preferences.map((preference) => (
            <div key={preference.type} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-3">
                {preference.icon}
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <p className="font-medium text-gray-900">{preference.label}</p>
                    {getPriorityBadge(preference.priority)}
                  </div>
                  <p className="text-sm text-gray-600">{preference.description}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                {/* In-App Toggle */}
                <div className="flex items-center space-x-2">
                  <Bell size={16} className="text-gray-400" />
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preference.inApp && globalSettings.inAppEnabled}
                      onChange={(e) => handlePreferenceChange(preference.type, 'inApp', e.target.checked)}
                      disabled={!globalSettings.inAppEnabled}
                      className="sr-only peer"
                    />
                    <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-[#2aa45c] peer-disabled:opacity-50"></div>
                  </label>
                </div>
                
                {/* Email Toggle */}
                <div className="flex items-center space-x-2">
                  <Mail size={16} className="text-gray-400" />
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preference.email && globalSettings.emailEnabled}
                      onChange={(e) => handlePreferenceChange(preference.type, 'email', e.target.checked)}
                      disabled={!globalSettings.emailEnabled}
                      className="sr-only peer"
                    />
                    <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-[#2aa45c] peer-disabled:opacity-50"></div>
                  </label>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NotificationSettings;
