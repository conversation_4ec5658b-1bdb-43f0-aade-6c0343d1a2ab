// import { apiService } from "./apiService";

// export interface AuthDetectionResponse {
//   authMethod: "microsoft" | "local" | null;
//   userExists: boolean;
//   requiresSetup?: boolean;
//   message?: string;
// }
// class AuthDetectionService {
//   /**
//    * Detect authentication method based on email
//    */
//   async detectAuthMethod(email: string): Promise<AuthDetectionResponse> {
//     try {
//       console.log("Detecting auth method for email:", email);

//       const response = await apiService.post<AuthDetectionResponse>(
//         "/auth/detect",
//         {
//           email: email.trim().toLowerCase(),
//         }
//       );

//       console.log("Auth detection result:", response);
//       return response;
//     } catch (error: any) {
//       console.error("Auth detection failed:", error);

//       // Handle 404 - no user found
//       if (error.message?.includes("404")) {
//         return {
//           authMethod: null,
//           userExists: false,
//           message:
//             "No account found for this email address. Please contact your administrator.",
//         };
//       }

//       // Handle other errors
//       throw new Error(
//         "Failed to detect authentication method. Please try again."
//       );
//     }
//   }
// }

// export const authDetectionService = new AuthDetectionService();

import { apiService } from "./apiService";

export interface AuthDetectionResponse {
  authMethod: "microsoft" | "local" | null;
  userExists: boolean;
  requiresSetup?: boolean;
  message?: string;
}

class AuthDetectionService {
  async detectAuthMethod(email: string): Promise<AuthDetectionResponse> {
    try {
      const response = await apiService.post<AuthDetectionResponse>(
        "/auth/detect",
        { email: email.trim().toLowerCase() }
      );
      return response;
    } catch (error: any) {
      console.error("Auth detection failed:", error);
      if (error.message?.includes("404")) {
        return {
          authMethod: null,
          userExists: false,
          message:
            "No account found for this email address. Please contact your administrator.",
        };
      }

      throw new Error(
        "Failed to detect authentication method. Please try again."
      );
    }
  }
}

export const authDetectionService = new AuthDetectionService();
