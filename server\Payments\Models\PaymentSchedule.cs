using System;

namespace Final_E_Receipt.Payments.Models
{
    public class PaymentSchedule
    {
        public string Id { get; set; }
        public string PaymentProfileId { get; set; }
        public string OrganizationId { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "NGN";
        public DateTime DueDate { get; set; }
        public string Status { get; set; } = "PENDING"; // PENDING, REPORTED, CONFIRMED, PAID
        public string PaymentId { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
        
        // Additional properties for display purposes (not stored in database)
        public string ProfileName { get; set; }
        public string OrganizationName { get; set; }
    }
}