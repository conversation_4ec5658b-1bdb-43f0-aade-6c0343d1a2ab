using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Final_E_Receipt.Infrastructure.Swagger
{
    public class SwaggerIgnoreFilter : IDocumentFilter
    {
        public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
        {
            var pathsToRemove = new List<string>();

            foreach (var path in swaggerDoc.Paths)
            {
                var operationsToRemove = new List<OperationType>();

                foreach (var operation in path.Value.Operations)
                {
                    var apiDescription = context.ApiDescriptions
                        .FirstOrDefault(x => x.RelativePath?.TrimStart('/') == path.Key.TrimStart('/') &&
                                           x.HttpMethod?.Equals(operation.Key.ToString(), StringComparison.OrdinalIgnoreCase) == true);

                    if (apiDescription?.ActionDescriptor is Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor actionDescriptor)
                    {
                        var hasIgnoreAttribute = actionDescriptor.MethodInfo.GetCustomAttributes(typeof(SwaggerIgnoreAttribute), false).Any() ||
                                               actionDescriptor.ControllerTypeInfo.GetCustomAttributes(typeof(SwaggerIgnoreAttribute), false).Any();

                        if (hasIgnoreAttribute)
                        {
                            operationsToRemove.Add(operation.Key);
                        }
                    }
                }

                foreach (var operationType in operationsToRemove)
                {
                    path.Value.Operations.Remove(operationType);
                }

                if (!path.Value.Operations.Any())
                {
                    pathsToRemove.Add(path.Key);
                }
            }

            foreach (var path in pathsToRemove)
            {
                swaggerDoc.Paths.Remove(path);
            }
        }
    }
}
