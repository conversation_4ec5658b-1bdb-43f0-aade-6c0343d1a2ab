-- Payment Profiles Table
CREATE TABLE PaymentProfiles (
    Id NVARCHAR(50) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500),
    Amount DECIMAL(18, 2),
    Currency NVARCHAR(10) NOT NULL DEFAULT 'NGN',
    Frequency NVARCHAR(20) NOT NULL, -- ONCE, MONTHLY, QUARTERLY, ANNUALLY
    DueDay INT, -- Day of month when payment is due
    IsFixedAmount BIT NOT NULL DEFAULT 1, -- If false, amount can vary per organization
    IsActive BIT NOT NULL DEFAULT 1,
    PaymentTypeId NVARCHAR(50), -- Link to PaymentType
    OrganizationId NVARCHAR(50), -- If null, applies to all organizations
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50),
    UpdatedAt DATETIME NULL,
    UpdatedBy NVARCHAR(50) NULL
);

-- Payment Schedules Table
CREATE TABLE PaymentSchedules (
    Id NVARCHAR(50) PRIMARY KEY,
    PaymentProfileId NVARCHAR(50) NOT NULL,
    OrganizationId NVARCHAR(50) NOT NULL,
    Amount DECIMAL(18, 2) NOT NULL,
    Currency NVARCHAR(10) NOT NULL DEFAULT 'NGN',
    DueDate DATETIME NOT NULL,
    Status NVARCHAR(20) NOT NULL DEFAULT 'PENDING', -- PENDING, REPORTED, CONFIRMED, PAID
    PaymentId NVARCHAR(50) NULL, -- Link to payment when paid
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50),
    UpdatedAt DATETIME NULL,
    UpdatedBy NVARCHAR(50) NULL,
    FOREIGN KEY (PaymentProfileId) REFERENCES PaymentProfiles(Id),
    FOREIGN KEY (OrganizationId) REFERENCES Organizations(Id)
);

-- Create Payment Profile
CREATE PROCEDURE CreatePaymentProfile
    @Id NVARCHAR(50),
    @Name NVARCHAR(100),
    @Description NVARCHAR(500),
    @Amount DECIMAL(18, 2),
    @Currency NVARCHAR(10),
    @Frequency NVARCHAR(20),
    @DueDay INT,
    @IsFixedAmount BIT,
    @IsActive BIT,
    @PaymentTypeId NVARCHAR(50),
    @OrganizationId NVARCHAR(50),
    @CreatedBy NVARCHAR(50)
AS
BEGIN
    INSERT INTO PaymentProfiles (
        Id, Name, Description, Amount, Currency, Frequency,
        DueDay, IsFixedAmount, IsActive, PaymentTypeId, OrganizationId, CreatedBy
    )
    VALUES (
        @Id, @Name, @Description, @Amount, @Currency, @Frequency,
        @DueDay, @IsFixedAmount, @IsActive, @PaymentTypeId, @OrganizationId, @CreatedBy
    )

    SELECT * FROM PaymentProfiles WHERE Id = @Id
END;

-- Get Payment Profile By Id
CREATE PROCEDURE GetPaymentProfileById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM PaymentProfiles WHERE Id = @Id
END;

-- Get Payment Profiles By Organization
CREATE PROCEDURE GetPaymentProfilesByOrganization
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM PaymentProfiles 
    WHERE OrganizationId = @OrganizationId OR OrganizationId IS NULL
    ORDER BY Name
END;

-- Update Payment Profile
CREATE PROCEDURE UpdatePaymentProfile
    @Id NVARCHAR(50),
    @Name NVARCHAR(100),
    @Description NVARCHAR(500),
    @Amount DECIMAL(18, 2),
    @Currency NVARCHAR(10),
    @Frequency NVARCHAR(20),
    @DueDay INT,
    @IsFixedAmount BIT,
    @IsActive BIT,
    @PaymentTypeId NVARCHAR(50),
    @OrganizationId NVARCHAR(50),
    @UpdatedBy NVARCHAR(50)
AS
BEGIN
    UPDATE PaymentProfiles
    SET
        Name = @Name,
        Description = @Description,
        Amount = @Amount,
        Currency = @Currency,
        Frequency = @Frequency,
        DueDay = @DueDay,
        IsFixedAmount = @IsFixedAmount,
        IsActive = @IsActive,
        PaymentTypeId = @PaymentTypeId,
        OrganizationId = @OrganizationId,
        UpdatedAt = GETDATE(),
        UpdatedBy = @UpdatedBy
    WHERE Id = @Id
    
    SELECT * FROM PaymentProfiles WHERE Id = @Id
END;

-- Delete Payment Profile
CREATE PROCEDURE DeletePaymentProfile
    @Id NVARCHAR(50)
AS
BEGIN
    -- First check if there are any payment schedules using this profile
    IF EXISTS (SELECT 1 FROM PaymentSchedules WHERE PaymentProfileId = @Id)
    BEGIN
        RAISERROR('Cannot delete profile with existing payment schedules', 16, 1)
        RETURN
    END
    
    DELETE FROM PaymentProfiles WHERE Id = @Id
END;

-- Create Payment Schedule
CREATE PROCEDURE CreatePaymentSchedule
    @Id NVARCHAR(50),
    @PaymentProfileId NVARCHAR(50),
    @OrganizationId NVARCHAR(50),
    @Amount DECIMAL(18, 2),
    @Currency NVARCHAR(10),
    @DueDate DATETIME,
    @Status NVARCHAR(20),
    @CreatedBy NVARCHAR(50)
AS
BEGIN
    INSERT INTO PaymentSchedules (
        Id, PaymentProfileId, OrganizationId, Amount, Currency, 
        DueDate, Status, CreatedBy
    )
    VALUES (
        @Id, @PaymentProfileId, @OrganizationId, @Amount, @Currency, 
        @DueDate, @Status, @CreatedBy
    )
    
    SELECT * FROM PaymentSchedules WHERE Id = @Id
END;

-- Get Payment Schedule By Id
CREATE PROCEDURE GetPaymentScheduleById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM PaymentSchedules WHERE Id = @Id
END;

-- Get Payment Schedules By Organization
CREATE PROCEDURE GetPaymentSchedulesByOrganization
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT 
        ps.*,
        pp.Name AS ProfileName,
        pp.Description AS ProfileDescription
    FROM PaymentSchedules ps
    JOIN PaymentProfiles pp ON ps.PaymentProfileId = pp.Id
    WHERE ps.OrganizationId = @OrganizationId
    ORDER BY ps.DueDate
END;

-- Get Payment Schedules By Profile
CREATE PROCEDURE GetPaymentSchedulesByProfile
    @PaymentProfileId NVARCHAR(50)
AS
BEGIN
    SELECT 
        ps.*,
        o.Name AS OrganizationName
    FROM PaymentSchedules ps
    JOIN Organizations o ON ps.OrganizationId = o.Id
    WHERE ps.PaymentProfileId = @PaymentProfileId
    ORDER BY ps.DueDate
END;

-- Update Payment Schedule Status
CREATE PROCEDURE UpdatePaymentScheduleStatus
    @Id NVARCHAR(50),
    @Status NVARCHAR(20),
    @PaymentId NVARCHAR(50),
    @UpdatedBy NVARCHAR(50)
AS
BEGIN
    UPDATE PaymentSchedules
    SET 
        Status = @Status,
        PaymentId = @PaymentId,
        UpdatedAt = GETDATE(),
        UpdatedBy = @UpdatedBy
    WHERE Id = @Id
    
    SELECT * FROM PaymentSchedules WHERE Id = @Id
END;

-- Delete Payment Schedule
CREATE PROCEDURE DeletePaymentSchedule
    @Id NVARCHAR(50)
AS
BEGIN
    -- Check if the schedule has been paid
    IF EXISTS (SELECT 1 FROM PaymentSchedules WHERE Id = @Id AND Status IN ('CONFIRMED', 'PAID'))
    BEGIN
        RAISERROR('Cannot delete a payment schedule that has been paid', 16, 1)
        RETURN
    END
    
    DELETE FROM PaymentSchedules WHERE Id = @Id
END;

-- Bulk Create Payment Schedules
CREATE PROCEDURE BulkCreatePaymentSchedules
    @PaymentProfileId NVARCHAR(50),
    @CreatedBy NVARCHAR(50)
AS
BEGIN
    DECLARE @ProfileAmount DECIMAL(18, 2)
    DECLARE @ProfileCurrency NVARCHAR(10)
    DECLARE @ProfileIsFixedAmount BIT
    DECLARE @ProfileOrganizationId NVARCHAR(50)
    DECLARE @DueDate DATETIME
    
    -- Get profile details
    SELECT 
        @ProfileAmount = Amount,
        @ProfileCurrency = Currency,
        @ProfileIsFixedAmount = IsFixedAmount,
        @ProfileOrganizationId = OrganizationId,
        @DueDate = DATEFROMPARTS(YEAR(GETDATE()), MONTH(GETDATE()), DueDay)
    FROM PaymentProfiles
    WHERE Id = @PaymentProfileId
    
    -- If due day has passed this month, set to next month
    IF @DueDate < GETDATE()
    BEGIN
        SET @DueDate = DATEADD(MONTH, 1, @DueDate)
    END
    
    -- If profile is for specific organization, create only for that org
    IF @ProfileOrganizationId IS NOT NULL
    BEGIN
        INSERT INTO PaymentSchedules (
            Id, PaymentProfileId, OrganizationId, Amount, Currency, 
            DueDate, Status, CreatedBy
        )
        VALUES (
            NEWID(), @PaymentProfileId, @ProfileOrganizationId, @ProfileAmount, @ProfileCurrency, 
            @DueDate, 'PENDING', @CreatedBy
        )
    END
    ELSE -- Create for all active organizations
    BEGIN
        INSERT INTO PaymentSchedules (
            Id, PaymentProfileId, OrganizationId, Amount, Currency, 
            DueDate, Status, CreatedBy
        )
        SELECT 
            NEWID(), @PaymentProfileId, Id, 
            CASE WHEN @ProfileIsFixedAmount = 1 THEN @ProfileAmount ELSE 0 END, 
            @ProfileCurrency, @DueDate, 'PENDING', @CreatedBy
        FROM Organizations
        WHERE IsActive = 1
    END
    
    -- Return created schedules
    SELECT 
        ps.*,
        pp.Name AS ProfileName,
        o.Name AS OrganizationName
    FROM PaymentSchedules ps
    JOIN PaymentProfiles pp ON ps.PaymentProfileId = pp.Id
    JOIN Organizations o ON ps.OrganizationId = o.Id
    WHERE ps.PaymentProfileId = @PaymentProfileId
      AND ps.DueDate = @DueDate
END;

-- Add PaymentTypeId column to existing PaymentProfiles table if it doesn't exist
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PaymentProfiles' AND COLUMN_NAME = 'PaymentTypeId')
BEGIN
    ALTER TABLE PaymentProfiles ADD PaymentTypeId NVARCHAR(50) NULL;
END;