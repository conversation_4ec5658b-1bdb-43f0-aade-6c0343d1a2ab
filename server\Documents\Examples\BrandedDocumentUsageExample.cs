using System;
using System.Threading.Tasks;
using Final_E_Receipt.Documents.Services;
using Final_E_Receipt.Receipts.Models;
using Final_E_Receipt.Compliance.Models;

namespace Final_E_Receipt.Documents.Examples
{
    /// <summary>
    /// Example usage of the BrandedDocumentService for generating receipts and certificates
    /// </summary>
    public class BrandedDocumentUsageExample
    {
        private readonly BrandedDocumentService _brandedDocumentService;

        public BrandedDocumentUsageExample(BrandedDocumentService brandedDocumentService)
        {
            _brandedDocumentService = brandedDocumentService;
        }

        /// <summary>
        /// Example: Generate LIRS receipt
        /// </summary>
        public async Task<byte[]> GenerateLirsReceiptExample()
        {
            var receipt = new Receipt
            {
                Id = Guid.NewGuid().ToString(),
                ReceiptNumber = "RCP-2024-001",
                PayerName = "ABC Company Limited",
                PayerEmail = "<EMAIL>",
                Amount = 50000.00m,
                Currency = "NGN",
                PaymentDate = DateTime.Now,
                PaymentMethod = "Bank Transfer",
                Description = "Annual Business License Payment",
                OrganizationId = "lirs-001", // LIRS organization
                CreatedBy = "system"
            };

            // Generate branded receipt PDF
            return await _brandedDocumentService.GenerateReceipt(receipt);
        }

        /// <summary>
        /// Example: Generate FIRS receipt
        /// </summary>
        public async Task<byte[]> GenerateFirsReceiptExample()
        {
            var receipt = new Receipt
            {
                Id = Guid.NewGuid().ToString(),
                ReceiptNumber = "RCP-2024-002",
                PayerName = "XYZ Corporation",
                PayerEmail = "<EMAIL>",
                Amount = 100000.00m,
                Currency = "NGN",
                PaymentDate = DateTime.Now,
                PaymentMethod = "Online Payment",
                Description = "Corporate Income Tax",
                OrganizationId = "firs-001", // FIRS organization
                CreatedBy = "system"
            };

            // Generate branded receipt PDF
            return await _brandedDocumentService.GenerateReceipt(receipt);
        }

        /// <summary>
        /// Example: Generate LIRS Annual License Certificate (Landscape)
        /// </summary>
        public async Task<byte[]> GenerateLirsAnnualLicenseCertificateExample()
        {
            var certificate = new ComplianceCertificate
            {
                Id = Guid.NewGuid().ToString(),
                CertificateNumber = "AL-2024-1234",
                OrganizationName = "ABC Company Limited",
                PaymentProfileName = "Annual Business License",
                CertificateType = "ANNUAL_LICENSE",
                TotalAmount = 50000.00m,
                Currency = "NGN",
                ValidFrom = new DateTime(2024, 1, 1),
                ValidUntil = new DateTime(2024, 12, 31),
                IssuedDate = DateTime.Now,
                RegulatoryBody = "Lagos Internal Revenue Service",
                ComplianceYear = "2024",
                OrganizationId = "lirs-001", // LIRS organization
                CreatedBy = "system"
            };

            // Generate branded certificate PDF (Landscape orientation)
            return await _brandedDocumentService.GenerateComplianceCertificate(certificate);
        }

        /// <summary>
        /// Example: Generate FIRS Tax Clearance Certificate (Portrait)
        /// </summary>
        public async Task<byte[]> GenerateFirsTaxClearanceCertificateExample()
        {
            var certificate = new ComplianceCertificate
            {
                Id = Guid.NewGuid().ToString(),
                CertificateNumber = "TC-2024-5678",
                OrganizationName = "XYZ Corporation",
                PaymentProfileName = "Corporate Income Tax",
                CertificateType = "TAX_CLEARANCE",
                TotalAmount = 100000.00m,
                Currency = "NGN",
                ValidFrom = new DateTime(2024, 1, 1),
                ValidUntil = new DateTime(2024, 12, 31),
                IssuedDate = DateTime.Now,
                RegulatoryBody = "Federal Inland Revenue Service",
                ComplianceYear = "2024",
                OrganizationId = "firs-001", // FIRS organization
                CreatedBy = "system"
            };

            // Generate branded certificate PDF (Portrait orientation)
            return await _brandedDocumentService.GenerateComplianceCertificate(certificate);
        }

        /// <summary>
        /// Example: Generate document with default template (fallback)
        /// </summary>
        public async Task<byte[]> GenerateDefaultReceiptExample()
        {
            var receipt = new Receipt
            {
                Id = Guid.NewGuid().ToString(),
                ReceiptNumber = "RCP-2024-003",
                PayerName = "Default Organization",
                PayerEmail = "<EMAIL>",
                Amount = 25000.00m,
                Currency = "NGN",
                PaymentDate = DateTime.Now,
                PaymentMethod = "Cash",
                Description = "General Payment",
                OrganizationId = "unknown-org", // Will use default template
                CreatedBy = "system"
            };

            // Generate receipt with default template
            return await _brandedDocumentService.GenerateReceipt(receipt);
        }

        /// <summary>
        /// Example: Save generated document to file
        /// </summary>
        public async Task SaveDocumentExample()
        {
            // Generate a receipt
            var receiptPdf = await GenerateLirsReceiptExample();

            // Save to file
            var fileName = $"receipt-{DateTime.Now:yyyyMMdd-HHmmss}.pdf";
            var filePath = Path.Combine("generated-documents", fileName);
            
            // Ensure directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(filePath));
            
            // Write to file
            await File.WriteAllBytesAsync(filePath, receiptPdf);
            
            Console.WriteLine($"Receipt saved to: {filePath}");
        }
    }
}

/*
USAGE INSTRUCTIONS:

1. Template Setup:
   - Place branded template images in wwwroot/templates/
   - Follow naming convention: {organizationId}-{documentType}-template.png
   - Examples:
     * lirs-001-receipt-template.png
     * firs-001-receipt-template.png
     * lirs-001-annual_license-template.png
     * firs-001-tax_clearance-template.png

2. Text Positioning:
   - Text positions are configured in BrandedDocumentService.cs
   - Each organization has specific coordinates for text placement
   - Coordinates are in pixels from top-left corner

3. Document Generation:
   - Receipts: Always portrait orientation
   - Certificates: Can be landscape or portrait per organization/type
   - Different fonts, colors, and styling per organization

4. Integration:
   - ReceiptService automatically generates branded receipts
   - ComplianceCertificateService automatically generates branded certificates
   - Both use the same BrandedDocumentService under the hood

5. File Storage:
   - Generated PDFs are automatically stored using FileService
   - Files are linked to receipts/certificates in database
   - Temporary files are cleaned up after upload
*/
