using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Reporting.Services
{
    public class ReportCacheService
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<ReportCacheService> _logger;
        
        // Cache durations
        private readonly TimeSpan _dashboardCacheDuration = TimeSpan.FromMinutes(15);
        private readonly TimeSpan _organizationReportCacheDuration = TimeSpan.FromMinutes(30);
        private readonly TimeSpan _metricsCacheDuration = TimeSpan.FromMinutes(10);
        private readonly TimeSpan _expiringCertificatesCacheDuration = TimeSpan.FromMinutes(5);

        public ReportCacheService(IMemoryCache cache, ILogger<ReportCacheService> logger)
        {
            _cache = cache;
            _logger = logger;
        }

        /// <summary>
        /// Gets or sets compliance dashboard data with caching
        /// </summary>
        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getItem, TimeSpan? cacheDuration = null)
        {
            if (_cache.TryGetValue(key, out T cachedItem))
            {
                _logger.LogDebug("Cache hit for key: {Key}", key);
                return cachedItem;
            }

            _logger.LogDebug("Cache miss for key: {Key}", key);
            var item = await getItem();
            
            var duration = cacheDuration ?? _dashboardCacheDuration;
            var cacheEntryOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = duration,
                SlidingExpiration = TimeSpan.FromMinutes(5),
                Priority = CacheItemPriority.Normal
            };

            _cache.Set(key, item, cacheEntryOptions);
            _logger.LogDebug("Cached item with key: {Key} for {Duration}", key, duration);
            
            return item;
        }

        /// <summary>
        /// Gets cached compliance dashboard
        /// </summary>
        public async Task<ComplianceDashboard> GetOrSetDashboard(Func<Task<ComplianceDashboard>> getDashboard)
        {
            return await GetOrSetAsync("compliance_dashboard", getDashboard, _dashboardCacheDuration);
        }

        /// <summary>
        /// Gets cached organization compliance report
        /// </summary>
        public async Task<ComplianceReport> GetOrSetOrganizationReport(string organizationId, Func<Task<ComplianceReport>> getReport)
        {
            var key = $"org_compliance_report_{organizationId}";
            return await GetOrSetAsync(key, getReport, _organizationReportCacheDuration);
        }

        /// <summary>
        /// Gets cached compliance metrics
        /// </summary>
        public async Task<ComplianceMetrics> GetOrSetMetrics(Func<Task<ComplianceMetrics>> getMetrics)
        {
            return await GetOrSetAsync("compliance_metrics", getMetrics, _metricsCacheDuration);
        }

        /// <summary>
        /// Gets cached expiring certificates report
        /// </summary>
        public async Task<ExpiringCertificatesReport> GetOrSetExpiringCertificates(int daysFromNow, Func<Task<ExpiringCertificatesReport>> getReport)
        {
            var key = $"expiring_certificates_{daysFromNow}";
            return await GetOrSetAsync(key, getReport, _expiringCertificatesCacheDuration);
        }

        /// <summary>
        /// Gets cached certificate issuance statistics
        /// </summary>
        public async Task<CertificateIssuanceStats> GetOrSetIssuanceStats(DateTime fromDate, DateTime toDate, Func<Task<CertificateIssuanceStats>> getStats)
        {
            var key = $"issuance_stats_{fromDate:yyyyMMdd}_{toDate:yyyyMMdd}";
            return await GetOrSetAsync(key, getStats, TimeSpan.FromHours(1));
        }

        /// <summary>
        /// Invalidates cache for a specific organization
        /// </summary>
        public void InvalidateOrganizationCache(string organizationId)
        {
            var keys = new[]
            {
                $"org_compliance_report_{organizationId}",
                "compliance_dashboard",
                "compliance_metrics"
            };

            foreach (var key in keys)
            {
                _cache.Remove(key);
                _logger.LogDebug("Invalidated cache for key: {Key}", key);
            }
        }

        /// <summary>
        /// Invalidates all compliance-related cache
        /// </summary>
        public void InvalidateAllComplianceCache()
        {
            // Since IMemoryCache doesn't provide a way to enumerate keys,
            // we'll need to track keys manually or use a more sophisticated caching solution
            // For now, we'll clear specific known patterns
            
            var commonKeys = new[]
            {
                "compliance_dashboard",
                "compliance_metrics",
                "expiring_certificates_30",
                "expiring_certificates_60",
                "expiring_certificates_90"
            };

            foreach (var key in commonKeys)
            {
                _cache.Remove(key);
            }

            _logger.LogInformation("Invalidated all compliance cache");
        }

        /// <summary>
        /// Invalidates cache when certificates are created, updated, or deleted
        /// </summary>
        public void InvalidateCacheOnCertificateChange(string organizationId, string action)
        {
            _logger.LogInformation("Invalidating cache due to certificate {Action} for organization {OrganizationId}", action, organizationId);
            
            // Invalidate organization-specific cache
            InvalidateOrganizationCache(organizationId);
            
            // Invalidate expiring certificates cache
            var expiringKeys = new[]
            {
                "expiring_certificates_7",
                "expiring_certificates_14",
                "expiring_certificates_30",
                "expiring_certificates_60",
                "expiring_certificates_90"
            };

            foreach (var key in expiringKeys)
            {
                _cache.Remove(key);
            }

            // Invalidate issuance stats cache for current period
            var currentMonth = DateTime.Now.ToString("yyyyMM");
            var issuanceKeys = new[]
            {
                $"issuance_stats_{DateTime.Now.AddMonths(-12):yyyyMMdd}_{DateTime.Now:yyyyMMdd}",
                $"issuance_stats_{DateTime.Now.AddMonths(-6):yyyyMMdd}_{DateTime.Now:yyyyMMdd}",
                $"issuance_stats_{DateTime.Now.AddMonths(-3):yyyyMMdd}_{DateTime.Now:yyyyMMdd}"
            };

            foreach (var key in issuanceKeys)
            {
                _cache.Remove(key);
            }
        }

        /// <summary>
        /// Gets cache statistics for monitoring
        /// </summary>
        public CacheStatistics GetCacheStatistics()
        {
            // Note: IMemoryCache doesn't provide built-in statistics
            // In a production environment, consider using a more sophisticated caching solution
            // like Redis with built-in metrics
            
            return new CacheStatistics
            {
                GeneratedAt = DateTime.Now,
                CacheProvider = "MemoryCache",
                Note = "Detailed statistics not available with IMemoryCache. Consider Redis for production."
            };
        }

        /// <summary>
        /// Warms up cache with frequently accessed data
        /// </summary>
        public async Task WarmUpCache(ComplianceReportingService reportingService)
        {
            try
            {
                _logger.LogInformation("Starting cache warm-up");

                // Warm up dashboard
                await GetOrSetDashboard(() => reportingService.GetComplianceDashboard());

                // Warm up metrics
                await GetOrSetMetrics(() => reportingService.GetComplianceMetrics());

                // Warm up expiring certificates for common periods
                var commonPeriods = new[] { 7, 14, 30, 60, 90 };
                foreach (var period in commonPeriods)
                {
                    await GetOrSetExpiringCertificates(period, () => reportingService.GetExpiringCertificatesReport(period));
                }

                _logger.LogInformation("Cache warm-up completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cache warm-up");
            }
        }
    }

    public class CacheStatistics
    {
        public DateTime GeneratedAt { get; set; }
        public string CacheProvider { get; set; }
        public string Note { get; set; }
        public int? TotalKeys { get; set; }
        public long? TotalMemoryUsage { get; set; }
        public int? HitCount { get; set; }
        public int? MissCount { get; set; }
        public double? HitRatio { get; set; }
    }
}
