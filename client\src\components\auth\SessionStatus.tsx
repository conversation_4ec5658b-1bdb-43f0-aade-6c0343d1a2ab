// import React, { useState, useEffect } from 'react';
// import { Clock, Shield, AlertTriangle, CheckCircle } from 'lucide-react';
// import { useAuth } from '../../hooks/useAuth';
// import { getAccessToken } from '../../services/msalAuthService';

// interface SessionStatusProps {
//   className?: string;
//   showDetails?: boolean;
// }

// const SessionStatus: React.FC<SessionStatusProps> = ({
//   className = '',
//   showDetails = false,
// }) => {
//   const { user, isAuthenticated, error } = useAuth();
//   const [tokenExpiry, setTokenExpiry] = useState<Date | null>(null);
//   const [timeUntilExpiry, setTimeUntilExpiry] = useState<string>('');
//   const [sessionStatus, setSessionStatus] = useState<'healthy' | 'warning' | 'expired' | 'error'>('healthy');

//   useEffect(() => {
//     if (!isAuthenticated) return;

//     const checkTokenStatus = async () => {
//       try {
//         // Get current token to check expiry
//         const token = await getAccessToken();
//         if (token) {
//           // Decode JWT to get expiry (simplified - in production use a proper JWT library)
//           const payload = JSON.parse(atob(token.split('.')[1]));
//           const expiry = new Date(payload.exp * 1000);
//           setTokenExpiry(expiry);
//         }
//       } catch (error) {
//         console.error('Error checking token status:', error);
//         setSessionStatus('error');
//       }
//     };

//     checkTokenStatus();

//     // Check token status every minute
//     const interval = setInterval(checkTokenStatus, 60000);

//     return () => clearInterval(interval);
//   }, [isAuthenticated]);

//   useEffect(() => {
//     if (!tokenExpiry) return;

//     const updateTimeUntilExpiry = () => {
//       const now = new Date();
//       const timeDiff = tokenExpiry.getTime() - now.getTime();

//       if (timeDiff <= 0) {
//         setTimeUntilExpiry('Expired');
//         setSessionStatus('expired');
//         return;
//       }

//       const minutes = Math.floor(timeDiff / (1000 * 60));
//       const hours = Math.floor(minutes / 60);
//       const days = Math.floor(hours / 24);

//       if (days > 0) {
//         setTimeUntilExpiry(`${days}d ${hours % 24}h`);
//         setSessionStatus('healthy');
//       } else if (hours > 0) {
//         setTimeUntilExpiry(`${hours}h ${minutes % 60}m`);
//         setSessionStatus(hours < 1 ? 'warning' : 'healthy');
//       } else {
//         setTimeUntilExpiry(`${minutes}m`);
//         setSessionStatus(minutes < 10 ? 'warning' : 'healthy');
//       }
//     };

//     updateTimeUntilExpiry();

//     // Update every minute
//     const interval = setInterval(updateTimeUntilExpiry, 60000);

//     return () => clearInterval(interval);
//   }, [tokenExpiry]);

//   useEffect(() => {
//     if (error) {
//       setSessionStatus('error');
//     }
//   }, [error]);

//   if (!isAuthenticated || !user) {
//     return null;
//   }

//   const getStatusIcon = () => {
//     switch (sessionStatus) {
//       case 'healthy':
//         return <CheckCircle size={16} className="text-green-600" />;
//       case 'warning':
//         return <AlertTriangle size={16} className="text-yellow-600" />;
//       case 'expired':
//       case 'error':
//         return <AlertTriangle size={16} className="text-red-600" />;
//       default:
//         return <Shield size={16} className="text-gray-600" />;
//     }
//   };

//   const getStatusColor = () => {
//     switch (sessionStatus) {
//       case 'healthy':
//         return 'text-green-600';
//       case 'warning':
//         return 'text-yellow-600';
//       case 'expired':
//       case 'error':
//         return 'text-red-600';
//       default:
//         return 'text-gray-600';
//     }
//   };

//   const getStatusText = () => {
//     if (error) {
//       return 'Session Error';
//     }

//     switch (sessionStatus) {
//       case 'healthy':
//         return 'Session Active';
//       case 'warning':
//         return 'Session Expiring Soon';
//       case 'expired':
//         return 'Session Expired';
//       case 'error':
//         return 'Session Error';
//       default:
//         return 'Session Status Unknown';
//     }
//   };

//   if (!showDetails) {
//     return (
//       <div className={`inline-flex items-center space-x-2 ${className}`}>
//         {getStatusIcon()}
//         <span className={`text-sm ${getStatusColor()}`}>
//           {getStatusText()}
//         </span>
//       </div>
//     );
//   }

//   return (
//     <div className={`bg-white rounded-lg shadow-sm border p-4 ${className}`}>
//       <div className="flex items-center justify-between mb-3">
//         <h3 className="text-sm font-medium text-gray-900">Session Status</h3>
//         {getStatusIcon()}
//       </div>

//       <div className="space-y-2">
//         <div className="flex justify-between text-sm">
//           <span className="text-gray-500">Status:</span>
//           <span className={getStatusColor()}>{getStatusText()}</span>
//         </div>

//         <div className="flex justify-between text-sm">
//           <span className="text-gray-500">User:</span>
//           <span className="text-gray-900">{user.name}</span>
//         </div>

//         <div className="flex justify-between text-sm">
//           <span className="text-gray-500">Role:</span>
//           <span className="text-gray-900">{user.role}</span>
//         </div>

//         {tokenExpiry && (
//           <div className="flex justify-between text-sm">
//             <span className="text-gray-500">Token Expires:</span>
//             <div className="text-right">
//               <div className="text-gray-900">{tokenExpiry.toLocaleTimeString()}</div>
//               <div className={`text-xs ${getStatusColor()}`}>
//                 {timeUntilExpiry && timeUntilExpiry !== 'Expired' ? `in ${timeUntilExpiry}` : timeUntilExpiry}
//               </div>
//             </div>
//           </div>
//         )}

//         {error && (
//           <div className="mt-3 p-2 bg-red-50 rounded border border-red-200">
//             <div className="flex items-center">
//               <AlertTriangle size={14} className="text-red-600 mr-2" />
//               <span className="text-xs text-red-700">{error.message}</span>
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default SessionStatus;
