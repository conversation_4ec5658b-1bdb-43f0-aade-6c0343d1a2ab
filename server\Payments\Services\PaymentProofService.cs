using Final_E_Receipt.Files.Services;
using Final_E_Receipt.Files.Models;
using Final_E_Receipt.Payments.Models;
using Final_E_Receipt.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Dapper;
using System.Data;

namespace Final_E_Receipt.Payments.Services
{
    public class PaymentProofService
    {
        private readonly FileService _fileService;
        private readonly IDatabaseService _dbService;
        private readonly ILogger<PaymentProofService> _logger;

        public PaymentProofService(FileService fileService, IDatabaseService dbService, ILogger<PaymentProofService> logger)
        {
            _fileService = fileService;
            _dbService = dbService;
            _logger = logger;
        }

        public async Task<FileUpload> UploadPaymentProof(string paymentId, IFormFile file, string uploadedBy, 
            string organizationId, string description = null)
        {
            try
            {
                // Upload the file with PAYMENT entity type
                var uploadedFile = await _fileService.UploadFile(
                    file,
                    "PAYMENT",
                    paymentId,
                    uploadedBy,
                    organizationId,
                    description ?? "Payment proof document",
                    "PROOF"
                );

                // Update payment status to indicate proof has been uploaded
                await UpdatePaymentProofStatus(paymentId, uploadedFile.Id, "PROOF_UPLOADED");

                _logger.LogInformation("Payment proof uploaded for payment {PaymentId} by user {UserId}", paymentId, uploadedBy);

                return uploadedFile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading payment proof for payment {PaymentId}", paymentId);
                throw;
            }
        }

        public async Task<List<FileUpload>> GetPaymentProofFiles(string paymentId)
        {
            return await _fileService.GetFilesByEntity("PAYMENT", paymentId);
        }

        public async Task<FileUpload> GetPaymentProofById(string fileId)
        {
            return await _fileService.GetFileById(fileId);
        }

        public async Task<byte[]> DownloadPaymentProof(string fileId)
        {
            return await _fileService.DownloadFile(fileId);
        }

        public async Task<bool> DeletePaymentProof(string fileId, string paymentId)
        {
            try
            {
                var success = await _fileService.DeleteFile(fileId);
                
                if (success)
                {
                    var remainingFiles = await GetPaymentProofFiles(paymentId);
                    var activeFiles = remainingFiles.Where(f => f.IsActive).ToList();
                    
                    if (!activeFiles.Any())
                    {
                        await UpdatePaymentProofStatus(paymentId, null, "PENDING");
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting payment proof {FileId} for payment {PaymentId}", fileId, paymentId);
                return false;
            }
        }

        public async Task<bool> ValidatePaymentProof(string fileId, string validatedBy, bool isValid, string comments = null)
        {
            try
            {
                var file = await _fileService.GetFileById(fileId);
                if (file == null)
                    return false;

                var scanResult = isValid ? "VALIDATED_APPROVED" : "VALIDATED_REJECTED";
                await UpdateFileScanResult(fileId, scanResult);

                var newStatus = isValid ? "ACKNOWLEDGED" : "REJECTED";
                await UpdatePaymentProofStatus(file.RelatedEntityId, fileId, newStatus);

                _logger.LogInformation("Payment proof {FileId} validated as {Result} by {ValidatedBy}", 
                    fileId, isValid ? "APPROVED" : "REJECTED", validatedBy);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating payment proof {FileId}", fileId);
                return false;
            }
        }

        private async Task UpdatePaymentProofStatus(string paymentId, string proofFileId, string status)
        {
            var parameters = new
            {
                PaymentId = paymentId,
                Status = status,
                ProofFileId = proofFileId
            };

            await _dbService.ExecuteAsync("UpdatePaymentProofStatus", parameters);
        }

        private async Task UpdateFileScanResult(string fileId, string scanResult)
        {
            var parameters = new
            {
                Id = fileId,
                ScanResult = scanResult
            };

            await _dbService.ExecuteAsync("UpdateFileScanResult", parameters);
        }
    }
}

