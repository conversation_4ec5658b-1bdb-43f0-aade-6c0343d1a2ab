import { useContext } from "react";
import { AuthContext } from "../contexts/AuthContext";
import type { AuthContextType } from "../types/auth";

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// Enhanced login hooks for different auth methods
export const useMicrosoftAuth = () => {
  const { login, logout, isLoading } = useAuth();

  const loginWithMicrosoft = async () => {
    return await login({ authMethod: "microsoft" });
  };

  const logoutFromMicrosoft = async () => {
    return await logout({ authMethod: "microsoft" });
  };

  return {
    loginWithMicrosoft,
    logoutFromMicrosoft,
    isLoading,
  };
};

export const useLocalAuth = () => {
  const { login, logout, isLoading } = useAuth();

  const loginWithCredentials = async (email: string, password: string) => {
    return await login({
      authMethod: "local",
      credentials: { email, password },
    });
  };

  const logoutFromLocal = async () => {
    return await logout({ authMethod: "local" });
  };

  return {
    loginWithCredentials,
    logoutFromLocal,
    isLoading,
  };
};

// Convenience hooks for specific roles
export const useIsAdmin = () => {
  const { hasRole } = useAuth();
  return hasRole("JTB_ADMIN");
};

export const useIsFinanceOfficer = () => {
  const { hasRole } = useAuth();
  return hasRole("FINANCE_OFFICER");
};

export const useIsSeniorFinanceOfficer = () => {
  const { hasRole } = useAuth();
  return hasRole("SENIOR_FINANCE_OFFICER");
};

export const useIsPayer = () => {
  const { hasRole } = useAuth();
  return hasRole("PAYER");
};

// Permission hooks
export const useCanManagePayments = () => {
  const { permissions } = useAuth();
  return permissions.canManagePayments;
};

export const useCanApprovePayments = () => {
  const { permissions } = useAuth();
  return permissions.canApprovePayments;
};

export const useCanAcknowledgePayments = () => {
  const { permissions } = useAuth();
  return permissions.canAcknowledgePayments;
};

export const useCanManageUsers = () => {
  const { permissions } = useAuth();
  return permissions.canManageUsers;
};

export const useCanManageOrganizations = () => {
  const { permissions } = useAuth();
  return permissions.canManageOrganizations;
};

export const useCanViewReports = () => {
  const { permissions } = useAuth();
  return permissions.canViewReports;
};

export const useCanManageCompliance = () => {
  const { permissions } = useAuth();
  return permissions.canManageCompliance;
};
