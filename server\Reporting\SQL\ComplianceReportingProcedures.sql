-- Optimized SQL Procedures for Compliance Reporting
-- These procedures are designed for high performance with large datasets

-- Get compliance dashboard summary with optimized queries
CREATE PROCEDURE GetComplianceDashboardSummary
AS
BEGIN
    SELECT 
        COUNT(*) as TotalCertificates,
        SUM(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() THEN 1 ELSE 0 END) as ActiveCertificates,
        SUM(CASE WHEN IsRevoked = 0 AND ValidUntil <= GETDATE() THEN 1 ELSE 0 END) as ExpiredCertificates,
        SUM(CASE WHEN IsRevoked = 1 THEN 1 ELSE 0 END) as RevokedCertificates,
        SUM(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() AND ValidUntil <= DATEADD(DAY, 30, GETDATE()) THEN 1 ELSE 0 END) as CertificatesExpiringSoon,
        SUM(CASE WHEN YEAR(IssuedDate) = YEAR(GETDATE()) AND MONTH(IssuedDate) = MONTH(GETDATE()) THEN 1 ELSE 0 END) as CertificatesIssuedThisMonth,
        SUM(CASE WHEN YEAR(IssuedDate) = YEAR(GETDATE()) THEN 1 ELSE 0 END) as CertificatesIssuedThisYear,
        COUNT(DISTINCT CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() THEN OrganizationId END) as OrganizationsWithActiveCertificates
    FROM ComplianceCertificates
END;

-- Get certificate type distribution
CREATE PROCEDURE GetCertificateTypeDistribution
AS
BEGIN
    SELECT 
        CertificateType,
        COUNT(*) as CertificateCount,
        SUM(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() THEN 1 ELSE 0 END) as ActiveCount,
        SUM(CASE WHEN IsRevoked = 0 AND ValidUntil <= GETDATE() THEN 1 ELSE 0 END) as ExpiredCount,
        SUM(CASE WHEN IsRevoked = 1 THEN 1 ELSE 0 END) as RevokedCount
    FROM ComplianceCertificates
    GROUP BY CertificateType
    ORDER BY CertificateCount DESC
END;

-- Get monthly issuance statistics
CREATE PROCEDURE GetMonthlyIssuanceStats
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL
AS
BEGIN
    IF @StartDate IS NULL SET @StartDate = DATEADD(YEAR, -1, GETDATE())
    IF @EndDate IS NULL SET @EndDate = GETDATE()
    
    SELECT 
        FORMAT(IssuedDate, 'yyyy-MM') as YearMonth,
        COUNT(*) as CertificatesIssued,
        COUNT(DISTINCT OrganizationId) as UniqueOrganizations,
        SUM(TotalAmount) as TotalValue
    FROM ComplianceCertificates
    WHERE IssuedDate >= @StartDate AND IssuedDate <= @EndDate
    GROUP BY FORMAT(IssuedDate, 'yyyy-MM')
    ORDER BY YearMonth
END;

-- Get organization compliance summary
CREATE PROCEDURE GetOrganizationComplianceSummary
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT 
        @OrganizationId as OrganizationId,
        COUNT(*) as TotalCertificates,
        SUM(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() THEN 1 ELSE 0 END) as ActiveCertificates,
        SUM(CASE WHEN IsRevoked = 0 AND ValidUntil <= GETDATE() THEN 1 ELSE 0 END) as ExpiredCertificates,
        SUM(CASE WHEN IsRevoked = 1 THEN 1 ELSE 0 END) as RevokedCertificates,
        SUM(CASE WHEN YEAR(IssuedDate) = YEAR(GETDATE()) THEN 1 ELSE 0 END) as CertificatesIssuedThisYear,
        SUM(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() AND ValidUntil <= DATEADD(DAY, 30, GETDATE()) THEN 1 ELSE 0 END) as CertificatesExpiringSoon,
        MAX(CASE WHEN IsRevoked = 0 THEN IssuedDate END) as LastCertificateIssued,
        MIN(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() THEN ValidUntil END) as NextExpiryDate,
        -- Calculate compliance score
        CASE 
            WHEN COUNT(*) = 0 THEN 0
            ELSE CAST(SUM(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*) * 100
        END as ComplianceScore
    FROM ComplianceCertificates
    WHERE OrganizationId = @OrganizationId
END;

-- Get organization compliance summary for all organizations
CREATE PROCEDURE GetAllOrganizationsComplianceSummary
AS
BEGIN
    SELECT 
        OrganizationId,
        OrganizationName,
        COUNT(*) as TotalCertificates,
        SUM(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() THEN 1 ELSE 0 END) as ActiveCertificates,
        SUM(CASE WHEN IsRevoked = 0 AND ValidUntil <= GETDATE() THEN 1 ELSE 0 END) as ExpiredCertificates,
        SUM(CASE WHEN IsRevoked = 1 THEN 1 ELSE 0 END) as RevokedCertificates,
        SUM(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() AND ValidUntil <= DATEADD(DAY, 30, GETDATE()) THEN 1 ELSE 0 END) as CertificatesExpiringSoon,
        MAX(CASE WHEN IsRevoked = 0 THEN IssuedDate END) as LastCertificateIssued,
        MIN(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() THEN ValidUntil END) as NextExpiryDate,
        -- Calculate compliance score
        CASE 
            WHEN COUNT(*) = 0 THEN 0
            ELSE CAST(SUM(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*) * 100
        END as ComplianceScore,
        -- Determine if organization is compliant
        CASE 
            WHEN SUM(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() THEN 1 ELSE 0 END) > 0 THEN 1
            ELSE 0
        END as IsCompliant
    FROM ComplianceCertificates
    GROUP BY OrganizationId, OrganizationName
    ORDER BY ComplianceScore DESC, OrganizationName
END;

-- Get expiring certificates with organization details
CREATE PROCEDURE GetExpiringCertificatesDetailed
    @DaysFromNow INT = 30
AS
BEGIN
    DECLARE @ExpiryDate DATETIME = DATEADD(DAY, @DaysFromNow, GETDATE())
    
    SELECT 
        c.*,
        DATEDIFF(DAY, GETDATE(), c.ValidUntil) as DaysUntilExpiry,
        CASE 
            WHEN DATEDIFF(DAY, GETDATE(), c.ValidUntil) <= 7 THEN 'CRITICAL'
            WHEN DATEDIFF(DAY, GETDATE(), c.ValidUntil) <= 14 THEN 'HIGH'
            WHEN DATEDIFF(DAY, GETDATE(), c.ValidUntil) <= 30 THEN 'MEDIUM'
            ELSE 'LOW'
        END as UrgencyLevel
    FROM ComplianceCertificates c
    WHERE c.ValidUntil <= @ExpiryDate 
    AND c.IsRevoked = 0 
    AND c.ValidUntil > GETDATE()
    ORDER BY c.ValidUntil ASC, c.OrganizationName
END;

-- Get expiring certificates summary by organization
CREATE PROCEDURE GetExpiringCertificatesByOrganization
    @DaysFromNow INT = 30
AS
BEGIN
    DECLARE @ExpiryDate DATETIME = DATEADD(DAY, @DaysFromNow, GETDATE())
    
    SELECT 
        OrganizationId,
        OrganizationName,
        COUNT(*) as ExpiringCount,
        MIN(ValidUntil) as EarliestExpiry,
        MAX(ValidUntil) as LatestExpiry,
        SUM(CASE WHEN DATEDIFF(DAY, GETDATE(), ValidUntil) <= 7 THEN 1 ELSE 0 END) as CriticalCount,
        SUM(CASE WHEN DATEDIFF(DAY, GETDATE(), ValidUntil) <= 14 THEN 1 ELSE 0 END) as HighPriorityCount
    FROM ComplianceCertificates
    WHERE ValidUntil <= @ExpiryDate 
    AND IsRevoked = 0 
    AND ValidUntil > GETDATE()
    GROUP BY OrganizationId, OrganizationName
    ORDER BY EarliestExpiry ASC, ExpiringCount DESC
END;

-- Get certificate issuance statistics with detailed breakdown
CREATE PROCEDURE GetCertificateIssuanceStatistics
    @FromDate DATETIME,
    @ToDate DATETIME
AS
BEGIN
    SELECT 
        COUNT(*) as TotalIssued,
        COUNT(DISTINCT OrganizationId) as UniqueOrganizations,
        SUM(TotalAmount) as TotalValue,
        AVG(TotalAmount) as AverageValue,
        -- Breakdown by type
        SUM(CASE WHEN CertificateType = 'ANNUAL_LICENSE' THEN 1 ELSE 0 END) as AnnualLicenses,
        SUM(CASE WHEN CertificateType = 'QUARTERLY_COMPLIANCE' THEN 1 ELSE 0 END) as QuarterlyCompliance,
        SUM(CASE WHEN CertificateType = 'TAX_CLEARANCE' THEN 1 ELSE 0 END) as TaxClearance,
        -- Breakdown by status
        SUM(CASE WHEN Status = 'ISSUED' THEN 1 ELSE 0 END) as IssuedCertificates,
        SUM(CASE WHEN Status = 'PENDING' THEN 1 ELSE 0 END) as PendingCertificates,
        SUM(CASE WHEN Status = 'GENERATED' THEN 1 ELSE 0 END) as GeneratedCertificates
    FROM ComplianceCertificates
    WHERE IssuedDate >= @FromDate AND IssuedDate <= @ToDate
END;

-- Get top organizations by certificate count
CREATE PROCEDURE GetTopOrganizationsByIssuance
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @TopCount INT = 10
AS
BEGIN
    IF @FromDate IS NULL SET @FromDate = DATEADD(YEAR, -1, GETDATE())
    IF @ToDate IS NULL SET @ToDate = GETDATE()
    
    SELECT TOP (@TopCount)
        OrganizationId,
        OrganizationName,
        COUNT(*) as CertificateCount,
        SUM(TotalAmount) as TotalValue,
        MAX(IssuedDate) as LatestIssuance,
        COUNT(DISTINCT CertificateType) as CertificateTypes
    FROM ComplianceCertificates
    WHERE IssuedDate >= @FromDate AND IssuedDate <= @ToDate
    GROUP BY OrganizationId, OrganizationName
    ORDER BY CertificateCount DESC, TotalValue DESC
END;

-- Get compliance trends over time
CREATE PROCEDURE GetComplianceTrends
    @MonthsBack INT = 12
AS
BEGIN
    DECLARE @StartDate DATETIME = DATEADD(MONTH, -@MonthsBack, GETDATE())
    
    SELECT 
        FORMAT(IssuedDate, 'yyyy-MM') as YearMonth,
        COUNT(*) as CertificatesIssued,
        COUNT(DISTINCT OrganizationId) as ActiveOrganizations,
        SUM(TotalAmount) as TotalValue,
        AVG(TotalAmount) as AverageValue,
        -- Calculate month-over-month growth
        LAG(COUNT(*)) OVER (ORDER BY FORMAT(IssuedDate, 'yyyy-MM')) as PreviousMonthCount,
        CASE 
            WHEN LAG(COUNT(*)) OVER (ORDER BY FORMAT(IssuedDate, 'yyyy-MM')) IS NULL THEN NULL
            WHEN LAG(COUNT(*)) OVER (ORDER BY FORMAT(IssuedDate, 'yyyy-MM')) = 0 THEN 100
            ELSE CAST((COUNT(*) - LAG(COUNT(*)) OVER (ORDER BY FORMAT(IssuedDate, 'yyyy-MM'))) AS FLOAT) / 
                 LAG(COUNT(*)) OVER (ORDER BY FORMAT(IssuedDate, 'yyyy-MM')) * 100
        END as GrowthPercentage
    FROM ComplianceCertificates
    WHERE IssuedDate >= @StartDate
    GROUP BY FORMAT(IssuedDate, 'yyyy-MM')
    ORDER BY YearMonth
END;

-- Get compliance alerts and warnings
CREATE PROCEDURE GetComplianceAlerts
AS
BEGIN
    -- Organizations with no active certificates
    SELECT 
        'NO_ACTIVE_CERTIFICATES' as AlertType,
        OrganizationId,
        OrganizationName,
        'Organization has no active certificates' as AlertMessage,
        'HIGH' as Priority,
        MAX(ValidUntil) as LastValidCertificate
    FROM ComplianceCertificates
    GROUP BY OrganizationId, OrganizationName
    HAVING SUM(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() THEN 1 ELSE 0 END) = 0
    
    UNION ALL
    
    -- Organizations with certificates expiring in 7 days
    SELECT 
        'CERTIFICATES_EXPIRING_SOON' as AlertType,
        OrganizationId,
        OrganizationName,
        'Certificates expiring within 7 days: ' + CAST(COUNT(*) AS NVARCHAR) as AlertMessage,
        'CRITICAL' as Priority,
        MIN(ValidUntil) as EarliestExpiry
    FROM ComplianceCertificates
    WHERE IsRevoked = 0 
    AND ValidUntil > GETDATE() 
    AND ValidUntil <= DATEADD(DAY, 7, GETDATE())
    GROUP BY OrganizationId, OrganizationName
    
    UNION ALL
    
    -- Organizations with low compliance scores
    SELECT 
        'LOW_COMPLIANCE_SCORE' as AlertType,
        OrganizationId,
        OrganizationName,
        'Compliance score below 70%: ' + 
        CAST(CAST(SUM(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*) * 100 AS NVARCHAR(10)) + '%' as AlertMessage,
        'MEDIUM' as Priority,
        NULL as EarliestExpiry
    FROM ComplianceCertificates
    GROUP BY OrganizationId, OrganizationName
    HAVING CAST(SUM(CASE WHEN IsRevoked = 0 AND ValidUntil > GETDATE() THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*) * 100 < 70
    
    ORDER BY 
        CASE Priority 
            WHEN 'CRITICAL' THEN 1 
            WHEN 'HIGH' THEN 2 
            WHEN 'MEDIUM' THEN 3 
            ELSE 4 
        END,
        OrganizationName
END;
