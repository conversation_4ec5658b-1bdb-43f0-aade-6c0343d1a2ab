-- ===== NOTIFICATION MANAGEMENT SYSTEM =====
-- Core notification storage and management procedures

-- Notifications Table
CREATE TABLE Notifications (
    Id NVARCHAR(50) PRIMARY KEY,
    UserId NVARCHAR(50) NOT NULL,
    OrganizationId NVARCHAR(50),
    Type NVARCHAR(50) NOT NULL, -- PAYMENT_DUE, PAYMENT_SCHEDULE_CREATED, etc.
    Title NVARCHAR(255) NOT NULL,
    Message NVARCHAR(MAX) NOT NULL,
    Priority NVARCHAR(20) NOT NULL DEFAULT 'MEDIUM', -- LOW, MEDIUM, HIGH, URGENT
    Status NVARCHAR(20) NOT NULL DEFAULT 'UNREAD', -- UNREAD, READ, ARCHIVED
    RelatedEntityId NVARCHAR(50),
    RelatedEntityType NVARCHAR(50), -- PAYMENT, PAYMENT_SCHEDULE, CERTIFICATE, etc.
    ScheduledDate DATETIME,
    SentDate DATETIME,
    ReadDate DATETIME,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    UpdatedAt DATETIME,
    FOR<PERSON><PERSON><PERSON> KEY (UserId) REFERENCES Users(Id),
    FOREIGN KEY (OrganizationId) REFERENCES Organizations(Id)
);

-- Notification Preferences Table
CREATE TABLE NotificationPreferences (
    Id NVARCHAR(50) PRIMARY KEY,
    UserId NVARCHAR(50) NOT NULL,
    NotificationType NVARCHAR(50) NOT NULL,
    InAppEnabled BIT NOT NULL DEFAULT 1,
    EmailEnabled BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    UpdatedAt DATETIME,
    FOREIGN KEY (UserId) REFERENCES Users(Id),
    UNIQUE(UserId, NotificationType)
);

-- Create Notification
CREATE PROCEDURE CreateNotification
    @Id NVARCHAR(50),
    @UserId NVARCHAR(50),
    @OrganizationId NVARCHAR(50) = NULL,
    @Type NVARCHAR(50),
    @Title NVARCHAR(255),
    @Message NVARCHAR(MAX),
    @Priority NVARCHAR(20) = 'MEDIUM',
    @RelatedEntityId NVARCHAR(50) = NULL,
    @RelatedEntityType NVARCHAR(50) = NULL,
    @ScheduledDate DATETIME = NULL
AS
BEGIN
    INSERT INTO Notifications (
        Id, UserId, OrganizationId, Type, Title, Message, Priority,
        RelatedEntityId, RelatedEntityType, ScheduledDate, SentDate
    )
    VALUES (
        @Id, @UserId, @OrganizationId, @Type, @Title, @Message, @Priority,
        @RelatedEntityId, @RelatedEntityType, @ScheduledDate, GETDATE()
    )
    
    SELECT * FROM Notifications WHERE Id = @Id
END;

-- Get Notification By Id
CREATE PROCEDURE GetNotificationById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Notifications WHERE Id = @Id
END;

-- Get User Notifications
CREATE PROCEDURE GetUserNotifications
    @UserId NVARCHAR(50),
    @Status NVARCHAR(20) = NULL,
    @Type NVARCHAR(50) = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT * FROM Notifications 
    WHERE UserId = @UserId
        AND (@Status IS NULL OR Status = @Status)
        AND (@Type IS NULL OR Type = @Type)
    ORDER BY CreatedAt DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY
END;

-- Get Unread Notifications
CREATE PROCEDURE GetUnreadNotifications
    @UserId NVARCHAR(50),
    @Limit INT = 10
AS
BEGIN
    SELECT TOP (@Limit) * FROM Notifications 
    WHERE UserId = @UserId AND Status = 'UNREAD'
    ORDER BY CreatedAt DESC
END;

-- Get Notification Stats
CREATE PROCEDURE GetNotificationStats
    @UserId NVARCHAR(50)
AS
BEGIN
    SELECT 
        COUNT(*) as TotalNotifications,
        SUM(CASE WHEN Status = 'UNREAD' THEN 1 ELSE 0 END) as UnreadCount,
        SUM(CASE WHEN Status = 'READ' THEN 1 ELSE 0 END) as ReadCount,
        SUM(CASE WHEN Status = 'ARCHIVED' THEN 1 ELSE 0 END) as ArchivedCount
    FROM Notifications 
    WHERE UserId = @UserId
END;

-- Mark Notification As Read
CREATE PROCEDURE MarkNotificationAsRead
    @Id NVARCHAR(50)
AS
BEGIN
    UPDATE Notifications 
    SET Status = 'read', ReadDate = GETDATE(), UpdatedAt = GETDATE()
    WHERE Id = @Id
    
    SELECT * FROM Notifications WHERE Id = @Id
END;

-- Mark Notification As Unread
CREATE PROCEDURE MarkNotificationAsUnread
    @Id NVARCHAR(50)
AS
BEGIN
    UPDATE Notifications 
    SET Status = 'UNREAD', ReadDate = NULL, UpdatedAt = GETDATE()
    WHERE Id = @Id
    
    SELECT * FROM Notifications WHERE Id = @Id
END;

-- Archive Notification
CREATE PROCEDURE ArchiveNotification
    @Id NVARCHAR(50)
AS
BEGIN
    UPDATE Notifications 
    SET Status = 'ARCHIVED', UpdatedAt = GETDATE()
    WHERE Id = @Id
    
    SELECT * FROM Notifications WHERE Id = @Id
END;

-- Delete Notification
CREATE PROCEDURE DeleteNotification
    @Id NVARCHAR(50)
AS
BEGIN
    DELETE FROM Notifications WHERE Id = @Id
END;

-- Mark Multiple As Read
CREATE PROCEDURE MarkMultipleAsRead
    @NotificationIds NVARCHAR(MAX) -- Comma-separated list of IDs
AS
BEGIN
    UPDATE Notifications 
    SET Status = 'read', ReadDate = GETDATE(), UpdatedAt = GETDATE()
    WHERE Id IN (SELECT value FROM STRING_SPLIT(@NotificationIds, ','))
END;

-- Archive Multiple Notifications
CREATE PROCEDURE ArchiveMultipleNotifications
    @NotificationIds NVARCHAR(MAX) -- Comma-separated list of IDs
AS
BEGIN
    UPDATE Notifications 
    SET Status = 'ARCHIVED', UpdatedAt = GETDATE()
    WHERE Id IN (SELECT value FROM STRING_SPLIT(@NotificationIds, ','))
END;

-- Delete Multiple Notifications
CREATE PROCEDURE DeleteMultipleNotifications
    @NotificationIds NVARCHAR(MAX) -- Comma-separated list of IDs
AS
BEGIN
    DELETE FROM Notifications 
    WHERE Id IN (SELECT value FROM STRING_SPLIT(@NotificationIds, ','))
END;

-- Get Organization Notifications
CREATE PROCEDURE GetOrganizationNotifications
    @OrganizationId NVARCHAR(50),
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT * FROM Notifications 
    WHERE OrganizationId = @OrganizationId
    ORDER BY CreatedAt DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY
END;

-- Create/Update Notification Preference
CREATE PROCEDURE UpsertNotificationPreference
    @Id NVARCHAR(50),
    @UserId NVARCHAR(50),
    @NotificationType NVARCHAR(50),
    @InAppEnabled BIT,
    @EmailEnabled BIT
AS
BEGIN
    IF EXISTS (SELECT 1 FROM NotificationPreferences WHERE UserId = @UserId AND NotificationType = @NotificationType)
    BEGIN
        UPDATE NotificationPreferences 
        SET InAppEnabled = @InAppEnabled, EmailEnabled = @EmailEnabled, UpdatedAt = GETDATE()
        WHERE UserId = @UserId AND NotificationType = @NotificationType
    END
    ELSE
    BEGIN
        INSERT INTO NotificationPreferences (Id, UserId, NotificationType, InAppEnabled, EmailEnabled)
        VALUES (@Id, @UserId, @NotificationType, @InAppEnabled, @EmailEnabled)
    END
    
    SELECT * FROM NotificationPreferences WHERE UserId = @UserId AND NotificationType = @NotificationType
END;

-- Get User Notification Preferences
CREATE PROCEDURE GetUserNotificationPreferences
    @UserId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM NotificationPreferences WHERE UserId = @UserId
END;

-- Search Notifications
CREATE PROCEDURE SearchNotifications
    @UserId NVARCHAR(50),
    @SearchTerm NVARCHAR(255),
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT * FROM Notifications 
    WHERE UserId = @UserId
        AND (Title LIKE '%' + @SearchTerm + '%' 
             OR Message LIKE '%' + @SearchTerm + '%'
             OR Type LIKE '%' + @SearchTerm + '%')
    ORDER BY CreatedAt DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY
END;

-- Get Notifications By Type
CREATE PROCEDURE GetNotificationsByType
    @UserId NVARCHAR(50),
    @Type NVARCHAR(50),
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT * FROM Notifications 
    WHERE UserId = @UserId AND Type = @Type
    ORDER BY CreatedAt DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY
END;

-- Get Notifications By Date Range
CREATE PROCEDURE GetNotificationsByDateRange
    @UserId NVARCHAR(50),
    @StartDate DATETIME,
    @EndDate DATETIME,
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT * FROM Notifications 
    WHERE UserId = @UserId
        AND CreatedAt BETWEEN @StartDate AND @EndDate
    ORDER BY CreatedAt DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY
END;

