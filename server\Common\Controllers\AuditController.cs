using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Final_E_Receipt.Common.Services;
using Final_E_Receipt.Common.Models;

namespace Final_E_Receipt.Common.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AuditController : ControllerBase
    {
        private readonly AuditService _auditService;
        private readonly ILogger<AuditController> _logger;

        public AuditController(AuditService auditService, ILogger<AuditController> logger)
        {
            _auditService = auditService;
            _logger = logger;
        }

        /// <summary>
        /// Get audit logs with filtering (Admin only)
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> GetAuditLogs(
            [FromQuery] string? userId = null,
            [FromQuery] string? action = null,
            [FromQuery] string? entityType = null,
            [FromQuery] string? organizationId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int pageSize = 50,
            [FromQuery] int pageNumber = 1)
        {
            try
            {
                var auditLogs = await _auditService.GetAuditLogsAsync(
                    userId, action, entityType, organizationId, 
                    startDate, endDate, pageSize, pageNumber);

                return Ok(auditLogs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving audit logs");
                return StatusCode(500, new { message = "Failed to retrieve audit logs" });
            }
        }

        /// <summary>
        /// Get audit logs for a specific entity
        /// </summary>
        [HttpGet("entity/{entityType}/{entityId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetEntityAuditLogs(string entityType, string entityId)
        {
            try
            {
                var auditLogs = await _auditService.GetEntityAuditLogsAsync(entityType, entityId);
                return Ok(auditLogs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving audit logs for {EntityType} {EntityId}", entityType, entityId);
                return StatusCode(500, new { message = "Failed to retrieve entity audit logs" });
            }
        }

        /// <summary>
        /// Get recent audit logs for dashboard
        /// </summary>
        [HttpGet("recent")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetRecentAuditLogs([FromQuery] int count = 20)
        {
            try
            {
                var auditLogs = await _auditService.GetAuditLogsAsync(
                    pageSize: count, pageNumber: 1);

                return Ok(auditLogs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving recent audit logs");
                return StatusCode(500, new { message = "Failed to retrieve recent audit logs" });
            }
        }

        /// <summary>
        /// Get audit statistics (Admin only)
        /// </summary>
        [HttpGet("stats")]
        [Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> GetAuditStats(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var stats = await _auditService.GetAuditStatsAsync(startDate, endDate);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving audit statistics");
                return StatusCode(500, new { message = "Failed to retrieve audit statistics" });
            }
        }

        /// <summary>
        /// Get available audit actions and entity types
        /// </summary>
        [HttpGet("metadata")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public IActionResult GetAuditMetadata()
        {
            try
            {
                var actions = typeof(AuditActions)
                    .GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static)
                    .Where(f => f.IsLiteral && !f.IsInitOnly)
                    .Select(f => new { Name = f.Name, Value = f.GetValue(null)?.ToString() })
                    .ToList();

                var entityTypes = typeof(EntityTypes)
                    .GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static)
                    .Where(f => f.IsLiteral && !f.IsInitOnly)
                    .Select(f => new { Name = f.Name, Value = f.GetValue(null)?.ToString() })
                    .ToList();

                return Ok(new { Actions = actions, EntityTypes = entityTypes });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving audit metadata");
                return StatusCode(500, new { message = "Failed to retrieve audit metadata" });
            }
        }

        /// <summary>
        /// Export audit logs to CSV (Admin only)
        /// </summary>
        [HttpGet("export")]
        [Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> ExportAuditLogs(
            [FromQuery] string? userId = null,
            [FromQuery] string? action = null,
            [FromQuery] string? entityType = null,
            [FromQuery] string? organizationId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var auditLogs = await _auditService.GetAuditLogsAsync(
                    userId, action, entityType, organizationId, 
                    startDate, endDate, pageSize: 10000, pageNumber: 1);

                var csv = GenerateCsv(auditLogs);
                var fileName = $"audit_logs_{DateTime.UtcNow:yyyyMMdd_HHmmss}.csv";

                return File(System.Text.Encoding.UTF8.GetBytes(csv), "text/csv", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting audit logs");
                return StatusCode(500, new { message = "Failed to export audit logs" });
            }
        }

        private string GenerateCsv(List<AuditLog> auditLogs)
        {
            var csv = new System.Text.StringBuilder();
            csv.AppendLine("Timestamp,UserId,UserName,UserEmail,Action,EntityType,EntityId,AdditionalDetails,IpAddress,OrganizationId");

            foreach (var log in auditLogs)
            {
                csv.AppendLine($"{log.Timestamp:yyyy-MM-dd HH:mm:ss},{log.UserId},{log.UserName},{log.UserEmail},{log.Action},{log.EntityType},{log.EntityId},\"{log.AdditionalDetails}\",{log.IpAddress},{log.OrganizationId}");
            }

            return csv.ToString();
        }
    }
}
