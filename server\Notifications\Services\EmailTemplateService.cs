using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Final_E_Receipt.Notifications.Models;
using Final_E_Receipt.Services;
using Dapper;

namespace Final_E_Receipt.Notifications.Services
{
    public class EmailTemplateService
    {
        private readonly IDatabaseService _dbService;

        public EmailTemplateService(IDatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<EmailTemplate> CreateEmailTemplate(EmailTemplate template)
        {
            var parameters = new
            {
                Id = template.Id ?? Guid.NewGuid().ToString(),
                OrganizationId = template.OrganizationId,
                Name = template.Name,
                Description = template.Description,
                Subject = template.Subject,
                BodyContent = template.BodyContent,
                Type = template.Type,
                IsDefault = template.IsDefault,
                CreatedBy = template.CreatedBy
            };

            return await _dbService.QueryFirstOrDefaultAsync<EmailTemplate>(
                "CreateEmailTemplate", parameters);
        }

        public async Task<EmailTemplate> GetEmailTemplateById(string id)
        {
            var parameters = new { Id = id };

            return await _dbService.QueryFirstOrDefaultAsync<EmailTemplate>(
                "GetEmailTemplateById", parameters);
        }

        public async Task<List<EmailTemplate>> GetEmailTemplatesByOrganization(string organizationId)
        {
            var parameters = new { OrganizationId = organizationId };

            var templates = await _dbService.QueryAsync<EmailTemplate>(
                "GetEmailTemplatesByOrganization", parameters);

            return templates.ToList();
        }

        public async Task<List<EmailTemplate>> GetEmailTemplatesByType(string organizationId, string type)
        {
            var parameters = new
            {
                OrganizationId = organizationId,
                Type = type
            };

            var templates = await _dbService.QueryAsync<EmailTemplate>(
                "GetEmailTemplatesByType", parameters);

            return templates.ToList();
        }

        public async Task<EmailTemplate> GetDefaultEmailTemplate(string organizationId, string type)
        {
            var parameters = new
            {
                OrganizationId = organizationId,
                Type = type
            };

            return await _dbService.QueryFirstOrDefaultAsync<EmailTemplate>(
                "GetDefaultEmailTemplateByType", parameters);
        }

        public async Task<EmailTemplate> UpdateEmailTemplate(EmailTemplate template)
        {
            var parameters = new
            {
                Id = template.Id,
                Name = template.Name,
                Description = template.Description,
                Subject = template.Subject,
                BodyContent = template.BodyContent,
                Type = template.Type,
                IsDefault = template.IsDefault,
                UpdatedBy = template.UpdatedBy
            };

            return await _dbService.QueryFirstOrDefaultAsync<EmailTemplate>(
                "UpdateEmailTemplate", parameters);
        }

        public async Task<bool> DeleteEmailTemplate(string id)
        {
            var parameters = new { Id = id };

            await _dbService.ExecuteAsync("DeleteEmailTemplate", parameters);
            return true;
        }
    }
}