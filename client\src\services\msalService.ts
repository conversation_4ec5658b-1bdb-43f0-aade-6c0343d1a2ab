import {
  PublicClientApplication,
  type AuthenticationResult,
  type SilentRequest,
  type RedirectRequest,
  type PopupRequest,
  type EndSessionRequest,
  type AccountInfo
} from '@azure/msal-browser';
import { msalConfig } from '../auth/msalConfig';
// Define apiScopes locally if not exported from msalConfig
const apiScopes = ["api://faa92dd9-5270-4024-8e1f-f2960dd20c6c/access_as_user"]; // adjust scopes as needed
// import loginScopes from msalConfig if it exists, otherwise define it here:
const loginScopes = ['openid', 'profile', 'email']; // adjust scopes as needed

class MSALService {
  private msalInstance: PublicClientApplication;

  constructor() {
    this.msalInstance = new PublicClientApplication(msalConfig);
  }

  /**
   * Initialize MSAL instance
   */
  async initialize(): Promise<void> {
    await this.msalInstance.initialize();
  }

  /**
   * Login with popup
   */
  async login(): Promise<AuthenticationResult> {
    try {
      const loginRequest: PopupRequest = {
        scopes: loginScopes,
        prompt: 'select_account',
      };

      const response = await this.msalInstance.loginPopup(loginRequest);
      this.msalInstance.setActiveAccount(response.account);

      return response;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  /**
   * Login with redirect
   */
  async loginRedirect(): Promise<void> {
    try {
      const loginRequest: RedirectRequest = {
        scopes: loginScopes,
        prompt: 'select_account',
      };

      await this.msalInstance.loginRedirect(loginRequest);
    } catch (error) {
      console.error('Login redirect failed:', error);
      throw error;
    }
  }

  /**
   * Handle redirect response
   */
  async handleRedirectResponse(): Promise<AuthenticationResult | null> {
    try {
      const response = await this.msalInstance.handleRedirectPromise();
      if (response && response.account) {
        this.msalInstance.setActiveAccount(response.account);
      }
      return response;
    } catch (error) {
      console.error('Handle redirect failed:', error);
      throw error;
    }
  }

  /**
   * Get access token silently
   */
  async getAccessToken(): Promise<string | null> {
    try {
      const account = this.getActiveAccount();
      if (!account) {
        throw new Error('No active account found');
      }

      const silentRequest: SilentRequest = {
        scopes: apiScopes,
        account: account,
      };

      const response = await this.msalInstance.acquireTokenSilent(silentRequest);
      return response.accessToken;
    } catch (error) {
      // If silent request fails, try popup
      try {
        const activeAccount = this.getActiveAccount();
        const popupRequest: PopupRequest = {
          scopes: apiScopes,
          account: activeAccount ?? undefined,
        };

        const response = await this.msalInstance.acquireTokenPopup(popupRequest);
        return response.accessToken;
      } catch (popupError) {
        console.error('Token acquisition failed:', popupError);
        throw popupError;
      }
    }
  }

  /**
   * Get active account
   */
  getActiveAccount(): AccountInfo | null {
    return this.msalInstance.getActiveAccount();
  }

  /**
   * Get all accounts
   */
  getAllAccounts(): AccountInfo[] {
    return this.msalInstance.getAllAccounts();
  }

  /**
   * Check if user is logged in
   */
  isLoggedIn(): boolean {
    const account = this.getActiveAccount();
    return !!account;
  }

  /**
   * Logout
   */
  async logout(): Promise<void> {
    try {
      const account = this.getActiveAccount();
      if (account) {
        const logoutRequest: EndSessionRequest = {
          account: account,
          postLogoutRedirectUri: window.location.origin,
        };
        
        await this.msalInstance.logoutPopup(logoutRequest);
      }
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    }
  }

  /**
   * Logout with redirect
   */
  async logoutRedirect(): Promise<void> {
    try {
      const account = this.getActiveAccount();
      if (account) {
        const logoutRequest: EndSessionRequest = {
          account: account,
          postLogoutRedirectUri: window.location.origin,
        };
        
        await this.msalInstance.logoutRedirect(logoutRequest);
      }
    } catch (error) {
      console.error('Logout redirect failed:', error);
      throw error;
    }
  }

  /**
   * Get user info from token claims
   */
  getUserInfo(): {
    objectId: string;
    email: string;
    name?: string;
    tenantId?: string;
  } | null {
    const account = this.getActiveAccount();
    if (!account) return null;

    return {
      objectId: account.localAccountId,
      email: account.username,
      name: account.name,
      tenantId: account.tenantId,
    };
  }

  /**
   * Clear cache
   */
  async clearCache(): Promise<void> {
    await this.msalInstance.clearCache();
  }
}

// Export singleton instance
export const msalService = new MSALService();

// Export for use in other parts of the app
export { apiScopes, msalConfig };
