import { useState, useCallback } from 'react';
import { apiService } from '../../services/apiService';

// Types for Compliance API
export interface ComplianceCertificate {
  id: string;
  organizationId: string;
  certificateType: string;
  certificateNumber: string;
  issuedDate: string;
  expiryDate: string;
  issuedBy: string;
  status: 'ACTIVE' | 'EXPIRED' | 'REVOKED';
  revokedDate?: string;
  revokedBy?: string;
  revokedReason?: string;
  certificateData: any; // JSON data
  createdAt: string;
  updatedAt?: string;
}

export interface CreateComplianceCertificateDTO {
  organizationId: string;
  certificateType: string;
  certificateNumber: string;
  issuedDate: string;
  expiryDate: string;
  certificateData?: any;
}

export interface RevokeComplianceCertificateDTO {
  revokedReason: string;
}

export interface CertificateSearchDTO {
  organizationId?: string;
  certificateType?: string;
  status?: string;
  issuedDateFrom?: string;
  issuedDateTo?: string;
  expiryDateFrom?: string;
  expiryDateTo?: string;
}

export interface ComplianceRule {
  id: string;
  name: string;
  description?: string;
  ruleType: string;
  ruleData: any; // JSON rule configuration
  isActive: boolean;
  createdBy: string;
  createdAt: string;
}

export interface CreateComplianceRuleDTO {
  name: string;
  description?: string;
  ruleType: string;
  ruleData: any;
}

// Compliance Certificate API Hooks
export const useComplianceCertificateApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Compliance Certificate CRUD endpoints
  const getAllComplianceCertificates = useCallback(() => 
    handleApiCall(() => apiService.get<ComplianceCertificate[]>('/ComplianceCertificate')), [handleApiCall]);

  const getComplianceCertificateById = useCallback((id: string) => 
    handleApiCall(() => apiService.get<ComplianceCertificate>(`/ComplianceCertificate/${id}`)), [handleApiCall]);

  const createComplianceCertificate = useCallback((data: CreateComplianceCertificateDTO) => 
    handleApiCall(() => apiService.post<ComplianceCertificate>('/ComplianceCertificate', data)), [handleApiCall]);

  const updateComplianceCertificate = useCallback((id: string, data: Partial<ComplianceCertificate>) => 
    handleApiCall(() => apiService.put<ComplianceCertificate>(`/ComplianceCertificate/${id}`, data)), [handleApiCall]);

  const deleteComplianceCertificate = useCallback((id: string) => 
    handleApiCall(() => apiService.delete(`/ComplianceCertificate/${id}`)), [handleApiCall]);

  // Certificate management
  const revokeComplianceCertificate = useCallback((id: string, data: RevokeComplianceCertificateDTO) => 
    handleApiCall(() => apiService.put(`/ComplianceCertificate/${id}/revoke`, data)), [handleApiCall]);

  const renewComplianceCertificate = useCallback((id: string) => 
    handleApiCall(() => apiService.post(`/ComplianceCertificate/${id}/renew`)), [handleApiCall]);

  // Certificate filtering and search
  const getComplianceCertificatesByOrganization = useCallback((organizationId: string) => 
    handleApiCall(() => apiService.get<ComplianceCertificate[]>(`/ComplianceCertificate/organization/${organizationId}`)), [handleApiCall]);

  const getExpiringComplianceCertificates = useCallback((days: number = 30) => 
    handleApiCall(() => apiService.get<ComplianceCertificate[]>('/ComplianceCertificate/expiring', { days })), [handleApiCall]);

  const getExpiredComplianceCertificates = useCallback(() => 
    handleApiCall(() => apiService.get<ComplianceCertificate[]>('/ComplianceCertificate/expired')), [handleApiCall]);

  const searchComplianceCertificates = useCallback((searchDto: CertificateSearchDTO) => 
    handleApiCall(() => apiService.post<ComplianceCertificate[]>('/ComplianceCertificate/search', searchDto)), [handleApiCall]);

  return {
    loading,
    error,
    getAllComplianceCertificates,
    getComplianceCertificateById,
    createComplianceCertificate,
    updateComplianceCertificate,
    deleteComplianceCertificate,
    revokeComplianceCertificate,
    renewComplianceCertificate,
    getComplianceCertificatesByOrganization,
    getExpiringComplianceCertificates,
    getExpiredComplianceCertificates,
    searchComplianceCertificates,
  };
};

// Compliance Rule API Hooks
export const useComplianceRuleApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Compliance Rule endpoints
  const getAllComplianceRules = useCallback(() => 
    handleApiCall(() => apiService.get<ComplianceRule[]>('/ComplianceRule')), [handleApiCall]);

  const getComplianceRuleById = useCallback((id: string) => 
    handleApiCall(() => apiService.get<ComplianceRule>(`/ComplianceRule/${id}`)), [handleApiCall]);

  const createComplianceRule = useCallback((data: CreateComplianceRuleDTO) => 
    handleApiCall(() => apiService.post<ComplianceRule>('/ComplianceRule', data)), [handleApiCall]);

  const updateComplianceRule = useCallback((id: string, data: Partial<ComplianceRule>) => 
    handleApiCall(() => apiService.put<ComplianceRule>(`/ComplianceRule/${id}`, data)), [handleApiCall]);

  const deleteComplianceRule = useCallback((id: string) => 
    handleApiCall(() => apiService.delete(`/ComplianceRule/${id}`)), [handleApiCall]);

  const getActiveComplianceRules = useCallback(() => 
    handleApiCall(() => apiService.get<ComplianceRule[]>('/ComplianceRule/active')), [handleApiCall]);

  const validateComplianceRule = useCallback((id: string, organizationId: string) => 
    handleApiCall(() => apiService.post(`/ComplianceRule/${id}/validate`, { organizationId })), [handleApiCall]);

  return {
    loading,
    error,
    getAllComplianceRules,
    getComplianceRuleById,
    createComplianceRule,
    updateComplianceRule,
    deleteComplianceRule,
    getActiveComplianceRules,
    validateComplianceRule,
  };
};
