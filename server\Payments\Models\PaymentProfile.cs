using System;

namespace Final_E_Receipt.Payments.Models
{
    public class PaymentProfile
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "NGN";
        public string Frequency { get; set; } // ONCE, MONTHLY, QUARTERLY, ANNUALLY
        public int DueDay { get; set; } // Day of month when payment is due
        public bool IsFixedAmount { get; set; } = true; // If false, amount can vary per organization
        public bool IsActive { get; set; } = true;
        public string PaymentTypeId { get; set; } // Link to PaymentType
        public string OrganizationId { get; set; } // If null, applies to all organizations
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
    }
}