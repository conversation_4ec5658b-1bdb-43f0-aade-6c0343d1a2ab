// Authentication & User Management Hooks
export {
  useAuth<PERSON><PERSON>,
  useUserInvitationApi,
  useAdminSetupApi,
  type CreateInvitationDTO,
  type UserInvitation,
} from './useAuth';

// Organization Management Hooks
export {
  useOrganizationApi,
  useOrganizationComplianceApi,
  type Organization,
  type CreateOrganizationDTO,
  type UpdateOrganizationDTO,
  type OrganizationCompliance,
} from './useOrganizations';

// Payment Management Hooks
export {
  usePaymentApi,
  usePaymentProfileApi,
  usePaymentScheduleApi,
  type Payment,
  type CreatePaymentDTO,
  type PaymentProfile,
  type CreatePaymentProfileDTO,
  type PaymentSchedule,
  type CreatePaymentScheduleDTO,
} from './usePayments';

// Receipt Management Hooks
export {
  useReceiptApi,
  useReceiptTemplateApi,
  type Receipt,
  type CreateReceiptDTO,
  type RevokeReceiptDTO,
  type ReceiptTemplate,
  type CreateReceiptTemplateDTO,
} from './useReceipts';

// Compliance Management Hooks
export {
  useComplianceCertificate<PERSON>pi,
  useComplianceRuleApi,
  type ComplianceCertificate,
  type CreateComplianceCertificateDTO,
  type RevokeComplianceCertificateDTO,
  type CertificateSearchDTO,
  type ComplianceRule,
  type CreateComplianceRuleDTO,
} from './useCompliance';

// Reporting Hooks
export {
  usePaymentReportingApi,
  useComplianceReportingApi,
  type PaymentReport,
  type ComplianceReport,
  type FinancialSummary,
  type YearOverYearComparison,
  type ReportFilters,
} from './useReporting';

// File Management Hooks
export {
  useFilesApi,
  type FileInfo,
  type UploadFileDTO,
  type FileSearchFilters,
  type BulkUploadResult,
} from './useFiles';

// Notification Hooks
export {
  useNotificationsApi,
  useNotificationTemplatesApi,
  type Notification,
  type CreateNotificationDTO,
  type NotificationTemplate,
  type CreateNotificationTemplateDTO,
  type NotificationFilters,
  type NotificationStats,
} from './useNotifications';

// Email Configuration Hooks
export {
  useEmailConfigApi,
  type EmailConfiguration,
  type CreateEmailConfigurationDTO,
  type UpdateEmailConfigurationDTO,
} from './useEmailConfig';

// Re-export common types for convenience
export type ApiHookReturn<T> = {
  loading: boolean;
  error: string | null;
  data?: T;
};

// Common API patterns
export type PaginatedResponse<T> = {
  data: T[];
  totalCount: number;
  pageSize: number;
  currentPage: number;
  totalPages: number;
};

export type ApiResponse<T> = {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
};

// Status enums for consistency
export const PaymentStatus = {
  PENDING: 'PENDING',
  ACKNOWLEDGED: 'ACKNOWLEDGED',
  APPROVED: 'APPROVED',
  COMPLETED: 'COMPLETED',
  REJECTED: 'REJECTED',
} as const;

export const CertificateStatus = {
  ACTIVE: 'ACTIVE',
  EXPIRED: 'EXPIRED',
  REVOKED: 'REVOKED',
} as const;

export const NotificationStatus = {
  UNREAD: 'UNREAD',
  READ: 'READ',
  ARCHIVED: 'ARCHIVED',
} as const;

export const NotificationPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT',
} as const;

export const UserRoles = {
  JTB_ADMIN: 'JTB_ADMIN',
  FINANCE_OFFICER: 'FINANCE_OFFICER',
  SENIOR_FINANCE_OFFICER: 'SENIOR_FINANCE_OFFICER',
  PAYER: 'PAYER',
} as const;
