import React, { useState, useEffect } from 'react';
import { Shield, Award, AlertCircle, Calendar, RefreshCw, TrendingUp } from 'lucide-react';
import { useComplianceReportingApi, useOrganizationApi } from '../../../hooks/api';
import ReportSummaryCard from '../Common/ReportSummaryCard';
import ReportTable, {type  TableColumn } from '../Common/ReportTable';
import ExportButtons from '../Common/ExportButtons';

interface ComplianceMetrics {
  totalOrganizations: number;
  averageComplianceScore: number;
  totalCertificates: number;
  activeCertificates: number;
  expiredCertificates: number;
  expiringCertificates: number;
  complianceLevels: {
    excellent: number;
    good: number;
    satisfactory: number;
    needsImprovement: number;
    poor: number;
  };
}

interface OrganizationCompliance {
  organizationId: string;
  organizationName: string;
  complianceScore: number;
  complianceLevel: 'EXCELLENT' | 'GOOD' | 'SATISFACTORY' | 'NEEDS_IMPROVEMENT' | 'POOR';
  totalCertificates: number;
  activeCertificates: number;
  expiredCertificates: number;
  expiringCertificates: number;
  lastAuditDate?: string;
  nextAuditDue?: string;
  contactEmail: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
}

const ComplianceDashboard: React.FC = () => {
  const { getComplianceDashboard, getComplianceMetrics, exportComplianceReport, loading } = useComplianceReportingApi();
  const { getAllOrganizations } = useOrganizationApi();
  
  const [metrics, setMetrics] = useState<ComplianceMetrics | null>(null);
  const [organizationData, setOrganizationData] = useState<OrganizationCompliance[]>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    pageSize: 25,
    totalCount: 0,
  });
  const [sortBy, setSortBy] = useState<string>('complianceScore');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    loadComplianceData();
  }, [pagination.currentPage, sortBy, sortDirection]);

  const loadComplianceData = async () => {
    try {
      // Load compliance metrics
      const metricsResult = await getComplianceMetrics();
      if (
        metricsResult &&
        typeof metricsResult === 'object' &&
        Object.keys(metricsResult).length > 0 &&
        'totalOrganizations' in metricsResult
      ) {
        setMetrics(metricsResult as ComplianceMetrics);
      } else {
        setMetrics(null);
      }

      // Load compliance dashboard data
      const dashboardResult = await getComplianceDashboard();
      if (dashboardResult) {
        setOrganizationData(dashboardResult.organizations || []);
        setPagination(prev => ({
          ...prev,
          totalPages: dashboardResult.totalPages || 1,
          totalCount: dashboardResult.totalCount || 0,
        }));
      }
    } catch (error) {
      console.error('Error loading compliance data:', error);
    }
  };

  const handleSort = (column: string, direction: 'asc' | 'desc') => {
    setSortBy(column);
    setSortDirection(direction);
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  const handleExportCSV = async () => {
    await exportComplianceReport('csv');
  };

  const handleExportExcel = async () => {
    await exportComplianceReport('excel');
  };

  const getComplianceLevelBadge = (level: string) => {
    const levelConfig = {
      EXCELLENT: { bg: 'bg-green-100', text: 'text-green-800', label: 'Excellent' },
      GOOD: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Good' },
      SATISFACTORY: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Satisfactory' },
      NEEDS_IMPROVEMENT: { bg: 'bg-orange-100', text: 'text-orange-800', label: 'Needs Improvement' },
      POOR: { bg: 'bg-red-100', text: 'text-red-800', label: 'Poor' },
    };

    const config = levelConfig[level as keyof typeof levelConfig] || levelConfig.SATISFACTORY;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const getRiskLevelBadge = (risk: string) => {
    const riskConfig = {
      LOW: { bg: 'bg-green-100', text: 'text-green-800', label: 'Low Risk' },
      MEDIUM: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Medium Risk' },
      HIGH: { bg: 'bg-red-100', text: 'text-red-800', label: 'High Risk' },
    };

    const config = riskConfig[risk as keyof typeof riskConfig] || riskConfig.LOW;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  const columns: TableColumn[] = [
    {
      key: 'organizationName',
      label: 'Organization',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm text-gray-500">{row.contactEmail}</div>
        </div>
      ),
    },
    {
      key: 'complianceScore',
      label: 'Compliance Score',
      sortable: true,
      render: (value) => (
        <div className="flex items-center">
          <span className={`text-lg font-bold ${getScoreColor(value)}`}>
            {value}%
          </span>
        </div>
      ),
    },
    {
      key: 'complianceLevel',
      label: 'Level',
      sortable: true,
      render: (value) => getComplianceLevelBadge(value),
    },
    {
      key: 'activeCertificates',
      label: 'Active Certificates',
      sortable: true,
      render: (value, row) => (
        <div className="text-center">
          <div className="text-lg font-medium text-green-600">{value}</div>
          <div className="text-xs text-gray-500">of {row.totalCertificates}</div>
        </div>
      ),
    },
    {
      key: 'expiringCertificates',
      label: 'Expiring Soon',
      sortable: true,
      render: (value) => (
        <span className={`font-medium ${value > 0 ? 'text-orange-600' : 'text-gray-500'}`}>
          {value}
        </span>
      ),
    },
    {
      key: 'riskLevel',
      label: 'Risk Level',
      sortable: true,
      render: (value) => getRiskLevelBadge(value),
    },
    {
      key: 'lastAuditDate',
      label: 'Last Audit',
      sortable: true,
      render: (value) => value ? new Date(value).toLocaleDateString() : 'N/A',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 rounded-lg bg-green-100 flex items-center justify-center">
            <Shield className="h-5 w-5 text-green-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Compliance Dashboard</h2>
            <p className="text-gray-600">Organization compliance status and certificate management</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={loadComplianceData}
            disabled={loading}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2 disabled:opacity-50"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
          <ExportButtons
            onExportCSV={handleExportCSV}
            onExportExcel={handleExportExcel}
            disabled={loading}
            variant="dropdown"
          />
        </div>
      </div>

      {/* Summary Cards */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <ReportSummaryCard
            title="Average Compliance Score"
            value={`${metrics.averageComplianceScore.toFixed(1)}%`}
            icon={TrendingUp}
            color="green"
            loading={loading}
          />
          <ReportSummaryCard
            title="Total Organizations"
            value={metrics.totalOrganizations}
            icon={Shield}
            color="blue"
            loading={loading}
          />
          <ReportSummaryCard
            title="Active Certificates"
            value={metrics.activeCertificates}
            icon={Award}
            color="green"
            loading={loading}
          />
          <ReportSummaryCard
            title="Expiring Soon"
            value={metrics.expiringCertificates}
            icon={AlertCircle}
            color="orange"
            loading={loading}
          />
        </div>
      )}

      {/* Compliance Levels Breakdown */}
      {metrics && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Compliance Levels Distribution</h3>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {[
              { key: 'excellent', label: 'Excellent', color: 'green' },
              { key: 'good', label: 'Good', color: 'blue' },
              { key: 'satisfactory', label: 'Satisfactory', color: 'yellow' },
              { key: 'needsImprovement', label: 'Needs Improvement', color: 'orange' },
              { key: 'poor', label: 'Poor', color: 'red' },
            ].map((level) => {
              const count = metrics.complianceLevels[level.key as keyof typeof metrics.complianceLevels];
              const percentage = metrics.totalOrganizations > 0 ? (count / metrics.totalOrganizations * 100).toFixed(1) : '0';
              return (
                <div key={level.key} className="text-center">
                  <div className={`text-2xl font-bold text-${level.color}-600`}>
                    {count}
                  </div>
                  <div className="text-sm text-gray-500">{level.label}</div>
                  <div className="text-xs text-gray-400">
                    {percentage}%
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Organizations Table */}
      <ReportTable
        columns={columns}
        data={organizationData}
        loading={loading}
        sortBy={sortBy}
        sortDirection={sortDirection}
        onSort={handleSort}
        pagination={{
          ...pagination,
          onPageChange: handlePageChange,
        }}
        emptyMessage="No compliance data available"
      />
    </div>
  );
};

export default ComplianceDashboard;
