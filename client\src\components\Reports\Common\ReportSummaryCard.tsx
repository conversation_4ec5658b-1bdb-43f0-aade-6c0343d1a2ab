import React from 'react';
import { type LucideIcon, TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface SummaryCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'gray';
  trend?: {
    value: number;
    label: string;
    direction: 'up' | 'down' | 'neutral';
  };
  loading?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const ReportSummaryCard: React.FC<SummaryCardProps> = ({
  title,
  value,
  icon: Icon,
  color = 'blue',
  trend,
  loading = false,
  className = '',
  size = 'md',
}) => {
  const getColorClasses = () => {
    const colors = {
      blue: {
        bg: 'bg-blue-500',
        bgLight: 'bg-blue-100',
        text: 'text-blue-600',
        textLight: 'text-blue-500',
      },
      green: {
        bg: 'bg-green-500',
        bgLight: 'bg-green-100',
        text: 'text-green-600',
        textLight: 'text-green-500',
      },
      red: {
        bg: 'bg-red-500',
        bgLight: 'bg-red-100',
        text: 'text-red-600',
        textLight: 'text-red-500',
      },
      yellow: {
        bg: 'bg-yellow-500',
        bgLight: 'bg-yellow-100',
        text: 'text-yellow-600',
        textLight: 'text-yellow-500',
      },
      purple: {
        bg: 'bg-purple-500',
        bgLight: 'bg-purple-100',
        text: 'text-purple-600',
        textLight: 'text-purple-500',
      },
      gray: {
        bg: 'bg-gray-500',
        bgLight: 'bg-gray-100',
        text: 'text-gray-600',
        textLight: 'text-gray-500',
      },
    };
    return colors[color];
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          padding: 'p-4',
          iconSize: 16,
          iconContainer: 'h-8 w-8',
          valueText: 'text-lg',
          titleText: 'text-xs',
          trendText: 'text-xs',
        };
      case 'lg':
        return {
          padding: 'p-8',
          iconSize: 28,
          iconContainer: 'h-16 w-16',
          valueText: 'text-3xl',
          titleText: 'text-base',
          trendText: 'text-sm',
        };
      default:
        return {
          padding: 'p-6',
          iconSize: 20,
          iconContainer: 'h-12 w-12',
          valueText: 'text-2xl',
          titleText: 'text-sm',
          trendText: 'text-sm',
        };
    }
  };

  const getTrendIcon = () => {
    if (!trend) return null;
    
    switch (trend.direction) {
      case 'up':
        return <TrendingUp size={14} className="text-green-500" />;
      case 'down':
        return <TrendingDown size={14} className="text-red-500" />;
      default:
        return <Minus size={14} className="text-gray-500" />;
    }
  };

  const getTrendColor = () => {
    if (!trend) return '';
    
    switch (trend.direction) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const colorClasses = getColorClasses();
  const sizeClasses = getSizeClasses();

  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      // Format large numbers with commas
      return val.toLocaleString();
    }
    return val;
  };

  return (
    <div className={`bg-white rounded-lg shadow-md ${sizeClasses.padding} ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className={`${sizeClasses.titleText} font-medium text-gray-500 mb-2`}>
            {title}
          </p>
          
          {loading ? (
            <div className="animate-pulse">
              <div className={`h-8 bg-gray-200 rounded w-24 mb-2`}></div>
              {trend && <div className="h-4 bg-gray-200 rounded w-16"></div>}
            </div>
          ) : (
            <>
              <p className={`${sizeClasses.valueText} font-bold text-gray-900 mb-1`}>
                {formatValue(value)}
              </p>
              
              {trend && (
                <div className="flex items-center space-x-1">
                  {getTrendIcon()}
                  <span className={`${sizeClasses.trendText} font-medium ${getTrendColor()}`}>
                    {trend.value > 0 ? '+' : ''}{trend.value}%
                  </span>
                  <span className={`${sizeClasses.trendText} text-gray-500`}>
                    {trend.label}
                  </span>
                </div>
              )}
            </>
          )}
        </div>
        
        <div className={`${sizeClasses.iconContainer} rounded-lg ${colorClasses.bgLight} flex items-center justify-center flex-shrink-0 ml-4`}>
          <Icon size={sizeClasses.iconSize} className={colorClasses.text} />
        </div>
      </div>
    </div>
  );
};

export default ReportSummaryCard;
