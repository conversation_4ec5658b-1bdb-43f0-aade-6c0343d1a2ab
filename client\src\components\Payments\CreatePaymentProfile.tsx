import React, { useState, useEffect } from 'react';
import {
  User,
  Save,
  X,
  Building2,
  FileText,
  AlertCircle,
  CheckCircle,
  Tag,
  Plus,
  Trash2,
} from 'lucide-react';
import { usePaymentProfileApi, useOrganizationApi } from '../../hooks/api';
import { useAuth } from '../../contexts/AuthContext';
import type { CreatePaymentProfileDTO, Organization } from '../../hooks/api';

interface CreatePaymentProfileProps {
  setActiveTab: (tab: string) => void;
  onPaymentProfileCreated?: () => void;
}

const CreatePaymentProfile: React.FC<CreatePaymentProfileProps> = ({
  setActiveTab,
  onPaymentProfileCreated,
}) => {
  const { loading, error, createPaymentProfile } = usePaymentProfileApi();
  const { getAllOrganizations } = useOrganizationApi();
  const { user } = useAuth();
  
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [formData, setFormData] = useState<CreatePaymentProfileDTO>({
    name: '',
    description: '',
    organizationId: '',
    paymentTypes: [],
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showSuccess, setShowSuccess] = useState(false);
  const [newPaymentType, setNewPaymentType] = useState('');

  // Role-based permissions
  const canCreateProfiles = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');

  // Predefined payment types
  const predefinedPaymentTypes = [
    'BANK_TRANSFER',
    'CREDIT_CARD',
    'DEBIT_CARD',
    'CASH',
    'CHECK',
    'ONLINE',
    'MOBILE_PAYMENT',
    'WIRE_TRANSFER',
    'ACH',
    'CRYPTOCURRENCY',
  ];

  useEffect(() => {
    if (!canCreateProfiles) {
      setActiveTab('payment-profiles');
      return;
    }
    
    loadOrganizations();
  }, []);

  const loadOrganizations = async () => {
    const result = await getAllOrganizations();
    if (result) {
      setOrganizations(result.filter(org => org.isActive));
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Profile name is required';
    }

    if (!formData.organizationId) {
      errors.organizationId = 'Organization is required';
    }

    if (formData.paymentTypes.length === 0) {
      errors.paymentTypes = 'At least one payment type is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for this field when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleAddPaymentType = (type: string) => {
    if (type && !formData.paymentTypes.includes(type)) {
      setFormData(prev => ({
        ...prev,
        paymentTypes: [...prev.paymentTypes, type],
      }));
      
      // Clear payment types error
      if (formErrors.paymentTypes) {
        setFormErrors(prev => ({
          ...prev,
          paymentTypes: '',
        }));
      }
    }
  };

  const handleAddCustomPaymentType = () => {
    if (newPaymentType.trim() && !formData.paymentTypes.includes(newPaymentType.trim().toUpperCase())) {
      handleAddPaymentType(newPaymentType.trim().toUpperCase());
      setNewPaymentType('');
    }
  };

  const handleRemovePaymentType = (type: string) => {
    setFormData(prev => ({
      ...prev,
      paymentTypes: prev.paymentTypes.filter(t => t !== type),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const result = await createPaymentProfile(formData);
    if (result) {
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        onPaymentProfileCreated?.();
        setActiveTab('payment-profiles');
      }, 2000);
    }
  };

  const handleCancel = () => {
    setActiveTab('payment-profiles');
  };

  if (!canCreateProfiles) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">You don't have permission to create payment profiles</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="h-8 w-8 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
            <User className="h-5 w-5 text-[#2aa45c]" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-[#045024]">Create Payment Profile</h2>
            <p className="text-gray-600">Add a new payment profile to organize payment types</p>
          </div>
        </div>
        <button
          onClick={handleCancel}
          className="text-gray-500 hover:text-gray-700 transition-colors"
        >
          <X size={24} />
        </button>
      </div>

      {/* Success Message */}
      {showSuccess && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircle className="text-green-500 mr-2" size={20} />
            <span className="text-green-700">Payment profile created successfully!</span>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={20} />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Form */}
      <div className="bg-white rounded-lg shadow-md">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Profile Name *
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                      formErrors.name ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Enter profile name"
                  />
                </div>
                {formErrors.name && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>
                )}
              </div>

              <div>
                <label htmlFor="organizationId" className="block text-sm font-medium text-gray-700 mb-2">
                  Organization *
                </label>
                <div className="relative">
                  <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <select
                    id="organizationId"
                    name="organizationId"
                    value={formData.organizationId}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                      formErrors.organizationId ? 'border-red-300' : 'border-gray-300'
                    }`}
                  >
                    <option value="">Select Organization</option>
                    {organizations.map((org) => (
                      <option key={org.id} value={org.id}>
                        {org.name}
                      </option>
                    ))}
                  </select>
                </div>
                {formErrors.organizationId && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.organizationId}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <div className="relative">
                  <FileText className="absolute left-3 top-3 text-gray-400" size={20} />
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                    placeholder="Enter profile description"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Payment Types */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Types *</h3>
            
            {/* Selected Payment Types */}
            {formData.paymentTypes.length > 0 && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">Selected Types</label>
                <div className="flex flex-wrap gap-2">
                  {formData.paymentTypes.map((type) => (
                    <span
                      key={type}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#2aa45c] text-white"
                    >
                      <Tag className="w-3 h-3 mr-1" />
                      {type}
                      <button
                        type="button"
                        onClick={() => handleRemovePaymentType(type)}
                        className="ml-2 text-white hover:text-gray-200"
                      >
                        <X size={14} />
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Predefined Payment Types */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Available Types</label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                {predefinedPaymentTypes
                  .filter(type => !formData.paymentTypes.includes(type))
                  .map((type) => (
                    <button
                      key={type}
                      type="button"
                      onClick={() => handleAddPaymentType(type)}
                      className="flex items-center justify-center px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50 transition-colors"
                    >
                      <Plus size={14} className="mr-1" />
                      {type}
                    </button>
                  ))}
              </div>
            </div>

            {/* Custom Payment Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Add Custom Type</label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newPaymentType}
                  onChange={(e) => setNewPaymentType(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  placeholder="Enter custom payment type"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddCustomPaymentType())}
                />
                <button
                  type="button"
                  onClick={handleAddCustomPaymentType}
                  className="px-4 py-2 bg-[#2aa45c] text-white rounded-lg hover:bg-[#076934] transition-colors"
                >
                  Add
                </button>
              </div>
            </div>

            {formErrors.paymentTypes && (
              <p className="mt-1 text-sm text-red-600">{formErrors.paymentTypes}</p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="bg-[#2aa45c] text-white px-6 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Creating...</span>
                </>
              ) : (
                <>
                  <Save size={16} />
                  <span>Create Profile</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreatePaymentProfile;
