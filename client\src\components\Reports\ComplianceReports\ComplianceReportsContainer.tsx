import React, { useState } from 'react';
import { Shield, Calendar, Building2, Award } from 'lucide-react';
import ComplianceDashboard from './ComplianceDashboard';
import ExpiringCertificatesReport from './ExpiringCertificatesReport';

const ComplianceReportsContainer: React.FC = () => {
  const [activeReport, setActiveReport] = useState<'dashboard' | 'expiring' | 'organization'>('dashboard');

  const reportTabs = [
    {
      id: 'dashboard',
      label: 'Compliance Dashboard',
      icon: Shield,
      description: 'Overall compliance metrics and organization status',
    },
    {
      id: 'expiring',
      label: 'Expiring Certificates',
      icon: Calendar,
      description: 'Certificates expiring soon with renewal tracking',
    },
    {
      id: 'organization',
      label: 'Organization Reports',
      icon: Building2,
      description: 'Detailed compliance reports by organization',
    },
  ];

  const renderActiveReport = () => {
    switch (activeReport) {
      case 'dashboard':
        return <ComplianceDashboard />;
      case 'expiring':
        return <ExpiringCertificatesReport />;
      case 'organization':
        return (
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Organization compliance reports coming soon</p>
            </div>
          </div>
        );
      default:
        return <ComplianceDashboard />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="h-10 w-10 rounded-lg bg-green-100 flex items-center justify-center">
          <Shield className="h-5 w-5 text-green-600" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Compliance Reports</h1>
          <p className="text-gray-600">Monitor compliance status and certificate management</p>
        </div>
      </div>

      {/* Report Navigation */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {reportTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveReport(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeReport === tab.id
                    ? 'border-[#2aa45c] text-[#2aa45c]'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon size={16} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Descriptions */}
        <div className="px-6 py-3 bg-gray-50">
          {reportTabs.map((tab) => (
            activeReport === tab.id && (
              <p key={tab.id} className="text-sm text-gray-600">
                {tab.description}
              </p>
            )
          ))}
        </div>
      </div>

      {/* Active Report Content */}
      {renderActiveReport()}
    </div>
  );
};

export default ComplianceReportsContainer;
