using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Final_E_Receipt.Payments.Services;
using Final_E_Receipt.Files.DTOs;

namespace Final_E_Receipt.Payments.Controllers
{
    [ApiController]
    [Route("api/payments/{paymentId}/proof")]
    [Authorize]
    public class PaymentProofController : ControllerBase
    {
        private readonly PaymentProofService _paymentProofService;
        private readonly ILogger<PaymentProofController> _logger;

        public PaymentProofController(PaymentProofService paymentProofService, ILogger<PaymentProofController> logger)
        {
            _paymentProofService = paymentProofService;
            _logger = logger;
        }

        [HttpPost("upload")]
        [Consumes("multipart/form-data")]
        //[ApiExplorerSettings(IgnoreApi = true)]
        public async Task<IActionResult> UploadPaymentProof(string paymentId, IFormFile file, string? description = null)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var organizationId = User.FindFirst("OrganizationId")?.Value;

                if (file == null || file.Length == 0)
                    return BadRequest(new { message = "File is required" });

                var uploadedFile = await _paymentProofService.UploadPaymentProof(
                    paymentId, 
                    file, 
                    userId, 
                    organizationId, 
                    description
                );

                var response = new FileUploadResponseDTO
                {
                    Id = uploadedFile.Id,
                    FileName = uploadedFile.FileName,
                    OriginalFileName = uploadedFile.OriginalFileName,
                    ContentType = uploadedFile.ContentType,
                    FileSize = uploadedFile.FileSize,
                    RelatedEntityType = uploadedFile.RelatedEntityType,
                    RelatedEntityId = uploadedFile.RelatedEntityId,
                    CreatedAt = uploadedFile.CreatedAt,
                    Description = uploadedFile.Description,
                    Category = uploadedFile.Category,
                    IsScanned = uploadedFile.IsScanned,
                    ScanResult = uploadedFile.ScanResult
                };

                return Ok(response);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading payment proof for payment {PaymentId}", paymentId);
                return StatusCode(500, new { message = "An error occurred while uploading the payment proof" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetPaymentProofFiles(string paymentId)
        {
            try
            {
                var files = await _paymentProofService.GetPaymentProofFiles(paymentId);
                
                var response = files.Select(f => new FileListDTO
                {
                    Id = f.Id,
                    FileName = f.FileName,
                    OriginalFileName = f.OriginalFileName,
                    ContentType = f.ContentType,
                    FileSize = f.FileSize,
                    RelatedEntityType = f.RelatedEntityType,
                    RelatedEntityId = f.RelatedEntityId,
                    CreatedAt = f.CreatedAt,
                    Description = f.Description,
                    Category = f.Category,
                    IsScanned = f.IsScanned,
                    ScanResult = f.ScanResult,
                    UploadedBy = f.UploadedBy
                }).ToList();

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving payment proof files for payment {PaymentId}", paymentId);
                return StatusCode(500, new { message = "An error occurred while retrieving payment proof files" });
            }
        }

        [HttpGet("{fileId}/download")]
        public async Task<IActionResult> DownloadPaymentProof(string paymentId, string fileId)
        {
            try
            {
                var file = await _paymentProofService.GetPaymentProofById(fileId);
                if (file == null || file.RelatedEntityId != paymentId)
                    return NotFound(new { message = "Payment proof file not found" });

                // Check if user has access to this payment proof
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var organizationId = User.FindFirst("OrganizationId")?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                // Allow access if user is admin, finance officer, or owns the file, or belongs to same organization
                if (userRole != "JTB_ADMIN" && userRole != "FINANCE_OFFICER" && userRole != "SENIOR_FINANCE_OFFICER" &&
                    file.UploadedBy != userId && file.OrganizationId != organizationId)
                {
                    return Forbid();
                }

                var fileContent = await _paymentProofService.DownloadPaymentProof(fileId);
                
                return File(fileContent, file.ContentType, file.OriginalFileName);
            }
            catch (FileNotFoundException)
            {
                return NotFound(new { message = "Payment proof file not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading payment proof {FileId} for payment {PaymentId}", fileId, paymentId);
                return StatusCode(500, new { message = "An error occurred while downloading the payment proof" });
            }
        }

        [HttpDelete("{fileId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> DeletePaymentProof(string paymentId, string fileId)
        {
            try
            {
                var file = await _paymentProofService.GetPaymentProofById(fileId);
                if (file == null || file.RelatedEntityId != paymentId)
                    return NotFound(new { message = "Payment proof file not found" });

                // Check if user has permission to delete this file
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                // Allow deletion if user is admin, finance officer, or owns the file
                if (userRole != "JTB_ADMIN" && userRole != "FINANCE_OFFICER" && userRole != "SENIOR_FINANCE_OFFICER" &&
                    file.UploadedBy != userId)
                {
                    return Forbid();
                }

                var success = await _paymentProofService.DeletePaymentProof(fileId, paymentId);
                if (!success)
                    return BadRequest(new { message = "Failed to delete payment proof file" });

                return Ok(new { message = "Payment proof file deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting payment proof {FileId} for payment {PaymentId}", fileId, paymentId);
                return StatusCode(500, new { message = "An error occurred while deleting the payment proof" });
            }
        }

        [HttpPost("{fileId}/validate")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> ValidatePaymentProof(string paymentId, string fileId, [FromBody] ValidateProofRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                
                var success = await _paymentProofService.ValidatePaymentProof(
                    fileId, 
                    userId, 
                    request.IsValid, 
                    request.Comments
                );

                if (!success)
                    return BadRequest(new { message = "Failed to validate payment proof" });

                return Ok(new { message = $"Payment proof {(request.IsValid ? "approved" : "rejected")} successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating payment proof {FileId} for payment {PaymentId}", fileId, paymentId);
                return StatusCode(500, new { message = "An error occurred while validating the payment proof" });
            }
        }
    }

    public class ValidateProofRequest
    {
        public bool IsValid { get; set; }
        public string? Comments { get; set; }
    }
}
