import React, { useState } from 'react';
import {  
  History,
  CheckCircle,
  XCircle,
  DollarSign,
} from 'lucide-react';

interface ApprovalHistoryItem {
  id: string;
  paymentId: string;
  payerName: string;
  amount: number;
  description: string;
  approvedDate: string;
  status: 'approved' | 'rejected';
  approvedBy: string;
  notes?: string;
}

const approvalHistory: ApprovalHistoryItem[] = [
  {
    id: 'HIST-001',
    paymentId: 'PAY-025',
    payerName: 'Tech Solutions Ltd',
    amount: 2500.00,
    description: 'Software license renewal',
    approvedDate: '2025-06-25',
    status: 'approved',
    approvedBy: 'Senior Finance Officer',
    notes: 'Approved for annual renewal'
  },
  {
    id: 'HIST-002',
    paymentId: 'PAY-024',
    payerName: 'Office Supplies Co',
    amount: 350.75,
    description: 'Office equipment purchase',
    approvedDate: '2025-06-24',
    status: 'rejected',
    approvedBy: 'Senior Finance Officer',
    notes: 'Insufficient budget allocation'
  }
];

// Approval History Page Components
const ApprovalHistoryStats: React.FC = () => (
  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">Total Processed</p>
          <p className="text-2xl font-bold" style={{ color: '#045024' }}>{approvalHistory.length}</p>
        </div>
        <History className="text-blue-500" size={24} />
      </div>
    </div>
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">Approved</p>
          <p className="text-2xl font-bold text-green-600">
            {approvalHistory.filter(h => h.status === 'approved').length}
          </p>
        </div>
        <CheckCircle className="text-green-500" size={24} />
      </div>
    </div>
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">Rejected</p>
          <p className="text-2xl font-bold text-red-600">
            {approvalHistory.filter(h => h.status === 'rejected').length}
          </p>
        </div>
        <XCircle className="text-red-500" size={24} />
      </div>
    </div>
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">Total Value</p>
          <p className="text-2xl font-bold" style={{ color: '#045024' }}>
            ₦{approvalHistory.reduce((sum, h) => sum + h.amount, 0).toLocaleString()}
          </p>
        </div>
        <DollarSign className="text-green-500" size={24} />
      </div>
    </div>
  </div>
);

interface ApprovalHistoryFiltersProps {
  dateFilter: string;
  setDateFilter: (date: string) => void;
  statusFilter: string;
  setStatusFilter: (status: string) => void;
}

const ApprovalHistoryFilters: React.FC<ApprovalHistoryFiltersProps> = ({
  dateFilter,
  setDateFilter,
  statusFilter,
  setStatusFilter
}) => (
  <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
    <div className="flex flex-col md:flex-row gap-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">From Date</label>
        <input
          type="date"
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#045024] focus:border-[#045024]"
          value={dateFilter}
          onChange={(e) => setDateFilter(e.target.value)}
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
        <select
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#045024] focus:border-[#045024]"
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
        >
          <option value="all">All Status</option>
          <option value="approved">Approved</option>
          <option value="rejected">Rejected</option>
        </select>
      </div>
    </div>
  </div>
);

interface ApprovalHistoryTableProps {
  history: ApprovalHistoryItem[];
}

const ApprovalHistoryTable: React.FC<ApprovalHistoryTableProps> = ({ history }) => (
  <div className="bg-white rounded-lg shadow-sm border border-gray-200">
    <div className="p-6 border-b border-gray-200">
      <h3 className="text-lg font-semibold" style={{ color: '#045024' }}>Approval History</h3>
    </div>
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment ID</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payer</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {history.map((item) => (
            <tr key={item.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {item.paymentId}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div>
                  <div className="text-sm font-medium text-gray-900">{item.payerName}</div>
                  <div className="text-sm text-gray-500">{item.description}</div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold" style={{ color: '#045024' }}>
                ₦{item.amount.toLocaleString()}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  item.status === 'approved' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {item.status.toUpperCase()}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {new Date(item.approvedDate).toLocaleDateString()}
              </td>
              <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                {item.notes || '-'}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
);

const ApprovalHistoryPage: React.FC = () => {
  const [dateFilter, setDateFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const filteredHistory = approvalHistory.filter(item => {
    const matchesDate = !dateFilter || item.approvedDate >= dateFilter;
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    return matchesDate && matchesStatus;
  });

  return (
    <div className="space-y-6">
      <ApprovalHistoryStats />
      <ApprovalHistoryFilters
        dateFilter={dateFilter}
        setDateFilter={setDateFilter}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
      />
      <ApprovalHistoryTable history={filteredHistory} />
    </div>
  );
};

export default ApprovalHistoryPage;