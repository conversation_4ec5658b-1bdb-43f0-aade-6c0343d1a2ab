# JRB Template Files - Generalized Service

## 🏢 Joint Revenue Board Service
JRB provides tax services to multiple organizations using generalized templates.

## 📁 Required Template Files (Only 3 Files Needed!)

### 📄 Receipt Template (ONE for all receipts)
```
wwwroot/templates/receipts/
├── jrb-receipt-template.png              # Used for ALL receipts
└── default-receipt-template.png          # Fallback
```

### 🏆 Certificate Templates (TWO for all certificates)

#### Landscape Certificates
```
wwwroot/templates/certificates/
├── jrb-certificate-landscape-template.png # Used for Annual License
```

#### Portrait Certificates
```
├── jrb-certificate-portrait-template.png  # Used for Tax Clearance, Quarterly, Payment
```

#### Default Fallback
```
└── default-certificate-template.png       # Fallback for all types
```

## 🎯 How It Works

### Receipt Generation
- Company A pays tax → Uses `jrb-receipt-template.png`
- Company B pays tax → Uses `jrb-receipt-template.png`
- Company C pays tax → Uses `jrb-receipt-template.png`
- **Same template, different payer information**

### Certificate Generation
- Annual License → Uses `jrb-certificate-landscape-template.png`
- Tax Clearance → Uses `jrb-certificate-portrait-template.png`
- Quarterly Compliance → Uses `jrb-certificate-portrait-template.png`
- Payment Compliance → Uses `jrb-certificate-portrait-template.png`

## 🖼️ Preview Images (Optional)
```
wwwroot/templates/previews/
├── jrb-001-annual_license-preview.png
├── jrb-001-annual_license-landscape-preview.png
├── jrb-001-tax_clearance-preview.png
├── jrb-001-quarterly_compliance-preview.png
├── jrb-001-payment_compliance-preview.png
└── default-certificate-preview.png
```

## 📐 Template Specifications

### Image Requirements
- **Format:** PNG (recommended) or JPG
- **Resolution:** 300 DPI minimum
- **Background:** White or transparent

### Portrait Templates (Tax Clearance, Quarterly, Payment)
- **Size:** 2480 x 3508 pixels (A4 Portrait at 300 DPI)
- **Orientation:** Vertical/Tall

### Landscape Templates (Annual License)
- **Size:** 3508 x 2480 pixels (A4 Landscape at 300 DPI)  
- **Orientation:** Horizontal/Wide

## 🎯 Text Positioning Areas

Leave these areas **BLANK** in your template images for dynamic text:

### Portrait Certificate Positions
- Certificate Number: Top right (400, 120)
- Organization Name: Upper left (200, 220)
- Certificate Type: Below org name (200, 270)
- Total Amount: Center right (400, 350)
- Valid From: Lower left (200, 400)
- Valid Until: Lower right (400, 400)
- Issued Date: Bottom left (200, 450)
- Regulatory Body: Bottom center (200, 500)

### Landscape Certificate Positions
- Certificate Number: Top right (600, 150)
- Organization Name: Upper center (300, 250)
- Certificate Type: Below org name (300, 300)
- Total Amount: Center right (500, 400)
- Valid From: Lower left (200, 450)
- Valid Until: Lower center (500, 450)
- Issued Date: Bottom left (300, 500)
- Regulatory Body: Bottom center (300, 550)

## ✅ Minimum Required Files

To get started, create these essential files:

1. **jrb-001-receipt-template.png** (Portrait)
2. **jrb-001-annual_license-landscape-template.png** (Landscape)
3. **jrb-001-tax_clearance-template.png** (Portrait)
4. **default-certificate-template.png** (Fallback)

## 🔍 How to Test

1. Create template files with the correct names
2. Place them in the correct folders
3. Run your application
4. Go to certificate creation page
5. Select certificate type
6. Check if your templates appear in the template selector

## 🚨 Common Issues

### Template Not Found
- Check file name spelling exactly matches pattern
- Ensure file is in correct folder
- Verify organization ID matches database

### Template Not Showing
- Check file permissions
- Ensure PNG/JPG format
- Verify file is not corrupted

### Wrong Orientation
- Annual License: Use landscape template
- Other certificates: Use portrait template
- File name must include "-landscape" for landscape orientation

## 📞 Support

If templates don't appear:
1. Check browser console for errors
2. Verify API endpoint: `/api/certificate-templates/available?organizationId=jrb-001&certificateType=ANNUAL_LICENSE`
3. Check server logs for file loading errors
