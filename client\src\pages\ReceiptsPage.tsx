import React, { useState } from 'react';
import { Receipt } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import ReceiptList from '../components/Receipts/ReceiptList';
import CreateReceipt from '../components/Receipts/CreateReceipt';
import ReceiptDetails from '../components/Receipts/ReceiptDetails';
import type { Receipt as ReceiptType } from '../hooks/api';

const ReceiptsPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<string>('receipts');
  const [selectedReceipt, setSelectedReceipt] = useState<ReceiptType | null>(null);

  // Role-based permissions
  const canCreateReceipts = ['JTB_ADMIN', 'FINANCE_OFFICER', 'SENIOR_FINANCE_OFFICER'].includes(user?.role || '');
  const canEditReceipts = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');

  const handleReceiptCreated = () => {
    // Refresh the receipt list when a new receipt is created
    setActiveTab('receipts');
  };

  const handleReceiptUpdated = () => {
    // Refresh the receipt list when a receipt is updated
    setActiveTab('receipts');
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'receipts':
        return (
          <ReceiptList
            setActiveTab={setActiveTab}
            setSelectedReceipt={setSelectedReceipt}
          />
        );
      
      case 'create-receipt':
        return canCreateReceipts ? (
          <CreateReceipt
            setActiveTab={setActiveTab}
            onReceiptCreated={handleReceiptCreated}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">You don't have permission to create receipts</p>
          </div>
        );
      
      case 'edit-receipt':
        return canEditReceipts && selectedReceipt ? (
          <div className="text-center py-8">
            <p className="text-gray-500">Edit receipt functionality coming soon</p>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {!canEditReceipts 
                ? "You don't have permission to edit receipts" 
                : "No receipt selected for editing"}
            </p>
          </div>
        );
      
      case 'receipt-details':
        return selectedReceipt ? (
          <ReceiptDetails
            receipt={selectedReceipt}
            setActiveTab={setActiveTab}
            setSelectedReceipt={setSelectedReceipt}
            onReceiptUpdated={handleReceiptUpdated}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">No receipt selected for viewing</p>
          </div>
        );
      
      default:
        return (
          <ReceiptList
            setActiveTab={setActiveTab}
            setSelectedReceipt={setSelectedReceipt}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderContent()}
      </div>
    </div>
  );
};

export default ReceiptsPage;
