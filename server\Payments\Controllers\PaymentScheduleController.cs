using System.Security.Claims;
using System.Threading.Tasks;
using Final_E_Receipt.Payments.Models;
using Final_E_Receipt.Payments.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Final_E_Receipt.Payments.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PaymentScheduleController : ControllerBase
    {
        private readonly PaymentScheduleService _scheduleService;
        private readonly PaymentScheduleImportService _importService;

        public PaymentScheduleController(PaymentScheduleService scheduleService, PaymentScheduleImportService importService)
        {
            _scheduleService = scheduleService;
            _importService = importService;
        }

        [HttpPost]
        [Authorize(Roles = "ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> CreatePaymentSchedule([FromBody] PaymentSchedule schedule)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            schedule.CreatedBy = userId;

            var createdSchedule = await _scheduleService.CreatePaymentSchedule(schedule);
            
            if (createdSchedule == null)
                return BadRequest(new { message = "Failed to create payment schedule" });

            return Ok(createdSchedule);
        }

        [HttpGet("{id}")]
        [Authorize]
        public async Task<IActionResult> GetPaymentScheduleById(string id)
        {
            var schedule = await _scheduleService.GetPaymentScheduleById(id);
            
            if (schedule == null)
                return NotFound(new { message = "Payment schedule not found" });

            return Ok(schedule);
        }

        [HttpGet("organization/{organizationId}")]
        [Authorize]
        public async Task<IActionResult> GetPaymentSchedulesByOrganization(string organizationId)
        {
            var schedules = await _scheduleService.GetPaymentSchedulesByOrganization(organizationId);
            return Ok(schedules);
        }

        [HttpGet("profile/{profileId}")]
        [Authorize]
        public async Task<IActionResult> GetPaymentSchedulesByProfile(string profileId)
        {
            var schedules = await _scheduleService.GetPaymentSchedulesByProfile(profileId);
            return Ok(schedules);
        }

        [HttpPut("{id}/status")]
        [Authorize(Roles = "ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> UpdatePaymentScheduleStatus(string id, [FromBody] UpdateScheduleStatusRequest request)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            var updatedSchedule = await _scheduleService.UpdatePaymentScheduleStatus(
                id, 
                request.Status, 
                request.PaymentId, 
                userId
            );
            
            if (updatedSchedule == null)
                return NotFound(new { message = "Payment schedule not found" });

            return Ok(updatedSchedule);
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> DeletePaymentSchedule(string id)
        {
            var success = await _scheduleService.DeletePaymentSchedule(id);
            
            if (!success)
                return BadRequest(new { message = "Failed to delete payment schedule. It may have already been paid." });

            return Ok(new { message = "Payment schedule deleted successfully" });
        }

        [HttpPost("import/{paymentProfileId}")]
        [Authorize(Roles = "ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> ImportFromExcel(string paymentProfileId,  IFormFile excelFile)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var organizationId = User.FindFirst("OrganizationId")?.Value;

                if (excelFile == null || excelFile.Length == 0)
                    return BadRequest(new { message = "Excel file is required" });

                if (!excelFile.FileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                    return BadRequest(new { message = "Only .xlsx files are supported" });

                var result = await _importService.ImportFromExcel(paymentProfileId, excelFile, userId, organizationId);

                if (result.IsSuccess)
                {
                    return Ok(new
                    {
                        message = "Import completed successfully",
                        totalRows = result.TotalRows,
                        successfulImports = result.SuccessfulImports,
                        failedImports = result.FailedImports,
                        uploadedFileId = result.UploadedFileId,
                        fileName = result.FileName,
                        errors = result.Errors,
                        importedSchedules = result.ImportedSchedules
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        message = result.ErrorMessage,
                        errors = result.Errors
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred during import", error = ex.Message });
            }
        }

        [HttpGet("template")]
        [Authorize(Roles = "ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> DownloadExcelTemplate()
        {
            try
            {
                var templateBytes = await _importService.GenerateExcelTemplate();
                var fileName = $"PaymentScheduleTemplate_{DateTime.Now:yyyyMMdd}.xlsx";

                return File(templateBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred generating template", error = ex.Message });
            }
        }
    }

    public class UpdateScheduleStatusRequest
    {
        public string Status { get; set; }
        public string PaymentId { get; set; }
    }
}
