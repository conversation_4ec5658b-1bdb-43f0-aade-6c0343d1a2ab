import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Save,
  X,
  DollarSign,
  Building2,
  User,
  FileText,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Clock,
} from 'lucide-react';
import { usePaymentScheduleApi, useOrganizationApi, usePaymentProfileApi } from '../../hooks/api';
import { useAuth } from '../../contexts/AuthContext';
import { useNotifications } from '../../contexts/NotificationContext';
import type { CreatePaymentScheduleDTO, Organization, PaymentProfile } from '../../hooks/api';

interface CreatePaymentScheduleProps {
  setActiveTab: (tab: string) => void;
  onPaymentScheduleCreated?: () => void;
}

const CreatePaymentSchedule: React.FC<CreatePaymentScheduleProps> = ({
  setActiveTab,
  onPaymentScheduleCreated,
}) => {
  const { loading, error, createPaymentSchedule } = usePaymentScheduleApi();
  const { getAllOrganizations } = useOrganizationApi();
  const { getAllPaymentProfiles } = usePaymentProfileApi();
  const { user } = useAuth();
  const { notifyPaymentScheduleCreated } = useNotifications();
  
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [paymentProfiles, setPaymentProfiles] = useState<PaymentProfile[]>([]);
  const [formData, setFormData] = useState<CreatePaymentScheduleDTO>({
    name: '',
    description: '',
    organizationId: '',
    paymentProfileId: '',
    amount: 0,
    currency: 'USD',
    frequency: 'MONTHLY',
    startDate: '',
    endDate: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showSuccess, setShowSuccess] = useState(false);
  const [nextPaymentDates, setNextPaymentDates] = useState<string[]>([]);

  // Role-based permissions
  const canCreateSchedules = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');

  useEffect(() => {
    if (!canCreateSchedules) {
      setActiveTab('payment-schedules');
      return;
    }
    
    loadOrganizations();
    loadPaymentProfiles();
  }, []);

  useEffect(() => {
    // Calculate next payment dates when form data changes
    if (formData.startDate && formData.frequency) {
      calculateNextPaymentDates();
    }
  }, [formData.startDate, formData.frequency]);

  const loadOrganizations = async () => {
    const result = await getAllOrganizations();
    if (result) {
      setOrganizations(result.filter(org => org.isActive));
    }
  };

  const loadPaymentProfiles = async () => {
    const result = await getAllPaymentProfiles();
    if (result) {
      setPaymentProfiles(result.filter(profile => profile.isActive));
    }
  };

  const calculateNextPaymentDates = () => {
    if (!formData.startDate) {
      setNextPaymentDates([]);
      return;
    }

    const startDate = new Date(formData.startDate);
    const dates: string[] = [];
    let currentDate = new Date(startDate);

    // Calculate next 5 payment dates
    for (let i = 0; i < 5; i++) {
      dates.push(currentDate.toLocaleDateString());
      
      switch (formData.frequency) {
        case 'MONTHLY':
          currentDate.setMonth(currentDate.getMonth() + 1);
          break;
        case 'QUARTERLY':
          currentDate.setMonth(currentDate.getMonth() + 3);
          break;
        case 'ANNUALLY':
          currentDate.setFullYear(currentDate.getFullYear() + 1);
          break;
      }
    }

    setNextPaymentDates(dates);
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Schedule name is required';
    }

    if (!formData.organizationId) {
      errors.organizationId = 'Organization is required';
    }

    if (!formData.paymentProfileId) {
      errors.paymentProfileId = 'Payment profile is required';
    }

    if (!formData.amount || formData.amount <= 0) {
      errors.amount = 'Amount must be greater than 0';
    }

    if (!formData.startDate) {
      errors.startDate = 'Start date is required';
    } else {
      const startDate = new Date(formData.startDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (startDate < today) {
        errors.startDate = 'Start date cannot be in the past';
      }
    }

    if (formData.endDate) {
      const startDate = new Date(formData.startDate);
      const endDate = new Date(formData.endDate);
      
      if (endDate <= startDate) {
        errors.endDate = 'End date must be after start date';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'amount' ? parseFloat(value) || 0 : value,
    }));

    // Clear error for this field when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const result = await createPaymentSchedule(formData);
    if (result) {
      // Send notification to the payer (if this is for a specific payer)
      // Note: In a real implementation, you'd get the payer's user ID from the payment profile or organization
      // For now, we'll assume the current user is creating it for themselves
      const selectedProfile = paymentProfiles.find(p => p.id === formData.paymentProfileId);
      if (selectedProfile) {
        await notifyPaymentScheduleCreated(
          result.id,
          user?.id || '', // In real implementation, this would be the payer's user ID
          formData.organizationId,
          formData.amount,
          formData.startDate,
          selectedProfile.name
        );
      }

      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        onPaymentScheduleCreated?.();
        setActiveTab('payment-schedules');
      }, 2000);
    }
  };

  const handleCancel = () => {
    setActiveTab('payment-schedules');
  };

  if (!canCreateSchedules) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">You don't have permission to create payment schedules</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="h-8 w-8 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
            <Calendar className="h-5 w-5 text-[#2aa45c]" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-[#045024]">Create Payment Schedule</h2>
            <p className="text-gray-600">Set up recurring payment automation</p>
          </div>
        </div>
        <button
          onClick={handleCancel}
          className="text-gray-500 hover:text-gray-700 transition-colors"
        >
          <X size={24} />
        </button>
      </div>

      {/* Success Message */}
      {showSuccess && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircle className="text-green-500 mr-2" size={20} />
            <span className="text-green-700">Payment schedule created successfully!</span>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={20} />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Form */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md">
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Basic Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      Schedule Name *
                    </label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                          formErrors.name ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Enter schedule name"
                      />
                    </div>
                    {formErrors.name && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="organizationId" className="block text-sm font-medium text-gray-700 mb-2">
                      Organization *
                    </label>
                    <div className="relative">
                      <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                      <select
                        id="organizationId"
                        name="organizationId"
                        value={formData.organizationId}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                          formErrors.organizationId ? 'border-red-300' : 'border-gray-300'
                        }`}
                      >
                        <option value="">Select Organization</option>
                        {organizations.map((org) => (
                          <option key={org.id} value={org.id}>
                            {org.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    {formErrors.organizationId && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.organizationId}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="paymentProfileId" className="block text-sm font-medium text-gray-700 mb-2">
                      Payment Profile *
                    </label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                      <select
                        id="paymentProfileId"
                        name="paymentProfileId"
                        value={formData.paymentProfileId}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                          formErrors.paymentProfileId ? 'border-red-300' : 'border-gray-300'
                        }`}
                      >
                        <option value="">Select Payment Profile</option>
                        {paymentProfiles.map((profile) => (
                          <option key={profile.id} value={profile.id}>
                            {profile.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    {formErrors.paymentProfileId && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.paymentProfileId}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="frequency" className="block text-sm font-medium text-gray-700 mb-2">
                      Frequency *
                    </label>
                    <div className="relative">
                      <RefreshCw className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                      <select
                        id="frequency"
                        name="frequency"
                        value={formData.frequency}
                        onChange={handleInputChange}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                      >
                        <option value="MONTHLY">Monthly</option>
                        <option value="QUARTERLY">Quarterly</option>
                        <option value="ANNUALLY">Annually</option>
                      </select>
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <div className="relative">
                      <FileText className="absolute left-3 top-3 text-gray-400" size={20} />
                      <textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                        placeholder="Enter schedule description"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Amount and Dates */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Amount and Schedule</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                      Amount *
                    </label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                      <input
                        type="number"
                        id="amount"
                        name="amount"
                        value={formData.amount}
                        onChange={handleInputChange}
                        step="0.01"
                        min="0"
                        className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                          formErrors.amount ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="0.00"
                      />
                    </div>
                    {formErrors.amount && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.amount}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-2">
                      Currency *
                    </label>
                    <select
                      id="currency"
                      name="currency"
                      value={formData.currency}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                    >
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                      <option value="GBP">GBP</option>
                      <option value="NGN">NGN</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-2">
                      Start Date *
                    </label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                      <input
                        type="date"
                        id="startDate"
                        name="startDate"
                        value={formData.startDate}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                          formErrors.startDate ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {formErrors.startDate && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.startDate}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-2">
                      End Date (Optional)
                    </label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                      <input
                        type="date"
                        id="endDate"
                        name="endDate"
                        value={formData.endDate}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                          formErrors.endDate ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {formErrors.endDate && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.endDate}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="bg-[#2aa45c] text-white px-6 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Creating...</span>
                    </>
                  ) : (
                    <>
                      <Save size={16} />
                      <span>Create Schedule</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* Preview Panel */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Schedule Preview</h3>
            {nextPaymentDates.length > 0 ? (
              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Clock size={16} />
                  <span>Next {nextPaymentDates.length} payments:</span>
                </div>
                {nextPaymentDates.map((date, index) => (
                  <div
                    key={index}
                    className={`flex items-center justify-between p-2 rounded ${
                      index === 0 ? 'bg-[#2aa45c] bg-opacity-10' : 'bg-gray-50'
                    }`}
                  >
                    <span className="text-sm font-medium">Payment {index + 1}</span>
                    <span className="text-sm text-gray-600">{date}</span>
                  </div>
                ))}
                {formData.amount > 0 && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <div className="text-sm text-blue-800">
                      <strong>Amount per payment:</strong> {formData.amount.toLocaleString()} {formData.currency}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-sm text-gray-500">Set start date and frequency to see preview</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreatePaymentSchedule;
