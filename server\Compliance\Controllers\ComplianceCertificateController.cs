using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Final_E_Receipt.Compliance.Services;
using Final_E_Receipt.Compliance.DTOs;

namespace Final_E_Receipt.Compliance.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ComplianceCertificateController : ControllerBase
    {
        private readonly ComplianceCertificateService _certificateService;
        private readonly ComplianceCertificateFileService _certificateFileService;
        private readonly ILogger<ComplianceCertificateController> _logger;

        public ComplianceCertificateController(
            ComplianceCertificateService certificateService,
            ComplianceCertificateFileService certificateFileService,
            ILogger<ComplianceCertificateController> logger)
        {
            _certificateService = certificateService;
            _certificateFileService = certificateFileService;
            _logger = logger;
        }

        [HttpPost]
        [Authorize(Policy = "RequireJTBAdmin")]
        public async Task<IActionResult> CreateComplianceCertificate([FromBody] CreateComplianceCertificateDTO dto)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                
                var certificateWithFiles = await _certificateFileService.CreateCertificateWithFiles(dto, userId);
                
                if (certificateWithFiles == null)
                    return BadRequest(new { message = "Failed to create compliance certificate" });

                return CreatedAtAction(
                    nameof(GetCertificateWithFiles), 
                    new { id = certificateWithFiles.Certificate.Id }, 
                    certificateWithFiles);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating compliance certificate");
                return StatusCode(500, new { message = "An error occurred while creating the certificate" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetComplianceCertificate(string id)
        {
            try
            {
                var certificate = await _certificateService.GetComplianceCertificateById(id);
                
                if (certificate == null)
                    return NotFound(new { message = "Certificate not found" });

                return Ok(certificate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving certificate {CertificateId}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the certificate" });
            }
        }

        [HttpGet("{id}/with-files")]
        public async Task<IActionResult> GetCertificateWithFiles(string id)
        {
            try
            {
                var certificateWithFiles = await _certificateFileService.GetCertificateWithFiles(id);
                
                if (certificateWithFiles == null)
                    return NotFound(new { message = "Certificate not found" });

                return Ok(certificateWithFiles);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving certificate with files {CertificateId}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the certificate" });
            }
        }

        [HttpGet("organization/{organizationId}")]
        public async Task<IActionResult> GetCertificatesByOrganization(string organizationId, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                var certificates = await _certificateService.GetComplianceCertificatesByOrganization(organizationId, pageNumber, pageSize);
                return Ok(certificates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving certificates for organization {OrganizationId}", organizationId);
                return StatusCode(500, new { message = "An error occurred while retrieving certificates" });
            }
        }

        [HttpPost("search")]
        public async Task<IActionResult> SearchCertificates([FromBody] CertificateSearchDTO searchDto)
        {
            try
            {
                var certificates = await _certificateService.SearchComplianceCertificates(searchDto);
                return Ok(certificates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching certificates");
                return StatusCode(500, new { message = "An error occurred while searching certificates" });
            }
        }

        [HttpPut("{id}/status")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> UpdateCertificateStatus(string id, [FromBody] UpdateCertificateStatusRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                
                var certificate = await _certificateService.UpdateCertificateStatus(id, request.Status, userId);
                
                if (certificate == null)
                    return NotFound(new { message = "Certificate not found" });

                return Ok(certificate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating certificate status {CertificateId}", id);
                return StatusCode(500, new { message = "An error occurred while updating the certificate status" });
            }
        }

        [HttpPost("{id}/revoke")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> RevokeCertificate(string id, [FromBody] RevokeCertificateDTO dto)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                
                var certificate = await _certificateService.RevokeCertificate(id, userId, dto.Reason);
                
                if (certificate == null)
                    return NotFound(new { message = "Certificate not found" });

                return Ok(new { message = "Certificate revoked successfully", certificate });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error revoking certificate {CertificateId}", id);
                return StatusCode(500, new { message = "An error occurred while revoking the certificate" });
            }
        }

        [HttpGet("expiring")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetExpiringCertificates([FromQuery] int daysFromNow = 30)
        {
            try
            {
                var certificates = await _certificateService.GetExpiringCertificates(daysFromNow);
                return Ok(certificates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving expiring certificates");
                return StatusCode(500, new { message = "An error occurred while retrieving expiring certificates" });
            }
        }

        [HttpGet("{id}/download-pdf")]
        public async Task<IActionResult> DownloadCertificatePdf(string id)
        {
            try
            {
                var certificate = await _certificateService.GetComplianceCertificateById(id);
                if (certificate == null)
                    return NotFound(new { message = "Certificate not found" });

                // Check access permissions
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var organizationId = User.FindFirst("OrganizationId")?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                // Allow access if user is admin, finance officer, or belongs to same organization
                if (userRole != "JTB_ADMIN" && userRole != "FINANCE_OFFICER" && userRole != "SENIOR_FINANCE_OFFICER" &&
                    certificate.OrganizationId != organizationId)
                {
                    return Forbid();
                }

                var pdfContent = await _certificateFileService.DownloadCertificatePdf(id);
                var fileName = $"{certificate.CertificateNumber}.pdf";
                
                return File(pdfContent, "application/pdf", fileName);
            }
            catch (FileNotFoundException)
            {
                return NotFound(new { message = "Certificate PDF not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading certificate PDF {CertificateId}", id);
                return StatusCode(500, new { message = "An error occurred while downloading the certificate PDF" });
            }
        }

        [HttpPost("bulk-generate")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> BulkGenerateCertificates([FromBody] BulkCertificateGenerationDTO dto)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var result = new BulkCertificateGenerationResultDTO
                {
                    TotalRequested = dto.OrganizationIds.Count
                };

                foreach (var organizationId in dto.OrganizationIds)
                {
                    try
                    {
                        var createDto = new CreateComplianceCertificateDTO
                        {
                            OrganizationId = organizationId,
                            PaymentProfileId = dto.PaymentProfileId,
                            CertificateType = dto.CertificateType,
                            TotalAmount = 0, // Will be calculated based on payments
                            Currency = "NGN",
                            ValidFrom = dto.ValidFrom,
                            ValidUntil = dto.ValidUntil,
                            Description = dto.Description,
                            Terms = dto.Terms,
                            ComplianceYear = dto.ComplianceYear,
                            CompliancePeriod = dto.CompliancePeriod,
                            RegulatoryBody = dto.RegulatoryBody,
                            LicenseCategory = dto.LicenseCategory
                        };

                        var certificateWithFiles = await _certificateFileService.CreateCertificateWithFiles(createDto, userId);
                        if (certificateWithFiles != null)
                        {
                            result.SuccessfullyCreated++;
                            result.CreatedCertificates.Add(certificateWithFiles.Certificate);
                        }
                        else
                        {
                            result.Failed++;
                            result.Errors.Add($"Failed to create certificate for organization {organizationId}");
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Failed++;
                        result.Errors.Add($"Error creating certificate for organization {organizationId}: {ex.Message}");
                    }
                }

                result.IsSuccess = result.SuccessfullyCreated > 0;
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in bulk certificate generation");
                return StatusCode(500, new { message = "An error occurred during bulk certificate generation" });
            }
        }
    }

    public class UpdateCertificateStatusRequest
    {
        public string Status { get; set; }
    }
}
