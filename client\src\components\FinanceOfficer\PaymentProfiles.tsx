import React, { useState } from 'react';
import { Plus, Eye, Edit, Download, X, Users, Receipt, Mail, FileText, CheckCircle, Clock, AlertCircle } from 'lucide-react';

interface PaymentProfile {
  id: number;
  name: string;
  description: string;
  type: string;
  year: string;
  dueDate: string;
  amount: string;
  payers: number;
  status: 'Active' | 'Pending' | 'Inactive';
}

interface Payer {
  id: number;
  name: string;
  email: string;
  amountPaid: string;
  paymentStatus: 'Paid' | 'Pending' | 'Overdue';
  paymentDate?: string;
}

interface PaymentReceipt {
  id: number;
  payerId: number;
  payerName: string;
  amount: string;
  paymentDate: string;
  receiptNumber: string;
  status: 'Active' | 'Historical';
}

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md';
  disabled?: boolean;
}

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
}

interface PaymentFormData {
  name: string;
  description: string;
  type: string;
  year: string;
  dueDate: string;
  amount: string;
  status: 'Active' | 'Pending' | 'Inactive';
}

const ActionButton: React.FC<ActionButtonProps> = ({ 
  children, 
  onClick, 
  variant = "primary", 
  size = "md", 
  disabled = false 
}) => {
  const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: disabled 
      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
      : "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: disabled
      ? "bg-gray-200 text-gray-400 cursor-not-allowed"
      : "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: disabled
      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
      : "bg-red-500 hover:bg-red-600 text-white"
  };

  return (
    <button
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]}`}
    >
      {children}
    </button>
  );
};

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children, size = 'md' }) => {
  if (!isOpen) return null;

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-2xl',
    lg: 'max-w-4xl'
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className={`bg-white rounded-lg shadow-xl ${sizeClasses[size]} w-full max-h-[90vh] overflow-y-auto`}>
        <div className="flex justify-between items-center p-6 border-b">
          <h3 className="text-lg font-semibold text-[#045024]">{title}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={20} />
          </button>
        </div>
        {children}
      </div>
    </div>
  );
};

const PaymentProfiles: React.FC = () => {
  const [paymentProfiles, setPaymentProfiles] = useState<PaymentProfile[]>([
    { 
      id: 1, 
      name: "2025 Annual License", 
      description: "Annual license fee for professional services",
      type: "Annual License Fee", 
      year: "2025", 
      dueDate: "2025-03-31", 
      amount: "₦2,500,000", 
      payers: 25, 
      status: "Active" 
    },
    { 
      id: 2, 
      name: "Q1 Compliance", 
      description: "Quarterly compliance and regulatory fees",
      type: "Compliance Fee", 
      year: "2025", 
      dueDate: "2025-04-15", 
      amount: "₦1,200,000", 
      payers: 18, 
      status: "Active" 
    },
    { 
      id: 3, 
      name: "New Registration", 
      description: "One-time registration fee for new members",
      type: "Registration Fee", 
      year: "2025", 
      dueDate: "2025-05-01", 
      amount: "₦500,000", 
      payers: 8, 
      status: "Pending" 
    }
  ]);

  // Mock data for payers
  const mockPayers: { [key: number]: Payer[] } = {
    1: [
      { id: 1, name: "Adebayo Industries Ltd", email: "<EMAIL>", amountPaid: "₦2,500,000", paymentStatus: "Paid", paymentDate: "2025-02-15" },
      { id: 2, name: "Lagos Tech Solutions", email: "<EMAIL>", amountPaid: "₦0", paymentStatus: "Pending" },
      { id: 3, name: "Nigeria Commerce Bank", email: "<EMAIL>", amountPaid: "₦2,500,000", paymentStatus: "Paid", paymentDate: "2025-01-20" }
    ],
    2: [
      { id: 4, name: "Federal Ministry of Trade", email: "<EMAIL>", amountPaid: "₦1,200,000", paymentStatus: "Paid", paymentDate: "2025-03-10" },
      { id: 5, name: "Abuja Business Center", email: "<EMAIL>", amountPaid: "₦0", paymentStatus: "Overdue" }
    ],
    3: [
      { id: 6, name: "New Venture Ltd", email: "<EMAIL>", amountPaid: "₦0", paymentStatus: "Pending" }
    ]
  };

  // Mock data for receipts
  const mockReceipts: { [key: number]: PaymentReceipt[] } = {
    1: [
      { id: 1, payerId: 1, payerName: "Adebayo Industries Ltd", amount: "₦2,500,000", paymentDate: "2025-02-15", receiptNumber: "RC-2025-001", status: "Active" },
      { id: 2, payerId: 3, payerName: "Nigeria Commerce Bank", amount: "₦2,500,000", paymentDate: "2025-01-20", receiptNumber: "RC-2025-002", status: "Active" }
    ],
    2: [
      { id: 3, payerId: 4, payerName: "Federal Ministry of Trade", amount: "₦1,200,000", paymentDate: "2025-03-10", receiptNumber: "RC-2025-003", status: "Active" }
    ],
    3: []
  };

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isPayersModalOpen, setIsPayersModalOpen] = useState(false);
  const [isReceiptsModalOpen, setIsReceiptsModalOpen] = useState(false);
  const [editingProfile, setEditingProfile] = useState<PaymentProfile | null>(null);
  const [viewingProfileId, setViewingProfileId] = useState<number | null>(null);
  const [emailNotificationSent, setEmailNotificationSent] = useState(false);
  const [formData, setFormData] = useState<PaymentFormData>({
    name: '',
    description: '',
    type: '',
    year: new Date().getFullYear().toString(),
    dueDate: '',
    amount: '',
    status: 'Active'
  });

  const handleCreateProfile = () => {
    const newProfile: PaymentProfile = {
      id: Math.max(...paymentProfiles.map(p => p.id)) + 1,
      ...formData,
      payers: 0
    };
    setPaymentProfiles([...paymentProfiles, newProfile]);
    setIsCreateModalOpen(false);
    resetForm();
  };

  const handleEditProfile = () => {
    if (!editingProfile) return;
    
    setPaymentProfiles(profiles =>
      profiles.map(profile =>
        profile.id === editingProfile.id
          ? { ...profile, ...formData }
          : profile
      )
    );
    
    // Send email notification
    setEmailNotificationSent(true);
    setTimeout(() => setEmailNotificationSent(false), 3000);
    
    setIsEditModalOpen(false);
    setEditingProfile(null);
    resetForm();
  };

  const openEditModal = (profile: PaymentProfile) => {
    setEditingProfile(profile);
    setFormData({
      name: profile.name,
      description: profile.description,
      type: profile.type,
      year: profile.year,
      dueDate: profile.dueDate,
      amount: profile.amount,
      status: profile.status
    });
    setIsEditModalOpen(true);
  };

  const openPayersModal = (profileId: number) => {
    setViewingProfileId(profileId);
    setIsPayersModalOpen(true);
  };

  const openReceiptsModal = (profileId: number) => {
    setViewingProfileId(profileId);
    setIsReceiptsModalOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      type: '',
      year: new Date().getFullYear().toString(),
      dueDate: '',
      amount: '',
      status: 'Active'
    });
  };

  const handleCloseModals = () => {
    setIsCreateModalOpen(false);
    setIsEditModalOpen(false);
    setIsPayersModalOpen(false);
    setIsReceiptsModalOpen(false);
    setEditingProfile(null);
    setViewingProfileId(null);
    resetForm();
  };

  const downloadPaymentSchedule = (profileId: number) => {
    // Mock download functionality
    alert(`Downloading payment schedule for profile ${profileId}...`);
  };

  const paymentTypes = [
    'Annual License Fee',
    'Compliance Fee',
    'Registration Fee',
    'Processing Fee',
    'Maintenance Fee',
    'Other'
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Paid':
        return <CheckCircle size={16} className="text-green-600" />;
      case 'Pending':
        return <Clock size={16} className="text-yellow-600" />;
      case 'Overdue':
        return <AlertCircle size={16} className="text-red-600" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Email Notification Banner */}
      {emailNotificationSent && (
        <div className="bg-green-100 border border-green-200 rounded-lg p-4 flex items-center gap-2">
          <Mail size={16} className="text-green-600" />
          <span className="text-green-800">Email notifications sent to all payers about the profile updates!</span>
        </div>
      )}

      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-[#045024]">Payment Profiles</h2>
        <ActionButton onClick={() => setIsCreateModalOpen(true)}>
          <Plus size={16} />
          Create Payment Profile
        </ActionButton>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {paymentProfiles.map(profile => (
          <div key={profile.id} className="bg-white rounded-lg shadow-md p-6 border-l-4 border-[#2aa45c]">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <h3 className="font-semibold text-lg text-[#045024] mb-1">{profile.name}</h3>
                <p className="text-sm text-gray-600 line-clamp-2">{profile.description}</p>
              </div>
              <span className={`px-2 py-1 text-xs font-semibold rounded-full ml-2 flex-shrink-0 ${
                profile.status === 'Active' 
                  ? 'bg-green-100 text-green-800' 
                  : profile.status === 'Pending'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {profile.status}
              </span>
            </div>
            
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Type:</span>
                <span className="font-medium">{profile.type}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Year:</span>
                <span className="font-medium">{profile.year}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Due Date:</span>
                <span className="font-medium">{profile.dueDate}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Amount:</span>
                <span className="font-bold text-[#2aa45c]">{profile.amount}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Payers:</span>
                <span className="font-medium">{profile.payers}</span>
              </div>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <ActionButton 
                variant="secondary" 
                size="sm"
                onClick={() => openPayersModal(profile.id)}
              >
                <Users size={14} />
                View Payers
              </ActionButton>
              <ActionButton 
                variant="secondary" 
                size="sm"
                onClick={() => openEditModal(profile)}
              >
                <Edit size={14} />
                Edit
              </ActionButton>
              <ActionButton 
                variant="secondary" 
                size="sm"
                onClick={() => downloadPaymentSchedule(profile.id)}
              >
                <Download size={14} />
                Schedule
              </ActionButton>
              <ActionButton 
                variant="secondary" 
                size="sm"
                onClick={() => openReceiptsModal(profile.id)}
              >
                <Receipt size={14} />
                Receipts
              </ActionButton>
            </div>
          </div>
        ))}
      </div>

      {/* Create Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={handleCloseModals}
        title="Create Payment Profile"
      >
        <div className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
              placeholder="Enter payment profile name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
              rows={3}
              placeholder="Enter description"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Payment Type *
            </label>
            <select
              value={formData.type}
              onChange={(e) => setFormData({ ...formData, type: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
            >
              <option value="">Select payment type</option>
              {paymentTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Year *
              </label>
              <input
                type="text"
                value={formData.year}
                onChange={(e) => setFormData({ ...formData, year: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Amount *
              </label>
              <input
                type="text"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                placeholder="₦0.00"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Due Date *
            </label>
            <input
              type="date"
              value={formData.dueDate}
              onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <ActionButton
              onClick={handleCreateProfile}
              disabled={!formData.name || !formData.description || !formData.type || !formData.amount || !formData.dueDate}
            >
              Create Profile
            </ActionButton>
            <ActionButton variant="secondary" onClick={handleCloseModals}>
              Cancel
            </ActionButton>
          </div>
        </div>
      </Modal>

      {/* Edit Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={handleCloseModals}
        title="Edit Payment Profile"
      >
        <div className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Payment Type *
            </label>
            <select
              value={formData.type}
              onChange={(e) => setFormData({ ...formData, type: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
            >
              {paymentTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Year *
              </label>
              <input
                type="text"
                value={formData.year}
                onChange={(e) => setFormData({ ...formData, year: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Amount *
              </label>
              <input
                type="text"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Due Date *
            </label>
            <input
              type="date"
              value={formData.dueDate}
              onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as PaymentProfile['status'] })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
            >
              <option value="Active">Active</option>
              <option value="Pending">Pending</option>
              <option value="Inactive">Inactive</option>
            </select>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Mail size={16} className="text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Email Notification</span>
            </div>
            <p className="text-sm text-blue-700">
              Saving changes will automatically send email notifications to all payers under this payment profile about the updates.
            </p>
          </div>

          <div className="flex gap-3 pt-4">
            <ActionButton
              onClick={handleEditProfile}
              disabled={!formData.name || !formData.description || !formData.type || !formData.amount || !formData.dueDate}
            >
              Save Changes & Notify Payers
            </ActionButton>
            <ActionButton variant="secondary" onClick={handleCloseModals}>
              Cancel
            </ActionButton>
          </div>
        </div>
      </Modal>

      {/* Payers Modal */}
      <Modal
        isOpen={isPayersModalOpen}
        onClose={handleCloseModals}
        title={`Payers - ${paymentProfiles.find(p => p.id === viewingProfileId)?.name}`}
        size="lg"
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h4 className="font-semibold text-[#045024]">Payment Status Overview</h4>
            <ActionButton 
              variant="secondary" 
              size="sm"
              onClick={() => viewingProfileId && downloadPaymentSchedule(viewingProfileId)}
            >
              <Download size={14} />
              Download Schedule
            </ActionButton>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full border border-gray-200 rounded-lg">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Payer Name</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Email</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Amount Paid</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Status</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Payment Date</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {viewingProfileId && mockPayers[viewingProfileId]?.map(payer => (
                  <tr key={payer.id} className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm font-medium text-gray-900">{payer.name}</td>
                    <td className="px-4 py-3 text-sm text-gray-600">{payer.email}</td>
                    <td className="px-4 py-3 text-sm font-medium text-[#2aa45c]">{payer.amountPaid}</td>
                    <td className="px-4 py-3 text-sm">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(payer.paymentStatus)}
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                          payer.paymentStatus === 'Paid' 
                            ? 'bg-green-100 text-green-800'
                            : payer.paymentStatus === 'Pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {payer.paymentStatus}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-600">
                      {payer.paymentDate || 'N/A'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </Modal>

      {/* Receipts Modal */}
      <Modal
        isOpen={isReceiptsModalOpen}
        onClose={handleCloseModals}
        title={`Receipts & Certificates - ${paymentProfiles.find(p => p.id === viewingProfileId)?.name}`}
        size="lg"
      >
        <div className="p-6">
          <div className="mb-4">
            <h4 className="font-semibold text-[#045024] mb-2">Generated Receipts & Compliance Certificates</h4>
            <p className="text-sm text-gray-600">View and download all receipts and certificates of compliance for this payment profile.</p>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full border border-gray-200 rounded-lg">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Receipt #</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Payer Name</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Amount</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Payment Date</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Status</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {viewingProfileId && mockReceipts[viewingProfileId]?.length > 0 ? (
                  mockReceipts[viewingProfileId].map(receipt => (
                    <tr key={receipt.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm font-medium text-gray-900">{receipt.receiptNumber}</td>
                      <td className="px-4 py-3 text-sm text-gray-900">{receipt.payerName}</td>
                      <td className="px-4 py-3 text-sm font-medium text-[#2aa45c]">{receipt.amount}</td>
                      <td className="px-4 py-3 text-sm text-gray-600">{receipt.paymentDate}</td>
                      <td className="px-4 py-3 text-sm">
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                          receipt.status === 'Active' 
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {receipt.status}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-sm">
                        <div className="flex gap-2">
                          <button className="text-[#2aa45c] hover:text-[#045024] transition-colors">
                            <Eye size={16} />
                          </button>
                          <button className="text-[#2aa45c] hover:text-[#045024] transition-colors">
                            <Download size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="px-4 py-8 text-center text-gray-500">
                      <div className="flex flex-col items-center gap-2">
                        <FileText size={24} className="text-gray-400" />
                        <span>No receipts or certificates generated yet</span>
                        <span className="text-sm">Receipts will appear here once payments are made</span>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          
          {viewingProfileId && mockReceipts[viewingProfileId]?.length > 0 && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Receipt size={16} className="text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Bulk Actions</span>
              </div>
              <div className="flex gap-2">
                <ActionButton variant="secondary" size="sm">
                  <Download size={14} />
                  Download All Receipts
                </ActionButton>
                <ActionButton variant="secondary" size="sm">
                  <FileText size={14} />
                  Generate Compliance Report
                </ActionButton>
              </div>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default PaymentProfiles;