# 🧪 Payment Schedule Email Integration - Testing Guide

## 🎯 **Testing Overview**

This guide helps you test the payment schedule email integration to ensure notifications are sent correctly when schedules are created or updated.

## 📋 **Prerequisites**

### **1. SMTP Configuration**
Ensure you have set up SMTP configuration in the database:
```sql
-- Check if email configuration exists
SELECT * FROM EmailConfigurations WHERE IsDefault = 1;
```

### **2. Test Data Setup**
You'll need:
- ✅ **Organization** with ID
- ✅ **Payment Profile** with ID  
- ✅ **PAYER users** in the organization
- ✅ **Valid email addresses** for testing

## 🧪 **Test Scenarios**

### **Test 1: Payment Schedule Creation**

#### **API Call:**
```bash
POST /api/PaymentSchedule
Content-Type: application/json
Authorization: Bearer <your-token>

{
  "paymentProfileId": "your-profile-id",
  "organizationId": "your-org-id", 
  "amount": 5000.00,
  "currency": "NGN",
  "dueDate": "2024-02-15T00:00:00Z",
  "status": "PENDING"
}
```

#### **Expected Results:**
1. ✅ **Payment schedule created** in database
2. ✅ **Background email task** started
3. ✅ **Emails sent** to all PAYER users in organization
4. ✅ **Success logged** in application logs

#### **Check Application Logs:**
```
INFO: Payment schedule created notifications sent for schedule {ScheduleId} to {UserCount} users
```

### **Test 2: Payment Schedule Status Update**

#### **API Call:**
```bash
PUT /api/PaymentSchedule/{schedule-id}/status
Content-Type: application/json
Authorization: Bearer <your-token>

{
  "status": "PAID",
  "paymentId": "payment-123",
  "updatedBy": "user-id"
}
```

#### **Expected Results:**
1. ✅ **Schedule status updated** in database
2. ✅ **Update notification emails** sent
3. ✅ **Reason included** in email ("Status updated to PAID")
4. ✅ **Success logged** in application logs

### **Test 3: Error Handling**

#### **Test with Invalid Organization:**
```json
{
  "paymentProfileId": "valid-profile-id",
  "organizationId": "invalid-org-id",
  "amount": 1000.00,
  "currency": "NGN", 
  "dueDate": "2024-02-15T00:00:00Z"
}
```

#### **Expected Results:**
1. ✅ **Schedule creation succeeds** (if profile exists)
2. ✅ **Email sending fails gracefully** (no users found)
3. ✅ **Error logged** but operation continues
4. ✅ **No system crash** or failure

## 📧 **Email Verification**

### **Check Email Content:**
The emails should contain:
- ✅ **Subject**: Payment schedule notification
- ✅ **Payment Profile Name**: (e.g., "Monthly Membership Dues")
- ✅ **Amount**: ₦5,000.00
- ✅ **Due Date**: February 15, 2024
- ✅ **Action**: CREATED or UPDATED
- ✅ **Professional formatting**

### **Check Email Recipients:**
- ✅ **Only PAYER users** in the organization receive emails
- ✅ **All PAYER users** in organization are notified
- ✅ **No duplicate emails** sent to same user

## 🔍 **Troubleshooting**

### **No Emails Sent:**

#### **Check 1: SMTP Configuration**
```sql
SELECT * FROM EmailConfigurations WHERE OrganizationId = 'your-org-id' OR OrganizationId = 'SYSTEM';
```

#### **Check 2: PAYER Users Exist**
```sql
SELECT * FROM Users WHERE OrganizationId = 'your-org-id' AND Role = 'PAYER';
```

#### **Check 3: Application Logs**
Look for error messages:
```
ERROR: Error getting payer users for organization {OrganizationId}
ERROR: Error sending payment schedule created notifications for schedule {ScheduleId}
```

### **Emails Go to Spam:**
- ✅ **Check sender domain** reputation
- ✅ **Use organization SMTP** server
- ✅ **Set up SPF/DKIM** records

### **Performance Issues:**
- ✅ **Monitor email sending time** for large organizations
- ✅ **Consider bulk email** optimizations
- ✅ **Check database query** performance

## 📊 **Monitoring**

### **Key Metrics to Track:**
- ✅ **Email delivery success rate**
- ✅ **Time to send notifications**
- ✅ **Number of recipients per schedule**
- ✅ **Error frequency and types**

### **Log Analysis:**
```bash
# Search for successful notifications
grep "Payment schedule created notifications sent" application.log

# Search for email failures  
grep "Failed to send notification for payment schedule" application.log

# Search for database errors
grep "Error getting payer users" application.log
```

## 🎯 **Success Criteria**

### **✅ Integration is Working When:**
1. **Payment schedules created** → Emails sent automatically
2. **Schedule status updated** → Update emails sent
3. **Email failures logged** but don't break operations
4. **Only PAYER users** receive notifications
5. **Professional email formatting** maintained
6. **Performance acceptable** for your organization size

### **🔧 Next Steps After Testing:**
1. **Monitor production** email delivery
2. **Set up email templates** for better formatting
3. **Configure email preferences** per user
4. **Add email analytics** tracking
5. **Consider SMS notifications** for critical updates

## ✅ **Testing Complete**

Once all tests pass, your payment schedule email integration is ready for production use! 🚀

**The system will automatically:**
- ✅ **Notify payers** when schedules are created
- ✅ **Send updates** when schedules change
- ✅ **Handle errors gracefully** without breaking core functionality
- ✅ **Log all activities** for monitoring and troubleshooting
