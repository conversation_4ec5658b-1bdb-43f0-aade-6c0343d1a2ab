import React, { useState } from 'react';
import PaymentProofUpload from '../common/PaymentProofUpload';
import FileUpload from '../common/FileUpload';
import { FileUploadResponse } from '../../services/fileUploadService';

const FileUploadTest: React.FC = () => {
  const [selectedPaymentId, setSelectedPaymentId] = useState('test-payment-123');
  const [uploadResults, setUploadResults] = useState<FileUploadResponse[]>([]);

  const handleProofUploaded = (file: FileUploadResponse) => {
    console.log('Payment proof uploaded:', file);
    setUploadResults(prev => [...prev, file]);
  };

  const handleProofValidated = (fileId: string, isValid: boolean) => {
    console.log('Payment proof validated:', fileId, isValid);
  };

  const handleGenericUpload = (file: FileUploadResponse) => {
    console.log('Generic file uploaded:', file);
    setUploadResults(prev => [...prev, file]);
  };

  const handleUploadError = (error: string) => {
    console.error('Upload error:', error);
    alert(`Upload error: ${error}`);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="bg-white shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">File Upload System Test</h1>
        
        <div className="space-y-6">
          {/* Payment ID Input */}
          <div>
            <label htmlFor="paymentId" className="block text-sm font-medium text-gray-700 mb-2">
              Payment ID (for testing)
            </label>
            <input
              type="text"
              id="paymentId"
              value={selectedPaymentId}
              onChange={(e) => setSelectedPaymentId(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter payment ID"
            />
          </div>

          {/* Payment Proof Upload Component */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Proof Upload</h2>
            <div className="border border-gray-200 rounded-lg p-4">
              <PaymentProofUpload
                paymentId={selectedPaymentId}
                canUpload={true}
                canValidate={true}
                canDelete={true}
                onProofUploaded={handleProofUploaded}
                onProofValidated={handleProofValidated}
              />
            </div>
          </div>

          {/* Generic File Upload Component */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Generic File Upload</h2>
            <div className="border border-gray-200 rounded-lg p-4">
              <FileUpload
                onUploadSuccess={handleGenericUpload}
                onUploadError={handleUploadError}
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                maxSize={10}
                multiple={true}
              />
            </div>
          </div>

          {/* Upload Results */}
          {uploadResults.length > 0 && (
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Upload Results</h2>
              <div className="bg-gray-50 rounded-lg p-4">
                <pre className="text-sm text-gray-800 whitespace-pre-wrap">
                  {JSON.stringify(uploadResults, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-md font-medium text-blue-900 mb-2">Testing Instructions</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Make sure the backend server is running on localhost:5000</li>
              <li>• The database should have the FileUploads table created</li>
              <li>• Try uploading PDF, JPG, PNG, DOC, or DOCX files</li>
              <li>• Files larger than 10MB should be rejected</li>
              <li>• Invalid file types should be rejected</li>
              <li>• Check the browser console for detailed logs</li>
              <li>• Uploaded files will be stored in the server's 'uploads' directory</li>
            </ul>
          </div>

          {/* API Endpoints */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="text-md font-medium text-green-900 mb-2">Available API Endpoints</h3>
            <ul className="text-sm text-green-800 space-y-1 font-mono">
              <li>• POST /api/payments/{'{paymentId}'}/proof/upload</li>
              <li>• GET /api/payments/{'{paymentId}'}/proof</li>
              <li>• GET /api/payments/{'{paymentId}'}/proof/{'{fileId}'}/download</li>
              <li>• DELETE /api/payments/{'{paymentId}'}/proof/{'{fileId}'}</li>
              <li>• POST /api/payments/{'{paymentId}'}/proof/{'{fileId}'}/validate</li>
              <li>• POST /api/file/upload</li>
              <li>• GET /api/file/entity/{'{entityType}'}/{'{entityId}'}</li>
              <li>• GET /api/file/download/{'{fileId}'}</li>
              <li>• DELETE /api/file/{'{fileId}'}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileUploadTest;
