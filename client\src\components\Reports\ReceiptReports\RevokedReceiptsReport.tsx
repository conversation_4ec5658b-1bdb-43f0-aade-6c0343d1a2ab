import React, { useState, useEffect } from 'react';
import { Ban, AlertTriangle, User, Calendar, RefreshCw, FileText } from 'lucide-react';
import { usePaymentReportingApi, useOrganizationApi } from '../../../hooks/api';
import ReportFilters, { type FilterOptions } from '../Common/ReportFilters';
import ReportTable, { type TableColumn } from '../Common/ReportTable';
import ReportSummaryCard from '../Common/ReportSummaryCard';
import ExportButtons from '../Common/ExportButtons';

interface RevokedReceipt {
  receiptId: string;
  receiptNumber: string;
  organizationId: string;
  organizationName: string;
  paymentId: string;
  paymentReference: string;
  amount: number;
  currency: string;
  issueDate: string;
  revokedDate: string;
  revokedBy: string;
  revokeReason: string;
  originalCreatedBy: string;
  daysBetweenIssueAndRevoke: number;
  payerName: string;
  payerEmail: string;
}

interface RevokedReceiptsSummary {
  totalRevoked: number;
  totalAmount: number;
  averageDaysToRevoke: number;
  mostCommonReason: string;
  revokeReasons: {
    [reason: string]: number;
  };
  monthlyTrend: {
    month: string;
    count: number;
  }[];
}

const RevokedReceiptsReport: React.FC = () => {
  const { getRevokedReceiptsReport, exportRevokedReceiptsReport, loading } = usePaymentReportingApi();
  const { getAllOrganizations } = useOrganizationApi();
  
  const [filters, setFilters] = useState<FilterOptions>({});
  const [receiptData, setReceiptData] = useState<RevokedReceipt[]>([]);
  const [summary, setSummary] = useState<RevokedReceiptsSummary | null>(null);
  const [organizations, setOrganizations] = useState<Array<{ id: string; name: string }>>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    pageSize: 50,
    totalCount: 0,
  });
  const [sortBy, setSortBy] = useState<string>('revokedDate');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    loadOrganizations();
    loadRevokedReceipts();
  }, [filters, pagination.currentPage, sortBy, sortDirection]);

  const loadOrganizations = async () => {
    const orgs = await getAllOrganizations();
    if (orgs) {
      setOrganizations(orgs.map(org => ({ id: org.id, name: org.name })));
    }
  };

  const loadRevokedReceipts = async () => {
    const result = await getRevokedReceiptsReport({
      ...filters,
      sortBy,
      sortDirection,
      pageNumber: pagination.currentPage,
      pageSize: pagination.pageSize,
    });

    if (result) {
      setReceiptData(result.data || []);
      setSummary(result.summary || null);
      setPagination(prev => ({
        ...prev,
        totalPages: result.totalPages || 1,
        totalCount: result.totalCount || 0,
      }));
    }
  };

  const handleSort = (column: string, direction: 'asc' | 'desc') => {
    setSortBy(column);
    setSortDirection(direction);
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  const handleExportCSV = async () => {
    await exportRevokedReceiptsReport('csv', filters);
  };

  const handleExportExcel = async () => {
    await exportRevokedReceiptsReport('excel', filters);
  };

  const formatCurrency = (amount: number, currency: string = 'NGN') => {
    return `${currency === 'NGN' ? '₦' : currency} ${amount.toLocaleString()}`;
  };

  const formatDays = (days: number) => {
    if (days === 0) return 'Same day';
    if (days === 1) return '1 day';
    return `${days} days`;
  };

  const getRevokeReasonBadge = (reason: string) => {
    const reasonColors: { [key: string]: string } = {
      'DUPLICATE': 'bg-yellow-100 text-yellow-800',
      'ERROR': 'bg-red-100 text-red-800',
      'FRAUD': 'bg-red-200 text-red-900',
      'INCORRECT_AMOUNT': 'bg-orange-100 text-orange-800',
      'POLICY_VIOLATION': 'bg-purple-100 text-purple-800',
      'OTHER': 'bg-gray-100 text-gray-800',
    };

    const colorClass = reasonColors[reason] || reasonColors['OTHER'];

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
        {reason.replace('_', ' ')}
      </span>
    );
  };

  const columns: TableColumn[] = [
    {
      key: 'receiptNumber',
      label: 'Receipt Number',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: 'organizationName',
      label: 'Organization',
      sortable: true,
    },
    {
      key: 'payerName',
      label: 'Payer',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm text-gray-500">{row.payerEmail}</div>
        </div>
      ),
    },
    {
      key: 'amount',
      label: 'Amount',
      sortable: true,
      render: (value, row) => (
        <span className="font-medium">{formatCurrency(value, row.currency)}</span>
      ),
    },
    {
      key: 'issueDate',
      label: 'Issue Date',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'revokedDate',
      label: 'Revoked Date',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'daysBetweenIssueAndRevoke',
      label: 'Days to Revoke',
      sortable: true,
      render: (value) => (
        <span className={`font-medium ${value <= 1 ? 'text-red-600' : value <= 7 ? 'text-orange-600' : 'text-gray-600'}`}>
          {formatDays(value)}
        </span>
      ),
    },
    {
      key: 'revokeReason',
      label: 'Reason',
      sortable: true,
      render: (value) => getRevokeReasonBadge(value),
    },
    {
      key: 'revokedBy',
      label: 'Revoked By',
      sortable: true,
    },
  ];

  const reasonOptions = [
    { value: 'DUPLICATE', label: 'Duplicate' },
    { value: 'ERROR', label: 'Error' },
    { value: 'FRAUD', label: 'Fraud' },
    { value: 'INCORRECT_AMOUNT', label: 'Incorrect Amount' },
    { value: 'POLICY_VIOLATION', label: 'Policy Violation' },
    { value: 'OTHER', label: 'Other' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 rounded-lg bg-red-100 flex items-center justify-center">
            <Ban className="h-5 w-5 text-red-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Revoked Receipts Report</h2>
            <p className="text-gray-600">Audit trail for all revoked receipts with detailed analysis</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={loadRevokedReceipts}
            disabled={loading}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2 disabled:opacity-50"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
          <ExportButtons
            onExportCSV={handleExportCSV}
            onExportExcel={handleExportExcel}
            disabled={loading}
            variant="dropdown"
          />
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <ReportSummaryCard
            title="Total Revoked"
            value={summary.totalRevoked}
            icon={Ban}
            color="red"
            loading={loading}
          />
          <ReportSummaryCard
            title="Total Amount"
            value={`₦${summary.totalAmount.toLocaleString()}`}
            icon={FileText}
            color="red"
            loading={loading}
          />
          <ReportSummaryCard
            title="Avg Days to Revoke"
            value={`${summary.averageDaysToRevoke.toFixed(1)} days`}
            icon={Calendar}
            color="orange"
            loading={loading}
          />
          <ReportSummaryCard
            title="Most Common Reason"
            value={summary.mostCommonReason.replace('_', ' ')}
            icon={AlertTriangle}
            color="yellow"
            loading={loading}
          />
        </div>
      )}

      {/* Revoke Reasons Breakdown */}
      {summary && Object.keys(summary.revokeReasons).length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Revoke Reasons Breakdown</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {Object.entries(summary.revokeReasons).map(([reason, count]) => (
              <div key={reason} className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {count}
                </div>
                <div className="text-sm text-gray-500">{reason.replace('_', ' ')}</div>
                <div className="text-xs text-gray-400">
                  {((count / summary.totalRevoked) * 100).toFixed(1)}%
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Filters */}
      <ReportFilters
        filters={filters}
        onFiltersChange={setFilters}
        organizations={organizations}
        categoryOptions={reasonOptions}
        showCategoryFilter={true}
        showDateFilters={true}
        showPayerFilter={true}
      />

      {/* Data Table */}
      <ReportTable
        columns={columns}
        data={receiptData}
        loading={loading}
        sortBy={sortBy}
        sortDirection={sortDirection}
        onSort={handleSort}
        pagination={{
          ...pagination,
          onPageChange: handlePageChange,
        }}
        emptyMessage="No revoked receipts found for the selected filters"
      />
    </div>
  );
};

export default RevokedReceiptsReport;
