// Payment Schedule Import Service
const API_BASE_URL = 'http://localhost:5000/api';

export interface PaymentScheduleImportResult {
  isSuccess: boolean;
  errorMessage?: string;
  uploadedFileId?: string;
  fileName?: string;
  totalRows: number;
  successfulImports: number;
  failedImports: number;
  errors: string[];
  importedSchedules: PaymentSchedule[];
}

export interface PaymentSchedule {
  id: string;
  paymentProfileId: string;
  organizationId: string;
  amount: number;
  currency: string;
  dueDate: string;
  status: string;
  paymentId?: string;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  profileName?: string;
  organizationName?: string;
}

class PaymentScheduleImportService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('authToken');
    return {
      'Authorization': `Bearer ${token}`,
    };
  }

  async importFromExcel(paymentProfileId: string, excelFile: File): Promise<PaymentScheduleImportResult> {
    const formData = new FormData();
    formData.append('excelFile', excelFile);

    const response = await fetch(`${API_BASE_URL}/paymentschedule/import/${paymentProfileId}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to import payment schedules');
    }

    return response.json();
  }

  async downloadTemplate(): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/paymentschedule/template`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to download template');
    }

    return response.blob();
  }

  async downloadTemplateFile(): Promise<void> {
    try {
      const blob = await this.downloadTemplate();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `PaymentScheduleTemplate_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading template:', error);
      throw error;
    }
  }

  validateExcelFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    if (!file.name.toLowerCase().endsWith('.xlsx')) {
      return {
        isValid: false,
        error: 'Only .xlsx files are supported'
      };
    }

    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size must be less than 5MB'
      };
    }

    return { isValid: true };
  }

  formatImportResult(result: PaymentScheduleImportResult): string {
    if (!result.isSuccess) {
      return `Import failed: ${result.errorMessage}`;
    }

    const summary = [
      `Import completed successfully!`,
      `Total rows processed: ${result.totalRows}`,
      `Successful imports: ${result.successfulImports}`,
      `Failed imports: ${result.failedImports}`
    ];

    if (result.errors.length > 0) {
      summary.push(`\nErrors:`);
      result.errors.forEach(error => summary.push(`• ${error}`));
    }

    return summary.join('\n');
  }

  generateSampleData(): any[] {
    return [
      {
        OrganizationId: 'ORG001',
        OrganizationName: 'Sample Company Ltd',
        Amount: 50000.00,
        DueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
        Currency: 'NGN',
        Description: 'Annual license fee'
      },
      {
        OrganizationId: 'ORG002',
        OrganizationName: 'Another Corp',
        Amount: 75000.00,
        DueDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 45 days from now
        Currency: 'NGN',
        Description: 'Quarterly compliance fee'
      }
    ];
  }

  exportToCSV(data: any[], filename: string = 'payment_schedules.csv'): void {
    if (data.length === 0) return;

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          // Escape commas and quotes in CSV
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }

  // Helper method to format file size
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Helper method to format currency
  formatCurrency(amount: number, currency: string = 'NGN'): string {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  }

  // Helper method to format date
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  // Helper method to get status color
  getStatusColor(status: string): string {
    switch (status.toUpperCase()) {
      case 'PENDING': return 'text-yellow-600 bg-yellow-100';
      case 'REPORTED': return 'text-blue-600 bg-blue-100';
      case 'CONFIRMED': return 'text-green-600 bg-green-100';
      case 'PAID': return 'text-green-800 bg-green-200';
      default: return 'text-gray-600 bg-gray-100';
    }
  }
}

export const paymentScheduleImportService = new PaymentScheduleImportService();
