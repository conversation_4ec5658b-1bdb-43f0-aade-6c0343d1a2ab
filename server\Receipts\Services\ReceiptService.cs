using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Final_E_Receipt.Receipts.Models;
using Final_E_Receipt.Services;
using Final_E_Receipt.Documents.Services;
using Final_E_Receipt.Files.Services;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Receipts.Services
{
    public class ReceiptService
    {
        private readonly IDatabaseService _dbService;
        private readonly ReceiptTemplateService _templateService;
        private readonly BrandedDocumentService _brandedDocumentService;
        private readonly FileService _fileService;
        private readonly ILogger<ReceiptService> _logger;

        public ReceiptService(
            IDatabaseService dbService,
            ReceiptTemplateService templateService,
            BrandedDocumentService brandedDocumentService,
            FileService fileService,
            ILogger<ReceiptService> logger)
        {
            _dbService = dbService;
            _templateService = templateService;
            _brandedDocumentService = brandedDocumentService;
            _fileService = fileService;
            _logger = logger;
        }

        public async Task<Receipt> CreateReceipt(Receipt receipt)
        {
            try
            {
                // Ensure receipt has required fields
                receipt.Id = receipt.Id ?? Guid.NewGuid().ToString();
                receipt.ReceiptNumber = receipt.ReceiptNumber ?? GenerateReceiptNumber();

                var parameters = new
                {
                    Id = receipt.Id,
                    PayerId = receipt.PayerId,
                    PayerName = receipt.PayerName,
                    PayerEmail = receipt.PayerEmail,
                    ReceiptNumber = receipt.ReceiptNumber,
                    Amount = receipt.Amount,
                    Currency = receipt.Currency,
                    PaymentDate = receipt.PaymentDate,
                    PaymentMethod = receipt.PaymentMethod,
                    Status = receipt.Status,
                    Description = receipt.Description,
                    Category = receipt.Category,
                    CreatedBy = receipt.CreatedBy,
                    OrganizationId = receipt.OrganizationId,
                    PaymentId = receipt.PaymentId
                };

                // Create receipt in database
                var createdReceipt = await _dbService.QueryFirstOrDefaultAsync<Receipt>("CreateReceipt", parameters);

                if (createdReceipt == null)
                    return null;

                // Generate branded receipt PDF
                await GenerateAndStoreBrandedReceipt(createdReceipt);

                return createdReceipt;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating receipt for payer {PayerId}", receipt.PayerId);
                throw;
            }
        }

        /// <summary>
        /// Generate branded receipt PDF and store as file
        /// </summary>
        private async Task GenerateAndStoreBrandedReceipt(Receipt receipt)
        {
            try
            {
                // Generate branded receipt using the unified document service
                var receiptPdf = await _brandedDocumentService.GenerateReceipt(receipt);

                // Store the PDF as a file
                var fileName = $"receipt-{receipt.ReceiptNumber}.pdf";

                // Create a temporary file to upload
                var tempFilePath = Path.GetTempFileName();
                await File.WriteAllBytesAsync(tempFilePath, receiptPdf);

                using var stream = new FileStream(tempFilePath, FileMode.Open);
                var formFile = new Microsoft.AspNetCore.Http.FormFile(stream, 0, stream.Length, "file", fileName)
                {
                    Headers = new Microsoft.AspNetCore.Http.HeaderDictionary(),
                    ContentType = "application/pdf"
                };

                // Upload the file
                var uploadedFile = await _fileService.UploadFile(
                    formFile,
                    "RECEIPT",
                    receipt.Id,
                    receipt.CreatedBy,
                    receipt.OrganizationId,
                    $"Branded receipt PDF for {receipt.ReceiptNumber}",
                    "RECEIPT_PDF"
                );

                // Clean up temp file
                File.Delete(tempFilePath);

                _logger.LogInformation("Branded receipt PDF generated and stored for receipt {ReceiptNumber}", receipt.ReceiptNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating branded receipt for {ReceiptNumber}", receipt.ReceiptNumber);
                // Don't throw - receipt creation should succeed even if PDF generation fails
            }
        }

        public async Task<Receipt> GetReceiptById(string id)
        {
            var parameters = new { Id = id };
            return await _dbService.QueryFirstOrDefaultAsync<Receipt>("GetReceiptById", parameters);
        }

        public async Task<List<Receipt>> GetReceiptsByPayer(string payerId)
        {
            var parameters = new { PayerId = payerId };
            var receipts = await _dbService.QueryAsync<Receipt>("GetReceiptsByPayer", parameters);
            return receipts.ToList();
        }

        public async Task<List<Receipt>> GetReceiptsByOrganization(string organizationId)
        {
            var parameters = new { OrganizationId = organizationId };
            var receipts = await _dbService.QueryAsync<Receipt>("GetReceiptsByOrganization", parameters);
            return receipts.ToList();
        }

        public async Task<List<Receipt>> GetReceiptsByDateRange(string organizationId, DateTime startDate, DateTime endDate)
        {
            var parameters = new
            {
                OrganizationId = organizationId,
                StartDate = startDate,
                EndDate = endDate
            };

            var receipts = await _dbService.QueryAsync<Receipt>("GetReceiptsByDateRange", parameters);
            return receipts.ToList();
        }

        public async Task<Receipt> RevokeReceipt(string id, string reason, string revokedBy)
        {
            var parameters = new
            {
                Id = id,
                RevokedReason = reason,
                RevokedBy = revokedBy
            };

            return await _dbService.QueryFirstOrDefaultAsync<Receipt>("RevokeReceipt", parameters);
        }

        public async Task<Receipt> MarkNotificationSent(string id)
        {
            var parameters = new { Id = id };
            return await _dbService.QueryFirstOrDefaultAsync<Receipt>("MarkReceiptNotificationSent", parameters);
        }

        public async Task<List<Receipt>> SearchReceipts(string organizationId, string searchTerm)
        {
            var parameters = new
            {
                OrganizationId = organizationId,
                SearchTerm = searchTerm
            };

            var receipts = await _dbService.QueryAsync<Receipt>("SearchReceipts", parameters);
            return receipts.ToList();
        }

        private string GenerateReceiptNumber()
        {
            // Format: REC-YYYYMMDD-XXXXX
            var dateString = DateTime.Now.ToString("yyyyMMdd");
            var random = new Random();
            var randomPart = random.Next(10000, 99999).ToString();
            
            return $"REC-{dateString}-{randomPart}";
        }

        public async Task<string> GenerateReceiptHtml(Receipt receipt)
        {
            // Get the default template for the organization
            var template = await _templateService.GetDefaultReceiptTemplate(receipt.OrganizationId);
            
            // If no template exists, use a basic default
            string templateContent = template?.TemplateContent ?? GetDefaultTemplateContent();
            
            // Replace placeholders with actual values
            string html = templateContent
                .Replace("{{ReceiptNumber}}", receipt.ReceiptNumber)
                .Replace("{{PayerName}}", receipt.PayerName)
                .Replace("{{PayerEmail}}", receipt.PayerEmail)
                .Replace("{{Amount}}", receipt.Amount.ToString("N2"))
                .Replace("{{Currency}}", receipt.Currency)
                .Replace("{{PaymentDate}}", receipt.PaymentDate.ToString("yyyy-MM-dd"))
                .Replace("{{PaymentMethod}}", receipt.PaymentMethod)
                .Replace("{{Description}}", receipt.Description)
                .Replace("{{Category}}", receipt.Category)
                .Replace("{{IssuedDate}}", DateTime.Now.ToString("yyyy-MM-dd"));
            
            return html;
        }

        private string GetDefaultTemplateContent()
        {
            return @"
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset='utf-8'>
                <title>Receipt</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                    .receipt { max-width: 800px; margin: 0 auto; border: 1px solid #ddd; padding: 20px; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .receipt-number { font-size: 18px; margin-bottom: 10px; }
                    .details { margin-bottom: 20px; }
                    .details table { width: 100%; border-collapse: collapse; }
                    .details th, .details td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
                    .amount { font-size: 18px; font-weight: bold; margin: 20px 0; }
                    .footer { margin-top: 30px; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class='receipt'>
                    <div class='header'>
                        <h1>RECEIPT</h1>
                        <div class='receipt-number'>Receipt Number: {{ReceiptNumber}}</div>
                    </div>
                    <div class='details'>
                        <table>
                            <tr>
                                <th>Payer:</th>
                                <td>{{PayerName}}</td>
                            </tr>
                            <tr>
                                <th>Email:</th>
                                <td>{{PayerEmail}}</td>
                            </tr>
                            <tr>
                                <th>Payment Date:</th>
                                <td>{{PaymentDate}}</td>
                            </tr>
                            <tr>
                                <th>Payment Method:</th>
                                <td>{{PaymentMethod}}</td>
                            </tr>
                            <tr>
                                <th>Description:</th>
                                <td>{{Description}}</td>
                            </tr>
                            <tr>
                                <th>Category:</th>
                                <td>{{Category}}</td>
                            </tr>
                        </table>
                    </div>
                    <div class='amount'>
                        Amount: {{Currency}} {{Amount}}
                    </div>
                    <div class='footer'>
                        <p>This receipt was automatically generated on {{IssuedDate}}.</p>
                        <p>This is an official receipt. Thank you for your payment.</p>
                    </div>
                </div>
            </body>
            </html>";
        }
    }
}


