using System;
using System.Collections.Generic;
using Final_E_Receipt.Files.DTOs;

namespace Final_E_Receipt.Compliance.DTOs
{
    public class CreateComplianceCertificateDTO
    {
        public string OrganizationId { get; set; }
        public string PaymentProfileId { get; set; }
        public string CertificateType { get; set; }
        public decimal TotalAmount { get; set; }
        public string Currency { get; set; } = "NGN";
        public DateTime ValidFrom { get; set; }
        public DateTime ValidUntil { get; set; }
        public string Description { get; set; }
        public string Terms { get; set; }
        public string ComplianceYear { get; set; }
        public string CompliancePeriod { get; set; }
        public string RegulatoryBody { get; set; }
        public string LicenseCategory { get; set; }
        public string Notes { get; set; }

        // NEW: Template selection
        public string SelectedTemplateId { get; set; } // Optional - if null, use automatic selection
        public string PreferredOrientation { get; set; } // "Portrait" or "Landscape"
    }

    public class ComplianceCertificateResponseDTO
    {
        public string Id { get; set; }
        public string CertificateNumber { get; set; }
        public string OrganizationId { get; set; }
        public string OrganizationName { get; set; }
        public string PaymentProfileId { get; set; }
        public string PaymentProfileName { get; set; }
        public string CertificateType { get; set; }
        public string Status { get; set; }
        public decimal TotalAmount { get; set; }
        public string Currency { get; set; }
        public DateTime ValidFrom { get; set; }
        public DateTime ValidUntil { get; set; }
        public DateTime IssuedDate { get; set; }
        public string IssuedBy { get; set; }
        public string Description { get; set; }
        public string Terms { get; set; }
        public string CertificatePdfFileId { get; set; }
        public bool IsRevoked { get; set; }
        public DateTime? RevokedDate { get; set; }
        public string RevokedBy { get; set; }
        public string RevokedReason { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public string ComplianceYear { get; set; }
        public string CompliancePeriod { get; set; }
        public string RegulatoryBody { get; set; }
        public string LicenseCategory { get; set; }
        public string Notes { get; set; }
    }

    public class ComplianceCertificateWithFilesDTO
    {
        public ComplianceCertificateResponseDTO Certificate { get; set; }
        public List<FileListDTO> SupportingDocuments { get; set; } = [];
        public FileListDTO CertificatePdf { get; set; }
        public ComplianceCertificateStatsDTO Stats { get; set; }
    }

    public class ComplianceCertificateStatsDTO
    {
        public int TotalSupportingFiles { get; set; }
        public long TotalFileSize { get; set; }
        public bool HasCertificatePdf { get; set; }
        public int PaymentProofFiles { get; set; }
        public int AdditionalDocuments { get; set; }
        public DateTime? LastFileUpload { get; set; }
    }

    public class GenerateCertificateDTO
    {
        public string CertificateId { get; set; }
        public string TemplateId { get; set; }
        public Dictionary<string, string> CustomFields { get; set; } = new Dictionary<string, string>();
    }

    public class RevokeCertificateDTO
    {
        public string Reason { get; set; }
        public string Comments { get; set; }
    }

    public class CertificateSearchDTO
    {
        public string OrganizationId { get; set; }
        public string CertificateType { get; set; }
        public string Status { get; set; }
        public string ComplianceYear { get; set; }
        public string CompliancePeriod { get; set; }
        public DateTime? ValidFromStart { get; set; }
        public DateTime? ValidFromEnd { get; set; }
        public DateTime? ValidUntilStart { get; set; }
        public DateTime? ValidUntilEnd { get; set; }
        public bool? IsRevoked { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }

    public class BulkCertificateGenerationDTO
    {
        public string PaymentProfileId { get; set; }
        public string CertificateType { get; set; }
        public string ComplianceYear { get; set; }
        public string CompliancePeriod { get; set; }
        public DateTime ValidFrom { get; set; }
        public DateTime ValidUntil { get; set; }
        public string Description { get; set; }
        public string Terms { get; set; }
        public string RegulatoryBody { get; set; }
        public string LicenseCategory { get; set; }
        public List<string> OrganizationIds { get; set; } = new List<string>();
        public bool GeneratePdfImmediately { get; set; } = false;
    }

    public class BulkCertificateGenerationResultDTO
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; }
        public int TotalRequested { get; set; }
        public int SuccessfullyCreated { get; set; }
        public int Failed { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<ComplianceCertificateResponseDTO> CreatedCertificates { get; set; } = new List<ComplianceCertificateResponseDTO>();
    }
}


