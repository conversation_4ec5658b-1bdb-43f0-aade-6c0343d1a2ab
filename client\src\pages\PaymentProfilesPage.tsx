import React, { useState } from 'react';
import { User } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import PaymentProfileList from '../components/Payments/PaymentProfileList';
import CreatePaymentProfile from '../components/Payments/CreatePaymentProfile';
import EditPaymentProfile from '../components/Payments/EditPaymentProfile';
import PaymentProfileDetails from '../components/Payments/PaymentProfileDetails';
import type { PaymentProfile } from '../hooks/api';

const PaymentProfilesPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<string>('payment-profiles');
  const [selectedPaymentProfile, setSelectedPaymentProfile] = useState<PaymentProfile | null>(null);

  // Role-based permissions
  const canCreateProfiles = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');
  const canEditProfiles = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');

  const handlePaymentProfileCreated = () => {
    // Refresh the payment profile list when a new profile is created
    setActiveTab('payment-profiles');
  };

  const handlePaymentProfileUpdated = () => {
    // Refresh the payment profile list when a profile is updated
    setActiveTab('payment-profiles');
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'payment-profiles':
        return (
          <PaymentProfileList
            setActiveTab={setActiveTab}
            setSelectedPaymentProfile={setSelectedPaymentProfile}
          />
        );
      
      case 'create-payment-profile':
        return canCreateProfiles ? (
          <CreatePaymentProfile
            setActiveTab={setActiveTab}
            onPaymentProfileCreated={handlePaymentProfileCreated}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">You don't have permission to create payment profiles</p>
          </div>
        );
      
      case 'edit-payment-profile':
        return canEditProfiles && selectedPaymentProfile ? (
          <EditPaymentProfile
            paymentProfile={selectedPaymentProfile}
            setActiveTab={setActiveTab}
            setSelectedPaymentProfile={setSelectedPaymentProfile}
            onPaymentProfileUpdated={handlePaymentProfileUpdated}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {!canEditProfiles 
                ? "You don't have permission to edit payment profiles" 
                : "No payment profile selected for editing"}
            </p>
          </div>
        );
      
      case 'payment-profile-details':
        return selectedPaymentProfile ? (
          <PaymentProfileDetails
            paymentProfile={selectedPaymentProfile}
            setActiveTab={setActiveTab}
            setSelectedPaymentProfile={setSelectedPaymentProfile}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">No payment profile selected for viewing</p>
          </div>
        );
      
      default:
        return (
          <PaymentProfileList
            setActiveTab={setActiveTab}
            setSelectedPaymentProfile={setSelectedPaymentProfile}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderContent()}
      </div>
    </div>
  );
};

export default PaymentProfilesPage;
