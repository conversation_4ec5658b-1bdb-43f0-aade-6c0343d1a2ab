using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Data;

namespace Final_E_Receipt.Services
{ 
public interface IDatabaseService
{
    Task<IEnumerable<T>> QueryAsync<T>(string procedureName, object parameters = null, IDbTransaction transaction = null);
    Task<T> QueryFirstOrDefaultAsync<T>(string procedureName, object parameters = null, IDbTransaction transaction = null);
    Task<int> ExecuteAsync(string procedureName, object parameters = null, IDbTransaction transaction = null);

    // Add overloads that accept existing connection
    Task<IEnumerable<T>> QueryAsync<T>(IDbConnection connection, string procedureName, object parameters = null, IDbTransaction transaction = null);
    Task<T> QueryFirstOrDefaultAsync<T>(IDbConnection connection, string procedureName, object parameters = null, IDbTransaction transaction = null);
    Task<int> ExecuteAsync(IDbConnection connection, string procedureName, object parameters = null, IDbTransaction transaction = null);

    SqlConnection GetConnection();
}

public class DatabaseService : IDatabaseService
{
    private readonly string _connectionString;
    private readonly ILogger<DatabaseService> _logger;
    private readonly int _commandTimeout;

    public DatabaseService(IConfiguration configuration, ILogger<DatabaseService> logger)
    {
        _connectionString = configuration.GetConnectionString("DefaultConnection")
            ?? throw new ArgumentException("Connection string 'DefaultConnection' not found");
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _commandTimeout = configuration.GetValue<int>("Database:CommandTimeout", 30);
    }

    public SqlConnection GetConnection() => new SqlConnection(_connectionString);

    // Original methods (create their own connection)
    public async Task<IEnumerable<T>> QueryAsync<T>(string procedureName, object parameters = null, IDbTransaction transaction = null)
    {
        using var connection = GetConnection();
        await connection.OpenAsync();
        return await connection.QueryAsync<T>(
            procedureName,
            parameters,
            transaction: transaction,
            commandType: CommandType.StoredProcedure,
            commandTimeout: _commandTimeout
        );
    }

    public async Task<T> QueryFirstOrDefaultAsync<T>(string procedureName, object parameters = null, IDbTransaction transaction = null)
    {
        using var connection = GetConnection();
        await connection.OpenAsync();
        return await connection.QueryFirstOrDefaultAsync<T>(
            procedureName,
            parameters,
            transaction: transaction,
            commandType: CommandType.StoredProcedure,
            commandTimeout: _commandTimeout
        );
    }

    public async Task<int> ExecuteAsync(string procedureName, object parameters = null, IDbTransaction transaction = null)
    {
        using var connection = GetConnection();
        await connection.OpenAsync();
        return await connection.ExecuteAsync(
            procedureName,
            parameters,
            transaction: transaction,
            commandType: CommandType.StoredProcedure,
            commandTimeout: _commandTimeout
        );
    }

    // New overloads that use existing connection
    public async Task<IEnumerable<T>> QueryAsync<T>(IDbConnection connection, string procedureName, object parameters = null, IDbTransaction transaction = null)
    {
        return await connection.QueryAsync<T>(
            procedureName,
            parameters,
            transaction: transaction,
            commandType: CommandType.StoredProcedure,
            commandTimeout: _commandTimeout
        );
    }

    public async Task<T> QueryFirstOrDefaultAsync<T>(IDbConnection connection, string procedureName, object parameters = null, IDbTransaction transaction = null)
    {
        return await connection.QueryFirstOrDefaultAsync<T>(
            procedureName,
            parameters,
            transaction: transaction,
            commandType: CommandType.StoredProcedure,
            commandTimeout: _commandTimeout
        );
    }

    public async Task<int> ExecuteAsync(IDbConnection connection, string procedureName, object parameters = null, IDbTransaction transaction = null)
    {
        return await connection.ExecuteAsync(
            procedureName,
            parameters,
            transaction: transaction,
            commandType: CommandType.StoredProcedure,
            commandTimeout: _commandTimeout
        );
    }
}
}