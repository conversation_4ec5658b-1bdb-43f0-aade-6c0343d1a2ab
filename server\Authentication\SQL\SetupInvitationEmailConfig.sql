-- ===================================================================
-- Setup Email Configuration for User Invitations
-- ===================================================================
-- This script sets up the default email configuration needed for 
-- user invitation emails to work properly.
-- ===================================================================

-- Check if EmailConfigurations table exists
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'EmailConfigurations')
BEGIN
    PRINT 'ERROR: EmailConfigurations table does not exist. Please run NotificationProcedures.sql first.'
    RETURN
END

-- Check if there's already a SYSTEM email configuration
IF EXISTS (SELECT 1 FROM EmailConfigurations WHERE OrganizationId = 'SYSTEM' AND IsDefault = 1)
BEGIN
    PRINT 'SYSTEM email configuration already exists. Skipping creation.'
    SELECT 
        'Existing Configuration' as Status,
        SmtpServer,
        Port,
        Username,
        SenderName,
        SenderEmail,
        EnableSsl,
        IsDefault
    FROM EmailConfigurations 
    WHERE OrganizationId = 'SYSTEM' AND IsDefault = 1
    RETURN
END

-- Insert default email configuration for SYSTEM (admin invitations)
-- NOTE: Replace the placeholder values with your actual SMTP settings
INSERT INTO EmailConfigurations (
    Id, 
    OrganizationId, 
    SmtpServer, 
    Port, 
    Username, 
    Password, 
    EnableSsl, 
    IsDefault, 
    SenderName, 
    SenderEmail, 
    CreatedBy
)
VALUES (
    NEWID(),
    'SYSTEM', -- For admin/system invitations
    'smtp.gmail.com', -- Replace with your SMTP server
    587, -- SMTP port
    '<EMAIL>', -- Replace with your email
    'your-app-password', -- Replace with your app password
    1, -- EnableSsl = true
    1, -- IsDefault = true
    'Payment Management System', -- Sender display name
    '<EMAIL>', -- Replace with your sender email
    'SYSTEM'
);

-- Verify the configuration was created
IF @@ROWCOUNT > 0
BEGIN
    PRINT 'SUCCESS: Default email configuration created for SYSTEM.'
    
    SELECT 
        'New Configuration Created' as Status,
        SmtpServer,
        Port,
        Username,
        SenderName,
        SenderEmail,
        EnableSsl,
        IsDefault,
        CreatedAt
    FROM EmailConfigurations 
    WHERE OrganizationId = 'SYSTEM' AND IsDefault = 1
END
ELSE
BEGIN
    PRINT 'ERROR: Failed to create email configuration.'
END

-- ===================================================================
-- IMPORTANT SETUP INSTRUCTIONS:
-- ===================================================================
-- 
-- 1. UPDATE THE SMTP SETTINGS ABOVE:
--    - Replace '<EMAIL>' with your actual Gmail address
--    - Replace 'your-app-password' with your Gmail App Password
--    - Replace '<EMAIL>' with your organization's email
--
-- 2. FOR GMAIL SETUP:
--    - Enable 2-Factor Authentication on your Gmail account
--    - Generate an App Password: https://myaccount.google.com/apppasswords
--    - Use the App Password (not your regular password)
--
-- 3. FOR OTHER SMTP PROVIDERS:
--    - Update SmtpServer, Port, and credentials accordingly
--    - Common settings:
--      * Outlook: smtp-mail.outlook.com, Port 587
--      * Yahoo: smtp.mail.yahoo.com, Port 587
--      * Custom SMTP: Check with your provider
--
-- 4. PRODUCTION CONSIDERATIONS:
--    - Use your organization's SMTP server instead of Gmail
--    - Set up proper SPF/DKIM records for your domain
--    - Use a dedicated email service (SendGrid, AWS SES, etc.)
--
-- ===================================================================
