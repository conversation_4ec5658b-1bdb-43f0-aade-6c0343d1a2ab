import React, { useState } from 'react';
import { DollarSign, History, AlertTriangle, TrendingUp } from 'lucide-react';
import PaymentHistoryReport from './PaymentHistoryReport';
import OutstandingBalancesReport from './OutstandingBalancesReport';

const PaymentReportsContainer: React.FC = () => {
  const [activeReport, setActiveReport] = useState<'history' | 'outstanding' | 'financial'>('history');

  const reportTabs = [
    {
      id: 'history',
      label: 'Payment History',
      icon: History,
      description: 'Detailed payment history with filtering and export',
    },
    {
      id: 'outstanding',
      label: 'Outstanding Balances',
      icon: AlertTriangle,
      description: 'Overdue payments with aging analysis',
    },
    {
      id: 'financial',
      label: 'Financial Summary',
      icon: TrendingUp,
      description: 'Financial metrics and trends',
    },
  ];

  const renderActiveReport = () => {
    switch (activeReport) {
      case 'history':
        return <PaymentHistoryReport />;
      case 'outstanding':
        return <OutstandingBalancesReport />;
      case 'financial':
        return (
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="text-center py-8">
              <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Financial summary report coming soon</p>
            </div>
          </div>
        );
      default:
        return <PaymentHistoryReport />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
          <DollarSign className="h-5 w-5 text-blue-600" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Reports</h1>
          <p className="text-gray-600">Comprehensive payment analytics and reporting</p>
        </div>
      </div>

      {/* Report Navigation */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {reportTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveReport(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeReport === tab.id
                    ? 'border-[#2aa45c] text-[#2aa45c]'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon size={16} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Descriptions */}
        <div className="px-6 py-3 bg-gray-50">
          {reportTabs.map((tab) => (
            activeReport === tab.id && (
              <p key={tab.id} className="text-sm text-gray-600">
                {tab.description}
              </p>
            )
          ))}
        </div>
      </div>

      {/* Active Report Content */}
      {renderActiveReport()}
    </div>
  );
};

export default PaymentReportsContainer;
