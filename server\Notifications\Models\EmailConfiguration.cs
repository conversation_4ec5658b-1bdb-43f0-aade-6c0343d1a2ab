using System;

namespace Final_E_Receipt.Notifications.Models
{
    public class EmailConfiguration
    {
        public string Id { get; set; }
        public string Name { get; set; } // e.g., "Gmail Configuration", "Outlook Configuration"
        public string Description { get; set; } // e.g., "Gmail SMTP for company emails"
        public string SmtpServer { get; set; }
        public int Port { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public bool EnableSsl { get; set; }
        public bool IsDefault { get; set; } // System-wide default
        public bool IsActive { get; set; } = true; // Can be enabled/disabled
        public string SenderName { get; set; }
        public string SenderEmail { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
    }
}