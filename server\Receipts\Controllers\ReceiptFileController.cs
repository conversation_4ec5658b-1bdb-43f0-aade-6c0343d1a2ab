using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Final_E_Receipt.Receipts.Services;
using Final_E_Receipt.Files.DTOs;

namespace Final_E_Receipt.Receipts.Controllers
{
    [ApiController]
    [Route("api/receipts/{receiptId}/files")]
    [Authorize]
    public class ReceiptFileController : ControllerBase
    {
        private readonly ReceiptFileService _receiptFileService;
        private readonly ILogger<ReceiptFileController> _logger;

        public ReceiptFileController(ReceiptFileService receiptFileService, ILogger<ReceiptFileController> logger)
        {
            _receiptFileService = receiptFileService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetReceiptSupportingFiles(string receiptId)
        {
            try
            {
                var files = await _receiptFileService.GetReceiptSupportingFiles(receiptId);
                
                var response = files.Select(f => new FileListDTO
                {
                    Id = f.Id,
                    FileName = f.FileName,
                    OriginalFileName = f.OriginalFileName,
                    ContentType = f.ContentType,
                    FileSize = f.FileSize,
                    RelatedEntityType = f.RelatedEntityType,
                    RelatedEntityId = f.RelatedEntityId,
                    CreatedAt = f.CreatedAt,
                    Description = f.Description,
                    Category = f.Category,
                    IsScanned = f.IsScanned,
                    ScanResult = f.ScanResult,
                    UploadedBy = f.UploadedBy
                }).ToList();

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving supporting files for receipt {ReceiptId}", receiptId);
                return StatusCode(500, new { message = "An error occurred while retrieving files" });
            }
        }

        [HttpPost("attach")]
        [Consumes("multipart/form-data")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> AttachFileToReceipt( string receiptId,  IFormFile file, string? description = null)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var organizationId = User.FindFirst("OrganizationId")?.Value;

                if (file == null || file.Length == 0)
                    return BadRequest(new { message = "File is required" });

                var uploadedFile = await _receiptFileService.AttachFileToReceipt(
                    receiptId,
                    file,
                    userId,
                    organizationId,
                    description
                );

                var response = new FileUploadResponseDTO
                {
                    Id = uploadedFile.Id,
                    FileName = uploadedFile.FileName,
                    OriginalFileName = uploadedFile.OriginalFileName,
                    ContentType = uploadedFile.ContentType,
                    FileSize = uploadedFile.FileSize,
                    RelatedEntityType = uploadedFile.RelatedEntityType,
                    RelatedEntityId = uploadedFile.RelatedEntityId,
                    CreatedAt = uploadedFile.CreatedAt,
                    Description = uploadedFile.Description,
                    Category = uploadedFile.Category,
                    IsScanned = uploadedFile.IsScanned,
                    ScanResult = uploadedFile.ScanResult
                };

                return Ok(response);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error attaching file to receipt {ReceiptId}", receiptId);
                return StatusCode(500, new { message = "An error occurred while attaching the file" });
            }
        }

        [HttpGet("stats")]
        public async Task<IActionResult> GetReceiptFileStats(string receiptId)
        {
            try
            {
                var stats = await _receiptFileService.GetReceiptFileStats(receiptId);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting file stats for receipt {ReceiptId}", receiptId);
                return StatusCode(500, new { message = "An error occurred while retrieving file statistics" });
            }
        }
    }

    [ApiController]
    [Route("api/receipts")]
    [Authorize]
    public class ReceiptWithFilesController : ControllerBase
    {
        private readonly ReceiptFileService _receiptFileService;
        private readonly ILogger<ReceiptWithFilesController> _logger;

        public ReceiptWithFilesController(ReceiptFileService receiptFileService, ILogger<ReceiptWithFilesController> logger)
        {
            _receiptFileService = receiptFileService;
            _logger = logger;
        }

        [HttpGet("{receiptId}/with-files")]
        public async Task<IActionResult> GetReceiptWithFiles(string receiptId)
        {
            try
            {
                var receiptWithFiles = await _receiptFileService.GetReceiptWithFiles(receiptId);
                
                if (receiptWithFiles == null)
                    return NotFound(new { message = "Receipt not found" });

                var response = new
                {
                    receipt = receiptWithFiles.Receipt,
                    supportingFiles = receiptWithFiles.SupportingFiles.Select(f => new FileListDTO
                    {
                        Id = f.Id,
                        FileName = f.FileName,
                        OriginalFileName = f.OriginalFileName,
                        ContentType = f.ContentType,
                        FileSize = f.FileSize,
                        RelatedEntityType = f.RelatedEntityType,
                        RelatedEntityId = f.RelatedEntityId,
                        CreatedAt = f.CreatedAt,
                        Description = f.Description,
                        Category = f.Category,
                        IsScanned = f.IsScanned,
                        ScanResult = f.ScanResult,
                        UploadedBy = f.UploadedBy
                    }).ToList(),
                    fileStats = await _receiptFileService.GetReceiptFileStats(receiptId)
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving receipt with files {ReceiptId}", receiptId);
                return StatusCode(500, new { message = "An error occurred while retrieving receipt details" });
            }
        }

        [HttpPost("with-files")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> CreateReceiptWithFiles([FromBody] CreateReceiptWithFilesDTO dto)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                
                var receipt = new Final_E_Receipt.Receipts.Models.Receipt
                {
                    PayerId = dto.PayerId,
                    PayerName = dto.PayerName,
                    PayerEmail = dto.PayerEmail,
                    Amount = dto.Amount,
                    Currency = dto.Currency,
                    PaymentDate = dto.PaymentDate,
                    PaymentMethod = dto.PaymentMethod,
                    Status = "Completed",
                    Description = dto.Description,
                    Category = dto.Category,
                    CreatedBy = userId,
                    OrganizationId = dto.OrganizationId,
                    PaymentId = dto.PaymentId // Link to payment
                };

                var receiptWithFiles = await _receiptFileService.CreateReceiptWithFiles(receipt);
                
                if (receiptWithFiles == null)
                    return BadRequest(new { message = "Failed to create receipt" });

                var response = new
                {
                    receipt = receiptWithFiles.Receipt,
                    supportingFiles = receiptWithFiles.SupportingFiles.Select(f => new FileListDTO
                    {
                        Id = f.Id,
                        FileName = f.FileName,
                        OriginalFileName = f.OriginalFileName,
                        ContentType = f.ContentType,
                        FileSize = f.FileSize,
                        RelatedEntityType = f.RelatedEntityType,
                        RelatedEntityId = f.RelatedEntityId,
                        CreatedAt = f.CreatedAt,
                        Description = f.Description,
                        Category = f.Category,
                        IsScanned = f.IsScanned,
                        ScanResult = f.ScanResult,
                        UploadedBy = f.UploadedBy
                    }).ToList()
                };

                return CreatedAtAction(nameof(GetReceiptWithFiles), new { receiptId = receiptWithFiles.Receipt.Id }, response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating receipt with files");
                return StatusCode(500, new { message = "An error occurred while creating the receipt" });
            }
        }
    }

    public class CreateReceiptWithFilesDTO
    {
        public string PayerId { get; set; }
        public string PayerName { get; set; }
        public string PayerEmail { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "NGN";
        public DateTime PaymentDate { get; set; }
        public string PaymentMethod { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public string OrganizationId { get; set; }
        public string PaymentId { get; set; } // Link to payment for file association
    }
}
