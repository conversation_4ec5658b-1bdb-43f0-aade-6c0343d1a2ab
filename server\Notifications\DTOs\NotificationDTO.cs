using System;
using System.ComponentModel.DataAnnotations;

namespace Final_E_Receipt.Notifications.DTOs
{
    public class CreateNotificationDTO
    {
        [Required]
        public string UserId { get; set; }
        
        public string OrganizationId { get; set; }
        
        [Required]
        public string Type { get; set; }
        
        [Required]
        [StringLength(255)]
        public string Title { get; set; }
        
        [Required]
        public string Message { get; set; }
        
        public string Priority { get; set; } = "MEDIUM";
        
        public string RelatedEntityId { get; set; }
        
        public string RelatedEntityType { get; set; }
        
        public DateTime? ScheduledDate { get; set; }
    }

    public class UpdateNotificationDTO
    {
        public string Title { get; set; }
        public string Message { get; set; }
        public string Priority { get; set; }
        public string Status { get; set; }
        public DateTime? ScheduledDate { get; set; }
    }

    public class NotificationFiltersDTO
    {
        public string Status { get; set; } // UNREAD, READ, ARCHIVED
        public string Type { get; set; }
        public string Priority { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }

    public class BulkNotificationActionDTO
    {
        [Required]
        public string[] NotificationIds { get; set; }
        
        [Required]
        public string Action { get; set; } // READ, ARCHIVE, DELETE
    }

    public class CreateNotificationPreferenceDTO
    {
        [Required]
        public string UserId { get; set; }
        
        [Required]
        public string NotificationType { get; set; }
        
        public bool InAppEnabled { get; set; } = true;
        
        public bool EmailEnabled { get; set; } = true;
    }

    public class BroadcastNotificationDTO
    {
        [Required]
        public string Type { get; set; }
        
        [Required]
        [StringLength(255)]
        public string Title { get; set; }
        
        [Required]
        public string Message { get; set; }
        
        public string Priority { get; set; } = "MEDIUM";
        
        public string RelatedEntityId { get; set; }
        
        public string RelatedEntityType { get; set; }
        
        public string[] UserIds { get; set; } // Specific users
        
        public string[] OrganizationIds { get; set; } // All users in organizations
        
        public DateTime? ScheduledDate { get; set; }
    }

    public class PaymentScheduleNotificationDTO
    {
        [Required]
        public string PaymentScheduleId { get; set; }
        
        [Required]
        public string PayerUserId { get; set; }
        
        [Required]
        public string OrganizationId { get; set; }
        
        [Required]
        public decimal Amount { get; set; }
        
        [Required]
        public DateTime DueDate { get; set; }
        
        [Required]
        public string PaymentProfileName { get; set; }
        
        public string Currency { get; set; } = "NGN";
        
        public string Action { get; set; } = "CREATED"; // CREATED, UPDATED, DELETED
        
        public string Reason { get; set; } // For updates/deletions
    }
}
