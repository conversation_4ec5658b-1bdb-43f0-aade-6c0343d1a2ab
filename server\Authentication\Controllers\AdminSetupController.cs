using Final_E_Receipt.Authentication.DTOs;
using Final_E_Receipt.Authentication.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using static Final_E_Receipt.Authentication.Controllers.AuthController;

namespace Final_E_Receipt.Authentication.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AdminSetupController : ControllerBase
    {
        private readonly AuthenticationService _authService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AdminSetupController> _logger;

        public AdminSetupController(AuthenticationService authService, IConfiguration configuration, ILogger<AdminSetupController> logger)
        {
            _authService = authService;
            _configuration = configuration;
            _logger = logger;

        }

        [HttpPost("initialize")]
        [Authorize]
        public async Task<IActionResult> InitializeAdmin()
        {
            // Get the Azure AD Object ID from the authenticated user (JWT token claims)
            var azureAdObjectId = User.FindFirst("oid")?.Value ??
                                 User.FindFirst("http://schemas.microsoft.com/identity/claims/objectidentifier")?.Value;
            Console.WriteLine($"AdminSetup/initialize called. Azure AD Object ID: {azureAdObjectId}");

            if (string.IsNullOrEmpty(azureAdObjectId))
            {
                Console.WriteLine("Azure AD Object ID is null or empty");
                return Unauthorized(new { message = "Azure AD Object ID not found in token" });
            }

            // Get the email from claims (JWT token claims)
            var email = User.FindFirst("preferred_username")?.Value ??
                        User.FindFirst("upn")?.Value ??
                        User.FindFirst(ClaimTypes.Email)?.Value;
            Console.WriteLine($"Email from claims: {email}");

            if (string.IsNullOrEmpty(email))
            {
                Console.WriteLine("Email not found in claims");
                return BadRequest(new { message = "Email not found in token claims" });
            }

            // Get the name from claims (JWT token claims)
            var firstName = User.FindFirst("given_name")?.Value ??
                           User.FindFirst(ClaimTypes.GivenName)?.Value ?? "Admin";
            var lastName = User.FindFirst("family_name")?.Value ??
                          User.FindFirst(ClaimTypes.Surname)?.Value ?? "User";
            Console.WriteLine($"Name from claims: {firstName} {lastName}");

            try
            {
                // Check if the email is in the allowed admin emails (optional security measure)
                var allowedAdminEmails = _configuration.GetSection("AdminSetup:AllowedEmails").Get<string[]>();
                Console.WriteLine($"Allowed admin emails: {string.Join(", ", allowedAdminEmails ?? new string[0])}");

                if (allowedAdminEmails != null && allowedAdminEmails.Length > 0)
                {
                    // Convert both to lowercase for case-insensitive comparison
                    var normalizedEmail = email.ToLower();
                    var normalizedAllowedEmails = allowedAdminEmails.Select(e => e.ToLower()).ToArray();
                    
                    if (!normalizedAllowedEmails.Contains(normalizedEmail))
                    {
                        Console.WriteLine($"Email {email} not in allowed admin emails");
                        return Forbid();
                    }
                }

                // Create the admin user
                Console.WriteLine("Creating admin user...");
                var user = await _authService.CreateInitialAdminUser(azureAdObjectId, email, firstName, lastName);
                Console.WriteLine($"Admin user created. ID: {user.Id}, Email: {user.Email}, Role: {user.Role}");
                
                return Ok(new { 
                    message = "Admin user created successfully", 
                    user = new {
                        id = user.Id,
                        email = user.Email,
                        firstName = user.FirstName,
                        lastName = user.LastName,
                        role = user.Role
                    }
                });
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"Error creating admin user: {ex.Message}");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error creating admin user: {ex.Message}");
                return StatusCode(500, new { message = "An error occurred while creating admin user", error = ex.Message });
            }
        }

        /// <summary>
        /// Register a new admin user (for initial setup)
        /// </summary>
        [HttpPost("register-admin")]
        [AllowAnonymous] // Restrict/remove after initial setup!
        public async Task<IActionResult> RegisterAdmin([FromBody] RegisterAdminDto dto)
        {
            try
            {
                var user = await _authService.CreateAdminUserAsync(dto.FirstName, dto.LastName, dto.Email, dto.Password);
                return Ok(new { message = "Admin user created", userId = user.Id });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Admin registration failed: {Message}", ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating admin user");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

    }
}

