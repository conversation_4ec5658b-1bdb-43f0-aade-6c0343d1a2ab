-- Payments Table (Enhanced with Approval Workflow)
CREATE TABLE Payments (
    Id NVARCHAR(50) PRIMARY KEY,
    PayerId NVARCHAR(50),
    PayerName NVARCHAR(100),
    PayerEmail NVARCHAR(255),
    Amount DECIMAL(18, 2) NOT NULL,
    Currency NVARCHAR(10) NOT NULL DEFAULT 'NGN',
    TransactionReference NVARCHAR(50) UNIQUE NOT NULL,
    PaymentMethod NVARCHAR(50) NOT NULL,
    Status NVARCHAR(50) NOT NULL, -- Pending, Proof_Uploaded, Acknowledged, Approved, Rejected, Completed, Failed
    Description NVARCHAR(500),
    Category NVARCHAR(100),
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CompletedAt DATETIME NULL,
    ReceiptId NVARCHAR(50),
    OrganizationId NVARCHAR(50),
    PaymentScheduleId NVARCHAR(50),
    ProofFileId NVARCHAR(50),

    -- Approval Workflow Fields
    AcknowledgedBy NVARCHAR(50) NULL,
    AcknowledgedDate DATETIME NULL,
    AcknowledgmentNotes NVARCHAR(1000) NULL,

    ApprovedBy NVARCHAR(50) NULL,
    ApprovedDate DATETIME NULL,
    ApprovalNotes NVARCHAR(1000) NULL,

    RejectedBy NVARCHAR(50) NULL,
    RejectedDate DATETIME NULL,
    RejectionReason NVARCHAR(1000) NULL
);

-- Stored Procedures for Payments
CREATE PROCEDURE CreatePayment
    @Id NVARCHAR(50),
    @PayerId NVARCHAR(50),
    @PayerName NVARCHAR(100),
    @PayerEmail NVARCHAR(255),
    @Amount DECIMAL(18, 2),
    @Currency NVARCHAR(10),
    @TransactionReference NVARCHAR(50),
    @PaymentMethod NVARCHAR(50),
    @Status NVARCHAR(20),
    @Description NVARCHAR(500),
    @Category NVARCHAR(100),
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    INSERT INTO Payments (
        Id, PayerId, PayerName, PayerEmail, Amount, Currency, TransactionReference,
        PaymentMethod, Status, Description, Category, OrganizationId
    )
    VALUES (
        @Id, @PayerId, @PayerName, @PayerEmail, @Amount, @Currency, @TransactionReference,
        @PaymentMethod, @Status, @Description, @Category, @OrganizationId
    )
    
    SELECT * FROM Payments WHERE Id = @Id
END;

CREATE PROCEDURE UpdatePaymentStatus
    @Id NVARCHAR(50),
    @Status NVARCHAR(20),
    @CompletedAt DATETIME
AS
BEGIN
    UPDATE Payments
    SET Status = @Status, CompletedAt = @CompletedAt
    WHERE Id = @Id
    
    SELECT * FROM Payments WHERE Id = @Id
END;

CREATE PROCEDURE UpdatePaymentReceiptId
    @Id NVARCHAR(50),
    @ReceiptId NVARCHAR(50)
AS
BEGIN
    UPDATE Payments
    SET ReceiptId = @ReceiptId
    WHERE Id = @Id
    
    SELECT * FROM Payments WHERE Id = @Id
END;

CREATE PROCEDURE GetPaymentById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Payments WHERE Id = @Id
END;

CREATE PROCEDURE GetPaymentsByPayer
    @PayerId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Payments WHERE PayerId = @PayerId ORDER BY CreatedAt DESC
END;

CREATE PROCEDURE GetPaymentsByOrganization
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Payments WHERE OrganizationId = @OrganizationId ORDER BY CreatedAt DESC
END;

CREATE PROCEDURE GetPaymentsByStatus
    @Status NVARCHAR(20)
AS
BEGIN
    SELECT * FROM Payments WHERE Status = @Status ORDER BY CreatedAt DESC
END;

-- Add ProofFileId column to Payments table if it doesn't exist
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Payments' AND COLUMN_NAME = 'ProofFileId')
BEGIN
    ALTER TABLE Payments ADD ProofFileId NVARCHAR(50) NULL;
END;

CREATE PROCEDURE UpdatePaymentProofStatus
    @PaymentId NVARCHAR(50),
    @Status NVARCHAR(20),
    @ProofFileId NVARCHAR(50) = NULL
AS
BEGIN
    UPDATE Payments
    SET Status = @Status, ProofFileId = @ProofFileId
    WHERE Id = @PaymentId

    SELECT * FROM Payments WHERE Id = @PaymentId
END;

-- ===== APPROVAL WORKFLOW PROCEDURES =====

-- Acknowledge Payment (Finance Officer)
CREATE PROCEDURE AcknowledgePayment
    @PaymentId NVARCHAR(50),
    @AcknowledgedBy NVARCHAR(50),
    @Notes NVARCHAR(1000) = NULL
AS
BEGIN
    -- Validate payment exists and is in correct status
    IF NOT EXISTS (SELECT 1 FROM Payments WHERE Id = @PaymentId AND Status IN ('Proof_Uploaded', 'Pending'))
    BEGIN
        RAISERROR('Payment not found or not in valid status for acknowledgment', 16, 1)
        RETURN
    END

    UPDATE Payments
    SET Status = 'Acknowledged',
        AcknowledgedBy = @AcknowledgedBy,
        AcknowledgedDate = GETDATE(),
        AcknowledgmentNotes = @Notes
    WHERE Id = @PaymentId

    SELECT * FROM Payments WHERE Id = @PaymentId
END;

-- Approve Payment (Senior Finance Officer)
CREATE PROCEDURE ApprovePayment
    @PaymentId NVARCHAR(50),
    @ApprovedBy NVARCHAR(50),
    @Notes NVARCHAR(1000) = NULL
AS
BEGIN
    -- Validate payment exists and is acknowledged
    IF NOT EXISTS (SELECT 1 FROM Payments WHERE Id = @PaymentId AND Status = 'Acknowledged')
    BEGIN
        RAISERROR('Payment not found or not acknowledged yet', 16, 1)
        RETURN
    END

    UPDATE Payments
    SET Status = 'Approved',
        ApprovedBy = @ApprovedBy,
        ApprovedDate = GETDATE(),
        ApprovalNotes = @Notes
    WHERE Id = @PaymentId

    SELECT * FROM Payments WHERE Id = @PaymentId
END;

-- Reject Payment (Finance Officer or Senior Finance Officer)
CREATE PROCEDURE RejectPayment
    @PaymentId NVARCHAR(50),
    @RejectedBy NVARCHAR(50),
    @Reason NVARCHAR(1000),
    @Notes NVARCHAR(1000) = NULL
AS
BEGIN
    -- Validate payment exists and is in valid status for rejection
    IF NOT EXISTS (SELECT 1 FROM Payments WHERE Id = @PaymentId AND Status IN ('Proof_Uploaded', 'Acknowledged', 'Pending'))
    BEGIN
        RAISERROR('Payment not found or not in valid status for rejection', 16, 1)
        RETURN
    END

    UPDATE Payments
    SET Status = 'Rejected',
        RejectedBy = @RejectedBy,
        RejectedDate = GETDATE(),
        RejectionReason = @Reason,
        ApprovalNotes = @Notes
    WHERE Id = @PaymentId

    SELECT * FROM Payments WHERE Id = @PaymentId
END;

-- Get Payments Pending Acknowledgment (for Finance Officers)
CREATE PROCEDURE GetPaymentsPendingAcknowledgment
    @OrganizationId NVARCHAR(50) = NULL
AS
BEGIN
    SELECT * FROM Payments
    WHERE Status IN ('Proof_Uploaded', 'Pending')
    AND (@OrganizationId IS NULL OR OrganizationId = @OrganizationId)
    ORDER BY CreatedAt ASC
END;

-- Get Payments Pending Approval (for Senior Finance Officers)
CREATE PROCEDURE GetPaymentsPendingApproval
    @OrganizationId NVARCHAR(50) = NULL
AS
BEGIN
    SELECT * FROM Payments
    WHERE Status = 'Acknowledged'
    AND (@OrganizationId IS NULL OR OrganizationId = @OrganizationId)
    ORDER BY AcknowledgedDate ASC
END;

-- Get Payment Approval History
CREATE PROCEDURE GetPaymentApprovalHistory
    @PaymentId NVARCHAR(50)
AS
BEGIN
    SELECT
        Id,
        Status,
        CreatedAt,
        AcknowledgedBy,
        AcknowledgedDate,
        AcknowledgmentNotes,
        ApprovedBy,
        ApprovedDate,
        ApprovalNotes,
        RejectedBy,
        RejectedDate,
        RejectionReason
    FROM Payments
    WHERE Id = @PaymentId
END;

-- Get Payments by Approval Status with User Details
CREATE PROCEDURE GetPaymentsByApprovalStatus
    @Status NVARCHAR(50),
    @OrganizationId NVARCHAR(50) = NULL,
    @UserId NVARCHAR(50) = NULL
AS
BEGIN
    SELECT * FROM Payments
    WHERE Status = @Status
    AND (@OrganizationId IS NULL OR OrganizationId = @OrganizationId)
    AND (
        @UserId IS NULL OR
        (@Status = 'Acknowledged' AND AcknowledgedBy = @UserId) OR
        (@Status = 'Approved' AND ApprovedBy = @UserId) OR
        (@Status = 'Rejected' AND RejectedBy = @UserId)
    )
    ORDER BY CreatedAt DESC
END;

--Payment type

-- PaymentType Table
CREATE TABLE PaymentTypes (
    Id NVARCHAR(50) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50) NOT NULL,
    UpdatedAt DATETIME NULL,
    UpdatedBy NVARCHAR(50) NULL
);

-- Add index for better performance
CREATE INDEX IX_PaymentTypes_IsActive ON PaymentTypes(IsActive);

CREATE PROCEDURE CreatePaymentType
    @Id NVARCHAR(50),
    @Name NVARCHAR(100),
    @Description NVARCHAR(500) = NULL,
    @CreatedBy NVARCHAR(50)
AS
BEGIN
    -- Check if name already exists
    IF EXISTS (SELECT 1 FROM PaymentTypes WHERE Name = @Name)
    BEGIN
        RAISERROR('Payment type with this name already exists', 16, 1)
        RETURN
    END

    INSERT INTO PaymentTypes (Id, Name, Description, CreatedBy)
    VALUES (@Id, @Name, @Description, @CreatedBy)
    
    SELECT * FROM PaymentTypes WHERE Id = @Id
END;

CREATE PROCEDURE GetPaymentTypeById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM PaymentTypes WHERE Id = @Id
END;

CREATE PROCEDURE GetAllPaymentTypes
    @IsActive BIT = NULL
AS
BEGIN
    SELECT * FROM PaymentTypes 
    WHERE @IsActive IS NULL OR IsActive = @IsActive
    ORDER BY Name
END;

CREATE PROCEDURE UpdatePaymentType
    @Id NVARCHAR(50),
    @Name NVARCHAR(100),
    @Description NVARCHAR(500) = NULL,
    @UpdatedBy NVARCHAR(50)
AS
BEGIN
    -- Check if name already exists for another record
    IF EXISTS (SELECT 1 FROM PaymentTypes WHERE Name = @Name AND Id <> @Id)
    BEGIN
        RAISERROR('Payment type with this name already exists', 16, 1)
        RETURN
    END

    UPDATE PaymentTypes
    SET 
        Name = @Name,
        Description = @Description,
        UpdatedAt = GETDATE(),
        UpdatedBy = @UpdatedBy
    WHERE Id = @Id
    
    SELECT * FROM PaymentTypes WHERE Id = @Id
END;


CREATE PROCEDURE TogglePaymentTypeStatus
    @Id NVARCHAR(50),
    @IsActive BIT,
    @UpdatedBy NVARCHAR(50)
AS
BEGIN
    UPDATE PaymentTypes
    SET 
        IsActive = @IsActive,
        UpdatedAt = GETDATE(),
        UpdatedBy = @UpdatedBy
    WHERE Id = @Id
    
    SELECT * FROM PaymentTypes WHERE Id = @Id
END;

CREATE PROCEDURE DeletePaymentType
    @Id NVARCHAR(50)
AS
BEGIN
    -- Check if payment type is being used
    IF EXISTS (SELECT 1 FROM Payments WHERE PaymentTypeId = @Id)
    BEGIN
        RAISERROR('Cannot delete payment type as it is being used by payments', 16, 1)
        RETURN
    END

    DELETE FROM PaymentTypes WHERE Id = @Id
    
    SELECT @@ROWCOUNT AS 'RowsAffected'
END;

-- Add PaymentTypeId to Payments table(alter)

ALTER PROCEDURE CreatePayment
    @Id NVARCHAR(50),
    @PayerId NVARCHAR(50),
    @PayerName NVARCHAR(100),
    @PayerEmail NVARCHAR(255),
    @Amount DECIMAL(18, 2),
    @Currency NVARCHAR(10),
    @TransactionReference NVARCHAR(50),
    @PaymentMethod NVARCHAR(50),
    @Status NVARCHAR(20),
    @Description NVARCHAR(500),
    @PaymentTypeId NVARCHAR(50),
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    -- Validate payment type exists and is active
    IF NOT EXISTS (SELECT 1 FROM PaymentTypes WHERE Id = @PaymentTypeId AND IsActive = 1)
    BEGIN
        RAISERROR('Invalid or inactive payment type', 16, 1)
        RETURN
    END

    INSERT INTO Payments (
        Id, PayerId, PayerName, PayerEmail, Amount, Currency, TransactionReference,
        PaymentMethod, Status, Description, PaymentTypeId, OrganizationId
    )
    VALUES (
        @Id, @PayerId, @PayerName, @PayerEmail, @Amount, @Currency, @TransactionReference,
        @PaymentMethod, @Status, @Description, @PaymentTypeId, @OrganizationId
    )
    
    SELECT * FROM Payments WHERE Id = @Id
END;

CREATE PROCEDURE GetPaymentsWithTypeInfo
    @PayerId NVARCHAR(50) = NULL,
    @OrganizationId NVARCHAR(50) = NULL,
    @Status NVARCHAR(20) = NULL,
    @PaymentTypeId NVARCHAR(50) = NULL
AS
BEGIN
    SELECT 
        p.*,
        pt.Name AS PaymentTypeName,
        pt.Description AS PaymentTypeDescription
    FROM Payments p
    LEFT JOIN PaymentTypes pt ON p.PaymentTypeId = pt.Id
    WHERE 
        (@PayerId IS NULL OR p.PayerId = @PayerId) AND
        (@OrganizationId IS NULL OR p.OrganizationId = @OrganizationId) AND
        (@Status IS NULL OR p.Status = @Status) AND
        (@PaymentTypeId IS NULL OR p.PaymentTypeId = @PaymentTypeId)
    ORDER BY p.CreatedAt DESC
END;

CREATE PROCEDURE GetPaymentTypeSummary
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @OrganizationId NVARCHAR(50) = NULL
AS
BEGIN
    SET @StartDate = ISNULL(@StartDate, DATEADD(MONTH, -1, GETDATE()))
    SET @EndDate = ISNULL(@EndDate, GETDATE())

    SELECT 
        pt.Id,
        pt.Name,
        pt.Description,
        COUNT(p.Id) AS TotalPayments,
        SUM(p.Amount) AS TotalAmount,
        MIN(p.CreatedAt) AS FirstPaymentDate,
        MAX(p.CreatedAt) AS LastPaymentDate
    FROM PaymentTypes pt
    LEFT JOIN Payments p ON pt.Id = p.PaymentTypeId
        AND p.CreatedAt BETWEEN @StartDate AND @EndDate
        AND (@OrganizationId IS NULL OR p.OrganizationId = @OrganizationId)
    GROUP BY pt.Id, pt.Name, pt.Description
    ORDER BY TotalAmount DESC
END;