import React, { useState } from 'react';
import { X, Upload, Download, FileSpreadsheet, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { usePaymentScheduleApi } from '../../hooks/api';
import { useNotifications } from '../../contexts/NotificationContext';

interface ExcelImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  paymentProfileId: string;
  paymentProfileName: string;
  onImportComplete: () => void;
}

interface ImportResult {
  success: boolean;
  totalRows: number;
  successfulImports: number;
  failedImports: number;
  errors: string[];
  warnings: string[];
}

const ExcelImportModal: React.FC<ExcelImportModalProps> = ({
  isOpen,
  onClose,
  paymentProfileId,
  paymentProfileName,
  onImportComplete,
}) => {
  const { importPaymentSchedules, downloadTemplate, loading } = usePaymentScheduleApi();
  const { notifyBulkSchedulesImported } = useNotifications();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileSelection = (file: File) => {
    // Validate file type
    if (!file.name.toLowerCase().endsWith('.xlsx')) {
      setError('Please select an Excel file (.xlsx)');
      setSelectedFile(null);
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      setError('File size must be less than 5MB');
      setSelectedFile(null);
      return;
    }

    setSelectedFile(file);
    setError(null);
    setImportResult(null);
  };

  const handleImport = async () => {
    if (!selectedFile) return;

    try {
      setImporting(true);
      setError(null);
      
      const result = await importPaymentSchedules(paymentProfileId, selectedFile);
      
      if (result) {
        setImportResult({
          success: result.success,
          totalRows: result.totalRows,
          successfulImports: result.successfulImports,
          failedImports: result.failedImports,
          errors: result.errors || [],
          warnings: result.warnings || [],
        });
        
        if (result.success && result.successfulImports > 0) {
          // Send bulk notification to all affected payers
          // Note: In a real implementation, you'd get the actual user IDs of the payers
          // from the import result or by looking up the organizations
          const mockUserIds = ['user1', 'user2']; // This would come from the backend result

          await notifyBulkSchedulesImported(
            paymentProfileId,
            paymentProfileName,
            result.successfulImports,
            result.totalRows,
            mockUserIds
          );

          onImportComplete();
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Import failed';
      setError(errorMessage);
    } finally {
      setImporting(false);
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      await downloadTemplate();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to download template');
    }
  };

  const clearSelection = () => {
    setSelectedFile(null);
    setImportResult(null);
    setError(null);
  };

  const handleClose = () => {
    clearSelection();
    onClose();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Import Payment Schedules</h3>
            <p className="text-sm text-gray-600">For: {paymentProfileName}</p>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={24} />
          </button>
        </div>

        <div className="space-y-6">
          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex">
              <Info className="h-5 w-5 text-blue-400" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-blue-900">Import Instructions</h4>
                <div className="mt-2 text-sm text-blue-800">
                  <ol className="list-decimal list-inside space-y-1">
                    <li>Download the Excel template using the button below</li>
                    <li>Fill in the required columns: OrganizationId, OrganizationName, Amount, DueDate</li>
                    <li>Optional columns: Currency (defaults to NGN), Description</li>
                    <li>Upload the completed Excel file (.xlsx format only)</li>
                    <li>Review the import results and fix any errors if needed</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>

          {/* Download Template */}
          <div className="flex justify-center">
            <button
              onClick={handleDownloadTemplate}
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#2aa45c] disabled:opacity-50"
            >
              <Download size={16} className="mr-2" />
              Download Excel Template
            </button>
          </div>

          {/* File Upload Area */}
          <div
            className={`
              border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer
              ${dragActive ? 'border-[#2aa45c] bg-green-50' : 'border-gray-300 hover:border-gray-400'}
              ${importing ? 'opacity-50 cursor-not-allowed' : ''}
            `}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={() => !importing && document.getElementById('excel-file-input')?.click()}
          >
            <input
              id="excel-file-input"
              type="file"
              accept=".xlsx"
              onChange={handleFileInput}
              className="hidden"
              disabled={importing}
            />
            
            <FileSpreadsheet className="mx-auto text-gray-400 mb-3" size={48} />
            <p className="text-gray-600 font-medium mb-1">
              Click to upload or drag and drop
            </p>
            <p className="text-xs text-gray-500">
              Excel files (.xlsx) up to 5MB
            </p>
          </div>

          {/* Selected File */}
          {selectedFile && (
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <FileSpreadsheet size={20} className="text-green-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(selectedFile.size)}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleImport}
                  disabled={importing}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[#2aa45c] hover:bg-[#076934] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#2aa45c] disabled:opacity-50"
                >
                  {importing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Importing...
                    </>
                  ) : (
                    <>
                      <Upload size={16} className="mr-2" />
                      Import
                    </>
                  )}
                </button>
                <button
                  onClick={clearSelection}
                  disabled={importing}
                  className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
                >
                  <X size={16} />
                </button>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <AlertCircle className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Import Results */}
          {importResult && (
            <div className={`border rounded-lg p-4 ${importResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
              <div className="flex items-start">
                {importResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-400 mt-0.5" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-400 mt-0.5" />
                )}
                <div className="ml-3 flex-1">
                  <h4 className={`text-sm font-medium ${importResult.success ? 'text-green-900' : 'text-red-900'}`}>
                    {importResult.success ? 'Import Completed' : 'Import Failed'}
                  </h4>
                  <div className={`mt-2 text-sm ${importResult.success ? 'text-green-800' : 'text-red-800'}`}>
                    <div className="grid grid-cols-3 gap-4 mb-3">
                      <div>
                        <span className="font-medium">Total Rows:</span> {importResult.totalRows}
                      </div>
                      <div>
                        <span className="font-medium">Successful:</span> {importResult.successfulImports}
                      </div>
                      <div>
                        <span className="font-medium">Failed:</span> {importResult.failedImports}
                      </div>
                    </div>
                    
                    {importResult.errors.length > 0 && (
                      <div className="mb-3">
                        <p className="font-medium mb-2">Errors:</p>
                        <ul className="list-disc list-inside space-y-1 text-xs">
                          {importResult.errors.map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {importResult.warnings.length > 0 && (
                      <div>
                        <p className="font-medium mb-2">Warnings:</p>
                        <ul className="list-disc list-inside space-y-1 text-xs">
                          {importResult.warnings.map((warning, index) => (
                            <li key={index}>{warning}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#2aa45c]"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExcelImportModal;
