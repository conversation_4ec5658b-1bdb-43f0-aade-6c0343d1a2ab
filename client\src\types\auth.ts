// User Types
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  role: 'JTB_ADMIN' | 'FINANCE_OFFICER' | 'SENIOR_FINANCE_OFFICER' | 'PAYER';
  organizationId?: string;
  isActive: boolean;
  createdAt: string;
  lastLogin?: string;
}

// Authentication Status
export interface AuthStatus {
  isAuthenticated: boolean;
  objectId?: string;
  email?: string;
  claims?: Array<{
    type: string;
    value: string;
  }>;
}

// Authentication State
export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error?: string;
}

// Role Types
export type UserRole = 'JTB_ADMIN' | 'FINANCE_OFFICER' | 'SENIOR_FINANCE_OFFICER' | 'PAYER';

// Permission Types
export interface Permissions {
  canManageUsers: boolean;
  canManageOrganizations: boolean;
  canManagePayments: boolean;
  canAcknowledgePayments: boolean;
  canApprovePayments: boolean;
  canViewReports: boolean;
  canManageCompliance: boolean;
}

// Login/Logout Types
export interface LoginOptions {
  authMethod?: 'microsoft' | 'local';
  useRedirect?: boolean;
  prompt?: 'login' | 'select_account' | 'consent' | 'none';
  credentials?: {
    email: string;
    password: string;
  };
}

export interface LogoutOptions {
  authMethod?: 'microsoft' | 'local';
  useRedirect?: boolean;
  postLogoutRedirectUri?: string;
}

// Error Types
export interface AuthError {
  code: string;
  message: string;
  details?: any;
}

// Token Types
export interface TokenInfo {
  accessToken: string;
  expiresOn: Date;
  scopes: string[];
}

// Account Types (from MSAL)
export type { AccountInfo } from '@azure/msal-browser';

// Navigation Types
export interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
  fallback?: React.ReactNode;
  redirectTo?: string;
}

// Hook Return Types
export interface UseAuthReturn {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (options?: LoginOptions) => Promise<void>;
  logout: (options?: LogoutOptions) => Promise<void>;
  refreshUser: () => Promise<void>;
  hasRole: (role: UserRole | UserRole[]) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;
  permissions: Permissions;
  error?: AuthError;
}

// Component Props
export interface LoginButtonProps {
  className?: string;
  children?: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  useRedirect?: boolean;
}

export interface LogoutButtonProps {
  className?: string;
  children?: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  useRedirect?: boolean;
}

export interface UserProfileProps {
  user: User;
  showRole?: boolean;
  showOrganization?: boolean;
  showLastLogin?: boolean;
  className?: string;
}

// Admin Setup Types
export interface AdminSetupResponse {
  message: string;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
  };
}

// Invitation Types
export interface CreateInvitationRequest {
  email: string;
  role: UserRole;
  organizationId?: string;
}

export interface UserInvitation {
  id: string;
  email: string;
  role: UserRole;
  organizationId?: string;
  invitedDate: string;
  status: 'Pending' | 'Accepted' | 'Cancelled';
  invitedBy: string;
  expiryDate: string;
  acceptedDate?: string;
}

// Context Types
export interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (options?: LoginOptions) => Promise<void>;
  logout: (options?: LogoutOptions) => Promise<void>;
  refreshUser: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
  hasRole: (role: UserRole | UserRole[]) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;
  permissions: Permissions;
  error?: AuthError;
}

// Utility Types
export type RolePermissionMap = Record<UserRole, Permissions>;

// Constants
export const USER_ROLES: Record<string, UserRole> = {
  JTB_ADMIN: 'JTB_ADMIN',
  FINANCE_OFFICER: 'FINANCE_OFFICER',
  SENIOR_FINANCE_OFFICER: 'SENIOR_FINANCE_OFFICER',
  PAYER: 'PAYER',
} as const;

export const ROLE_DISPLAY_NAMES: Record<UserRole, string> = {
  JTB_ADMIN: 'Administrator',
  FINANCE_OFFICER: 'Finance Officer',
  SENIOR_FINANCE_OFFICER: 'Senior Finance Officer',
  PAYER: 'Payer',
} as const;

