using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Final_E_Receipt.Services;
using Final_E_Receipt.Payments.Models;
using Final_E_Receipt.Notifications.Services;
using Final_E_Receipt.Notifications.DTOs;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Payments.Services
{
    public class PaymentScheduleService
    {
        private readonly IDatabaseService _dbService;
        private readonly CentralizedEmailService _emailService;
        private readonly ILogger<PaymentScheduleService> _logger;

        public PaymentScheduleService(
            IDatabaseService dbService,
            CentralizedEmailService emailService,
            ILogger<PaymentScheduleService> logger)
        {
            _dbService = dbService;
            _emailService = emailService;
            _logger = logger;
        }

        public async Task<PaymentSchedule> CreatePaymentSchedule(PaymentSchedule schedule)
        {
            var parameters = new
            {
                Id = schedule.Id ?? Guid.NewGuid().ToString(),
                PaymentProfileId = schedule.PaymentProfileId,
                OrganizationId = schedule.OrganizationId,
                Amount = schedule.Amount,
                Currency = schedule.Currency,
                DueDate = schedule.DueDate,
                Status = schedule.Status ?? "PENDING",
                CreatedBy = schedule.CreatedBy
            };

            var createdSchedule = await _dbService.QueryFirstOrDefaultAsync<PaymentSchedule>("CreatePaymentSchedule", parameters);

            if (createdSchedule == null)
                return null;

            // Send email notification (fire and forget - don't fail schedule creation if email fails)
            _ = Task.Run(async () =>
            {
                try
                {
                    await SendPaymentScheduleCreatedNotification(createdSchedule);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send notification for payment schedule {ScheduleId}", createdSchedule.Id);
                }
            });

            return createdSchedule;
        }

        public async Task<PaymentSchedule> GetPaymentScheduleById(string id)
        {
            var parameters = new { Id = id };
            return await _dbService.QueryFirstOrDefaultAsync<PaymentSchedule>("GetPaymentScheduleById", parameters);
        }

        public async Task<List<PaymentSchedule>> GetPaymentSchedulesByOrganization(string organizationId)
        {
            var parameters = new { OrganizationId = organizationId };
            var schedules = await _dbService.QueryAsync<PaymentSchedule>("GetPaymentSchedulesByOrganization", parameters);
            return schedules.ToList();
        }

        public async Task<List<PaymentSchedule>> GetPaymentSchedulesByProfile(string profileId)
        {
            var parameters = new { PaymentProfileId = profileId };
            var schedules = await _dbService.QueryAsync<PaymentSchedule>("GetPaymentSchedulesByProfile", parameters);
            return schedules.ToList();
        }

        public async Task<PaymentSchedule> UpdatePaymentScheduleStatus(string id, string status, string paymentId, string updatedBy)
        {
            var parameters = new
            {
                Id = id,
                Status = status,
                PaymentId = paymentId,
                UpdatedBy = updatedBy
            };

            var updatedSchedule = await _dbService.QueryFirstOrDefaultAsync<PaymentSchedule>("UpdatePaymentScheduleStatus", parameters);

            if (updatedSchedule == null)
                return null;

            // Send email notification for status updates (fire and forget)
            _ = Task.Run(async () =>
            {
                try
                {
                    await SendPaymentScheduleUpdatedNotification(updatedSchedule, $"Status updated to {status}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send update notification for payment schedule {ScheduleId}", updatedSchedule.Id);
                }
            });

            return updatedSchedule;
        }

        public async Task<bool> DeletePaymentSchedule(string id)
        {
            var parameters = new { Id = id };

            try
            {
                await _dbService.ExecuteAsync("DeletePaymentSchedule", parameters);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Send payment schedule created notification to organization users
        /// </summary>
        private async Task SendPaymentScheduleCreatedNotification(PaymentSchedule schedule)
        {
            try
            {
                // Get payment profile name for the notification
                var profileName = await GetPaymentProfileName(schedule.PaymentProfileId);

                // Get all PAYER users in the organization
                var payerUsers = await GetPayerUsersInOrganization(schedule.OrganizationId);

                foreach (var payerUserId in payerUsers)
                {
                    await _emailService.SendPaymentScheduleCreatedNotificationAsync(
                        schedule.OrganizationId,
                        payerUserId,
                        profileName,
                        schedule.Amount,
                        schedule.DueDate,
                        schedule.Currency
                    );
                }

                _logger.LogInformation("Payment schedule created notifications sent for schedule {ScheduleId} to {UserCount} users",
                    schedule.Id, payerUsers.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending payment schedule created notifications for schedule {ScheduleId}", schedule.Id);
                throw;
            }
        }

        /// <summary>
        /// Send payment schedule updated notification to organization users
        /// </summary>
        private async Task SendPaymentScheduleUpdatedNotification(PaymentSchedule schedule, string reason)
        {
            try
            {
                // Get payment profile name for the notification
                var profileName = await GetPaymentProfileName(schedule.PaymentProfileId);

                // Send notifications to all PAYER users in the organization
                var payerUsers = await GetPayerUsersInOrganization(schedule.OrganizationId);

                foreach (var payerUserId in payerUsers)
                {
                    await _emailService.SendPaymentScheduleUpdatedNotificationAsync(
                        schedule.OrganizationId,
                        payerUserId,
                        profileName,
                        schedule.Amount,
                        schedule.DueDate,
                        schedule.Currency
                    );
                }

                _logger.LogInformation("Payment schedule updated notifications sent for schedule {ScheduleId} to {UserCount} users",
                    schedule.Id, payerUsers.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending payment schedule updated notifications for schedule {ScheduleId}", schedule.Id);
                throw;
            }
        }

        /// <summary>
        /// Get payment profile name by ID
        /// </summary>
        private async Task<string> GetPaymentProfileName(string paymentProfileId)
        {
            try
            {
                var parameters = new { Id = paymentProfileId };
                var result = await _dbService.QueryFirstOrDefaultAsync<dynamic>("GetPaymentProfileById", parameters);

                return result?.Name?.ToString() ?? "Unknown Profile";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment profile name for ID {ProfileId}", paymentProfileId);
                return "Unknown Profile";
            }
        }

        /// <summary>
        /// Get all PAYER users in an organization
        /// </summary>
        private async Task<List<string>> GetPayerUsersInOrganization(string organizationId)
        {
            try
            {
                var parameters = new
                {
                    OrganizationId = organizationId,
                    Role = "PAYER"
                };

                var users = await _dbService.QueryAsync<dynamic>("GetUsersByOrganizationAndRole", parameters);
                return users.Select(u => (string)u.Id.ToString()).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payer users for organization {OrganizationId}", organizationId);
                return new List<string>();
            }
        }
    }
}
