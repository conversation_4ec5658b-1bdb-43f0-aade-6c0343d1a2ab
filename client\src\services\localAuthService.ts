import { apiService } from './apiService';
import type { User } from '../types/auth';

export interface LocalLoginCredentials {
  email: string;
  password: string;
}

export interface LocalLoginResponse {
  user: User;
  token?: string;
}

class LocalAuthService {
  /**
   * Login with email and password
   */
  async login(credentials: LocalLoginCredentials): Promise<LocalLoginResponse> {
    try {
      const response = await apiService.post<LocalLoginResponse>('/auth/local/login', credentials);
      
      // Store token if provided (for local auth)
      if (response.token) {
        localStorage.setItem('localAuthToken', response.token);
      }
      
      return response;
    } catch (error) {
      console.error('Local login failed:', error);
      throw error;
    }
  }

  /**
   * Logout local user
   */
  async logout(): Promise<void> {
    try {
      // Clear local token
      localStorage.removeItem('localAuthToken');
      
      // Call logout endpoint if needed
      await apiService.post('/auth/local/logout');
    } catch (error) {
      console.error('Local logout failed:', error);
      // Still clear token even if API call fails
      localStorage.removeItem('localAuthToken');
    }
  }

  /**
   * Get stored local token
   */
  getLocalToken(): string | null {
    return localStorage.getItem('localAuthToken');
  }

  /**
   * Check if user has local token
   */
  hasLocalToken(): boolean {
    return !!this.getLocalToken();
  }
}

export const localAuthService = new LocalAuthService();