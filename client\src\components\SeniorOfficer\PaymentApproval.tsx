import React, { useState } from 'react';
import { 
  CheckCircle,
  XCircle,
  FileText,
} from 'lucide-react';


interface Payment {
  id: string;
  payerName: string;
  amount: number;
  description: string;
  requestDate: string;
  priority: 'low' | 'medium' | 'high';
  category: string;
  requestedBy: string;
}
// Payment Approval Page Components
interface PaymentListSidebarProps {
  payments: Payment[];
  selectedPayment: Payment | null;
  onSelectPayment: (payment: Payment) => void;
}

const pendingPayments: Payment[] = [
  {
    id: 'PAY-001',
    payerName: '<PERSON>',
    amount: 1250.00,
    description: 'Monthly service fee payment',
    requestDate: '2025-06-28',
    priority: 'high',
    category: 'Service Fees',
    requestedBy: 'Finance Officer A'
  },
  {
    id: 'PAY-002',
    payerName: 'ABC Corporation',
    amount: 5000.00,
    description: 'Quarterly subscription payment',
    requestDate: '2025-06-27',
    priority: 'medium',
    category: 'Subscriptions',
    requestedBy: 'Finance Officer B'
  },
  {
    id: 'PAY-003',
    payerName: '<PERSON>',
    amount: 750.50,
    description: 'Utility bill payment',
    requestDate: '2025-06-29',
    priority: 'low',
    category: 'Utilities',
    requestedBy: 'Finance Officer A'
  }
];

const PaymentListSidebar: React.FC<PaymentListSidebarProps> = ({
  payments,
  selectedPayment,
  onSelectPayment
}) => (
  <div className="bg-white rounded-lg shadow-sm border border-gray-200">
    <div className="p-4 border-b border-gray-200">
      <h3 className="font-semibold" style={{ color: '#045024' }}>Pending Payments</h3>
    </div>
    <div className="divide-y divide-gray-200">
      {payments.map((payment) => (
        <button
          key={payment.id}
          onClick={() => onSelectPayment(payment)}
          className={`w-full p-4 text-left hover:bg-gray-50 ${
            selectedPayment?.id === payment.id ? 'bg-[#045024] bg-opacity-5 border-r-4 border-[#045024]' : ''
          }`}
        >
          <div className="flex justify-between items-start mb-2">
            <h4 className="font-medium text-gray-900">{payment.payerName}</h4>
            <span className="text-sm font-bold" style={{ color: '#045024' }}>
              ₦{payment.amount.toLocaleString()}
            </span>
          </div>
          <p className="text-sm text-gray-600 truncate">{payment.description}</p>
          <p className="text-xs text-gray-500 mt-1">{payment.id}</p>
        </button>
      ))}
    </div>
  </div>
);

interface PaymentReviewDetailsProps {
  payment: Payment;
  approvalNotes: string;
  setApprovalNotes: (notes: string) => void;
  onApprove: () => void;
  onReject: () => void;
}

const PaymentReviewDetails: React.FC<PaymentReviewDetailsProps> = ({
  payment,
  approvalNotes,
  setApprovalNotes,
  onApprove,
  onReject
}) => (
  <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
    <div className="flex items-center justify-between mb-6">
      <h3 className="text-xl font-semibold" style={{ color: '#045024' }}>Payment Review</h3>
      <span className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium">
        Pending Approval
      </span>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Payment ID</label>
        <p className="text-gray-900">{payment.id}</p>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Payer Name</label>
        <p className="text-gray-900">{payment.payerName}</p>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Amount</label>
        <p className="text-2xl font-bold" style={{ color: '#045024' }}>
          ₦{payment.amount.toLocaleString()}
        </p>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          payment.priority === 'high' ? 'bg-red-100 text-red-800' :
          payment.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
          'bg-green-100 text-green-800'
        }`}>
          {payment.priority.toUpperCase()}
        </span>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
        <p className="text-gray-900">{payment.category}</p>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Request Date</label>
        <p className="text-gray-900">{new Date(payment.requestDate).toLocaleDateString()}</p>
      </div>
      <div className="md:col-span-2">
        <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
        <p className="text-gray-900">{payment.description}</p>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Requested By</label>
        <p className="text-gray-900">{payment.requestedBy}</p>
      </div>
    </div>

    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">Approval Notes</label>
      <textarea
        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#045024] focus:border-[#045024]"
        rows={4}
        placeholder="Add notes for approval or rejection..."
        value={approvalNotes}
        onChange={(e) => setApprovalNotes(e.target.value)}
      />
    </div>

    <div className="flex gap-4">
      <button
        onClick={onApprove}
        className="flex items-center gap-2 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
      >
        <CheckCircle size={20} />
        Approve Payment
      </button>
      <button
        onClick={onReject}
        className="flex items-center gap-2 bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors"
      >
        <XCircle size={20} />
        Reject Payment
      </button>
    </div>
  </div>
);

const PaymentApprovalPage: React.FC = () => {
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(pendingPayments[0]);
  const [approvalNotes, setApprovalNotes] = useState('');

  const handleApprove = () => {
    if (selectedPayment) {
      alert(`Payment ${selectedPayment.id} approved successfully!`);
      setApprovalNotes('');
    }
  };

  const handleReject = () => {
    if (!selectedPayment) return;
    
    if (!approvalNotes.trim()) {
      alert('Please provide rejection notes.');
      return;
    }
    alert(`Payment ${selectedPayment.id} rejected.`);
    setApprovalNotes('');
  };

  if (!selectedPayment) {
    return (
      <div className="bg-white rounded-lg p-8 text-center">
        <FileText className="mx-auto mb-4 text-gray-400" size={48} />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Payment Selected</h3>
        <p className="text-gray-600">Select a payment from the pending approvals to review.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <PaymentListSidebar
        payments={pendingPayments}
        selectedPayment={selectedPayment}
        onSelectPayment={setSelectedPayment}
      />
      <div className="lg:col-span-2">
        <PaymentReviewDetails
          payment={selectedPayment}
          approvalNotes={approvalNotes}
          setApprovalNotes={setApprovalNotes}
          onApprove={handleApprove}
          onReject={handleReject}
        />
      </div>
    </div>
  );
}
export default PaymentApprovalPage