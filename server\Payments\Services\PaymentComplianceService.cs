using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Final_E_Receipt.Payments.Models;
using Final_E_Receipt.Compliance.Services;
using Final_E_Receipt.Compliance.DTOs;
using Final_E_Receipt.Services;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Payments.Services
{
    public class PaymentComplianceService
    {
        private readonly PaymentScheduleService _scheduleService;
        private readonly ComplianceCertificateFileService _certificateFileService;
        private readonly IDatabaseService _dbService;
        private readonly ILogger<PaymentComplianceService> _logger;

        public PaymentComplianceService(
            PaymentScheduleService scheduleService,
            ComplianceCertificateFileService certificateFileService,
            IDatabaseService dbService,
            ILogger<PaymentComplianceService> logger)
        {
            _scheduleService = scheduleService;
            _certificateFileService = certificateFileService;
            _dbService = dbService;
            _logger = logger;
        }

        /// <summary>
        /// Checks if a completed payment makes an organization eligible for a compliance certificate
        /// </summary>
        public async Task<bool> CheckAndCreateComplianceCertificate(Payment payment)
        {
            try
            {
                if (payment.Status != "Completed" || string.IsNullOrEmpty(payment.PaymentScheduleId))
                    return false;

                // Get payment schedule to determine payment profile
                var schedule = await _scheduleService.GetPaymentScheduleById(payment.PaymentScheduleId);
                if (schedule == null)
                {
                    _logger.LogWarning("No payment schedule found for payment {PaymentId}", payment.Id);
                    return false;
                }

                // Check if organization has completed all required payments for this profile
                var isEligible = await CheckCertificateEligibility(payment.OrganizationId, schedule.PaymentProfileId);
                if (!isEligible)
                {
                    _logger.LogInformation("Organization {OrganizationId} not yet eligible for certificate", payment.OrganizationId);
                    return false;
                }

                // Create compliance certificate
                var certificate = await CreateComplianceCertificateFromPayments(payment.OrganizationId, schedule.PaymentProfileId);
                if (certificate != null)
                {
                    _logger.LogInformation(
                        "Compliance certificate {CertificateNumber} created for organization {OrganizationId}",
                        certificate.Certificate.CertificateNumber, payment.OrganizationId);

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking compliance certificate eligibility for payment {PaymentId}", payment.Id);
                return false;
            }
        }

        /// <summary>
        /// Checks if an organization is eligible for a compliance certificate
        /// </summary>
        public async Task<bool> CheckCertificateEligibility(string organizationId, string paymentProfileId)
        {
            try
            {
                // Get all payment schedules for this organization and profile
                var schedules = await _scheduleService.GetPaymentSchedulesByOrganization(organizationId);
                var profileSchedules = schedules.Where(s => s.PaymentProfileId == paymentProfileId).ToList();

                if (!profileSchedules.Any())
                    return false;

                // Check if all schedules have been paid
                var allPaid = profileSchedules.All(schedule =>
                    !string.IsNullOrEmpty(schedule.PaymentId) && schedule.Status == "PAID");

                return allPaid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking certificate eligibility for organization {OrganizationId}", organizationId);
                return false;
            }
        }

        /// <summary>
        /// Creates a compliance certificate based on completed payments
        /// </summary>
        public async Task<ComplianceCertificateWithFilesDTO> CreateComplianceCertificateFromPayments(string organizationId, string paymentProfileId)
        {
            try
            {
                // Calculate total amount from completed payments
                var totalAmount = await CalculateTotalPaidAmount(organizationId, paymentProfileId);

                // Determine certificate type and validity period
                var certificateType = "PAYMENT_COMPLIANCE";
                var (validFrom, validUntil) = CalculateValidityPeriod(certificateType);

                var createDto = new CreateComplianceCertificateDTO
                {
                    OrganizationId = organizationId,
                    PaymentProfileId = paymentProfileId,
                    CertificateType = certificateType,
                    TotalAmount = totalAmount,
                    Currency = "NGN",
                    ValidFrom = validFrom,
                    ValidUntil = validUntil,
                    Description = $"Payment compliance certificate",
                    Terms = GenerateStandardTerms(certificateType),
                    ComplianceYear = DateTime.Now.Year.ToString(),
                    CompliancePeriod = DetermineCompliancePeriod(certificateType),
                    RegulatoryBody = "Jamaica Tourist Board",
                    LicenseCategory = "GENERAL",
                    Notes = $"Auto-generated from completed payments for profile {paymentProfileId}"
                };

                return await _certificateFileService.CreateCertificateWithFiles(createDto, "SYSTEM_AUTO_GENERATED");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating compliance certificate from payments");
                return null;
            }
        }

        /// <summary>
        /// Gets payment compliance status for an organization
        /// </summary>
        public async Task<PaymentComplianceStatus> GetPaymentComplianceStatus(string organizationId, string paymentProfileId)
        {
            try
            {
                var schedules = await _scheduleService.GetPaymentSchedulesByOrganization(organizationId);
                var profileSchedules = schedules.Where(s => s.PaymentProfileId == paymentProfileId).ToList();

                var totalSchedules = profileSchedules.Count;
                var paidSchedules = profileSchedules.Count(s => s.Status == "PAID");
                var totalAmount = profileSchedules.Sum(s => s.Amount);
                var paidAmount = profileSchedules.Where(s => s.Status == "PAID").Sum(s => s.Amount);

                return new PaymentComplianceStatus
                {
                    OrganizationId = organizationId,
                    PaymentProfileId = paymentProfileId,
                    TotalSchedules = totalSchedules,
                    PaidSchedules = paidSchedules,
                    PendingSchedules = totalSchedules - paidSchedules,
                    TotalAmount = totalAmount,
                    PaidAmount = paidAmount,
                    OutstandingAmount = totalAmount - paidAmount,
                    CompliancePercentage = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0,
                    IsEligibleForCertificate = paidSchedules == totalSchedules && totalSchedules > 0,
                    NextDueDate = profileSchedules.Where(s => s.Status != "PAID").OrderBy(s => s.DueDate).FirstOrDefault()?.DueDate
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment compliance status");
                return new PaymentComplianceStatus { OrganizationId = organizationId, PaymentProfileId = paymentProfileId };
            }
        }

        // Helper methods
        private async Task<decimal> CalculateTotalPaidAmount(string organizationId, string paymentProfileId)
        {
            try
            {
                var schedules = await _scheduleService.GetPaymentSchedulesByOrganization(organizationId);
                var profileSchedules = schedules.Where(s => s.PaymentProfileId == paymentProfileId && s.Status == "PAID");
                return profileSchedules.Sum(s => s.Amount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating total paid amount for organization {OrganizationId}", organizationId);
                return 0;
            }
        }

        private (DateTime validFrom, DateTime validUntil) CalculateValidityPeriod(string certificateType)
        {
            var now = DateTime.Now;
            return (now, now.AddYears(1)); // Default to 1 year validity
        }

        private string DetermineCompliancePeriod(string certificateType)
        {
            return "ANNUAL";
        }

        private string GenerateStandardTerms(string certificateType)
        {
            return "This certificate confirms compliance with applicable regulations and is valid for one year from the date of issue.";
        }
    }

    // Supporting class
    public class PaymentComplianceStatus
    {
        public string OrganizationId { get; set; }
        public string PaymentProfileId { get; set; }
        public int TotalSchedules { get; set; }
        public int PaidSchedules { get; set; }
        public int PendingSchedules { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public decimal CompliancePercentage { get; set; }
        public bool IsEligibleForCertificate { get; set; }
        public DateTime? NextDueDate { get; set; }
    }
}
