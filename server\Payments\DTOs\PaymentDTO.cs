using System;

namespace Final_E_Receipt.Payments.DTOs
{
    public class PaymentDTO
    {
        public string Id { get; set; }
        public string PayerId { get; set; }
        public string PayerName { get; set; }
        public string PayerEmail { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; }
        public string TransactionReference { get; set; }
        public string PaymentMethod { get; set; }
        public string Status { get; set; }
        public string Description { get; set; }
        public string PaymentTypeId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string ReceiptId { get; set; }
        public string OrganizationId { get; set; }
        public string ProofFileId { get; set; }

        // Approval Workflow Fields
        public string AcknowledgedBy { get; set; }
        public DateTime? AcknowledgedDate { get; set; }
        public string AcknowledgedNotes { get; set; }  // CHANGED from AcknowledgmentNotes

        public string ApprovedBy { get; set; }
        public DateTime? ApprovedDate { get; set; }
        public string ApprovalNotes { get; set; }

        public string RejectedBy { get; set; }
        public DateTime? RejectedDate { get; set; }
        public string RejectionReason { get; set; }

        public string PaymentScheduleId { get; set; }
    }

    public class CreatePaymentDTO
    {
        public string PayerId { get; set; }
        public string PayerName { get; set; }
        public string PayerEmail { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "NGN";
        public string PaymentMethod { get; set; }
        public string Description { get; set; }
        public string PaymentTypeId { get; set; }
        public string OrganizationId { get; set; }
    }

    public class UpdatePaymentStatusDTO
    {
        public string Status { get; set; }
    }

    // Approval Workflow DTOs
    public class AcknowledgePaymentDTO
    {
        public string Notes { get; set; }
    }

    public class ApprovePaymentDTO
    {
        public string Notes { get; set; }
    }

    public class RejectPaymentDTO
    {
        public string Reason { get; set; }
        public string Notes { get; set; }
    }
}


