import React, { useState, useEffect } from 'react';
import { 
  CheckCircle, 
  XCircle, 
  Eye, 
  Download, 
  AlertTriangle, 
  Clock,
  FileText,
  Image,
  MessageSquare,
  User
} from 'lucide-react';
import { usePaymentApi, useFilesApi } from '../../hooks/api';
import { useAuth } from '../../contexts/AuthContext';

interface PaymentProofFile {
  id: string;
  fileName: string;
  originalFileName: string;
  fileSize: number;
  contentType: string;
  uploadedAt: string;
  uploadedBy: string;
  isValidated: boolean;
  validatedBy?: string;
  validatedAt?: string;
  validationNotes?: string;
  isApproved?: boolean;
}

interface PaymentProofValidationProps {
  paymentId: string;
  paymentReference: string;
  payerName: string;
  amount: number;
  currency: string;
  onValidationComplete?: (isApproved: boolean) => void;
  className?: string;
}

const PaymentProofValidation: React.FC<PaymentProofValidationProps> = ({
  paymentId,
  paymentReference,
  payerName,
  amount,
  currency,
  onValidationComplete,
  className = '',
}) => {
  const { updatePaymentStatus, loading: paymentLoading } = usePaymentApi();
  const { getFilesByEntity, downloadFile, loading: fileLoading } = useFilesApi();
  const { user } = useAuth();
  
  const [proofFiles, setProofFiles] = useState<PaymentProofFile[]>([]);
  const [selectedFile, setSelectedFile] = useState<PaymentProofFile | null>(null);
  const [validationNotes, setValidationNotes] = useState('');
  const [validating, setValidating] = useState(false);

  // Role-based permissions
  const canValidate = ['JTB_ADMIN', 'FINANCE_OFFICER', 'SENIOR_FINANCE_OFFICER'].includes(user?.role || '');

  useEffect(() => {
    loadPaymentProofFiles();
  }, [paymentId]);

  const loadPaymentProofFiles = async () => {
    const files = await getFilesByEntity('PAYMENT', paymentId);
    if (files) {
      setProofFiles(files.filter(file => file.category === 'PAYMENT_PROOF'));
    }
  };

  const handleValidateProof = async (fileId: string, isApproved: boolean) => {
    if (!canValidate) return;
    
    try {
      setValidating(true);
      
      // Update file validation status
      // Note: This would need a backend API endpoint for file validation
      // For now, we'll update the payment status directly
      
      const newStatus = isApproved ? 'ACKNOWLEDGED' : 'REJECTED';
      await updatePaymentStatus(paymentId, newStatus, validationNotes);
      
      // Update local state
      setProofFiles(prev => prev.map(file => 
        file.id === fileId 
          ? { 
              ...file, 
              isValidated: true, 
              isApproved, 
              validatedBy: user?.name || 'Unknown',
              validatedAt: new Date().toISOString(),
              validationNotes 
            }
          : file
      ));
      
      setValidationNotes('');
      onValidationComplete?.(isApproved);
      
    } catch (error) {
      console.error('Validation failed:', error);
    } finally {
      setValidating(false);
    }
  };

  const handleDownloadFile = async (file: PaymentProofFile) => {
    try {
      await downloadFile(file.id, file.originalFileName);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith('image/')) {
      return <Image size={20} className="text-blue-500" />;
    }
    return <FileText size={20} className="text-gray-500" />;
  };

  const getValidationStatus = (file: PaymentProofFile) => {
    if (!file.isValidated) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          <Clock size={12} className="mr-1" />
          Pending Review
        </span>
      );
    }
    
    if (file.isApproved) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <CheckCircle size={12} className="mr-1" />
          Approved
        </span>
      );
    }
    
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
        <XCircle size={12} className="mr-1" />
        Rejected
      </span>
    );
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatCurrency = (amount: number, currency: string) => {
    return `${currency === 'NGN' ? '₦' : currency} ${amount.toLocaleString()}`;
  };

  if (proofFiles.length === 0) {
    return (
      <div className={`bg-gray-50 rounded-lg p-6 text-center ${className}`}>
        <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">No payment proof files uploaded yet</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Payment Information */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-500">Reference</label>
            <p className="mt-1 text-sm font-mono text-gray-900">{paymentReference}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">Payer</label>
            <p className="mt-1 text-sm text-gray-900">{payerName}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">Amount</label>
            <p className="mt-1 text-sm font-medium text-gray-900">{formatCurrency(amount, currency)}</p>
          </div>
        </div>
      </div>

      {/* Proof Files */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Proof Files</h3>
        <div className="space-y-4">
          {proofFiles.map((file) => (
            <div key={file.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  {getFileIcon(file.contentType)}
                  <div>
                    <p className="text-sm font-medium text-gray-900">{file.originalFileName}</p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.fileSize)} • Uploaded {new Date(file.uploadedAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getValidationStatus(file)}
                  <button
                    onClick={() => handleDownloadFile(file)}
                    disabled={fileLoading}
                    className="text-blue-600 hover:text-blue-800 disabled:opacity-50"
                    title="Download file"
                  >
                    <Download size={16} />
                  </button>
                </div>
              </div>

              {/* Validation Information */}
              {file.isValidated && (
                <div className="bg-gray-50 rounded-lg p-3 mb-3">
                  <div className="flex items-center space-x-2 mb-2">
                    <User size={14} className="text-gray-400" />
                    <span className="text-sm text-gray-600">
                      Validated by {file.validatedBy} on {file.validatedAt ? new Date(file.validatedAt).toLocaleDateString() : 'Unknown'}
                    </span>
                  </div>
                  {file.validationNotes && (
                    <div className="flex items-start space-x-2">
                      <MessageSquare size={14} className="text-gray-400 mt-0.5" />
                      <p className="text-sm text-gray-600">{file.validationNotes}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Validation Actions */}
              {canValidate && !file.isValidated && (
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Validation Notes
                    </label>
                    <textarea
                      value={validationNotes}
                      onChange={(e) => setValidationNotes(e.target.value)}
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                      placeholder="Add notes about the validation..."
                    />
                  </div>
                  <div className="flex space-x-3">
                    <button
                      onClick={() => handleValidateProof(file.id, true)}
                      disabled={validating || paymentLoading}
                      className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 disabled:opacity-50"
                    >
                      <CheckCircle size={16} />
                      <span>Approve</span>
                    </button>
                    <button
                      onClick={() => handleValidateProof(file.id, false)}
                      disabled={validating || paymentLoading}
                      className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center space-x-2 disabled:opacity-50"
                    >
                      <XCircle size={16} />
                      <span>Reject</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PaymentProofValidation;
