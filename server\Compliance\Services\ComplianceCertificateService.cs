using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Final_E_Receipt.Compliance.Models;
using Final_E_Receipt.Compliance.DTOs;
using Final_E_Receipt.Services;
using Final_E_Receipt.Notifications.Services;
using Final_E_Receipt.Documents.Services;
using Final_E_Receipt.Files.Services;
using Final_E_Receipt.Organizations.Services;
using Microsoft.Extensions.Logging;
using Dapper;

namespace Final_E_Receipt.Compliance.Services
{
    public class ComplianceCertificateService
    {
        private readonly IDatabaseService _dbService;
        private readonly BrandedDocumentService _brandedDocumentService;
        private readonly FileService _fileService;
        private readonly CentralizedEmailService _emailService;
        private readonly OrganizationService _organizationService;
        private readonly ILogger<ComplianceCertificateService> _logger;

        public ComplianceCertificateService(
            IDatabaseService dbService,
            BrandedDocumentService brandedDocumentService,
            FileService fileService,
            CentralizedEmailService emailService,
            OrganizationService organizationService,
            ILogger<ComplianceCertificateService> logger)
        {
            _dbService = dbService;
            _brandedDocumentService = brandedDocumentService;
            _fileService = fileService;
            _emailService = emailService;
            _organizationService = organizationService;
            _logger = logger;
        }

        public async Task<ComplianceCertificate> CreateComplianceCertificate(
    CreateComplianceCertificateDTO dto,
    string createdBy)
        {
            try
            {
                var certificateId = Guid.NewGuid().ToString();
                var organizationName = await GetOrganizationName(dto.OrganizationId);
                var paymentProfileName = await GetPaymentProfileName(dto.PaymentProfileId);

                var certificate = await _dbService.QueryFirstOrDefaultAsync<ComplianceCertificate>(
                    "CreateComplianceCertificate",
                    new
                    {
                        Id = certificateId,
                        OrganizationId = dto.OrganizationId,
                        OrganizationName = organizationName,
                        PaymentProfileId = dto.PaymentProfileId,
                        PaymentProfileName = paymentProfileName,
                        CertificateType = dto.CertificateType,
                        TotalAmount = dto.TotalAmount,
                        Currency = dto.Currency,
                        ValidFrom = dto.ValidFrom,
                        ValidUntil = dto.ValidUntil,
                        IssuedBy = createdBy,
                        Description = dto.Description,
                        Terms = dto.Terms,
                        CreatedBy = createdBy,
                        ComplianceYear = dto.ComplianceYear,
                        CompliancePeriod = dto.CompliancePeriod,
                        RegulatoryBody = dto.RegulatoryBody,
                        LicenseCategory = dto.LicenseCategory,
                        Notes = dto.Notes
                    });

                if (certificate != null)
                {
                    await GenerateAndStoreBrandedCertificate(certificate);
                    _logger.LogInformation("Compliance certificate created for organization {OrganizationId}",
                        dto.OrganizationId);
                }

                return certificate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating compliance certificate for organization {OrganizationId}",
                    dto.OrganizationId);
                throw;
            }
        }
        /// <summary>
        /// Generate branded certificate PDF and store as file
        /// </summary>
        private async Task GenerateAndStoreBrandedCertificate(ComplianceCertificate certificate)
        {
            try
            {
                // Generate branded certificate using the unified document service
                var certificatePdf = await _brandedDocumentService.GenerateComplianceCertificate(certificate);

                // Store the PDF as a file
                var fileName = $"certificate-{certificate.CertificateNumber}.pdf";

                // Create a temporary file to upload
                var tempFilePath = Path.GetTempFileName();
                await File.WriteAllBytesAsync(tempFilePath, certificatePdf);

                using var stream = new FileStream(tempFilePath, FileMode.Open);
                var formFile = new Microsoft.AspNetCore.Http.FormFile(stream, 0, stream.Length, "file", fileName)
                {
                    Headers = new Microsoft.AspNetCore.Http.HeaderDictionary(),
                    ContentType = "application/pdf"
                };

                // Upload the file
                var uploadedFile = await _fileService.UploadFile(
                    formFile,
                    "CERTIFICATE",
                    certificate.Id,
                    certificate.CreatedBy,
                    certificate.OrganizationId,
                    $"Branded certificate PDF for {certificate.CertificateNumber}",
                    "CERTIFICATE_PDF"
                );

                // Update certificate with PDF file ID
                await UpdateCertificatePdfFileId(certificate.Id, uploadedFile.Id);

                // Clean up temp file
                File.Delete(tempFilePath);

                _logger.LogInformation("Branded certificate PDF generated and stored for certificate {CertificateNumber}", certificate.CertificateNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating branded certificate for {CertificateNumber}", certificate.CertificateNumber);
                // Don't throw - certificate creation should succeed even if PDF generation fails
            }
        }

        /// <summary>
        /// Update certificate with PDF file ID
        /// </summary>
        private async Task UpdateCertificatePdfFileId(string certificateId, string pdfFileId)
        {
            try
            {
                await _dbService.ExecuteAsync("UpdateCertificatePdfFileId", 
                    new { Id = certificateId, CertificatePdfFileId = pdfFileId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating certificate PDF file ID for certificate {CertificateId}", certificateId);
            }
        }

        public async Task<ComplianceCertificate> GetComplianceCertificateById(string id)
        {
            return await _dbService.QueryFirstOrDefaultAsync<ComplianceCertificate>(
                "GetComplianceCertificateById", new { Id = id });
        }

        public async Task<List<ComplianceCertificate>> GetComplianceCertificatesByOrganization(string organizationId, int pageNumber = 1, int pageSize = 50)
        {
            var parameters = new
            {
                OrganizationId = organizationId,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var certificates = await _dbService.QueryAsync<ComplianceCertificate>(
                "GetComplianceCertificatesByOrganization", parameters);

            return certificates.ToList();
        }

        public async Task<List<ComplianceCertificate>> SearchComplianceCertificates(CertificateSearchDTO searchDto)
        {
            var certificates = await _dbService.QueryAsync<ComplianceCertificate>(
                "SearchComplianceCertificates", searchDto);

            return certificates.ToList();
        }

        public async Task<ComplianceCertificate> UpdateCertificateStatus(string id, string status, string updatedBy)
        {
            var parameters = new { Id = id, Status = status, UpdatedBy = updatedBy };

            var certificate = await _dbService.QueryFirstOrDefaultAsync<ComplianceCertificate>(
                "UpdateComplianceCertificateStatus", parameters);

            if (certificate != null && status == "ISSUED")
            {
                try
                {
                    // Get organization email
                    var organization = await _organizationService.GetOrganizationById(certificate.OrganizationId);
                    if (organization?.Email != null)
                    {
                        await _emailService.SendCertificateIssuedNotificationAsync(
                            certificate.OrganizationId,
                            organization.Email,
                            certificate.OrganizationName,
                            certificate.CertificateNumber,
                            certificate.CertificateType);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error sending certificate issued notification for {CertificateId}", id);
                }
            }

            return certificate;
        }

        public async Task<ComplianceCertificate> UpdateCertificatePdfFile(string id, string pdfFileId, string updatedBy)
        {
            var parameters = new
            {
                Id = id,
                CertificatePdfFileId = pdfFileId,
                UpdatedBy = updatedBy
            };

            return await _dbService.QueryFirstOrDefaultAsync<ComplianceCertificate>(
                "UpdateComplianceCertificatePdfFile", parameters);
        }

        public async Task<ComplianceCertificate> RevokeCertificate(string id, string revokedBy, string reason)
        {
            var parameters = new { Id = id, RevokedBy = revokedBy, RevokedReason = reason };

            var certificate = await _dbService.QueryFirstOrDefaultAsync<ComplianceCertificate>(
                "RevokeComplianceCertificate", parameters);

            if (certificate != null)
            {
                _logger.LogInformation("Compliance certificate revoked: {CertificateNumber} by {RevokedBy}",
                    certificate.CertificateNumber, revokedBy);

                try
                {
                    // Get organization email
                    var organization = await _organizationService.GetOrganizationById(certificate.OrganizationId);
                    if (organization?.Email != null)
                    {
                        await _emailService.SendCertificateRevokedNotificationAsync(
                            certificate.OrganizationId,
                            organization.Email,
                            certificate.OrganizationName,
                            certificate.CertificateNumber,
                            certificate.CertificateType,
                            certificate.RevokedReason);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error sending certificate revocation notification for {CertificateId}", id);
                }
            }

            return certificate;
        }

        public async Task<List<ComplianceCertificate>> GetExpiringCertificates(int daysFromNow = 30)
        {
            var certificates = await _dbService.QueryAsync<ComplianceCertificate>(
                "GetExpiringCertificates", new { DaysFromNow = daysFromNow });

            return certificates.ToList();
        }

        private async Task<string> GenerateCertificateNumber(string certificateType, string complianceYear)
        {
            // This would typically call a stored function, but for simplicity, we'll generate it here
            var typePrefix = certificateType switch
            {
                "ANNUAL_LICENSE" => "AL",
                "QUARTERLY_COMPLIANCE" => "QC",
                "TAX_CLEARANCE" => "TC",
                _ => "CC"
            };

            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var random = new Random().Next(1000, 9999);
            
            return $"{typePrefix}-{complianceYear}-{random}";
        }

        private async Task<string> GetOrganizationName(string organizationId)
        {
            var result = await _dbService.QueryFirstOrDefaultAsync<dynamic>(
                "SELECT Name FROM Organizations WHERE Id = @Id", new { Id = organizationId });
            return result?.Name ?? "Unknown Organization";
        }

        private async Task<string> GetPaymentProfileName(string paymentProfileId)
        {
            var result = await _dbService.QueryFirstOrDefaultAsync<dynamic>(
                "SELECT Name FROM PaymentProfiles WHERE Id = @Id", new { Id = paymentProfileId });
            return result?.Name ?? "Unknown Profile";
        }
    }
}


