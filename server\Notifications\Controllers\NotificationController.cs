using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Final_E_Receipt.Notifications.Services;
using Final_E_Receipt.Notifications.DTOs;
using Final_E_Receipt.Notifications.Models;

namespace Final_E_Receipt.Notifications.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class NotificationController : ControllerBase
    {
        private readonly NotificationManagementService _notificationService;
        private readonly ILogger<NotificationController> _logger;

        public NotificationController(
            NotificationManagementService notificationService,
            ILogger<NotificationController> logger)
        {
            _notificationService = notificationService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetUserNotifications([FromQuery] NotificationFiltersDTO filters)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var notifications = await _notificationService.GetUserNotifications(userId, filters);
                return Ok(notifications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user notifications");
                return StatusCode(500, new { message = "Error retrieving notifications" });
            }
        }

        [HttpGet("stats")]
        public async Task<IActionResult> GetNotificationStats()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var stats = await _notificationService.GetNotificationStats(userId);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notification stats");
                return StatusCode(500, new { message = "Error retrieving notification stats" });
            }
        }

        [HttpPost("{id}/read")]
        public async Task<IActionResult> MarkAsRead(string id)
        {
            try
            {
                var notification = await _notificationService.MarkAsRead(id);
                if (notification == null)
                    return NotFound(new { message = "Notification not found" });

                return Ok(notification);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification as read: {NotificationId}", id);
                return StatusCode(500, new { message = "Error updating notification" });
            }
        }

        [HttpPost("bulk/read")]
        public async Task<IActionResult> BulkMarkAsRead([FromBody] NotificationBulkMarkAsReadDTO dto)
        {
            try
            {
                var result = await _notificationService.MarkMultipleAsRead(dto.NotificationIds);
                return Ok(new { updatedCount = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk marking notifications as read");
                return StatusCode(500, new { message = "Error updating notifications" });
            }
        }

        [HttpPost("bulk-action")]
        public async Task<IActionResult> BulkAction([FromBody] NotificationBulkActionDTO dto)
        {
            try
            {
                int result = 0;
                
                switch (dto.Action.ToUpper())
                {
                    case "READ":
                        var readResult = await _notificationService.MarkMultipleAsRead(dto.NotificationIds);
                        result = readResult ? dto.NotificationIds.Length : 0;
                        break;
                    case "ARCHIVE":
                        var archiveResult = await _notificationService.ArchiveMultiple(dto.NotificationIds);
                        result = archiveResult ? dto.NotificationIds.Length : 0;
                        break;
                    case "DELETE":
                        var deleteResult = await _notificationService.DeleteMultiple(dto.NotificationIds);
                        result = deleteResult ? dto.NotificationIds.Length : 0;
                        break;
                    default:
                        return BadRequest(new { message = "Invalid action. Use READ, ARCHIVE, or DELETE" });
                }

                return Ok(new { updatedCount = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing bulk action: {Action}", dto.Action);
                return StatusCode(500, new { message = "Error performing bulk action" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteNotification(string id)
        {
            try
            {
                var success = await _notificationService.DeleteNotification(id);
                if (!success)
                    return NotFound(new { message = "Notification not found" });

                return Ok(new { message = "Notification deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting notification: {NotificationId}", id);
                return StatusCode(500, new { message = "Error deleting notification" });
            }
        }
    }
}








