-- Stored Procedures for Dashboard and Reporting

-- Dashboard Summary
CREATE PROCEDURE GetDashboardSummary
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT
        (SELECT COUNT(*) FROM Receipts WHERE OrganizationId = @OrganizationId) AS TotalReceipts,
        (SELECT ISNULL(SUM(Amount), 0) FROM Receipts WHERE OrganizationId = @OrganizationId) AS TotalAmount,
        (SELECT COUNT(*) FROM Payments WHERE OrganizationId = @OrganizationId AND Status = 'Completed') AS CompletedPayments,
        (SELECT COUNT(*) FROM Payments WHERE OrganizationId = @OrganizationId AND Status = 'Pending') AS PendingPayments,
        (SELECT COUNT(*) FROM Payments WHERE OrganizationId = @OrganizationId AND Status = 'Failed') AS FailedPayments,
        (SELECT COUNT(*) FROM Users WHERE OrganizationId = @OrganizationId) AS TotalUsers,
        (SELECT COUNT(*) FROM Users WHERE OrganizationId = @OrganizationId AND IsActive = 1) AS ActiveUsers
END;

-- Monthly Revenue Report
CREATE PROCEDURE GetMonthlyRevenue
    @OrganizationId NVARCHAR(50),
    @Year INT
AS
BEGIN
    SELECT 
        MONTH(PaymentDate) AS Month,
        YEAR(PaymentDate) AS Year,
        SUM(Amount) AS TotalAmount,
        COUNT(*) AS ReceiptCount
    FROM Receipts
    WHERE 
        OrganizationId = @OrganizationId AND
        YEAR(PaymentDate) = @Year AND
        IsRevoked = 0
    GROUP BY MONTH(PaymentDate), YEAR(PaymentDate)
    ORDER BY Year, Month
END;

-- Payment Method Summary
CREATE PROCEDURE GetPaymentMethodSummary
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT 
        PaymentMethod,
        COUNT(*) AS Count,
        SUM(Amount) AS TotalAmount
    FROM Receipts
    WHERE 
        OrganizationId = @OrganizationId AND
        IsRevoked = 0
    GROUP BY PaymentMethod
    ORDER BY TotalAmount DESC
END;

-- Category Summary
CREATE PROCEDURE GetCategorySummary
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT 
        Category,
        COUNT(*) AS Count,
        SUM(Amount) AS TotalAmount
    FROM Receipts
    WHERE 
        OrganizationId = @OrganizationId AND
        IsRevoked = 0
    GROUP BY Category
    ORDER BY TotalAmount DESC
END;

-- Daily Revenue Report
CREATE PROCEDURE GetDailyRevenue
    @OrganizationId NVARCHAR(50),
    @StartDate DATE,
    @EndDate DATE
AS
BEGIN
    SELECT 
        CAST(PaymentDate AS DATE) AS Date,
        SUM(Amount) AS TotalAmount,
        COUNT(*) AS ReceiptCount
    FROM Receipts
    WHERE 
        OrganizationId = @OrganizationId AND
        CAST(PaymentDate AS DATE) BETWEEN @StartDate AND @EndDate AND
        IsRevoked = 0
    GROUP BY CAST(PaymentDate AS DATE)
    ORDER BY Date
END;

-- Top Payers Report
CREATE PROCEDURE GetTopPayers
    @OrganizationId NVARCHAR(50),
    @Limit INT = 10
AS
BEGIN
    SELECT TOP (@Limit)
        PayerId,
        PayerName,
        PayerEmail,
        COUNT(*) AS TransactionCount,
        SUM(Amount) AS TotalAmount
    FROM Receipts
    WHERE 
        OrganizationId = @OrganizationId AND
        IsRevoked = 0
    GROUP BY PayerId, PayerName, PayerEmail
    ORDER BY TotalAmount DESC
END;

-- Revenue Comparison (Year over Year)
CREATE PROCEDURE GetYearOverYearComparison
    @OrganizationId NVARCHAR(50),
    @CurrentYear INT,
    @PreviousYear INT
AS
BEGIN
    SELECT 
        'Current' AS Period,
        MONTH(PaymentDate) AS Month,
        SUM(Amount) AS TotalAmount
    FROM Receipts
    WHERE 
        OrganizationId = @OrganizationId AND
        YEAR(PaymentDate) = @CurrentYear AND
        IsRevoked = 0
    GROUP BY MONTH(PaymentDate)
    
    UNION ALL
    
    SELECT 
        'Previous' AS Period,
        MONTH(PaymentDate) AS Month,
        SUM(Amount) AS TotalAmount
    FROM Receipts
    WHERE 
        OrganizationId = @OrganizationId AND
        YEAR(PaymentDate) = @PreviousYear AND
        IsRevoked = 0
    GROUP BY MONTH(PaymentDate)
    
    ORDER BY Month, Period
END;