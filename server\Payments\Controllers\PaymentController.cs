using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Final_E_Receipt.Payments.Models;
using Final_E_Receipt.Payments.Services;
using Final_E_Receipt.Payments.DTOs;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Final_E_Receipt.Payments.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Policy = "RequireJTBAdmin")]
    public class PaymentController : ControllerBase
    {
        private readonly PaymentService _paymentService;

        public PaymentController(PaymentService paymentService)
        {
            _paymentService = paymentService;
        }

        [HttpPost]
        public async Task<IActionResult> InitiatePayment([FromBody] CreatePaymentDTO dto)
        {
            var payment = new Payment
            {
                PayerId = dto.PayerId,
                PayerName = dto.PayerName,
                PayerEmail = dto.PayerEmail,
                Amount = dto.Amount,
                Currency = dto.Currency,
                PaymentMethod = dto.PaymentMethod,
                Description = dto.Description,
                PaymentTypeId = dto.PaymentTypeId,
                OrganizationId = dto.OrganizationId
            };

            var createdPayment = await _paymentService.InitiatePayment(payment);
            
            if (createdPayment == null)
                return BadRequest(new { message = "Failed to initiate payment" });
                
            return CreatedAtAction(nameof(GetPaymentById), new { id = createdPayment.Id }, createdPayment);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetPaymentById(string id)
        {
            var payment = await _paymentService.GetPaymentById(id);
            
            if (payment == null)
                return NotFound(new { message = "Payment not found" });
                
            return Ok(payment);
        }

        [HttpGet("payer/{payerId}")]
        public async Task<IActionResult> GetPaymentsByPayer(string payerId)
        {
            var payments = await _paymentService.GetPaymentsByPayer(payerId);
            return Ok(payments);
        }

        [HttpPut("{id}/complete")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> CompletePayment(string id)
        {
            var payment = await _paymentService.CompletePayment(id);
            
            if (payment == null)
                return NotFound(new { message = "Payment not found" });
                
            return Ok(payment);
        }

        [HttpPut("{id}/status")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> UpdatePaymentStatus(string id, [FromBody] UpdatePaymentStatusDTO dto)
        {
            // This would need to be implemented in the PaymentService
            // For now, we'll just complete the payment if status is "Completed"
            if (dto.Status == "Completed")
            {
                var payment = await _paymentService.CompletePayment(id);
                
                if (payment == null)
                    return NotFound(new { message = "Payment not found" });
                    
                return Ok(payment);
            }
            
            return BadRequest(new { message = "Invalid status update" });
        }
    }
}