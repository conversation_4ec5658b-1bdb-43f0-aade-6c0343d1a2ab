import React, { useState, useCallback } from 'react';
import { Upload, X, FileText, Image, AlertCircle, CheckCircle } from 'lucide-react';
import { useFilesApi } from '../../hooks/api';

interface ReceiptFileUploadProps {
  receiptId?: string;
  onFileUploaded?: (fileId: string, fileName: string) => void;
  onFileRemoved?: (fileId: string) => void;
  existingFiles?: Array<{ id: string; fileName: string; fileSize: number; uploadedAt: string }>;
  maxFiles?: number;
  maxFileSize?: number; // in bytes
  allowedTypes?: string[];
  className?: string;
}

const ReceiptFileUpload: React.FC<ReceiptFileUploadProps> = ({
  receiptId,
  onFileUploaded,
  onFileRemoved,
  existingFiles = [],
  maxFiles = 5,
  maxFileSize = 5 * 1024 * 1024, // 5MB
  allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
  className = '',
}) => {
  const { uploadFile, deleteFile, loading } = useFilesApi();
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const [errors, setErrors] = useState<string[]>([]);

  const validateFile = (file: File): string | null => {
    if (file.size > maxFileSize) {
      return `File size must be less than ${(maxFileSize / 1024 / 1024).toFixed(1)}MB`;
    }
    
    if (!allowedTypes.includes(file.type)) {
      return `File type ${file.type} is not allowed`;
    }
    
    if (existingFiles.length >= maxFiles) {
      return `Maximum ${maxFiles} files allowed`;
    }
    
    return null;
  };

  const handleFiles = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files);
    const newErrors: string[] = [];
    
    for (const file of fileArray) {
      const error = validateFile(file);
      if (error) {
        newErrors.push(`${file.name}: ${error}`);
        continue;
      }
      
      try {
        setUploadProgress(prev => ({ ...prev, [file.name]: 0 }));
        
        const result = await uploadFile(file, {
          entityType: 'RECEIPT',
          entityId: receiptId,
          description: `Receipt attachment: ${file.name}`,
          category: 'RECEIPT_ATTACHMENT',
        });
        
        if (result) {
          setUploadProgress(prev => ({ ...prev, [file.name]: 100 }));
          onFileUploaded?.(result.id, result.fileName);
          
          // Remove progress after success
          setTimeout(() => {
            setUploadProgress(prev => {
              const newProgress = { ...prev };
              delete newProgress[file.name];
              return newProgress;
            });
          }, 2000);
        }
      } catch (error) {
        newErrors.push(`${file.name}: Upload failed`);
        setUploadProgress(prev => {
          const newProgress = { ...prev };
          delete newProgress[file.name];
          return newProgress;
        });
      }
    }
    
    if (newErrors.length > 0) {
      setErrors(newErrors);
      setTimeout(() => setErrors([]), 5000);
    }
  }, [receiptId, onFileUploaded, uploadFile, maxFiles, maxFileSize, allowedTypes, existingFiles.length]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
    }
  }, [handleFiles]);

  const handleRemoveFile = async (fileId: string, fileName: string) => {
    try {
      await deleteFile(fileId);
      onFileRemoved?.(fileId);
    } catch (error) {
      setErrors([`Failed to remove ${fileName}`]);
      setTimeout(() => setErrors([]), 3000);
    }
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif'].includes(extension || '')) {
      return <Image size={16} className="text-blue-500" />;
    }
    return <FileText size={16} className="text-gray-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        className={`
          border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer
          ${dragActive ? 'border-[#2aa45c] bg-green-50' : 'border-gray-300 hover:border-gray-400'}
          ${loading ? 'opacity-50 cursor-not-allowed' : ''}
          ${existingFiles.length >= maxFiles ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => {
          if (!loading && existingFiles.length < maxFiles) {
            document.getElementById('receipt-file-input')?.click();
          }
        }}
      >
        <input
          id="receipt-file-input"
          type="file"
          multiple
          accept={allowedTypes.join(',')}
          onChange={handleInputChange}
          className="hidden"
          disabled={loading || existingFiles.length >= maxFiles}
        />
        
        <Upload className="mx-auto text-gray-400 mb-3" size={48} />
        <p className="text-gray-600 font-medium mb-1">
          {existingFiles.length >= maxFiles 
            ? `Maximum ${maxFiles} files reached`
            : 'Click to upload or drag and drop'
          }
        </p>
        <p className="text-xs text-gray-500">
          Images (JPG, PNG, GIF) and PDF files up to {(maxFileSize / 1024 / 1024).toFixed(1)}MB
        </p>
        <p className="text-xs text-gray-500 mt-1">
          {existingFiles.length}/{maxFiles} files uploaded
        </p>
      </div>

      {/* Upload Progress */}
      {Object.keys(uploadProgress).length > 0 && (
        <div className="space-y-2">
          {Object.entries(uploadProgress).map(([fileName, progress]) => (
            <div key={fileName} className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">{fileName}</span>
                <span className="text-sm text-gray-500">{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-[#2aa45c] h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start">
            <AlertCircle className="h-5 w-5 text-red-400 mt-0.5 mr-3" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-red-800">Upload Errors</h4>
              <ul className="mt-2 text-sm text-red-700 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Existing Files */}
      {existingFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-900">Attached Files</h4>
          {existingFiles.map((file) => (
            <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                {getFileIcon(file.fileName)}
                <div>
                  <p className="text-sm font-medium text-gray-900">{file.fileName}</p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(file.fileSize)} • {new Date(file.uploadedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle size={16} className="text-green-500" />
                <button
                  onClick={() => handleRemoveFile(file.id, file.fileName)}
                  disabled={loading}
                  className="text-red-500 hover:text-red-700 disabled:opacity-50"
                  title="Remove file"
                >
                  <X size={16} />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ReceiptFileUpload;
