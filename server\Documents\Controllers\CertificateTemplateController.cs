using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Final_E_Receipt.Documents.Models;
using Final_E_Receipt.Documents.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Documents.Controllers
{
    [ApiController]
    [Route("api/certificate-templates")]
    [Authorize]
    public class CertificateTemplateController : ControllerBase
    {
        private readonly CertificateTemplateService _templateService;
        private readonly ILogger<CertificateTemplateController> _logger;

        public CertificateTemplateController(
            CertificateTemplateService templateService,
            ILogger<CertificateTemplateController> logger)
        {
            _templateService = templateService;
            _logger = logger;
        }

        /// <summary>
        /// Get available certificate templates for organization and certificate type
        /// </summary>
        [HttpGet("available")]
        public async Task<IActionResult> GetAvailableTemplates(
            [FromQuery] string organizationId,
            [FromQuery] string certificateType)
        {
            try
            {
                if (string.IsNullOrEmpty(organizationId) || string.IsNullOrEmpty(certificateType))
                {
                    return BadRequest(new { message = "OrganizationId and CertificateType are required" });
                }

                var templates = await _templateService.GetAvailableTemplates(organizationId, certificateType);
                
                return Ok(new 
                { 
                    organizationId,
                    certificateType,
                    availableTemplates = templates,
                    totalCount = templates.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available templates for {OrganizationId}, {CertificateType}", organizationId, certificateType);
                return StatusCode(500, new { message = "An error occurred while retrieving templates" });
            }
        }

        /// <summary>
        /// Get template preview information
        /// </summary>
        [HttpGet("{templateId}/preview")]
        public async Task<IActionResult> GetTemplatePreview(string templateId)
        {
            try
            {
                // This would return template preview information
                // For now, return basic info
                return Ok(new 
                { 
                    templateId,
                    previewUrl = $"/templates/previews/{templateId}-preview.png",
                    message = "Template preview functionality to be implemented"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting template preview for {TemplateId}", templateId);
                return StatusCode(500, new { message = "An error occurred while retrieving template preview" });
            }
        }

        /// <summary>
        /// Get all templates for an organization (admin function)
        /// </summary>
        [HttpGet("organization/{organizationId}")]
        [Authorize(Roles = "ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetOrganizationTemplates(string organizationId)
        {
            try
            {
                var certificateTypes = new[] { "ANNUAL_LICENSE", "TAX_CLEARANCE", "QUARTERLY_COMPLIANCE", "PAYMENT_COMPLIANCE" };
                var allTemplates = new Dictionary<string, List<AvailableTemplate>>();

                foreach (var certType in certificateTypes)
                {
                    var templates = await _templateService.GetAvailableTemplates(organizationId, certType);
                    if (templates.Count > 0)
                    {
                        allTemplates[certType] = templates;
                    }
                }

                return Ok(new 
                { 
                    organizationId,
                    templatesByType = allTemplates,
                    totalTypes = allTemplates.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting organization templates for {OrganizationId}", organizationId);
                return StatusCode(500, new { message = "An error occurred while retrieving organization templates" });
            }
        }
    }
}

/*
API USAGE EXAMPLES:

1. Get Available Templates (Dynamic for any organization):
   GET /api/certificate-templates/available?organizationId=jrb-001&certificateType=ANNUAL_LICENSE

   Response:
   {
     "organizationId": "jrb-001",
     "certificateType": "ANNUAL_LICENSE",
     "availableTemplates": [
       {
         "id": "jrb-001-annual_license-landscape",
         "name": "Annual License (Landscape)",
         "description": "Official Annual License certificate - landscape format",
         "previewImageUrl": "/templates/previews/jrb-001-annual_license-landscape-preview.png",
         "orientation": "Landscape",
         "isDefault": true,
         "organizationName": "Organization",
         "certificateTypeName": "Annual License"
       },
       {
         "id": "jrb-001-annual_license-portrait",
         "name": "Annual License (Portrait)",
         "description": "Official Annual License certificate - portrait format",
         "previewImageUrl": "/templates/previews/jrb-001-annual_license-portrait-preview.png",
         "orientation": "Portrait",
         "isDefault": false,
         "organizationName": "Organization",
         "certificateTypeName": "Annual License"
       }
     ],
     "totalCount": 2
   }

2. Get Template Preview (Dynamic):
   GET /api/certificate-templates/jrb-001-annual_license-landscape/preview

3. Get All Organization Templates (Dynamic):
   GET /api/certificate-templates/organization/jrb-001

4. Works with any organization ID:
   GET /api/certificate-templates/available?organizationId=any-org-123&certificateType=TAX_CLEARANCE
*/
