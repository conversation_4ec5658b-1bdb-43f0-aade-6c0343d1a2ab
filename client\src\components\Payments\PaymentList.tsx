import React, { useState, useEffect } from 'react';
import {
  Credit<PERSON>ard,
  Plus,
  Eye,
  Filter,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  DollarSign,
  Calendar,
  Building2,
} from 'lucide-react';
import { usePaymentApi, useOrganizationApi } from '../../hooks/api';
import { useAuth } from '../../contexts/AuthContext';
import type { Payment, Organization } from '../../hooks/api';

interface PaymentListProps {
  setActiveTab: (tab: string) => void;
  setSelectedPayment: (payment: Payment | null) => void;
}

const PaymentList: React.FC<PaymentListProps> = ({
  setActiveTab,
  setSelectedPayment,
}) => {
  const { loading, error, getAllPayments, getPaymentsByStatus, completePayment } = usePaymentApi();
  const { getAllOrganizations } = useOrganizationApi();
  const { user } = useAuth();
  
  const [payments, setPayments] = useState<Payment[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const paymentsPerPage = 10;

  // Role-based permissions (Updated to match backend)
  const canCreatePayments = user?.role === 'JTB_ADMIN';
  const canCompletePayments = ['FINANCE_OFFICER', 'SENIOR_FINANCE_OFFICER'].includes(user?.role || '');
  const canAcknowledgePayments = user?.role === 'FINANCE_OFFICER';
  const canApprovePayments = user?.role === 'SENIOR_FINANCE_OFFICER';

  useEffect(() => {
    loadPayments();
    loadOrganizations();
  }, []);

  const loadPayments = async () => {
    const result = await getAllPayments();
    if (result) {
      setPayments(result);
    }
  };

  const loadOrganizations = async () => {
    const result = await getAllOrganizations();
    if (result) {
      setOrganizations(result);
    }
  };

  const handleStatusFilter = async () => {
    if (statusFilter === 'all') {
      loadPayments();
    } else {
      const result = await getPaymentsByStatus(statusFilter);
      if (result) {
        setPayments(result);
      }
    }
  };

  const handleViewDetails = (payment: Payment) => {
    setSelectedPayment(payment);
    setActiveTab('payment-details');
  };

  const handleCompletePayment = async (paymentId: string) => {
    const result = await completePayment(paymentId);
    if (result) {
      loadPayments(); // Refresh the list
    }
  };

  const getOrganizationName = (organizationId: string) => {
    const org = organizations.find(o => o.id === organizationId);
    return org?.name || 'Unknown Organization';
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'Pending': { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      'Proof_Uploaded': { color: 'bg-blue-100 text-blue-800', icon: Eye },
      'Acknowledged': { color: 'bg-blue-100 text-blue-800', icon: CheckCircle },
      'Approved': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      'Completed': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      'Rejected': { color: 'bg-red-100 text-red-800', icon: XCircle },
      'Failed': { color: 'bg-red-100 text-red-800', icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {status}
      </span>
    );
  };

  // Filter payments based on status only (search removed to match backend)
  const filteredPayments = payments.filter(payment => {
    const matchesStatus = statusFilter === 'all' || payment.status === statusFilter;
    return matchesStatus;
  });

  // Calculate pagination
  const totalPages = Math.ceil(filteredPayments.length / paymentsPerPage);
  const startIndex = (currentPage - 1) * paymentsPerPage;
  const endIndex = startIndex + paymentsPerPage;
  const currentPayments = filteredPayments.slice(startIndex, endIndex);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2aa45c]"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-[#045024]">Payments</h2>
          <p className="text-gray-600">
            {canCreatePayments ? 'Manage payments and track their status' : 'View payment information and status'}
          </p>
        </div>
        {canCreatePayments && (
          <button
            onClick={() => setActiveTab('create-payment')}
            className="bg-[#2aa45c] text-white px-4 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2"
          >
            <Plus size={20} />
            <span>Add Payment</span>
          </button>
        )}
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="Pending">Pending</option>
              <option value="Proof_Uploaded">Proof Uploaded</option>
              <option value="Acknowledged">Acknowledged</option>
              <option value="Approved">Approved</option>
              <option value="Completed">Completed</option>
              <option value="Rejected">Rejected</option>
              <option value="Failed">Failed</option>
            </select>
            <button
              onClick={handleStatusFilter}
              className="bg-[#2aa45c] text-white px-4 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2"
            >
              <Filter size={16} />
              <span>Filter</span>
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={20} />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Payments Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Organization
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentPayments.map((payment) => (
                <tr key={payment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
                          <CreditCard className="h-5 w-5 text-[#2aa45c]" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {payment.transactionReference}
                        </div>
                        <div className="text-sm text-gray-500">
                          {payment.paymentMethod}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Building2 className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900">
                        {getOrganizationName(payment.organizationId)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 text-gray-400 mr-1" />
                      <span className="text-sm font-medium text-gray-900">
                        {payment.amount.toLocaleString()} {payment.currency}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(payment.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900">
                        {new Date(payment.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleViewDetails(payment)}
                        className="text-[#2aa45c] hover:text-[#076934] transition-colors"
                        title="View Details"
                      >
                        <Eye size={16} />
                      </button>
                      {canCompletePayments && payment.status === 'Approved' && (
                        <button
                          onClick={() => handleCompletePayment(payment.id)}
                          className="text-green-600 hover:text-green-800 transition-colors"
                          title="Complete Payment"
                        >
                          <CheckCircle size={16} />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                  <span className="font-medium">{Math.min(endIndex, filteredPayments.length)}</span> of{' '}
                  <span className="font-medium">{filteredPayments.length}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        page === currentPage
                          ? 'z-10 bg-[#2aa45c] border-[#2aa45c] text-white'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>


    </div>
  );
};

export default PaymentList;
