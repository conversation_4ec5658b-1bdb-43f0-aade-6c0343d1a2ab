using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Final_E_Receipt.Files.Models;
using Final_E_Receipt.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Files.Services
{
    public class FileService
    {
        private readonly IDatabaseService _dbService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<FileService> _logger;
        private readonly string _uploadPath;
        private readonly long _maxFileSize;
        private readonly string[] _allowedExtensions;
        private readonly string[] _allowedContentTypes;

        public FileService(IDatabaseService dbService, IConfiguration configuration, ILogger<FileService> logger)
        {
            _dbService = dbService;
            _configuration = configuration;
            _logger = logger;
            
            // Configure file upload settings
            _uploadPath = _configuration.GetValue<string>("FileUpload:UploadPath") ?? "uploads";
            _maxFileSize = _configuration.GetValue<long?>("FileUpload:MaxFileSize") ?? 10 * 1024 * 1024; // 10MB default
            _allowedExtensions = _configuration.GetSection("FileUpload:AllowedExtensions").Get<string[]>() ?? 
                new[] { ".pdf", ".jpg", ".jpeg", ".png", ".doc", ".docx" };
            _allowedContentTypes = _configuration.GetSection("FileUpload:AllowedContentTypes").Get<string[]>() ?? 
                new[] { "application/pdf", "image/jpeg", "image/png", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document" };

            // Ensure upload directory exists
            EnsureUploadDirectoryExists();
        }

        public async Task<FileUpload> UploadFile(IFormFile file, string relatedEntityType, string relatedEntityId, 
            string uploadedBy, string organizationId, string description = null, string category = null)
        {
            try
            {
                // Validate file
                ValidateFile(file);

                // Generate unique file name and path
                var fileId = Guid.NewGuid().ToString();
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                var fileName = $"{fileId}{fileExtension}";
                var relativePath = Path.Combine(relatedEntityType.ToLower(), DateTime.Now.ToString("yyyy/MM"));
                var fullDirectoryPath = Path.Combine(_uploadPath, relativePath);
                var filePath = Path.Combine(fullDirectoryPath, fileName);

                // Ensure directory exists
                Directory.CreateDirectory(fullDirectoryPath);

                // Calculate file hash
                string fileHash;
                using (var stream = file.OpenReadStream())
                {
                    fileHash = await CalculateFileHash(stream);
                }

                // Save file to disk
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // Create file upload record
                var fileUpload = new FileUpload
                {
                    Id = fileId,
                    FileName = fileName,
                    OriginalFileName = file.FileName,
                    FilePath = Path.Combine(relativePath, fileName).Replace('\\', '/'),
                    ContentType = file.ContentType,
                    FileSize = file.Length,
                    FileHash = fileHash,
                    UploadedBy = uploadedBy,
                    OrganizationId = organizationId,
                    RelatedEntityType = relatedEntityType,
                    RelatedEntityId = relatedEntityId,
                    Description = description,
                    Category = category,
                    CreatedAt = DateTime.Now
                };

                // Save to database
                var savedFile = await SaveFileUploadToDatabase(fileUpload);
                
                _logger.LogInformation("File uploaded successfully: {FileName} by {UploadedBy}", file.FileName, uploadedBy);
                
                return savedFile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file: {FileName}", file.FileName);
                throw;
            }
        }

        public async Task<FileUpload> GetFileById(string id)
        {
            var parameters = new { Id = id };
            return await _dbService.QueryFirstOrDefaultAsync<FileUpload>(
                "GetFileUploadById", parameters);
        }

        public async Task<List<FileUpload>> GetFilesByEntity(string relatedEntityType, string relatedEntityId)
        {
            var parameters = new
            {
                RelatedEntityType = relatedEntityType,
                RelatedEntityId = relatedEntityId
            };

            var files = await _dbService.QueryAsync<FileUpload>(
                "GetFileUploadsByEntity", parameters);

            return files.ToList();
        }

        public async Task<byte[]> DownloadFile(string id)
        {
            var fileUpload = await GetFileById(id);
            if (fileUpload == null)
                throw new FileNotFoundException("File not found");

            var fullPath = Path.Combine(_uploadPath, fileUpload.FilePath);
            if (!File.Exists(fullPath))
                throw new FileNotFoundException("Physical file not found");

            return await File.ReadAllBytesAsync(fullPath);
        }

        public async Task<bool> DeleteFile(string id)
        {
            try
            {
                var fileUpload = await GetFileById(id);
                if (fileUpload == null)
                    return false;

                var parameters = new { Id = id };
                var rowsAffected = await _dbService.ExecuteAsync("DeleteFileUpload", parameters);

                _logger.LogInformation("File deleted: {FileId}", id);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file: {FileId}", id);
                return false;
            }
        }

        private void ValidateFile(IFormFile file)
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("File is required");

            if (file.Length > _maxFileSize)
                throw new ArgumentException($"File size exceeds maximum allowed size of {_maxFileSize / (1024 * 1024)}MB");

            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (!Array.Exists(_allowedExtensions, ext => ext == extension))
                throw new ArgumentException($"File type {extension} is not allowed");

            if (!Array.Exists(_allowedContentTypes, ct => ct == file.ContentType))
                throw new ArgumentException($"Content type {file.ContentType} is not allowed");
        }

        private async Task<string> CalculateFileHash(Stream stream)
        {
            using (var sha256 = SHA256.Create())
            {
                var hash = await Task.Run(() => sha256.ComputeHash(stream));
                return Convert.ToHexString(hash);
            }
        }

        private void EnsureUploadDirectoryExists()
        {
            if (!Directory.Exists(_uploadPath))
            {
                Directory.CreateDirectory(_uploadPath);
                _logger.LogInformation("Created upload directory: {UploadPath}", _uploadPath);
            }
        }

        private async Task<FileUpload> SaveFileUploadToDatabase(FileUpload fileUpload)
        {
            var parameters = new
            {
                Id = fileUpload.Id,
                FileName = fileUpload.FileName,
                OriginalFileName = fileUpload.OriginalFileName,
                FilePath = fileUpload.FilePath,
                ContentType = fileUpload.ContentType,
                FileSize = fileUpload.FileSize,
                FileHash = fileUpload.FileHash,
                UploadedBy = fileUpload.UploadedBy,
                OrganizationId = fileUpload.OrganizationId,
                RelatedEntityType = fileUpload.RelatedEntityType,
                RelatedEntityId = fileUpload.RelatedEntityId,
                Description = fileUpload.Description,
                Category = fileUpload.Category,
                CreatedAt = fileUpload.CreatedAt
            };

            return await _dbService.QueryFirstOrDefaultAsync<FileUpload>(
                "CreateFileUpload", parameters);
        }

       
    }
}

