import { useState, useCallback } from 'react';
import { apiService } from '../../services/apiService';
import type { User, AuthStatus } from '../../types/auth';

// Types for Auth API
export interface CreateInvitationDTO {
  email: string;
  role: string;
  organizationId: string;
  authType: 'MICROSOFT' | 'LOCAL';
}

export interface UserInvitation {
  id: string;
  email: string;
  role: string;
  organizationId: string;
  invitedDate: string;
  status: string;
  invitedBy: string;
  expiryDate: string;
}

// Auth API Hooks
export const useAuthApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An error occurred');
      }
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Auth endpoints
  const getCurrentUser = useCallback(() => 
    handleApiCall(() => apiService.get<User>('/auth/me')), [handleApiCall]);

  const getAuthStatus = useCallback(() => 
    handleApiCall(() => apiService.get<AuthStatus>('/auth/status')), [handleApiCall]);

  const healthCheck = useCallback(() => 
    handleApiCall(() => apiService.get('/auth/health')), [handleApiCall]);

  const createTestInvitation = useCallback(() => 
    handleApiCall(() => apiService.post('/auth/create-test-invitation')), [handleApiCall]);

  return {
    loading,
    error,
    getCurrentUser,
    getAuthStatus,
    healthCheck,
    createTestInvitation,
  };
};

// User Invitation API Hooks
export const useUserInvitationApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An error occurred');
      }
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // User invitation endpoints
  const getAllInvitations = useCallback(() => 
    handleApiCall(() => apiService.get<UserInvitation[]>('/user-invitations')), [handleApiCall]);

  const createInvitation = useCallback((data: CreateInvitationDTO) => 
    handleApiCall(() => apiService.post<UserInvitation>('/user-invitations/invite', data)), [handleApiCall]);

  const getPendingInvitations = useCallback(() => 
    handleApiCall(() => apiService.get<UserInvitation[]>('/user-invitations/pending')), [handleApiCall]);

  const resendInvitation = useCallback((id: string) => 
    handleApiCall(() => apiService.post(`/user-invitations/${id}/resend`)), [handleApiCall]);

  const cancelInvitation = useCallback((id: string) => 
    handleApiCall(() => apiService.delete(`/user-invitations/${id}`)), [handleApiCall]);

  return {
    loading,
    error,
    getAllInvitations,
    createInvitation,
    getPendingInvitations,
    resendInvitation,
    cancelInvitation,
  };
};

// Admin Setup API Hook
export const useAdminSetupApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initializeAdmin = useCallback(async (): Promise<User | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiService.post<User>('/auth/admin-setup');
      return result;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Failed to initialize admin');
      }
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    initializeAdmin,
  };
};

