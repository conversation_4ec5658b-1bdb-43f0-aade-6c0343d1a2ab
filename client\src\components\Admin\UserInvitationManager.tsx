import React, { useState, useEffect } from "react";
import {
  ChevronLeft,
  Mail,
  Loader2,
  Clock,
  Trash2,
  Users,
  UserPlus,
  CheckCircle,
  XCircle,
  Calendar,
  Building,
  AlertTriangle,
} from "lucide-react";

import { API_BASE_URL } from "../../config/api";

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: "primary" | "secondary" | "danger" | "ghost";
  size?: "sm" | "md" | "lg";
  disabled?: boolean;
  className?: string;
}

interface CreateUserProps {
  setActiveTab?: (tab: string) => void;
  onInvitationSent: (invitation: Invitation) => void;
  organizations: Organization[];
}

interface UserInvitationsProps {
  invitations: Invitation[];
  onInvitationUpdate: (updatedInvitations: Invitation[]) => void;
  organizations: Organization[];
}

interface InvitationData {
  email: string;
  role: string;
  organizationId: string;
  authType: number;
}

interface Invitation {
  id: string;
  email: string;
  role: string;
  organizationId: string;
  organizationName: string | null;
  invitedDate: string;
  status: string;
  expiryDate: string;
  acceptedDate: string | null;
  invitedBy: string | null;
  invitedByName: string | null;
}

interface Organization {
  id: string;
  name: string;
  email: string;
  phoneNumber: string;
  address: string;
  city: string | null;
  state: string | null;
  country: string | null;
  logoUrl: string | null;
  website: string | null;
  isActive: boolean;
  createdAt: string;
  createdBy: string | null;
}

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText: string;
  cancelText?: string;
  variant?: "danger" | "primary";
  isLoading?: boolean;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText,
  cancelText = "Cancel",
  variant = "primary",
  isLoading = false,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-white rounded-2xl shadow-2xl border border-gray-200 p-6 w-full max-w-md mx-4 transform transition-all">
        <div className="flex items-center gap-4 mb-4">
          {variant === "danger" ? (
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="text-red-600" size={24} />
            </div>
          ) : (
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="text-green-600" size={24} />
            </div>
          )}
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <p className="text-gray-600 text-sm mt-1">{message}</p>
          </div>
        </div>

        <div className="flex gap-3 mt-6">
          <ActionButton
            variant="secondary"
            onClick={onClose}
            disabled={isLoading}
            className="flex-1"
          >
            {cancelText}
          </ActionButton>
          <ActionButton
            variant={variant}
            onClick={onConfirm}
            disabled={isLoading}
            className="flex-1"
          >
            {isLoading ? (
              <>
                <Loader2 size={16} className="animate-spin" />
                Processing...
              </>
            ) : (
              confirmText
            )}
          </ActionButton>
        </div>
      </div>
    </div>
  );
};

const ActionButton: React.FC<ActionButtonProps> = ({
  children,
  onClick,
  variant = "primary",
  size = "md",
  disabled = false,
  className = "",
}) => {
  const baseClasses =
    "inline-flex items-center justify-center gap-2 font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";

  const sizeClasses = {
    sm: "px-3 py-2 text-sm",
    md: "px-4 py-2.5 text-sm",
    lg: "px-6 py-3 text-base",
  };

  const variantClasses = {
    primary:
      "bg-[#2aa45c] hover:bg-[#045024] text-white shadow-lg hover:shadow-xl focus:ring-[#2aa45c]",
    secondary:
      "bg-white border-2 border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 hover:bg-gray-50 shadow-sm hover:shadow-md focus:ring-gray-300",
    danger:
      "bg-red-500 hover:bg-red-600 text-white shadow-lg hover:shadow-xl focus:ring-red-500",
    ghost:
      "text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-gray-300",
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses[size]} ${
        variantClasses[variant]
      } ${disabled ? "opacity-50 cursor-not-allowed" : ""} ${className}`}
    >
      {children}
    </button>
  );
};

const CreateUser: React.FC<CreateUserProps> = ({
  setActiveTab,
  onInvitationSent,
  organizations,
}) => {
  const [email, setEmail] = useState("");
  const [role, setRole] = useState("admin");
  const [organizationId, setOrganizationId] = useState("");
  const [authType, setAuthType] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);

  const sendInvitation = async () => {
    if (!email.trim()) {
      setMessage({ type: "error", text: "Email is required" });
      return;
    }

    if (!organizationId.trim()) {
      setMessage({ type: "error", text: "Organization is required" });
      return;
    }

    setIsLoading(true);
    setMessage(null);

    try {
      const token = localStorage.getItem("auth_token");

      const invitationData: InvitationData = {
        email: email.trim(),
        role: role,
        organizationId: organizationId.trim(),
        authType: authType,
      };

      const response = await fetch(`${API_BASE_URL}/user-invitations/invite`, {
        method: "POST",
        headers: {
          accept: "*/*",
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(invitationData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log("Backend response:", result); // Add this to debug

        // Use the result from backend instead of manually creating the invitation
        const newInvitation: Invitation = {
          id: result.id,
          email: result.email,
          role: result.role,
          organizationId: result.organizationId,
          organizationName: result.organizationName, // This should come from backend now
          invitedDate: result.invitedDate,
          status: result.status,
          expiryDate: result.expiryDate,
          acceptedDate: result.acceptedDate,
          invitedBy: result.invitedBy,
          invitedByName: result.invitedByName,
        };

        onInvitationSent(newInvitation);

        setMessage({
          type: "success",
          text: `Invitation sent successfully to ${email}!`,
        });

        setTimeout(() => {
          setEmail("");
          setRole("admin");
          setOrganizationId("");
          setAuthType(1);
          setMessage(null);
        }, 2000);
      } else {
        const errorData = await response.json().catch(() => ({}));
        setMessage({
          type: "error",
          text:
            errorData.message ||
            `Failed to send invitation. Status: ${response.status}`,
        });
      }
    } catch (error) {
      console.error("Error sending invitation:", error);
      setMessage({
        type: "error",
        text: "Network error. Please check your connection and try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className=" bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            {setActiveTab && (
              <ActionButton
                variant="ghost"
                size="sm"
                onClick={() => setActiveTab("user-list")}
                className="p-2"
              >
                <ChevronLeft size={20} />
              </ActionButton>
            )}
            <div>
              <h1 className="text-3xl font-bold text-[#045024] flex items-center gap-3">
                <UserPlus className="text-[#2aa45c]" size={32} />
                Invite New User
              </h1>
              <p className="text-gray-600 mt-1">Send an invitation to user</p>
            </div>
          </div>
        </div>

        {/* Main Card */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
          <div className="bg-[#2aa45c] p-6">
            <h2 className="text-xl font-semibold text-white flex items-center gap-2">
              <Mail size={20} />
              Invitation Details
            </h2>
            <p className="text-green-100 mt-1">
              Fill in the details below to send an invitation
            </p>
          </div>

          <div className="p-8">
            {/* Message Display */}
            {message && (
              <div
                className={`mb-6 p-4 rounded-lg border-l-4 ${
                  message.type === "success"
                    ? "bg-green-50 border-green-400 text-green-800"
                    : "bg-red-50 border-red-400 text-red-800"
                }`}
              >
                <div className="flex items-center gap-2">
                  {message.type === "success" ? (
                    <CheckCircle size={18} className="text-green-600" />
                  ) : (
                    <XCircle size={18} className="text-red-600" />
                  )}
                  <span className="font-medium">{message.text}</span>
                </div>
              </div>
            )}

            <div className="space-y-8">
              {/* Email Input */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Email Address <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-3 pl-12 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent transition-all duration-200"
                    required
                  />
                  <Mail
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                    size={18}
                  />
                </div>
              </div>

              {/* Form Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Role Selection */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Role <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={role}
                    onChange={(e) => setRole(e.target.value)}
                    className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent transition-all duration-200"
                    required
                  >
                    <option value="admin">Admin</option>
                    <option value="payer">Payer</option>
                    <option value="finance_officer">Finance Officer</option>
                    <option value="senior_finance_officer">
                      Senior Finance Officer
                    </option>
                  </select>
                </div>

                {/* Auth Type */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Authentication Type <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={authType}
                    onChange={(e) => setAuthType(Number(e.target.value))}
                    className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent transition-all duration-200"
                    required
                  >
                    <option value={0}>Microsoft</option>
                    <option value={1}>Local</option>
                  </select>
                </div>

                {/* Organization Selection */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Organization <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <select
                      value={organizationId}
                      onChange={(e) => setOrganizationId(e.target.value)}
                      className="w-full px-4 py-3 pl-12 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent transition-all duration-200 appearance-none"
                      required
                    >
                      <option value="">Select an organization</option>
                      {organizations.map((org) => (
                        <option key={org.id} value={org.id}>
                          {org.name}
                        </option>
                      ))}
                    </select>
                    <Building
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                      size={18}
                    />
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <svg
                        className="fill-current h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-100">
                <ActionButton
                  onClick={sendInvitation}
                  disabled={
                    !email.trim() || !organizationId.trim() || isLoading
                  }
                  size="lg"
                  className="flex-1 sm:flex-none"
                >
                  {isLoading ? (
                    <>
                      <Loader2 size={18} className="animate-spin" />
                      Sending Invitation...
                    </>
                  ) : (
                    <>
                      <Mail size={18} />
                      Send Invitation
                    </>
                  )}
                </ActionButton>

                {setActiveTab && (
                  <ActionButton
                    variant="secondary"
                    onClick={() => setActiveTab("user-list")}
                    size="lg"
                  >
                    Cancel
                  </ActionButton>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const UserInvitations: React.FC<UserInvitationsProps> = ({
  invitations,
  onInvitationUpdate,
}) => {
  const [resendingId, setResendingId] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [confirmModal, setConfirmModal] = useState<{
    isOpen: boolean;
    type: "resend" | "delete";
    invitation: Invitation | null;
  }>({
    isOpen: false,
    type: "resend",
    invitation: null,
  });

  const handleResendInvitation = async (invitation: Invitation) => {
    try {
      setResendingId(invitation.id);
      const token = localStorage.getItem("auth_token");
      const response = await fetch(
        `${API_BASE_URL}/user-invitations/${invitation.id}/resend`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to resend invitation");
      }

      const updatedInvitations = invitations.map((inv) =>
        inv.id === invitation.id
          ? { ...inv, invitedDate: new Date().toISOString() }
          : inv
      );
      onInvitationUpdate(updatedInvitations);
      setConfirmModal({ isOpen: false, type: "resend", invitation: null });
    } catch (err) {
      console.error("Failed to resend invitation:", err);
    } finally {
      setResendingId(null);
    }
  };

  const handleDeleteInvitation = async (invitation: Invitation) => {
    try {
      setDeletingId(invitation.id);
      const token = localStorage.getItem("auth_token");
      const response = await fetch(
        `${API_BASE_URL}/user-invitations/${invitation.id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to delete invitation");
      }

      const updatedInvitations = invitations.filter(
        (inv) => inv.id !== invitation.id
      );
      onInvitationUpdate(updatedInvitations);
      setConfirmModal({ isOpen: false, type: "delete", invitation: null });
    } catch (err) {
      console.error("Failed to delete invitation:", err);
    } finally {
      setDeletingId(null);
    }
  };

  const openConfirmModal = (
    type: "resend" | "delete",
    invitation: Invitation
  ) => {
    setConfirmModal({ isOpen: true, type, invitation });
  };

  const handleConfirm = () => {
    if (!confirmModal.invitation) return;

    if (confirmModal.type === "resend") {
      handleResendInvitation(confirmModal.invitation);
    } else {
      handleDeleteInvitation(confirmModal.invitation);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatRole = (role: string) => {
    const roleMap: Record<string, string> = {
      admin: "Admin",
      payer: "Payer",
      finance_officer: "Finance Officer",
      senior_finance_officer: "Senior Finance Officer",
    };
    return roleMap[role] || role;
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: {
        color: "bg-yellow-100 text-yellow-800 border-yellow-200",
        icon: Clock,
      },
      accepted: {
        color: "bg-green-100 text-green-800 border-green-200",
        icon: CheckCircle,
      },
      expired: {
        color: "bg-red-100 text-red-800 border-red-200",
        icon: XCircle,
      },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <span
        className={`inline-flex items-center gap-1.5 px-3 py-1 rounded-full text-xs font-medium border ${config.color}`}
      >
        <Icon size={12} />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  return (
    <div className="mt-8">
      <div className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
        <div className="bg-[#2aa45c] p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-white flex items-center gap-2">
                <Users size={20} />
                Pending Invitations
              </h2>
              <p className="text-green-100 mt-1">
                {invitations.length} invitation
                {invitations.length !== 1 ? "s" : ""} waiting for response
              </p>
            </div>
            <div className="bg-white/20 rounded-lg px-3 py-1">
              <span className="text-white font-bold text-lg">
                {invitations.length}
              </span>
            </div>
          </div>
        </div>

        <div className="p-6">
          {invitations.length === 0 ? (
            <div className="text-center py-12">
              <Users className="mx-auto text-gray-300 mb-4" size={48} />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No pending invitations
              </h3>
              <p className="text-gray-500">
                All your invitations have been processed or you haven't sent any
                yet.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b-2 border-gray-100">
                    <th className="text-left py-4 px-2 font-semibold text-[#045024]">
                      User
                    </th>
                    <th className="text-left py-4 px-2 font-semibold text-[#045024]">
                      Role
                    </th>
                    <th className="text-left py-4 px-2 font-semibold text-[#045024]">
                      Organization
                    </th>
                    <th className="text-left py-4 px-2 font-semibold text-[#045024]">
                      Invited
                    </th>
                    <th className="text-left py-4 px-2 font-semibold text-[#045024]">
                      Status
                    </th>
                    <th className="text-left py-4 px-2 font-semibold text-[#045024]">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {invitations.map((invitation, index) => (
                    <tr
                      key={invitation.id}
                      className={`border-b border-gray-50 hover:bg-gray-50 transition-colors duration-150 ${
                        index % 2 === 0 ? "bg-white" : "bg-gray-25"
                      }`}
                    >
                      <td className="py-4 px-2">
                        <div className="flex items-center gap-3">
                          <div>
                            <div className="font-medium text-gray-900">
                              {invitation.email}
                            </div>
                            <div className="text-sm text-gray-500">
                              Invited by {invitation.invitedByName || "Unknown"}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-2">
                        <span className="inline-flex items-center px-2.5 py-1 rounded-lg text-sm font-medium bg-green-100 text-green-800">
                          {formatRole(invitation.role)}
                        </span>
                      </td>
                      <td className="py-4 px-2">
                        <div className="flex items-center gap-2">
                          <Building size={16} className="text-gray-400" />
                          <span className="text-gray-900">
                            {invitation.organizationName ||
                              invitation.organizationId}
                          </span>
                        </div>
                      </td>
                      <td className="py-4 px-2">
                        <div className="flex items-center gap-2">
                          <Calendar size={16} className="text-gray-400" />
                          <span className="text-sm text-gray-600">
                            {formatDate(invitation.invitedDate)}
                          </span>
                        </div>
                      </td>
                      <td className="py-4 px-2">
                        {getStatusBadge(invitation.status)}
                      </td>
                      <td className="py-4 px-2">
                        <div className="flex items-center gap-2">
                          <ActionButton
                            size="sm"
                            variant="secondary"
                            onClick={() =>
                              openConfirmModal("resend", invitation)
                            }
                            disabled={resendingId === invitation.id}
                          >
                            {resendingId === invitation.id ? (
                              <Loader2 className="animate-spin" size={14} />
                            ) : (
                              <Mail size={14} />
                            )}
                            Resend
                          </ActionButton>
                          <ActionButton
                            size="sm"
                            variant="danger"
                            onClick={() =>
                              openConfirmModal("delete", invitation)
                            }
                            disabled={deletingId === invitation.id}
                          >
                            {deletingId === invitation.id ? (
                              <Loader2 className="animate-spin" size={14} />
                            ) : (
                              <Trash2 size={14} />
                            )}
                            Cancel
                          </ActionButton>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmModal.isOpen}
        onClose={() =>
          setConfirmModal({ isOpen: false, type: "resend", invitation: null })
        }
        onConfirm={handleConfirm}
        title={
          confirmModal.type === "resend"
            ? "Resend Invitation"
            : "Cancel Invitation"
        }
        message={
          confirmModal.type === "resend"
            ? `Are you sure you want to resend the invitation to ${confirmModal.invitation?.email}?`
            : `Are you sure you want to cancel the invitation for ${confirmModal.invitation?.email}? This action cannot be undone.`
        }
        confirmText={
          confirmModal.type === "resend" ? "Resend" : "Cancel Invitation"
        }
        variant={confirmModal.type === "resend" ? "primary" : "danger"}
        isLoading={
          resendingId === confirmModal.invitation?.id ||
          deletingId === confirmModal.invitation?.id
        }
      />
    </div>
  );
};
// Main component that combines both CreateUser and UserInvitations
const UserInvitationManager: React.FC<{
  setActiveTab?: (tab: string) => void;
}> = ({ setActiveTab }) => {
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Function to enrich invitations with organization names
  const enrichInvitationsWithOrgNames = (
    invs: Invitation[],
    orgs: Organization[]
  ): Invitation[] => {
    return invs.map((inv) => {
      const org = orgs.find((o) => o.id === inv.organizationId);
      return {
        ...inv,
        organizationName: org ? org.name : null,
      };
    });
  };

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = localStorage.getItem("auth_token");

        // Fetch organizations
        const orgResponse = await fetch(`${API_BASE_URL}/Organization`, {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });

        if (!orgResponse.ok) {
          throw new Error("Failed to fetch organizations");
        }
        const orgData = await orgResponse.json();
        setOrganizations(orgData);

        // Fetch pending invitations
        const invResponse = await fetch(
          `${API_BASE_URL}/user-invitations/pending`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!invResponse.ok) {
          throw new Error("Failed to fetch pending invitations");
        }

        const invData = await invResponse.json();
        // Enrich invitations with organization names before setting state
        const enrichedInvitations = enrichInvitationsWithOrgNames(
          invData,
          orgData
        );
        setInvitations(enrichedInvitations);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "An unknown error occurred"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleInvitationSent = (newInvitation: Invitation) => {
    // Enrich the new invitation with organization name
    const org = organizations.find(
      (o) => o.id === newInvitation.organizationId
    );
    const enrichedInvitation = {
      ...newInvitation,
      organizationName: org ? org.name : null,
    };
    setInvitations((prev) => [enrichedInvitation, ...prev]);
  };

  const handleInvitationUpdate = (updatedInvitations: Invitation[]) => {
    setInvitations(updatedInvitations);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="animate-spin text-[#2aa45c]" size={32} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 text-red-800 p-4 rounded-lg">
        Error loading data: {error}
      </div>
    );
  }

  return (
    <div>
      <CreateUser
        setActiveTab={setActiveTab}
        onInvitationSent={handleInvitationSent}
        organizations={organizations}
      />
      <UserInvitations
        invitations={invitations}
        onInvitationUpdate={handleInvitationUpdate}
        organizations={organizations}
      />
    </div>
  );
};

export default UserInvitationManager;
