import React, { useState, useEffect } from 'react';
import {
  User,
  Edit,
  ArrowLeft,
  Building2,
  Tag,
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle,
  Activity,
  CreditCard,
} from 'lucide-react';
import { usePaymentProfileApi, useOrganizationApi } from '../../hooks/api';
import { useAuth } from '../../contexts/AuthContext';
import type { PaymentProfile, Organization } from '../../hooks/api';

interface PaymentProfileDetailsProps {
  paymentProfile: PaymentProfile;
  setActiveTab: (tab: string) => void;
  setSelectedPaymentProfile: (profile: PaymentProfile | null) => void;
}

const PaymentProfileDetails: React.FC<PaymentProfileDetailsProps> = ({
  paymentProfile,
  setActiveTab,
  setSelectedPaymentProfile,
}) => {
  const { loading, error } = usePaymentProfileApi();
  const { getAllOrganizations } = useOrganizationApi();
  const { user } = useAuth();
  
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [activeSection, setActiveSection] = useState<'overview' | 'activity'>('overview');

  // Role-based permissions
  const canEditProfiles = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');

  useEffect(() => {
    loadOrganization();
  }, [paymentProfile.organizationId]);

  const loadOrganization = async () => {
    const result = await getAllOrganizations();
    if (result) {
      const org = result.find(o => o.id === paymentProfile.organizationId);
      setOrganization(org || null);
    }
  };

  const handleEdit = () => {
    setSelectedPaymentProfile(paymentProfile);
    setActiveTab('edit-payment-profile');
  };

  const handleBack = () => {
    setSelectedPaymentProfile(null);
    setActiveTab('payment-profiles');
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
        isActive 
          ? 'bg-green-100 text-green-800' 
          : 'bg-red-100 text-red-800'
      }`}>
        {isActive ? (
          <>
            <CheckCircle className="w-4 h-4 mr-2" />
            Active
          </>
        ) : (
          <>
            <XCircle className="w-4 h-4 mr-2" />
            Inactive
          </>
        )}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleBack}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <ArrowLeft size={24} />
          </button>
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
              <User className="h-6 w-6 text-[#2aa45c]" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-[#045024]">{paymentProfile.name}</h2>
              <div className="flex items-center space-x-2">
                {getStatusBadge(paymentProfile.isActive)}
                <span className="text-gray-500">•</span>
                <span className="text-sm text-gray-500">
                  Created {new Date(paymentProfile.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </div>
        {canEditProfiles && (
          <button
            onClick={handleEdit}
            className="bg-[#2aa45c] text-white px-4 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2"
          >
            <Edit size={16} />
            <span>Edit Profile</span>
          </button>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={20} />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: User },
            { id: 'activity', label: 'Activity', icon: Activity },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveSection(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeSection === tab.id
                  ? 'border-[#2aa45c] text-[#2aa45c]'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content Sections */}
      {activeSection === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Information */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Profile Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Profile Name</label>
                  <p className="mt-1 text-sm text-gray-900">{paymentProfile.name}</p>
                </div>
                {paymentProfile.description && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Description</label>
                    <p className="mt-1 text-sm text-gray-900">{paymentProfile.description}</p>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Organization</h3>
              {organization ? (
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
                    <Building2 className="h-5 w-5 text-[#2aa45c]" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{organization.name}</p>
                    <p className="text-sm text-gray-500">{organization.contactEmail}</p>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-gray-500">Loading organization details...</p>
              )}
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Types</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {paymentProfile.paymentTypes.map((type, index) => (
                  <div
                    key={index}
                    className="flex items-center p-3 border border-gray-200 rounded-lg"
                  >
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-lg bg-blue-100 flex items-center justify-center">
                        <CreditCard className="h-4 w-4 text-blue-600" />
                      </div>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">{type}</p>
                    </div>
                  </div>
                ))}
              </div>
              {paymentProfile.paymentTypes.length === 0 && (
                <p className="text-sm text-gray-500">No payment types configured</p>
              )}
            </div>
          </div>

          {/* Status and Metadata */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Status Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Current Status</label>
                  <div className="mt-1">
                    {getStatusBadge(paymentProfile.isActive)}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-500">Payment Types Count</label>
                  <p className="mt-1 text-sm text-gray-900">{paymentProfile.paymentTypes.length} types</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">System Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Created By</label>
                  <p className="mt-1 text-sm text-gray-900">{paymentProfile.createdBy}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Created Date</label>
                  <div className="flex items-center mt-1">
                    <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                    <p className="text-sm text-gray-900">
                      {new Date(paymentProfile.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Stats</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">Total Payments</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">-</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Tag className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">Payment Types</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {paymentProfile.paymentTypes.length}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeSection === 'activity' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Profile Activity</h3>
          <div className="text-center py-8">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Activity tracking coming soon</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentProfileDetails;
