import React, { useState, useEffect } from 'react';
import {
  Building2,
  Edit,
  ArrowLeft,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Users,
  CreditCard,
  FileText,
  AlertCircle,
  CheckCircle,
  XCircle,
  Activity,
  TrendingUp,
  DollarSign,
} from 'lucide-react';
import { useOrganizationApi, useOrganizationComplianceApi } from '../../hooks/api';
import type { Organization, OrganizationCompliance } from '../../hooks/api';

interface OrganizationDetailsProps {
  organization: Organization;
  setActiveTab: (tab: string) => void;
  setSelectedOrganization: (org: Organization | null) => void;
}

const OrganizationDetails: React.FC<OrganizationDetailsProps> = ({
  organization,
  setActiveTab,
  setSelectedOrganization,
}) => {
  const { loading: orgLoading, error: orgError } = useOrganizationApi();
  const { 
    loading: complianceLoading, 
    error: complianceError, 
    getOrganizationComplianceStatus 
  } = useOrganizationComplianceApi();
  
  const [complianceData, setComplianceData] = useState<OrganizationCompliance | null>(null);
  const [activeSection, setActiveSection] = useState<'overview' | 'compliance' | 'activity'>('overview');

  useEffect(() => {
    loadComplianceData();
  }, [organization.id]);

  const loadComplianceData = async () => {
    const result = await getOrganizationComplianceStatus(organization.id);
    if (result) {
      setComplianceData(result);
    }
  };

  const handleEdit = () => {
    setSelectedOrganization(organization);
    setActiveTab('edit-organization');
  };

  const handleBack = () => {
    setSelectedOrganization(null);
    setActiveTab('organizations');
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        isActive 
          ? 'bg-green-100 text-green-800' 
          : 'bg-red-100 text-red-800'
      }`}>
        {isActive ? (
          <>
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </>
        ) : (
          <>
            <XCircle className="w-3 h-3 mr-1" />
            Inactive
          </>
        )}
      </span>
    );
  };

  const getComplianceStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'compliant':
        return 'text-green-600 bg-green-100';
      case 'non-compliant':
        return 'text-red-600 bg-red-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleBack}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <ArrowLeft size={24} />
          </button>
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
              <Building2 className="h-6 w-6 text-[#2aa45c]" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-[#045024]">{organization.name}</h2>
              <div className="flex items-center space-x-2">
                {getStatusBadge(organization.isActive)}
                <span className="text-gray-500">•</span>
                <span className="text-sm text-gray-500">
                  Created {new Date(organization.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </div>
        <button
          onClick={handleEdit}
          className="bg-[#2aa45c] text-white px-4 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2"
        >
          <Edit size={16} />
          <span>Edit Organization</span>
        </button>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: Building2 },
            { id: 'compliance', label: 'Compliance', icon: CheckCircle },
            { id: 'activity', label: 'Activity', icon: Activity },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveSection(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeSection === tab.id
                  ? 'border-[#2aa45c] text-[#2aa45c]'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content Sections */}
      {activeSection === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Basic Information */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Organization Name</label>
                  <p className="mt-1 text-sm text-gray-900">{organization.name}</p>
                </div>
                {organization.website && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Website</label>
                    <p className="mt-1 text-sm text-gray-900">
                      <a href={organization.website} target="_blank" rel="noopener noreferrer" className="text-[#2aa45c] hover:underline">
                        {organization.website}
                      </a>
                    </p>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-gray-400" />
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Email</label>
                    <p className="text-sm text-gray-900">{organization.email}</p>
                  </div>
                </div>
                {organization.phoneNumber && (
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-gray-400" />
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Phone</label>
                      <p className="text-sm text-gray-900">{organization.phoneNumber}</p>
                    </div>
                  </div>
                )}
                {(organization.address || organization.city || organization.state || organization.country) && (
                  <div className="flex items-start space-x-3">
                    <MapPin className="h-5 w-5 text-gray-400 mt-0.5" />
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Location</label>
                      <div className="text-sm text-gray-900 space-y-1">
                        {organization.address && <p>{organization.address}</p>}
                        {(organization.city || organization.state || organization.country) && (
                          <p>
                            {[organization.city, organization.state, organization.country]
                              .filter(Boolean)
                              .join(', ')}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Stats</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">Total Users</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">-</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">Total Payments</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">-</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">Certificates</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {complianceData?.certificatesCount || 0}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">System Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Created Date</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(organization.createdAt).toLocaleDateString()}
                  </p>
                </div>
                {organization.updatedAt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Last Updated</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(organization.updatedAt).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeSection === 'compliance' && (
        <div className="space-y-6">
          {complianceLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2aa45c]"></div>
            </div>
          ) : complianceError ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="text-red-500 mr-2" size={20} />
                <span className="text-red-700">{complianceError}</span>
              </div>
            </div>
          ) : complianceData ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <FileText className="h-8 w-8 text-[#2aa45c]" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Total Certificates</p>
                    <p className="text-2xl font-semibold text-gray-900">{complianceData.certificatesCount}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CheckCircle className="h-8 w-8 text-green-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Active Certificates</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {complianceData.certificatesCount - complianceData.expiredCertificates}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-8 w-8 text-yellow-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Expiring Soon</p>
                    <p className="text-2xl font-semibold text-gray-900">{complianceData.expiringCertificates}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <XCircle className="h-8 w-8 text-red-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Expired</p>
                    <p className="text-2xl font-semibold text-gray-900">{complianceData.expiredCertificates}</p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 rounded-lg p-8 text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No compliance data available</p>
            </div>
          )}
        </div>
      )}

      {activeSection === 'activity' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
          <div className="text-center py-8">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Activity tracking coming soon</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrganizationDetails;
