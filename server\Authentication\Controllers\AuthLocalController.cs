using Microsoft.AspNetCore.Mvc;
using Final_E_Receipt.Authentication.Services;
using Final_E_Receipt.Authentication.DTOs;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Authentication.Controllers
{
    [ApiController]
    [Route("api/auth/local")]
    public class AuthLocalController : ControllerBase
    {
        private readonly AuthenticationService _authService;
        private readonly ILogger<AuthLocalController> _logger;

        public AuthLocalController(AuthenticationService authService, ILogger<AuthLocalController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        [HttpPost("setup")]
        public async Task<IActionResult> CompleteSetup([FromBody] LocalRegistrationDTO dto)
        {
            try
            {
                var user = await _authService.CompleteLocalAccountSetup(
                    dto.Email, dto.TemporaryPassword, dto.NewPassword, dto.FirstName, dto.LastName);

                _logger.LogInformation("Local account setup completed for {Email}", dto.Email);

                return Ok(new
                {
                    user.Id,
                    user.Email,
                    user.FirstName,
                    user.LastName,
                    user.Role,
                    user.AuthType
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing local account setup for {Email}", dto.Email);
                return BadRequest(new { message = "Failed to complete account setup" });
            }
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LocalLoginDTO dto)
        {
            try
            {
                var user = await _authService.ValidateLocalLogin(dto.Email, dto.Password);
                if (user == null)
                    return Unauthorized(new { message = "Invalid credentials" });
                    // Generate JWT token
                var token = _authService.GenerateInternalJwtToken(user);

                _logger.LogInformation("Local login successful for {Email}", dto.Email);

                return Ok(new
                {
                    token,
                    user.Id,
                    user.Email,
                    user.FirstName,
                    user.LastName,
                    user.Role,
                    user.AuthType
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during local login for {Email}", dto.Email);
                return BadRequest(new { message = "Login failed" });
            }
        }
    }
}