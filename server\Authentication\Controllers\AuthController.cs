using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Final_E_Receipt.Authentication.Services;
using Final_E_Receipt.Authentication.DTOs;
using System.Security.Claims;
using Final_E_Receipt.Authentication.Models;
using System.ComponentModel.DataAnnotations;

namespace Final_E_Receipt.Authentication.Controllers
{
    [ApiController]
    [Route("api/auth")]
    public class AuthController : ControllerBase
    {
        private readonly AuthenticationService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(AuthenticationService authService, ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// Get current authenticated user info using Microsoft's official pattern
        /// </summary>
        [HttpGet("me")]
        [Authorize]
        public async Task<IActionResult> GetCurrentUser()
        {
            try
            {
                _logger.LogInformation("Getting current user info from token");

                var principal = HttpContext.User;
                var user = await _authService.ProcessMicrosoftLogin(principal);

                return Ok(new UserDTO
                {
                    Id = user.Id,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Email = user.Email,
                    //PhoneNumber = user.PhoneNumber,
                    Role = user.Role,
                    OrganizationId = user.OrganizationId,
                    IsActive = user.IsActive,
                    CreatedAt = user.CreatedAt,
                    LastLogin = user.LastLogin
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning("Unauthorized access attempt: {Message}", ex.Message);
                return Unauthorized(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current user");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Check authentication status
        /// </summary>
        [HttpGet("status")]
        [Authorize]
        public IActionResult GetAuthStatus()
        {
            try
            {
                var objectId = User.FindFirst("oid")?.Value;
                var email = User.FindFirst("preferred_username")?.Value;

                return Ok(new
                {
                    isAuthenticated = true,
                    objectId = objectId,
                    email = email,
                    claims = User.Claims.Select(c => new { type = c.Type, value = c.Value }).ToList()
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking auth status");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Health check endpoint (no auth required)
        /// </summary>
        [HttpGet("health")]
        [AllowAnonymous]
        public IActionResult HealthCheck()
        {
            return Ok(new { status = "healthy", timestamp = DateTime.UtcNow });
        }

        /// <summary>
        /// Detect authentication method based on email
        /// </summary>
        [HttpPost("detect")]
        [AllowAnonymous]
        public async Task<IActionResult> DetectAuthMethod([FromBody] EmailDetectionDTO dto)
        {
            try
            {
                _logger.LogInformation("Detecting auth method for email: {Email_service}", dto.Email);

                // Check if user exists in our system
                var existingUser = await _authService.GetUserByEmail(dto.Email);
                if (existingUser != null)
                {
                    _logger.LogInformation("Found existing user with auth type: {AuthType}", existingUser.AuthType);
                    return Ok(new
                    {
                        authMethod = existingUser.AuthType.ToString().ToLower(), // "microsoft" or "local"
                        userExists = true,
                        requiresSetup = existingUser.AuthType == AuthenticationType.LOCAL && existingUser.MustResetPassword
                    });
                }

                // Check for pending invitation
                var invitation = await _authService.GetPendingInvitation(dto.Email);
                if (invitation != null)
                {
                    _logger.LogInformation("Found pending invitation with auth type: {AuthType}", invitation.AuthType);
                    return Ok(new
                    {
                        authMethod = invitation.AuthType.ToString().ToLower(),
                        userExists = false,
                        requiresSetup = invitation.AuthType == AuthenticationType.LOCAL
                    });
                }

                // No user or invitation found
                _logger.LogWarning("No user or invitation found for email: {Email}", dto.Email);
                return NotFound(new
                {
                    message = "No account found for this email address. Please contact your administrator.",
                    authMethod = (string)null,
                    userExists = false
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error detecting auth method for email: {Email}", dto.Email);
                return BadRequest(new { message = "Failed to detect authentication method" });
            }
        }

        /// <summary>
        /// Exchange Microsoft token for internal JWT
        /// </summary>
        [HttpGet("exchange")]
        [Authorize] // Must be called with a valid Microsoft token
        public async Task<IActionResult> ExchangeMicrosoftToken()
        {
            var principal = HttpContext.User;
            var user = await _authService.ProcessMicrosoftLogin(principal);

            // Generate a new JWT with internal roles
            var token = _authService.GenerateInternalJwtToken(user);

            return Ok(new { token });
        }

        
        /// <summary>
        /// Login using email and password (local authentication)
        /// </summary>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginDto dto)
        {
            try
            {
                var user = await _authService.ValidateLocalLogin(dto.Email, dto.Password);
                
                if (user == null)
                {
                    return Unauthorized(new { message = "Invalid email or password" });
                }

                if (!user.IsActive)
                {
                    return Unauthorized(new { message = "Account is disabled" });
                }

                // Generate JWT token
                var token = _authService.GenerateInternalJwtToken(user);

                // Return user info and token
                return Ok(new
                {
                    token,
                    user = new UserDTO
                    {
                        Id = user.Id,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Email = user.Email,
                        Role = user.Role,
                        OrganizationId = user.OrganizationId,
                        IsActive = user.IsActive
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login attempt for email: {Email}", dto.Email);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        public class EmailDetectionDTO
        {
            public string Email { get; set; }
        }

        public class RegisterAdminDto
        {
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public string Email { get; set; }
            public string Password { get; set; }
        }

        public class LoginDto
        {
            [Required]
            [EmailAddress]
            public string Email { get; set; }

            [Required]
            public string Password { get; set; }
        }

    }
}
