# 🌐 Global Email Configuration System

## 🎯 **New Architecture Overview**

The email system has been restructured to support:

1. **Global Email Configurations** - Users can choose from multiple SMTP configurations
2. **Organization-Specific Templates** - Each organization has custom email templates for branding
3. **User-Selectable Configurations** - Users can switch between different email providers

## 🏗️ **Architecture Comparison**

### **❌ Old System:**
```
Organization A → Email Config A + Templates A
Organization B → Email Config B + Templates B
Organization C → Email Config C + Templates C
```

### **✅ New System:**
```
Global Email Configurations:
├── Gmail Configuration (selectable by any user)
├── Outlook Configuration (selectable by any user)
├── SendGrid Configuration (selectable by any user)
└── Custom SMTP Configuration (selectable by any user)

Organization-Specific Templates:
├── Organization A Templates (branding/content)
├── Organization B Templates (branding/content)
└── Organization C Templates (branding/content)
```

## 📊 **Database Schema Changes**

### **EmailConfigurations Table (Updated):**
```sql
CREATE TABLE EmailConfigurations (
    Id NVARCHAR(50) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,           -- NEW: "Gmail Config", "Outlook Config"
    Description NVARCHAR(500) NULL,        -- NEW: Description of the config
    SmtpServer NVARCHAR(255) NOT NULL,
    Port INT NOT NULL,
    Username NVARCHAR(255) NOT NULL,
    Password NVARCHAR(255) NOT NULL,
    EnableSsl BIT NOT NULL DEFAULT 1,
    IsDefault BIT NOT NULL DEFAULT 0,      -- System-wide default
    IsActive BIT NOT NULL DEFAULT 1,       -- NEW: Can be enabled/disabled
    SenderName NVARCHAR(100) NOT NULL,
    SenderEmail NVARCHAR(255) NOT NULL,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50),
    UpdatedAt DATETIME NULL,
    UpdatedBy NVARCHAR(50) NULL
    -- REMOVED: OrganizationId (no longer organization-specific)
);
```

### **EmailTemplates Table (Unchanged):**
```sql
-- Templates remain organization-specific for branding
CREATE TABLE EmailTemplates (
    Id NVARCHAR(50) PRIMARY KEY,
    OrganizationId NVARCHAR(50) NOT NULL,  -- Still organization-specific
    Name NVARCHAR(100) NOT NULL,
    Subject NVARCHAR(255) NOT NULL,
    BodyContent NVARCHAR(MAX) NOT NULL,
    Type NVARCHAR(50) NOT NULL,
    IsDefault BIT NOT NULL DEFAULT 0,
    -- ... other fields
);
```

## 🔧 **API Changes**

### **Email Configuration Endpoints:**

```http
# Get all available email configurations
GET /api/email-configurations

# Get only active email configurations (for user selection)
GET /api/email-configurations/active

# Create new global email configuration
POST /api/email-configurations
{
  "name": "Gmail Configuration",
  "description": "Gmail SMTP for company emails",
  "smtpServer": "smtp.gmail.com",
  "port": 587,
  "username": "<EMAIL>",
  "password": "app-password",
  "enableSsl": true,
  "isDefault": false,
  "isActive": true,
  "senderName": "Company Name",
  "senderEmail": "<EMAIL>"
}
```

### **Email Sending (Updated):**

```csharp
// Send email with specific configuration
await emailService.SendCustomEmailAsync(
    recipientEmail: "<EMAIL>",
    recipientName: "John Doe",
    subject: "Payment Due",
    body: "<h1>Payment is due</h1>",
    isHtml: true,
    emailConfigId: "gmail-config-id"  // NEW: Optional config selection
);

// Send templated email (templates still organization-specific)
await emailService.SendTemplatedEmailAsync(
    organizationId: "org-123",        // For template selection
    templateType: "PAYMENT_DUE",
    recipientEmail: "<EMAIL>",
    recipientName: "John Doe",
    templateData: new Dictionary<string, string> { ... },
    emailConfigId: "outlook-config-id"  // NEW: Optional config selection
);
```

## 🎨 **User Experience**

### **Admin Configuration:**
1. **Create Global Email Configurations:**
   - Gmail Configuration
   - Outlook Configuration
   - SendGrid Configuration
   - Custom SMTP Configuration

2. **Manage Organization Templates:**
   - Payment Due Templates
   - Receipt Templates
   - Invitation Templates
   - Custom Templates

### **User Email Sending:**
1. **Select Email Configuration:**
   - Choose from available active configurations
   - Use default if none selected

2. **Organization Branding:**
   - Templates automatically use organization-specific branding
   - Content customized per organization

## 🚀 **Migration Steps**

### **1. Update Database Schema:**
```sql
-- Add new columns to EmailConfigurations
ALTER TABLE EmailConfigurations ADD Name NVARCHAR(100);
ALTER TABLE EmailConfigurations ADD Description NVARCHAR(500);
ALTER TABLE EmailConfigurations ADD IsActive BIT DEFAULT 1;

-- Update existing records
UPDATE EmailConfigurations SET 
    Name = 'Legacy Configuration',
    Description = 'Migrated from organization-specific config',
    IsActive = 1;

-- Remove OrganizationId dependency (optional, for cleanup)
-- ALTER TABLE EmailConfigurations DROP COLUMN OrganizationId;
```

### **2. Create Sample Global Configurations:**
```sql
-- Gmail Configuration
INSERT INTO EmailConfigurations (Id, Name, Description, SmtpServer, Port, Username, Password, EnableSsl, IsDefault, IsActive, SenderName, SenderEmail, CreatedBy)
VALUES (NEWID(), 'Gmail Configuration', 'Gmail SMTP for company emails', 'smtp.gmail.com', 587, '<EMAIL>', 'app-password', 1, 1, 1, 'Company Name', '<EMAIL>', 'SYSTEM');

-- Outlook Configuration
INSERT INTO EmailConfigurations (Id, Name, Description, SmtpServer, Port, Username, Password, EnableSsl, IsDefault, IsActive, SenderName, SenderEmail, CreatedBy)
VALUES (NEWID(), 'Outlook Configuration', 'Outlook SMTP for company emails', 'smtp-mail.outlook.com', 587, '<EMAIL>', 'password', 1, 0, 1, 'Company Name', '<EMAIL>', 'SYSTEM');
```

## ✅ **Benefits of New System**

1. **Flexibility:** Users can switch between email providers easily
2. **Centralized Management:** Global email configurations managed in one place
3. **Organization Branding:** Templates remain organization-specific for branding
4. **Scalability:** Easy to add new email providers without affecting organizations
5. **User Choice:** Users can select preferred email configuration
6. **Maintenance:** Easier to update SMTP settings globally

## 🎯 **Use Cases**

1. **Multi-Provider Setup:** Use Gmail for marketing, SendGrid for transactional
2. **Failover:** Switch to backup email provider if primary fails
3. **Testing:** Use different configurations for development/production
4. **User Preference:** Let users choose their preferred email provider
5. **Cost Optimization:** Switch providers based on volume/cost

This new architecture provides the flexibility you requested while maintaining organization-specific branding through templates!
