import { useState, useCallback } from 'react';
import { apiService } from '../../services/apiService';

// Types for Receipt API
export interface Receipt {
  id: string;
  paymentId: string;
  organizationId: string;
  receiptNumber: string;
  amount: number;
  currency: string;
  issuedDate: string;
  issuedBy: string;
  status: 'ACTIVE' | 'REVOKED';
  revokedDate?: string;
  revokedBy?: string;
  revokedReason?: string;
  receiptData: any; // JSON data
  createdAt: string;
  updatedAt?: string;
}

export interface CreateReceiptDTO {
  paymentId: string;
  organizationId: string;
  amount: number;
  currency: string;
  receiptData?: any;
}

export interface RevokeReceiptDTO {
  revokedReason: string;
}

export interface ReceiptTemplate {
  id: string;
  name: string;
  description?: string;
  templateData: any; // JSON template
  isActive: boolean;
  createdBy: string;
  createdAt: string;
}

export interface CreateReceiptTemplateDTO {
  name: string;
  description?: string;
  templateData: any;
}

// Receipt API Hooks
export const useReceiptApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Receipt CRUD endpoints
  const getAllReceipts = useCallback(() => 
    handleApiCall(() => apiService.get<Receipt[]>('/Receipt')), [handleApiCall]);

  const getReceiptById = useCallback((id: string) => 
    handleApiCall(() => apiService.get<Receipt>(`/Receipt/${id}`)), [handleApiCall]);

  const createReceipt = useCallback((data: CreateReceiptDTO) => 
    handleApiCall(() => apiService.post<Receipt>('/Receipt', data)), [handleApiCall]);

  const updateReceipt = useCallback((id: string, data: Partial<Receipt>) => 
    handleApiCall(() => apiService.put<Receipt>(`/Receipt/${id}`, data)), [handleApiCall]);

  const deleteReceipt = useCallback((id: string) => 
    handleApiCall(() => apiService.delete(`/Receipt/${id}`)), [handleApiCall]);

  // Receipt management
  const revokeReceipt = useCallback((id: string, data: RevokeReceiptDTO) => 
    handleApiCall(() => apiService.put(`/Receipt/${id}/revoke`, data)), [handleApiCall]);

  const downloadReceipt = useCallback((id: string) => 
    handleApiCall(() => apiService.get(`/Receipt/${id}/download`)), [handleApiCall]);

  const printReceipt = useCallback((id: string) => 
    handleApiCall(() => apiService.get(`/Receipt/${id}/print`)), [handleApiCall]);

  // Receipt filtering and search
  const getReceiptsByOrganization = useCallback((organizationId: string) => 
    handleApiCall(() => apiService.get<Receipt[]>(`/Receipt/organization/${organizationId}`)), [handleApiCall]);

  const getReceiptsByPayment = useCallback((paymentId: string) => 
    handleApiCall(() => apiService.get<Receipt[]>(`/Receipt/payment/${paymentId}`)), [handleApiCall]);

  const getReceiptsByDateRange = useCallback((organizationId: string, startDate: string, endDate: string) => 
    handleApiCall(() => apiService.get<Receipt[]>(`/Receipt/organization/${organizationId}/daterange`, { startDate, endDate })), [handleApiCall]);

  const getReceiptsByStatus = useCallback((status: string) => 
    handleApiCall(() => apiService.get<Receipt[]>(`/Receipt/status/${status}`)), [handleApiCall]);

  const searchReceipts = useCallback((query: string) => 
    handleApiCall(() => apiService.get<Receipt[]>('/Receipt/search', { query })), [handleApiCall]);

  return {
    loading,
    error,
    getAllReceipts,
    getReceiptById,
    createReceipt,
    updateReceipt,
    deleteReceipt,
    revokeReceipt,
    downloadReceipt,
    printReceipt,
    getReceiptsByOrganization,
    getReceiptsByPayment,
    getReceiptsByDateRange,
    getReceiptsByStatus,
    searchReceipts,
  };
};

// Receipt Template API Hooks
export const useReceiptTemplateApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Receipt Template endpoints
  const getAllReceiptTemplates = useCallback(() => 
    handleApiCall(() => apiService.get<ReceiptTemplate[]>('/ReceiptTemplate')), [handleApiCall]);

  const getReceiptTemplateById = useCallback((id: string) => 
    handleApiCall(() => apiService.get<ReceiptTemplate>(`/ReceiptTemplate/${id}`)), [handleApiCall]);

  const createReceiptTemplate = useCallback((data: CreateReceiptTemplateDTO) => 
    handleApiCall(() => apiService.post<ReceiptTemplate>('/ReceiptTemplate', data)), [handleApiCall]);

  const updateReceiptTemplate = useCallback((id: string, data: Partial<ReceiptTemplate>) => 
    handleApiCall(() => apiService.put<ReceiptTemplate>(`/ReceiptTemplate/${id}`, data)), [handleApiCall]);

  const deleteReceiptTemplate = useCallback((id: string) => 
    handleApiCall(() => apiService.delete(`/ReceiptTemplate/${id}`)), [handleApiCall]);

  const getActiveReceiptTemplates = useCallback(() => 
    handleApiCall(() => apiService.get<ReceiptTemplate[]>('/ReceiptTemplate/active')), [handleApiCall]);

  return {
    loading,
    error,
    getAllReceiptTemplates,
    getReceiptTemplateById,
    createReceiptTemplate,
    updateReceiptTemplate,
    deleteReceiptTemplate,
    getActiveReceiptTemplates,
  };
};
