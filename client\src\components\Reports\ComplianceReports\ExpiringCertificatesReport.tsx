import React, { useState, useEffect } from 'react';
import { Calendar, AlertTriangle, Clock, RefreshCw, Award } from 'lucide-react';
import { useComplianceReportingApi, useOrganizationApi } from '../../../hooks/api';
import ReportFilters, {type FilterOptions } from '../Common/ReportFilters';
import ReportTable, { type TableColumn } from '../Common/ReportTable';
import ReportSummaryCard from '../Common/ReportSummaryCard';
import ExportButtons from '../Common/ExportButtons';

interface ExpiringCertificate {
  certificateId: string;
  certificateNumber: string;
  organizationId: string;
  organizationName: string;
  certificateType: string;
  issueDate: string;
  expiryDate: string;
  daysUntilExpiry: number;
  status: 'ACTIVE' | 'EXPIRING_SOON' | 'EXPIRED';
  urgencyLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  contactEmail: string;
  lastRenewalDate?: string;
  renewalRequired: boolean;
}

interface ExpiringCertificatesSummary {
  totalCertificates: number;
  expiringIn7Days: number;
  expiringIn30Days: number;
  expiringIn90Days: number;
  expired: number;
  urgencyBreakdown: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
}

const ExpiringCertificatesReport: React.FC = () => {
  const { getExpiringCertificatesReport, exportComplianceReport, loading } = useComplianceReportingApi();
  const { getAllOrganizations } = useOrganizationApi();
  
  const [filters, setFilters] = useState<FilterOptions>({});
  const [certificateData, setCertificateData] = useState<ExpiringCertificate[]>([]);
  const [summary, setSummary] = useState<ExpiringCertificatesSummary | null>(null);
  const [organizations, setOrganizations] = useState<Array<{ id: string; name: string }>>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    pageSize: 50,
    totalCount: 0,
  });
  const [sortBy, setSortBy] = useState<string>('daysUntilExpiry');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  useEffect(() => {
    loadOrganizations();
    loadExpiringCertificates();
  }, [filters, pagination.currentPage, sortBy, sortDirection]);

  const loadOrganizations = async () => {
    const orgs = await getAllOrganizations();
    if (orgs) {
      setOrganizations(orgs.map(org => ({ id: org.id, name: org.name })));
    }
  };

  const loadExpiringCertificates = async () => {
    const result = await getExpiringCertificatesReport();
    if (result) {
      setCertificateData(result.certificates || []);
      setSummary(result.summary || null);
      setPagination(prev => ({
        ...prev,
        totalPages: result.totalPages || 1,
        totalCount: result.totalCount || 0,
      }));
    }
  };

  const handleSort = (column: string, direction: 'asc' | 'desc') => {
    setSortBy(column);
    setSortDirection(direction);
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  const handleExportCSV = async () => {
    await exportComplianceReport('csv');
  };

  const handleExportExcel = async () => {
    await exportComplianceReport('excel');
  };

  const getStatusBadge = (status: string, daysUntilExpiry: number) => {
    if (daysUntilExpiry < 0) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          Expired
        </span>
      );
    }
    
    if (daysUntilExpiry <= 7) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          Critical
        </span>
      );
    }
    
    if (daysUntilExpiry <= 30) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
          Expiring Soon
        </span>
      );
    }
    
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
        Active
      </span>
    );
  };

  const getUrgencyBadge = (urgency: string) => {
    const urgencyConfig = {
      LOW: { bg: 'bg-green-100', text: 'text-green-800', label: 'Low' },
      MEDIUM: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Medium' },
      HIGH: { bg: 'bg-orange-100', text: 'text-orange-800', label: 'High' },
      CRITICAL: { bg: 'bg-red-100', text: 'text-red-800', label: 'Critical' },
    };

    const config = urgencyConfig[urgency as keyof typeof urgencyConfig] || urgencyConfig.LOW;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const formatDaysUntilExpiry = (days: number) => {
    if (days < 0) return `${Math.abs(days)} days ago`;
    if (days === 0) return 'Today';
    if (days === 1) return '1 day';
    return `${days} days`;
  };

  const columns: TableColumn[] = [
    {
      key: 'certificateNumber',
      label: 'Certificate #',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: 'organizationName',
      label: 'Organization',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm text-gray-500">{row.contactEmail}</div>
        </div>
      ),
    },
    {
      key: 'certificateType',
      label: 'Type',
      sortable: true,
    },
    {
      key: 'expiryDate',
      label: 'Expiry Date',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'daysUntilExpiry',
      label: 'Days Until Expiry',
      sortable: true,
      render: (value) => (
        <span className={`font-medium ${value < 0 ? 'text-red-600' : value <= 30 ? 'text-orange-600' : 'text-green-600'}`}>
          {formatDaysUntilExpiry(value)}
        </span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value, row) => getStatusBadge(value, row.daysUntilExpiry),
    },
    {
      key: 'urgencyLevel',
      label: 'Urgency',
      sortable: true,
      render: (value) => getUrgencyBadge(value),
    },
    {
      key: 'renewalRequired',
      label: 'Renewal',
      render: (value) => (
        <span className={`text-sm ${value ? 'text-red-600 font-medium' : 'text-gray-500'}`}>
          {value ? 'Required' : 'Not Required'}
        </span>
      ),
    },
  ];

  const statusOptions = [
    { value: 'ACTIVE', label: 'Active' },
    { value: 'EXPIRING_SOON', label: 'Expiring Soon' },
    { value: 'EXPIRED', label: 'Expired' },
  ];

  const urgencyOptions = [
    { value: 'LOW', label: 'Low' },
    { value: 'MEDIUM', label: 'Medium' },
    { value: 'HIGH', label: 'High' },
    { value: 'CRITICAL', label: 'Critical' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 rounded-lg bg-orange-100 flex items-center justify-center">
            <Calendar className="h-5 w-5 text-orange-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Expiring Certificates Report</h2>
            <p className="text-gray-600">Track certificate expiry dates and renewal requirements</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={loadExpiringCertificates}
            disabled={loading}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2 disabled:opacity-50"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
          <ExportButtons
            onExportCSV={handleExportCSV}
            onExportExcel={handleExportExcel}
            disabled={loading}
            variant="dropdown"
          />
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <ReportSummaryCard
            title="Total Certificates"
            value={summary.totalCertificates}
            icon={Award}
            color="blue"
            loading={loading}
          />
          <ReportSummaryCard
            title="Expiring in 7 Days"
            value={summary.expiringIn7Days}
            icon={AlertTriangle}
            color="red"
            loading={loading}
          />
          <ReportSummaryCard
            title="Expiring in 30 Days"
            value={summary.expiringIn30Days}
            icon={Clock}
            color="orange"
            loading={loading}
          />
          <ReportSummaryCard
            title="Already Expired"
            value={summary.expired}
            icon={AlertTriangle}
            color="red"
            loading={loading}
          />
        </div>
      )}

      {/* Urgency Breakdown */}
      {summary && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Urgency Breakdown</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[
              { key: 'critical', label: 'Critical', color: 'red' },
              { key: 'high', label: 'High', color: 'orange' },
              { key: 'medium', label: 'Medium', color: 'yellow' },
              { key: 'low', label: 'Low', color: 'green' },
            ].map((urgency) => {
              const count = summary.urgencyBreakdown[urgency.key as keyof typeof summary.urgencyBreakdown];
              return (
                <div key={urgency.key} className="text-center">
                  <div className={`text-2xl font-bold text-${urgency.color}-600`}>
                    {count}
                  </div>
                  <div className="text-sm text-gray-500">{urgency.label} Priority</div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Filters */}
      <ReportFilters
        filters={filters}
        onFiltersChange={setFilters}
        organizations={organizations}
        statusOptions={statusOptions}
        categoryOptions={urgencyOptions}
        showStatusFilter={true}
        showCategoryFilter={true}
        showDateFilters={true}
      />

      {/* Data Table */}
      <ReportTable
        columns={columns}
        data={certificateData}
        loading={loading}
        sortBy={sortBy}
        sortDirection={sortDirection}
        onSort={handleSort}
        pagination={{
          ...pagination,
          onPageChange: handlePageChange,
        }}
        emptyMessage="No expiring certificates found for the selected filters"
      />
    </div>
  );
};

export default ExpiringCertificatesReport;
