{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\notifications\\controllers\\emailconfigurationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:notifications\\controllers\\emailconfigurationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\notifications\\dtos\\emailconfigurationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:notifications\\dtos\\emailconfigurationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\authentication\\controllers\\authlocalcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:authentication\\controllers\\authlocalcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\authentication\\sql\\authenticationprocedures.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:authentication\\sql\\authenticationprocedures.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\controllers\\paymentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\controllers\\paymentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\services\\paymentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\services\\paymentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\controllers\\paymentapprovalcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\controllers\\paymentapprovalcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\common\\services\\auditservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:common\\services\\auditservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\authentication\\services\\authenticationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:authentication\\services\\authenticationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\authentication\\controllers\\userinvitationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:authentication\\controllers\\userinvitationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\authentication\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:authentication\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\controllers\\paymenttypecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\controllers\\paymenttypecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\authentication\\controllers\\adminsetupcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:authentication\\controllers\\adminsetupcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\services\\paymenttypeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\services\\paymenttypeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\models\\payment.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\models\\payment.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\sql\\paymentprocedures.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\sql\\paymentprocedures.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\dtos\\paymenttypedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\dtos\\paymenttypedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\dtos\\paymentdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\dtos\\paymentdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\services\\databaseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:services\\databaseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\organizations\\controllers\\organizationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:organizations\\controllers\\organizationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\notifications\\sql\\notificationprocedures.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:notifications\\sql\\notificationprocedures.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\notifications\\sql\\notificationmanagementprocedures.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:notifications\\sql\\notificationmanagementprocedures.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\authentication\\dtos\\localregistrationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:authentication\\dtos\\localregistrationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\compliance\\controllers\\compliancecertificatecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:compliance\\controllers\\compliancecertificatecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\authentication\\dtos\\createinvitationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:authentication\\dtos\\createinvitationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\receipts\\controllers\\receiptfilecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:receipts\\controllers\\receiptfilecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\controllers\\paymentschedulecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\controllers\\paymentschedulecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\controllers\\paymentproofcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\controllers\\paymentproofcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\compliance\\controllers\\compliancecertificatefilecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:compliance\\controllers\\compliancecertificatefilecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\files\\controllers\\filecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:files\\controllers\\filecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\notifications\\dtos\\notificationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:notifications\\dtos\\notificationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\reporting\\services\\reportingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:reporting\\services\\reportingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\reporting\\services\\reportexportservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:reporting\\services\\reportexportservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\reporting\\services\\reportexcelservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:reporting\\services\\reportexcelservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\reporting\\services\\reportcsvservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:reporting\\services\\reportcsvservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\reporting\\services\\compliancereportingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:reporting\\services\\compliancereportingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\receipts\\services\\receipttemplateservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:receipts\\services\\receipttemplateservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\receipts\\services\\receiptservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:receipts\\services\\receiptservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\receipts\\services\\receiptfileservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:receipts\\services\\receiptfileservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\services\\paymentscheduleservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\services\\paymentscheduleservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\services\\paymentscheduleimportservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\services\\paymentscheduleimportservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\services\\paymentproofservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\services\\paymentproofservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\services\\paymentprofileservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\services\\paymentprofileservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\services\\paymentcomplianceservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\services\\paymentcomplianceservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\services\\paymentapprovalservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\services\\paymentapprovalservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\organizations\\services\\organizationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:organizations\\services\\organizationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\organizations\\services\\organizationcomplianceservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:organizations\\services\\organizationcomplianceservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\notifications\\services\\useremailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:notifications\\services\\useremailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\notifications\\services\\notificationmanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:notifications\\services\\notificationmanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\notifications\\services\\emailtemplateservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:notifications\\services\\emailtemplateservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\notifications\\services\\emailconfigurationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:notifications\\services\\emailconfigurationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\notifications\\services\\centralizedemailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:notifications\\services\\centralizedemailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\files\\services\\fileservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:files\\services\\fileservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\documents\\services\\certificatetemplateservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:documents\\services\\certificatetemplateservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\documents\\services\\brandeddocumentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:documents\\services\\brandeddocumentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\compliance\\services\\compliancecertificatefileservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:compliance\\services\\compliancecertificatefileservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\compliance\\services\\compliancecertificateservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:compliance\\services\\compliancecertificateservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\compliance\\dtos\\compliancecertificatedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:compliance\\dtos\\compliancecertificatedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\notifications\\dtos\\notificationfiltersdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:notifications\\dtos\\notificationfiltersdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\notifications\\controllers\\notificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:notifications\\controllers\\notificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\documents\\controllers\\certificatetemplatecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:documents\\controllers\\certificatetemplatecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\compliance\\sql\\compliancecertificateprocedures.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:compliance\\sql\\compliancecertificateprocedures.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\compliance\\models\\compliancecertificate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:compliance\\models\\compliancecertificate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\organizations\\dtos\\organizationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:organizations\\dtos\\organizationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\sql\\dummytestdata.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:sql\\dummytestdata.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\fileuploadoperationfilter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:fileuploadoperationfilter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\final_e_receipt.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:final_e_receipt.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\jrb_implementation_status.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:jrb_implementation_status.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\wwwroot\\templates\\template_file_names.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:wwwroot\\templates\\template_file_names.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\wwwroot\\templates\\readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:wwwroot\\templates\\readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\models\\paymentschedule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\models\\paymentschedule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\reporting\\controllers\\reportingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:reporting\\controllers\\reportingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\payments\\controllers\\paymentprofilecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:payments\\controllers\\paymentprofilecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\integration_summary.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:integration_summary.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|c:\\users\\<USER>\\source\\repos\\final_e_receipt\\server\\.gitignore||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{1C99EEDF-87FF-4C47-8C1E-C42DC66CCFB1}|Final_E_Receipt.csproj|solutionrelative:.gitignore||{3B902123-F8A7-4915-9F01-361F908088D0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 270, "SelectedChildIndex": 11, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c64b9c2-e352-428e-a56d-0ace190b99a6}"}, {"$type": "Bookmark", "Name": "ST:0:0:{40ea2e6b-2121-4bb8-a43e-c83c04b51041}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "EmailConfigurationDTO.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\DTOs\\EmailConfigurationDTO.cs", "RelativeDocumentMoniker": "Notifications\\DTOs\\EmailConfigurationDTO.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\DTOs\\EmailConfigurationDTO.cs", "RelativeToolTip": "Notifications\\DTOs\\EmailConfigurationDTO.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAYwAMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T12:38:24.574Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AuthLocalController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\Controllers\\AuthLocalController.cs", "RelativeDocumentMoniker": "Authentication\\Controllers\\AuthLocalController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\Controllers\\AuthLocalController.cs", "RelativeToolTip": "Authentication\\Controllers\\AuthLocalController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAABbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T12:36:28.833Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "UserInvitationController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\Controllers\\UserInvitationController.cs", "RelativeDocumentMoniker": "Authentication\\Controllers\\UserInvitationController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\Controllers\\UserInvitationController.cs", "RelativeToolTip": "Authentication\\Controllers\\UserInvitationController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T09:38:42.194Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "AuthenticationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\Services\\AuthenticationService.cs", "RelativeDocumentMoniker": "Authentication\\Services\\AuthenticationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\Services\\AuthenticationService.cs", "RelativeToolTip": "Authentication\\Services\\AuthenticationService.cs", "ViewState": "AgIAAGQAAAAAAAAAAAA8wIYAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T09:27:43.835Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Program.cs", "RelativeDocumentMoniker": "Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Program.cs", "RelativeToolTip": "Program.cs", "ViewState": "AgIAABUBAAAAAAAAAAAAAOQAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T09:40:25.842Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "PaymentApprovalController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Controllers\\PaymentApprovalController.cs", "RelativeDocumentMoniker": "Payments\\Controllers\\PaymentApprovalController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Controllers\\PaymentApprovalController.cs", "RelativeToolTip": "Payments\\Controllers\\PaymentApprovalController.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAAAAsAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T17:12:31.591Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "EmailConfigurationController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Controllers\\EmailConfigurationController.cs", "RelativeDocumentMoniker": "Notifications\\Controllers\\EmailConfigurationController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Controllers\\EmailConfigurationController.cs", "RelativeToolTip": "Notifications\\Controllers\\EmailConfigurationController.cs", "ViewState": "AgIAABgAAAAAAAAAAAAAABwAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T14:30:51.868Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "PaymentController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Controllers\\PaymentController.cs", "RelativeDocumentMoniker": "Payments\\Controllers\\PaymentController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Controllers\\PaymentController.cs", "RelativeToolTip": "Payments\\Controllers\\PaymentController.cs", "ViewState": "AgIAAEUAAAAAAAAAAAAAACIAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T15:02:51.743Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "AuthenticationProcedures.sql", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\SQL\\AuthenticationProcedures.sql", "RelativeDocumentMoniker": "Authentication\\SQL\\AuthenticationProcedures.sql", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\SQL\\AuthenticationProcedures.sql", "RelativeToolTip": "Authentication\\SQL\\AuthenticationProcedures.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-13T13:04:34.251Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "AuthController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "Authentication\\Controllers\\AuthController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\Controllers\\AuthController.cs", "RelativeToolTip": "Authentication\\Controllers\\AuthController.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAAABAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T10:37:53.634Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "AuditService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Common\\Services\\AuditService.cs", "RelativeDocumentMoniker": "Common\\Services\\AuditService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Common\\Services\\AuditService.cs", "RelativeToolTip": "Common\\Services\\AuditService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T14:50:27.273Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "PaymentService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentService.cs", "RelativeDocumentMoniker": "Payments\\Services\\PaymentService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentService.cs", "RelativeToolTip": "Payments\\Services\\PaymentService.cs", "ViewState": "AgIAAGkAAAAAAAAAAAAkwHcAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T18:09:07.503Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "AdminSetupController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\Controllers\\AdminSetupController.cs", "RelativeDocumentMoniker": "Authentication\\Controllers\\AdminSetupController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\Controllers\\AdminSetupController.cs", "RelativeToolTip": "Authentication\\Controllers\\AdminSetupController.cs", "ViewState": "AgIAABoAAAAAAAAAAAAAAH4AAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T13:28:02.302Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "PaymentTypeDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\DTOs\\PaymentTypeDto.cs", "RelativeDocumentMoniker": "Payments\\DTOs\\PaymentTypeDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\DTOs\\PaymentTypeDto.cs", "RelativeToolTip": "Payments\\DTOs\\PaymentTypeDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T23:07:16.502Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "PaymentTypeController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Controllers\\PaymentTypeController.cs", "RelativeDocumentMoniker": "Payments\\Controllers\\PaymentTypeController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Controllers\\PaymentTypeController.cs", "RelativeToolTip": "Payments\\Controllers\\PaymentTypeController.cs", "ViewState": "AgIAABIAAAAAAAAAAAAAAGcAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T23:08:38.636Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "PaymentProcedures.sql", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\SQL\\PaymentProcedures.sql", "RelativeDocumentMoniker": "Payments\\SQL\\PaymentProcedures.sql", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\SQL\\PaymentProcedures.sql", "RelativeToolTip": "Payments\\SQL\\PaymentProcedures.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-28T23:11:55.25Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "PaymentTypeService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentTypeService.cs", "RelativeDocumentMoniker": "Payments\\Services\\PaymentTypeService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentTypeService.cs", "RelativeToolTip": "Payments\\Services\\PaymentTypeService.cs", "ViewState": "AgIAAF0AAAAAAAAAAAAAAHAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T23:10:11.976Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "Payment.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Models\\Payment.cs", "RelativeDocumentMoniker": "Payments\\Models\\Payment.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Models\\Payment.cs", "RelativeToolTip": "Payments\\Models\\Payment.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T23:50:40.422Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "DatabaseService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Services\\DatabaseService.cs", "RelativeDocumentMoniker": "Services\\DatabaseService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Services\\DatabaseService.cs", "RelativeToolTip": "Services\\DatabaseService.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAjwD8AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T09:01:48.012Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAACWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-10T13:16:27.021Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "PaymentDTO.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\DTOs\\PaymentDTO.cs", "RelativeDocumentMoniker": "Payments\\DTOs\\PaymentDTO.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\DTOs\\PaymentDTO.cs", "RelativeToolTip": "Payments\\DTOs\\PaymentDTO.cs", "ViewState": "AgIAADoAAAAAAAAAAAAAAEgAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T17:12:24.407Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "OrganizationController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Organizations\\Controllers\\OrganizationController.cs", "RelativeDocumentMoniker": "Organizations\\Controllers\\OrganizationController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Organizations\\Controllers\\OrganizationController.cs", "RelativeToolTip": "Organizations\\Controllers\\OrganizationController.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAAABsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T11:06:14.231Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "NotificationManagementProcedures.sql", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\SQL\\NotificationManagementProcedures.sql", "RelativeDocumentMoniker": "Notifications\\SQL\\NotificationManagementProcedures.sql", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\SQL\\NotificationManagementProcedures.sql", "RelativeToolTip": "Notifications\\SQL\\NotificationManagementProcedures.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-14T15:15:23.642Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "NotificationProcedures.sql", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\SQL\\NotificationProcedures.sql", "RelativeDocumentMoniker": "Notifications\\SQL\\NotificationProcedures.sql", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\SQL\\NotificationProcedures.sql", "RelativeToolTip": "Notifications\\SQL\\NotificationProcedures.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-14T15:11:57.101Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "LocalRegistrationDTO.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\DTOs\\LocalRegistrationDTO.cs", "RelativeDocumentMoniker": "Authentication\\DTOs\\LocalRegistrationDTO.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\DTOs\\LocalRegistrationDTO.cs", "RelativeToolTip": "Authentication\\DTOs\\LocalRegistrationDTO.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAAABYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T12:13:10.12Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "ComplianceCertificateController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\Controllers\\ComplianceCertificateController.cs", "RelativeDocumentMoniker": "Compliance\\Controllers\\ComplianceCertificateController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\Controllers\\ComplianceCertificateController.cs", "RelativeToolTip": "Compliance\\Controllers\\ComplianceCertificateController.cs", "ViewState": "AgIAAHEAAAAAAAAAAIBJwBcBAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T10:15:45.838Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "CreateInvitationDTO.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\DTOs\\CreateInvitationDTO.cs", "RelativeDocumentMoniker": "Authentication\\DTOs\\CreateInvitationDTO.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Authentication\\DTOs\\CreateInvitationDTO.cs", "RelativeToolTip": "Authentication\\DTOs\\CreateInvitationDTO.cs", "ViewState": "AgIAAAQAAAAAAAAAAAArwBIAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T12:26:24.201Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "ReceiptFileController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Receipts\\Controllers\\ReceiptFileController.cs", "RelativeDocumentMoniker": "Receipts\\Controllers\\ReceiptFileController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Receipts\\Controllers\\ReceiptFileController.cs", "RelativeToolTip": "Receipts\\Controllers\\ReceiptFileController.cs", "ViewState": "AgIAACwAAAAAAAAAAAArwDwAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T11:32:51.026Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "FileController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Files\\Controllers\\FileController.cs", "RelativeDocumentMoniker": "Files\\Controllers\\FileController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Files\\Controllers\\FileController.cs", "RelativeToolTip": "Files\\Controllers\\FileController.cs", "ViewState": "AgIAABAAAAAAAAAAAABQwCgAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T13:57:35.02Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "ComplianceCertificateFileController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\Controllers\\ComplianceCertificateFileController.cs", "RelativeDocumentMoniker": "Compliance\\Controllers\\ComplianceCertificateFileController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\Controllers\\ComplianceCertificateFileController.cs", "RelativeToolTip": "Compliance\\Controllers\\ComplianceCertificateFileController.cs", "ViewState": "AgIAADUAAAAAAAAAAAArwEYAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T13:47:26.002Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "PaymentProofController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Controllers\\PaymentProofController.cs", "RelativeDocumentMoniker": "Payments\\Controllers\\PaymentProofController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Controllers\\PaymentProofController.cs", "RelativeToolTip": "Payments\\Controllers\\PaymentProofController.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAjwBkAAABeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T22:03:24.818Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "PaymentScheduleController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Controllers\\PaymentScheduleController.cs", "RelativeDocumentMoniker": "Payments\\Controllers\\PaymentScheduleController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Controllers\\PaymentScheduleController.cs", "RelativeToolTip": "Payments\\Controllers\\PaymentScheduleController.cs", "ViewState": "AgIAAFQAAAAAAAAAAAArwGIAAABSAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T17:36:43.931Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "ReportExcelService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Reporting\\Services\\ReportExcelService.cs", "RelativeDocumentMoniker": "Reporting\\Services\\ReportExcelService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Reporting\\Services\\ReportExcelService.cs", "RelativeToolTip": "Reporting\\Services\\ReportExcelService.cs", "ViewState": "AgIAAAgAAAAAAAAAAIBJwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T13:32:08.404Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "NotificationDTO.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\DTOs\\NotificationDTO.cs", "RelativeDocumentMoniker": "Notifications\\DTOs\\NotificationDTO.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\DTOs\\NotificationDTO.cs", "RelativeToolTip": "Notifications\\DTOs\\NotificationDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T14:32:46.063Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "ReportingService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Reporting\\Services\\ReportingService.cs", "RelativeDocumentMoniker": "Reporting\\Services\\ReportingService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Reporting\\Services\\ReportingService.cs", "RelativeToolTip": "Reporting\\Services\\ReportingService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T14:33:35.615Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "ReportCsvService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Reporting\\Services\\ReportCsvService.cs", "RelativeDocumentMoniker": "Reporting\\Services\\ReportCsvService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Reporting\\Services\\ReportCsvService.cs", "RelativeToolTip": "Reporting\\Services\\ReportCsvService.cs", "ViewState": "AgIAAAEAAAAAAAAAAIBJwMoAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T12:45:31.719Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "ComplianceReportingService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Reporting\\Services\\ComplianceReportingService.cs", "RelativeDocumentMoniker": "Reporting\\Services\\ComplianceReportingService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Reporting\\Services\\ComplianceReportingService.cs", "RelativeToolTip": "Reporting\\Services\\ComplianceReportingService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T14:32:55.958Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "ReportExportService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Reporting\\Services\\ReportExportService.cs", "RelativeDocumentMoniker": "Reporting\\Services\\ReportExportService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Reporting\\Services\\ReportExportService.cs", "RelativeToolTip": "Reporting\\Services\\ReportExportService.cs", "ViewState": "AgIAAAIAAAAAAAAAAIBJwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T13:38:11.966Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "ReceiptService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Receipts\\Services\\ReceiptService.cs", "RelativeDocumentMoniker": "Receipts\\Services\\ReceiptService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Receipts\\Services\\ReceiptService.cs", "RelativeToolTip": "Receipts\\Services\\ReceiptService.cs", "ViewState": "AgIAAAoAAAAAAAAAAABQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T14:32:40.331Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "ReceiptFileService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Receipts\\Services\\ReceiptFileService.cs", "RelativeDocumentMoniker": "Receipts\\Services\\ReceiptFileService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Receipts\\Services\\ReceiptFileService.cs", "RelativeToolTip": "Receipts\\Services\\ReceiptFileService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T14:32:34.537Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "PaymentScheduleImportService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentScheduleImportService.cs", "RelativeDocumentMoniker": "Payments\\Services\\PaymentScheduleImportService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentScheduleImportService.cs", "RelativeToolTip": "Payments\\Services\\PaymentScheduleImportService.cs", "ViewState": "AgIAAAMAAAAAAAAAAIBJwCsAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T17:32:05.718Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "ReceiptTemplateService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Receipts\\Services\\ReceiptTemplateService.cs", "RelativeDocumentMoniker": "Receipts\\Services\\ReceiptTemplateService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Receipts\\Services\\ReceiptTemplateService.cs", "RelativeToolTip": "Receipts\\Services\\ReceiptTemplateService.cs", "ViewState": "AgIAAAEAAAAAAAAAAIBJwAgAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T17:43:54.64Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "PaymentProofService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentProofService.cs", "RelativeDocumentMoniker": "Payments\\Services\\PaymentProofService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentProofService.cs", "RelativeToolTip": "Payments\\Services\\PaymentProofService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T13:13:28.893Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "PaymentScheduleService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentScheduleService.cs", "RelativeDocumentMoniker": "Payments\\Services\\PaymentScheduleService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentScheduleService.cs", "RelativeToolTip": "Payments\\Services\\PaymentScheduleService.cs", "ViewState": "AgIAAAIAAAAAAAAAAIBJwOoAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T17:20:51.121Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "PaymentProfileService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentProfileService.cs", "RelativeDocumentMoniker": "Payments\\Services\\PaymentProfileService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentProfileService.cs", "RelativeToolTip": "Payments\\Services\\PaymentProfileService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T12:45:22.275Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "PaymentComplianceService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentComplianceService.cs", "RelativeDocumentMoniker": "Payments\\Services\\PaymentComplianceService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentComplianceService.cs", "RelativeToolTip": "Payments\\Services\\PaymentComplianceService.cs", "ViewState": "AgIAAAMAAAAAAAAAAIBJwBYAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T12:49:11.904Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "PaymentApprovalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentApprovalService.cs", "RelativeDocumentMoniker": "Payments\\Services\\PaymentApprovalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Services\\PaymentApprovalService.cs", "RelativeToolTip": "Payments\\Services\\PaymentApprovalService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T14:31:23.495Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "OrganizationComplianceService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Organizations\\Services\\OrganizationComplianceService.cs", "RelativeDocumentMoniker": "Organizations\\Services\\OrganizationComplianceService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Organizations\\Services\\OrganizationComplianceService.cs", "RelativeToolTip": "Organizations\\Services\\OrganizationComplianceService.cs", "ViewState": "AgIAAAkAAAAAAAAAAEBVwBoAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T11:10:31.722Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "UserEmailService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Services\\UserEmailService.cs", "RelativeDocumentMoniker": "Notifications\\Services\\UserEmailService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Services\\UserEmailService.cs", "RelativeToolTip": "Notifications\\Services\\UserEmailService.cs", "ViewState": "AgIAAAsAAAAAAAAAAABQwAgAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T12:56:57.397Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "NotificationManagementService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Services\\NotificationManagementService.cs", "RelativeDocumentMoniker": "Notifications\\Services\\NotificationManagementService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Services\\NotificationManagementService.cs", "RelativeToolTip": "Notifications\\Services\\NotificationManagementService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAEkAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T13:02:48.926Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "OrganizationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Organizations\\Services\\OrganizationService.cs", "RelativeDocumentMoniker": "Organizations\\Services\\OrganizationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Organizations\\Services\\OrganizationService.cs", "RelativeToolTip": "Organizations\\Services\\OrganizationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T11:01:26.611Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "EmailTemplateService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Services\\EmailTemplateService.cs", "RelativeDocumentMoniker": "Notifications\\Services\\EmailTemplateService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Services\\EmailTemplateService.cs", "RelativeToolTip": "Notifications\\Services\\EmailTemplateService.cs", "ViewState": "AgIAAAwAAAAAAAAAAABQwA4AAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T14:21:40.051Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "CentralizedEmailService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Services\\CentralizedEmailService.cs", "RelativeDocumentMoniker": "Notifications\\Services\\CentralizedEmailService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Services\\CentralizedEmailService.cs", "RelativeToolTip": "Notifications\\Services\\CentralizedEmailService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAxwAwCAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T10:17:32.341Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "FileService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Files\\Services\\FileService.cs", "RelativeDocumentMoniker": "Files\\Services\\FileService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Files\\Services\\FileService.cs", "RelativeToolTip": "Files\\Services\\FileService.cs", "ViewState": "AgIAAAoAAAAAAAAAAIBJwBgAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T17:40:58.716Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "CertificateTemplateService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Documents\\Services\\CertificateTemplateService.cs", "RelativeDocumentMoniker": "Documents\\Services\\CertificateTemplateService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Documents\\Services\\CertificateTemplateService.cs", "RelativeToolTip": "Documents\\Services\\CertificateTemplateService.cs", "ViewState": "AgIAAA0AAAAAAAAAAABQwAoAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T16:46:08.095Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "EmailConfigurationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Services\\EmailConfigurationService.cs", "RelativeDocumentMoniker": "Notifications\\Services\\EmailConfigurationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Services\\EmailConfigurationService.cs", "RelativeToolTip": "Notifications\\Services\\EmailConfigurationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T22:46:16.299Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "BrandedDocumentService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Documents\\Services\\BrandedDocumentService.cs", "RelativeDocumentMoniker": "Documents\\Services\\BrandedDocumentService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Documents\\Services\\BrandedDocumentService.cs", "RelativeToolTip": "Documents\\Services\\BrandedDocumentService.cs", "ViewState": "AgIAAAoAAAAAAAAAAIBJwA8AAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T16:30:38.976Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "ComplianceCertificateFileService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\Services\\ComplianceCertificateFileService.cs", "RelativeDocumentMoniker": "Compliance\\Services\\ComplianceCertificateFileService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\Services\\ComplianceCertificateFileService.cs", "RelativeToolTip": "Compliance\\Services\\ComplianceCertificateFileService.cs", "ViewState": "AgIAAAYAAAAAAAAAAIBJwA8AAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T14:08:28.624Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "ComplianceCertificateService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\Services\\ComplianceCertificateService.cs", "RelativeDocumentMoniker": "Compliance\\Services\\ComplianceCertificateService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\Services\\ComplianceCertificateService.cs", "RelativeToolTip": "Compliance\\Services\\ComplianceCertificateService.cs", "ViewState": "AgIAAAcAAAAAAAAAAEBVwBEAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T11:54:16.829Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "ComplianceCertificateDTO.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\DTOs\\ComplianceCertificateDTO.cs", "RelativeDocumentMoniker": "Compliance\\DTOs\\ComplianceCertificateDTO.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\DTOs\\ComplianceCertificateDTO.cs", "RelativeToolTip": "Compliance\\DTOs\\ComplianceCertificateDTO.cs", "ViewState": "AgIAADQAAAAAAAAAAIBWwDwAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T13:58:18.781Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "NotificationFiltersDTO.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\DTOs\\NotificationFiltersDTO.cs", "RelativeDocumentMoniker": "Notifications\\DTOs\\NotificationFiltersDTO.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\DTOs\\NotificationFiltersDTO.cs", "RelativeToolTip": "Notifications\\DTOs\\NotificationFiltersDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwA0AAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T14:39:31.671Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "NotificationController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Controllers\\NotificationController.cs", "RelativeDocumentMoniker": "Notifications\\Controllers\\NotificationController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Notifications\\Controllers\\NotificationController.cs", "RelativeToolTip": "Notifications\\Controllers\\NotificationController.cs", "ViewState": "AgIAAEgAAAAAAAAAAIBJwFAAAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T14:31:08.721Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "CertificateTemplateController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Documents\\Controllers\\CertificateTemplateController.cs", "RelativeDocumentMoniker": "Documents\\Controllers\\CertificateTemplateController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Documents\\Controllers\\CertificateTemplateController.cs", "RelativeToolTip": "Documents\\Controllers\\CertificateTemplateController.cs", "ViewState": "AgIAAIUAAAAAAAAAAIBJwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T14:09:08.372Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "ComplianceCertificateProcedures.sql", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\SQL\\ComplianceCertificateProcedures.sql", "RelativeDocumentMoniker": "Compliance\\SQL\\ComplianceCertificateProcedures.sql", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\SQL\\ComplianceCertificateProcedures.sql", "RelativeToolTip": "Compliance\\SQL\\ComplianceCertificateProcedures.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-18T12:40:18.718Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "ComplianceCertificate.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\Models\\ComplianceCertificate.cs", "RelativeDocumentMoniker": "Compliance\\Models\\ComplianceCertificate.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Compliance\\Models\\ComplianceCertificate.cs", "RelativeToolTip": "Compliance\\Models\\ComplianceCertificate.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAxwAQAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T12:43:57.848Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "OrganizationDTO.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Organizations\\DTOs\\OrganizationDTO.cs", "RelativeDocumentMoniker": "Organizations\\DTOs\\OrganizationDTO.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Organizations\\DTOs\\OrganizationDTO.cs", "RelativeToolTip": "Organizations\\DTOs\\OrganizationDTO.cs", "ViewState": "AgIAACEAAAAAAAAAAAAywB8AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T11:11:32.278Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "DummyTestData.sql", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\SQL\\DummyTestData.sql", "RelativeDocumentMoniker": "SQL\\DummyTestData.sql", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\SQL\\DummyTestData.sql", "RelativeToolTip": "SQL\\DummyTestData.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-15T20:01:31.593Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "FileUploadOperationFilter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\FileUploadOperationFilter.cs", "RelativeDocumentMoniker": "FileUploadOperationFilter.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\FileUploadOperationFilter.cs", "RelativeToolTip": "FileUploadOperationFilter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAALYAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T13:16:42.816Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "Final_E_Receipt.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Final_E_Receipt.csproj", "RelativeDocumentMoniker": "Final_E_Receipt.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Final_E_Receipt.csproj", "RelativeToolTip": "Final_E_Receipt.csproj", "ViewState": "AgIAAAsAAAAAAAAAAAAqwBQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-10T09:53:46.231Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "TEMPLATE_FILE_NAMES.md", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\wwwroot\\templates\\TEMPLATE_FILE_NAMES.md", "RelativeDocumentMoniker": "wwwroot\\templates\\TEMPLATE_FILE_NAMES.md", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\wwwroot\\templates\\TEMPLATE_FILE_NAMES.md", "RelativeToolTip": "wwwroot\\templates\\TEMPLATE_FILE_NAMES.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-15T17:05:48.469Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "JRB_IMPLEMENTATION_STATUS.md", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\JRB_IMPLEMENTATION_STATUS.md", "RelativeDocumentMoniker": "JRB_IMPLEMENTATION_STATUS.md", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\JRB_IMPLEMENTATION_STATUS.md", "RelativeToolTip": "JRB_IMPLEMENTATION_STATUS.md", "ViewState": "AgIAAAsAAAAAAAAAACBjwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-15T20:55:20.145Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "README.md", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\wwwroot\\templates\\README.md", "RelativeDocumentMoniker": "wwwroot\\templates\\README.md", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\wwwroot\\templates\\README.md", "RelativeToolTip": "wwwroot\\templates\\README.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-15T16:41:00.01Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "PaymentSchedule.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Models\\PaymentSchedule.cs", "RelativeDocumentMoniker": "Payments\\Models\\PaymentSchedule.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Models\\PaymentSchedule.cs", "RelativeToolTip": "Payments\\Models\\PaymentSchedule.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAswBcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T22:54:12.736Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "ReportingController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Reporting\\Controllers\\ReportingController.cs", "RelativeDocumentMoniker": "Reporting\\Controllers\\ReportingController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Reporting\\Controllers\\ReportingController.cs", "RelativeToolTip": "Reporting\\Controllers\\ReportingController.cs", "ViewState": "AgIAAMoAAAAAAAAAAAAAwNoAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T12:45:14.279Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "PaymentProfileController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Controllers\\PaymentProfileController.cs", "RelativeDocumentMoniker": "Payments\\Controllers\\PaymentProfileController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\Payments\\Controllers\\PaymentProfileController.cs", "RelativeToolTip": "Payments\\Controllers\\PaymentProfileController.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAmwBcAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T12:47:53.813Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "INTEGRATION_SUMMARY.md", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\INTEGRATION_SUMMARY.md", "RelativeDocumentMoniker": "INTEGRATION_SUMMARY.md", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\INTEGRATION_SUMMARY.md", "RelativeToolTip": "INTEGRATION_SUMMARY.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-10T17:31:36.961Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": ".giti<PERSON>re", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\.gitignore", "RelativeDocumentMoniker": ".giti<PERSON>re", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\.gitignore", "RelativeToolTip": ".giti<PERSON>re", "ViewState": "AgIAAFwBAAAAAAAAACBjwG4BAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-10T12:28:44.024Z"}]}]}]}