using System;

namespace Final_E_Receipt.Receipts.DTOs
{
    public class ReceiptDTO
    {
        public string Id { get; set; }
        public string PayerId { get; set; }
        public string PayerName { get; set; }
        public string PayerEmail { get; set; }
        public string ReceiptNumber { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; }
        public DateTime PaymentDate { get; set; }
        public string PaymentMethod { get; set; }
        public string Status { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public string OrganizationId { get; set; }
        public bool IsRevoked { get; set; }
        public DateTime? RevokedDate { get; set; }
        public string RevokedReason { get; set; }
        public string RevokedBy { get; set; }
        public bool NotificationSent { get; set; }
        public DateTime? NotificationSentDate { get; set; }
    }

    public class CreateReceiptDTO
    {
        public string PayerId { get; set; }
        public string PayerName { get; set; }
        public string PayerEmail { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "NGN";
        public DateTime PaymentDate { get; set; }
        public string PaymentMethod { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public string OrganizationId { get; set; }
    }

    public class RevokeReceiptDTO
    {
        public string RevokedReason { get; set; }
    }

    public class SearchReceiptsDTO
    {
        public string SearchTerm { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }
}