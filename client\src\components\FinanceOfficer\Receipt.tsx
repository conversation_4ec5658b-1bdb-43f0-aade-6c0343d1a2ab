import React, { useState, useEffect } from "react";
import {
  Search,
  X,
  Download,
  Upload,
  FileText,
  Eye,
  Mail,
  DollarSign,
  Send,
  Check,
  User,
  Calendar,
  CreditCard,
  AlertCircle,
} from "lucide-react";
import { useReceiptApi } from "../../hooks/api/useReceipts"; // Adjust import path as needed

interface Receipt {
  id: string;
  paymentId: string;
  organizationId: string;
  receiptNumber: string;
  amount: number;
  currency: string;
  issuedDate: string;
  issuedBy: string;
  status: "ACTIVE" | "REVOKED";
  revokedDate?: string;
  revokedBy?: string;
  revokedReason?: string;
  receiptData: any;
  createdAt: string;
  updatedAt?: string;
}

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: "primary" | "secondary" | "danger" | "success";
  size?: "sm" | "md";
  disabled?: boolean;
  className?: string;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  children,
  onClick,
  variant = "primary",
  size = "md",
  disabled = false,
  className = "",
}) => {
  const baseClasses =
    "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white",
    success: "bg-green-500 hover:bg-green-600 text-white",
  };

  const disabledClasses = disabled ? "opacity-50 cursor-not-allowed" : "";

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${disabledClasses} ${className}`}
    >
      {children}
    </button>
  );
};

const Modal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-[#045024]">{title}</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>
        {children}
      </div>
    </div>
  );
};

const ReceiptComponent: React.FC = () => {
  const {
    loading,
    error,
    getAllReceipts,
    getReceiptsByOrganization,
    revokeReceipt,
    downloadReceipt,
    searchReceipts,
  } = useReceiptApi();

  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterBy, setFilterBy] = useState<"all" | "organization" | "status">(
    "all"
  );
  const [selectedReceipt, setSelectedReceipt] = useState<Receipt | null>(null);
  const [showRevocationModal, setShowRevocationModal] = useState(false);
  const [showCustomEmailModal, setShowCustomEmailModal] = useState(false);
  const [revocationReason, setRevocationReason] = useState("");
  const [customEmailContent, setCustomEmailContent] = useState("");
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [organizationId, setOrganizationId] = useState(""); // You'll need to get this from your app state/context

  // Load receipts on component mount
  useEffect(() => {
    loadReceipts();
  }, []);

  const loadReceipts = async () => {
    try {
      let result;
      if (filterBy === "organization" && organizationId) {
        result = await getReceiptsByOrganization(organizationId);
      } else {
        result = await getAllReceipts();
      }

      if (result) {
        setReceipts(result);
      }
    } catch (err) {
      console.error("Failed to load receipts:", err);
    }
  };

  // Search functionality
  useEffect(() => {
    if (searchTerm.trim()) {
      handleSearch();
    } else {
      loadReceipts();
    }
  }, [searchTerm]);

  const handleSearch = async () => {
    if (searchTerm.trim()) {
      const result = await searchReceipts(searchTerm);
      if (result) {
        setReceipts(result);
      }
    }
  };

  const filteredReceipts = receipts.filter((receipt) => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      receipt.receiptNumber.toLowerCase().includes(searchLower) ||
      receipt.paymentId.toLowerCase().includes(searchLower) ||
      receipt.organizationId.toLowerCase().includes(searchLower) ||
      receipt.issuedBy.toLowerCase().includes(searchLower)
    );
  });

  const handleDownload = async (receipt: Receipt) => {
    try {
      const result = await downloadReceipt(receipt.id);
      if (result) {
        // Handle the download result - this might be a blob or URL
        console.log("Receipt downloaded successfully");
        // You might want to create a download link or open in new tab
      }
    } catch (err) {
      console.error("Download failed:", err);
    }
  };

  const handleRevoke = (receipt: Receipt) => {
    setSelectedReceipt(receipt);
    setShowRevocationModal(true);
  };

  const confirmRevocation = async () => {
    if (selectedReceipt && revocationReason) {
      try {
        const result = await revokeReceipt(selectedReceipt.id, {
          revokedReason: revocationReason,
        });
        if (result) {
          // Update local state
          setReceipts((prev) =>
            prev.map((r) =>
              r.id === selectedReceipt.id
                ? {
                    ...r,
                    status: "REVOKED" as const,
                    revokedReason: revocationReason,
                    revokedDate: new Date().toISOString(),
                  }
                : r
            )
          );
          setShowRevocationModal(false);
          setRevocationReason("");
          setShowConfirmation(true);
          setTimeout(() => setShowConfirmation(false), 3000);
        }
      } catch (err) {
        console.error("Revocation failed:", err);
      }
    }
  };

  const sendNotification = (receipt: Receipt) => {
    // This would need to be implemented in your API
    alert(`Revocation notification sent for ${receipt.receiptNumber}`);
  };

  const sendCustomEmail = (receipt: Receipt) => {
    setSelectedReceipt(receipt);
    setShowCustomEmailModal(true);
  };

  const confirmCustomEmail = () => {
    if (selectedReceipt && customEmailContent) {
      // This would need to be implemented in your API
      alert(`Custom email sent for receipt ${selectedReceipt.receiptNumber}`);
      setShowCustomEmailModal(false);
      setCustomEmailContent("");
    }
  };

  const confirmDebit = (receipt: Receipt) => {
    // This would need to be implemented in your API
    alert(
      `Debit confirmed for ${receipt.currency} ${receipt.amount} from payment ${receipt.paymentId}`
    );
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-NG", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-NG");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2aa45c]"></div>
        <span className="ml-2">Loading receipts...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-[#045024]">
          Receipts Management
        </h2>
        <ActionButton variant="secondary">
          <Upload size={16} />
          Upload Receipt Template
        </ActionButton>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-red-800">
            <AlertCircle size={16} />
            <span>Error: {error}</span>
          </div>
        </div>
      )}

      {/* Search and Filter Section */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={16}
            />
            <input
              type="text"
              placeholder="Search by receipt number, payment ID, or organization..."
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2aa45c]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <select
              className="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2aa45c]"
              value={filterBy}
              onChange={(e) => {
                setFilterBy(
                  e.target.value as "all" | "organization" | "status"
                );
                loadReceipts();
              }}
            >
              <option value="all">All Receipts</option>
              <option value="organization">By Organization</option>
              <option value="status">By Status</option>
            </select>
          </div>
        </div>

        {/* Organization filter input */}
        {filterBy === "organization" && (
          <div className="mt-4">
            <input
              type="text"
              placeholder="Enter Organization ID"
              className="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2aa45c]"
              value={organizationId}
              onChange={(e) => setOrganizationId(e.target.value)}
            />
            <ActionButton
              variant="secondary"
              size="sm"
              onClick={loadReceipts}
              className="ml-2"
            >
              Filter
            </ActionButton>
          </div>
        )}
      </div>

      {/* Confirmation Banner */}
      {showConfirmation && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-green-800">
            <Check size={16} />
            <span>
              Receipt revoked successfully. Notification and debit processes can
              now be initiated.
            </span>
          </div>
        </div>
      )}

      {/* Receipts List */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-[#045024] mb-4">
          Receipt Records
        </h3>

        {filteredReceipts.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {searchTerm
              ? "No receipts found matching your search."
              : "No receipts available."}
          </div>
        ) : (
          <div className="space-y-3">
            {filteredReceipts.map((receipt) => (
              <div key={receipt.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-2">
                      <div className="font-medium text-gray-900">
                        {receipt.receiptNumber}
                      </div>
                      <span
                        className={`px-2 py-1 text-xs font-semibold rounded-full ${
                          receipt.status === "ACTIVE"
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {receipt.status}
                      </span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center gap-2">
                        <User size={14} />
                        <span>Issued by: {receipt.issuedBy}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CreditCard size={14} />
                        <span>Payment: {receipt.paymentId}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CreditCard size={14} />
                        <span>Org: {receipt.organizationId}</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <Calendar size={14} />
                        <span>{formatDate(receipt.issuedDate)}</span>
                      </div>
                      <div className="font-medium text-[#045024]">
                        {formatCurrency(receipt.amount, receipt.currency)}
                      </div>
                    </div>

                    {receipt.status === "REVOKED" && (
                      <div className="mt-2 p-2 bg-red-50 rounded text-sm">
                        <div className="text-red-800">
                          Revoked on:{" "}
                          {receipt.revokedDate
                            ? formatDate(receipt.revokedDate)
                            : "N/A"}
                        </div>
                        {receipt.revokedReason && (
                          <div className="text-red-600">
                            Reason: {receipt.revokedReason}
                          </div>
                        )}
                        {receipt.revokedBy && (
                          <div className="text-red-600">
                            Revoked by: {receipt.revokedBy}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col gap-2 ml-4">
                    {receipt.status === "ACTIVE" && (
                      <>
                        <ActionButton
                          variant="secondary"
                          size="sm"
                          onClick={() => handleDownload(receipt)}
                        >
                          <Download size={12} />
                          Download
                        </ActionButton>
                        <ActionButton
                          variant="danger"
                          size="sm"
                          onClick={() => handleRevoke(receipt)}
                        >
                          <X size={12} />
                          Revoke
                        </ActionButton>
                      </>
                    )}

                    {receipt.status === "REVOKED" && (
                      <div className="flex flex-col gap-2">
                        <ActionButton
                          variant="secondary"
                          size="sm"
                          onClick={() => sendNotification(receipt)}
                        >
                          <Mail size={12} />
                          Send Notice
                        </ActionButton>
                        <ActionButton
                          variant="secondary"
                          size="sm"
                          onClick={() => sendCustomEmail(receipt)}
                        >
                          <Send size={12} />
                          Custom Email
                        </ActionButton>
                        <ActionButton
                          variant="success"
                          size="sm"
                          onClick={() => confirmDebit(receipt)}
                        >
                          <DollarSign size={12} />
                          Confirm Debit
                        </ActionButton>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Revocation Modal */}
      <Modal
        isOpen={showRevocationModal}
        onClose={() => setShowRevocationModal(false)}
        title="Revoke Receipt"
      >
        <div className="space-y-4">
          <div className="text-gray-600">
            Are you sure you want to revoke receipt{" "}
            <strong>{selectedReceipt?.receiptNumber}</strong>?
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Reason for Revocation
            </label>
            <textarea
              className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2aa45c]"
              rows={3}
              value={revocationReason}
              onChange={(e) => setRevocationReason(e.target.value)}
              placeholder="Please provide a reason for this revocation..."
            />
          </div>
          <div className="flex gap-2 justify-end">
            <ActionButton
              variant="secondary"
              onClick={() => setShowRevocationModal(false)}
            >
              Cancel
            </ActionButton>
            <ActionButton
              variant="danger"
              onClick={confirmRevocation}
              disabled={!revocationReason || loading}
            >
              {loading ? "Revoking..." : "Confirm Revocation"}
            </ActionButton>
          </div>
        </div>
      </Modal>

      {/* Custom Email Modal */}
      <Modal
        isOpen={showCustomEmailModal}
        onClose={() => setShowCustomEmailModal(false)}
        title="Send Custom Email"
      >
        <div className="space-y-4">
          <div className="text-gray-600">
            Send custom email regarding receipt{" "}
            <strong>{selectedReceipt?.receiptNumber}</strong>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Content
            </label>
            <textarea
              className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2aa45c]"
              rows={5}
              value={customEmailContent}
              onChange={(e) => setCustomEmailContent(e.target.value)}
              placeholder="Enter your custom message..."
            />
          </div>
          <div className="flex gap-2 justify-end">
            <ActionButton
              variant="secondary"
              onClick={() => setShowCustomEmailModal(false)}
            >
              Cancel
            </ActionButton>
            <ActionButton
              onClick={confirmCustomEmail}
              disabled={!customEmailContent}
            >
              <Send size={16} />
              Send Email
            </ActionButton>
          </div>
        </div>
      </Modal>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-[#045024] mb-4">
            Quick Stats
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Receipts:</span>
              <span className="font-medium">{receipts.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Active:</span>
              <span className="font-medium text-green-600">
                {receipts.filter((r) => r.status === "ACTIVE").length}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Revoked:</span>
              <span className="font-medium text-red-600">
                {receipts.filter((r) => r.status === "REVOKED").length}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-[#045024] mb-4">Actions</h3>
          <div className="space-y-3">
            <ActionButton variant="secondary" onClick={loadReceipts}>
              <Eye size={16} />
              Refresh Receipts
            </ActionButton>
            <ActionButton variant="secondary">
              <FileText size={16} />
              Export Report
            </ActionButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReceiptComponent;

// import React, { useState } from 'react';
// import { Search, X, Download, Upload, FileText, Eye, Mail, DollarSign, Send, Check, User, Calendar, CreditCard } from 'lucide-react';

// interface Receipt {
//   id: string;
//   payer: string;
//   payerProfile: string;
//   paymentProfile: string;
//   amount: string;
//   date: string;
//   status: 'Issued' | 'Revoked';
//   revocationReason?: string;
//   revocationDate?: string;
//   notificationSent?: boolean;
//   debitConfirmed?: boolean;
// }

// interface ActionButtonProps {
//   children: React.ReactNode;
//   onClick?: () => void;
//   variant?: 'primary' | 'secondary' | 'danger' | 'success';
//   size?: 'sm' | 'md';
//   disabled?: boolean;
// }

// const ActionButton: React.FC<ActionButtonProps> = ({ children, onClick, variant = "primary", size = "md", disabled = false }) => {
//   const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
//   const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
//   const variantClasses = {
//     primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
//     secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
//     danger: "bg-red-500 hover:bg-red-600 text-white",
//     success: "bg-green-500 hover:bg-green-600 text-white"
//   };

//   const disabledClasses = disabled ? "opacity-50 cursor-not-allowed" : "";

//   return (
//     <button
//       onClick={onClick}
//       disabled={disabled}
//       className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${disabledClasses}`}
//     >
//       {children}
//     </button>
//   );
// };

// const Modal: React.FC<{ isOpen: boolean; onClose: () => void; title: string; children: React.ReactNode }> = ({ isOpen, onClose, title, children }) => {
//   if (!isOpen) return null;

//   return (
//     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//       <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
//         <div className="flex justify-between items-center mb-4">
//           <h3 className="text-lg font-semibold text-[#045024]">{title}</h3>
//           <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
//             <X size={20} />
//           </button>
//         </div>
//         {children}
//       </div>
//     </div>
//   );
// };

// const ReceiptComponent: React.FC = () => {
//   const [receipts, setReceipts] = useState<Receipt[]>([
//     { id: "R-2025-001", payer: "Acme Corp", payerProfile: "ACME-001", paymentProfile: "PAY-001", amount: "₦2,500", date: "2025-06-18", status: "Issued" },
//     { id: "R-2025-002", payer: "TechStart Inc", payerProfile: "TECH-002", paymentProfile: "PAY-002", amount: "₦1,200", date: "2025-06-17", status: "Issued" },
//     { id: "R-2025-003", payer: "Global Solutions", payerProfile: "GLOB-003", paymentProfile: "PAY-003", amount: "₦500", date: "2025-06-16", status: "Revoked", revocationReason: "Duplicate payment", revocationDate: "2025-06-17", notificationSent: true, debitConfirmed: true }
//   ]);

//   const [searchTerm, setSearchTerm] = useState('');
//   const [filterBy, setFilterBy] = useState<'all' | 'payer' | 'payment'>('all');
//   const [selectedReceipt, setSelectedReceipt] = useState<Receipt | null>(null);
//   const [showRevocationModal, setShowRevocationModal] = useState(false);
//   const [showCustomEmailModal, setShowCustomEmailModal] = useState(false);
//   const [revocationReason, setRevocationReason] = useState('');
//   const [customEmailContent, setCustomEmailContent] = useState('');
//   const [showConfirmation, setShowConfirmation] = useState(false);

//   const filteredReceipts = receipts.filter(receipt => {
//     const matchesSearch = receipt.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
//                          receipt.payer.toLowerCase().includes(searchTerm.toLowerCase()) ||
//                          receipt.payerProfile.toLowerCase().includes(searchTerm.toLowerCase()) ||
//                          receipt.paymentProfile.toLowerCase().includes(searchTerm.toLowerCase());

//     if (filterBy === 'all') return matchesSearch;
//     return matchesSearch;
//   });

//   const handleDownload = (receipt: Receipt) => {
//     // Simulate download functionality
//     console.log(`Downloading receipt ${receipt.id}`);
//     alert(`Receipt ${receipt.id} downloaded successfully!`);
//   };

//   const handleRevoke = (receipt: Receipt) => {
//     setSelectedReceipt(receipt);
//     setShowRevocationModal(true);
//   };

//   const confirmRevocation = () => {
//     if (selectedReceipt && revocationReason) {
//       setReceipts(prev => prev.map(r =>
//         r.id === selectedReceipt.id
//           ? { ...r, status: 'Revoked' as const, revocationReason, revocationDate: new Date().toISOString().split('T')[0] }
//           : r
//       ));
//       setShowRevocationModal(false);
//       setRevocationReason('');
//       setShowConfirmation(true);
//       setTimeout(() => setShowConfirmation(false), 3000);
//     }
//   };

//   const sendNotification = (receipt: Receipt) => {
//     setReceipts(prev => prev.map(r =>
//       r.id === receipt.id ? { ...r, notificationSent: true } : r
//     ));
//     alert(`Revocation notification sent to ${receipt.payer}`);
//   };

//   const sendCustomEmail = (receipt: Receipt) => {
//     setSelectedReceipt(receipt);
//     setShowCustomEmailModal(true);
//   };

//   const confirmCustomEmail = () => {
//     if (selectedReceipt && customEmailContent) {
//       alert(`Custom email sent to ${selectedReceipt.payer} with custom message`);
//       setShowCustomEmailModal(false);
//       setCustomEmailContent('');
//     }
//   };

//   const confirmDebit = (receipt: Receipt) => {
//     setReceipts(prev => prev.map(r =>
//       r.id === receipt.id ? { ...r, debitConfirmed: true } : r
//     ));
//     alert(`Debit confirmed for ${receipt.amount} from payment profile ${receipt.paymentProfile}`);
//   };

//   return (
//     <div className="space-y-6">
//       <div className="flex justify-between items-center">
//         <h2 className="text-2xl font-bold text-[#045024]">Receipts Management</h2>
//         <ActionButton variant="secondary">
//           <Upload size={16} />
//           Upload Receipt Template
//         </ActionButton>
//       </div>

//       {/* Search and Filter Section */}
//       <div className="bg-white rounded-lg shadow-md p-6">
//         <div className="flex flex-col md:flex-row gap-4">
//           <div className="flex-1 relative">
//             <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
//             <input
//               type="text"
//               placeholder="Search by receipt ID, payer, or profile..."
//               className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2aa45c]"
//               value={searchTerm}
//               onChange={(e) => setSearchTerm(e.target.value)}
//             />
//           </div>
//           <div className="flex gap-2">
//             <select
//               className="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2aa45c]"
//               value={filterBy}
//               onChange={(e) => setFilterBy(e.target.value as 'all' | 'payer' | 'payment')}
//             >
//               <option value="all">All Receipts</option>
//               <option value="payer">By Payer Profile</option>
//               <option value="payment">By Payment Profile</option>
//             </select>
//           </div>
//         </div>
//       </div>

//       {/* Confirmation Banner */}
//       {showConfirmation && (
//         <div className="bg-green-50 border border-green-200 rounded-lg p-4">
//           <div className="flex items-center gap-2 text-green-800">
//             <Check size={16} />
//             <span>Receipt revoked successfully. Notification and debit processes can now be initiated.</span>
//           </div>
//         </div>
//       )}

//       {/* Receipts List */}
//       <div className="bg-white rounded-lg shadow-md p-6">
//         <h3 className="text-lg font-semibold text-[#045024] mb-4">Receipt Records</h3>
//         <div className="space-y-3">
//           {filteredReceipts.map(receipt => (
//             <div key={receipt.id} className="border rounded-lg p-4">
//               <div className="flex justify-between items-start">
//                 <div className="flex-1">
//                   <div className="flex items-center gap-4 mb-2">
//                     <div className="font-medium text-gray-900">{receipt.id}</div>
//                     <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
//                       receipt.status === 'Issued' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
//                     }`}>
//                       {receipt.status}
//                     </span>
//                   </div>

//                   <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
//                     <div className="flex items-center gap-2">
//                       <User size={14} />
//                       <span>{receipt.payer}</span>
//                     </div>
//                     <div className="flex items-center gap-2">
//                       <CreditCard size={14} />
//                       <span>Payer: {receipt.payerProfile}</span>
//                     </div>
//                     <div className="flex items-center gap-2">
//                       <CreditCard size={14} />
//                       <span>Payment: {receipt.paymentProfile}</span>
//                     </div>
//                   </div>

//                   <div className="flex items-center gap-4 text-sm text-gray-600">
//                     <div className="flex items-center gap-2">
//                       <Calendar size={14} />
//                       <span>{receipt.date}</span>
//                     </div>
//                     <div className="font-medium text-[#045024]">{receipt.amount}</div>
//                   </div>

//                   {receipt.status === 'Revoked' && (
//                     <div className="mt-2 p-2 bg-red-50 rounded text-sm">
//                       <div className="text-red-800">Revoked on: {receipt.revocationDate}</div>
//                       <div className="text-red-600">Reason: {receipt.revocationReason}</div>
//                       <div className="flex gap-4 mt-1">
//                         <span className={`text-xs ${receipt.notificationSent ? 'text-green-600' : 'text-red-600'}`}>
//                           Notification: {receipt.notificationSent ? 'Sent' : 'Pending'}
//                         </span>
//                         <span className={`text-xs ${receipt.debitConfirmed ? 'text-green-600' : 'text-red-600'}`}>
//                           Debit: {receipt.debitConfirmed ? 'Confirmed' : 'Pending'}
//                         </span>
//                       </div>
//                     </div>
//                   )}
//                 </div>

//                 <div className="flex flex-col gap-2 ml-4">
//                   {receipt.status === 'Issued' && (
//                     <>
//                       <ActionButton variant="secondary" size="sm" onClick={() => handleDownload(receipt)}>
//                         <Download size={12} />
//                         Download
//                       </ActionButton>
//                       <ActionButton variant="danger" size="sm" onClick={() => handleRevoke(receipt)}>
//                         <X size={12} />
//                         Revoke
//                       </ActionButton>
//                     </>
//                   )}

//                   {receipt.status === 'Revoked' && (
//                     <div className="flex flex-col gap-2">
//                       <ActionButton
//                         variant="secondary"
//                         size="sm"
//                         onClick={() => sendNotification(receipt)}
//                         disabled={receipt.notificationSent}
//                       >
//                         <Mail size={12} />
//                         {receipt.notificationSent ? 'Sent' : 'Send Notice'}
//                       </ActionButton>
//                       <ActionButton variant="secondary" size="sm" onClick={() => sendCustomEmail(receipt)}>
//                         <Send size={12} />
//                         Custom Email
//                       </ActionButton>
//                       <ActionButton
//                         variant="success"
//                         size="sm"
//                         onClick={() => confirmDebit(receipt)}
//                         disabled={receipt.debitConfirmed}
//                       >
//                         <DollarSign size={12} />
//                         {receipt.debitConfirmed ? 'Debited' : 'Confirm Debit'}
//                       </ActionButton>
//                     </div>
//                   )}
//                 </div>
//               </div>
//             </div>
//           ))}
//         </div>
//       </div>

//       {/* Revocation Modal */}
//       <Modal isOpen={showRevocationModal} onClose={() => setShowRevocationModal(false)} title="Revoke Receipt">
//         <div className="space-y-4">
//           <div className="text-gray-600">
//             Are you sure you want to revoke receipt <strong>{selectedReceipt?.id}</strong> for <strong>{selectedReceipt?.payer}</strong>?
//           </div>
//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-2">Reason for Revocation</label>
//             <textarea
//               className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2aa45c]"
//               rows={3}
//               value={revocationReason}
//               onChange={(e) => setRevocationReason(e.target.value)}
//               placeholder="Please provide a reason for this revocation..."
//             />
//           </div>
//           <div className="flex gap-2 justify-end">
//             <ActionButton variant="secondary" onClick={() => setShowRevocationModal(false)}>
//               Cancel
//             </ActionButton>
//             <ActionButton variant="danger" onClick={confirmRevocation} disabled={!revocationReason}>
//               Confirm Revocation
//             </ActionButton>
//           </div>
//         </div>
//       </Modal>

//       {/* Custom Email Modal */}
//       <Modal isOpen={showCustomEmailModal} onClose={() => setShowCustomEmailModal(false)} title="Send Custom Email">
//         <div className="space-y-4">
//           <div className="text-gray-600">
//             Send custom email to <strong>{selectedReceipt?.payer}</strong> regarding receipt <strong>{selectedReceipt?.id}</strong>
//           </div>
//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-2">Email Content</label>
//             <textarea
//               className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2aa45c]"
//               rows={5}
//               value={customEmailContent}
//               onChange={(e) => setCustomEmailContent(e.target.value)}
//               placeholder="Enter your custom message..."
//             />
//           </div>
//           <div className="flex gap-2 justify-end">
//             <ActionButton variant="secondary" onClick={() => setShowCustomEmailModal(false)}>
//               Cancel
//             </ActionButton>
//             <ActionButton onClick={confirmCustomEmail} disabled={!customEmailContent}>
//               <Send size={16} />
//               Send Email
//             </ActionButton>
//           </div>
//         </div>
//       </Modal>

//       {/* Templates and Tools */}
//       <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//         <div className="bg-white rounded-lg shadow-md p-6">
//           <h3 className="text-lg font-semibold text-[#045024] mb-4">Receipt Templates</h3>
//           <div className="space-y-3">
//             <ActionButton variant="secondary">
//               <FileText size={16} />
//               Compliance Certificate Template
//             </ActionButton>
//             <ActionButton variant="secondary">
//               <Eye size={16} />
//               View Current Templates
//             </ActionButton>
//           </div>
//         </div>

//         <div className="bg-white rounded-lg shadow-md p-6">
//           <h3 className="text-lg font-semibold text-[#045024] mb-4">Quick Stats</h3>
//           <div className="space-y-3">
//             <div className="flex justify-between">
//               <span className="text-gray-600">Total Receipts:</span>
//               <span className="font-medium">{receipts.length}</span>
//             </div>
//             <div className="flex justify-between">
//               <span className="text-gray-600">Issued:</span>
//               <span className="font-medium text-green-600">{receipts.filter(r => r.status === 'Issued').length}</span>
//             </div>
//             <div className="flex justify-between">
//               <span className="text-gray-600">Revoked:</span>
//               <span className="font-medium text-red-600">{receipts.filter(r => r.status === 'Revoked').length}</span>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default ReceiptComponent;
