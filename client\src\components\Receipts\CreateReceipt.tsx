import React, { useState, useEffect } from 'react';
import {
  Receipt,
  Save,
  X,
  DollarSign,
  Calendar,
  Building2,
  CreditCard,
  FileText,
  Upload,
  AlertCircle,
  CheckCircle,
  Paperclip,
  Image,
  File,
} from 'lucide-react';
import { useReceiptApi, useOrganizationApi, usePaymentApi } from '../../hooks/api';
import { useAuth } from '../../contexts/AuthContext';
import type { CreateReceiptDTO, Organization, Payment } from '../../hooks/api';

interface CreateReceiptProps {
  setActiveTab: (tab: string) => void;
  onReceiptCreated?: () => void;
}

const CreateReceipt: React.FC<CreateReceiptProps> = ({
  setActiveTab,
  onReceiptCreated,
}) => {
  const { loading, error, createReceipt } = useReceiptApi();
  const { getAllOrganizations } = useOrganizationApi();
  const { getAllPayments } = usePaymentApi();
  const { user } = useAuth();
  
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [formData, setFormData] = useState<CreateReceiptDTO>({
    receiptNumber: '',
    organizationId: '',
    paymentId: '',
    amount: 0,
    currency: 'USD',
    issueDate: '',
    description: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showSuccess, setShowSuccess] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Role-based permissions
  const canCreateReceipts = ['JTB_ADMIN', 'FINANCE_OFFICER', 'SENIOR_FINANCE_OFFICER'].includes(user?.role || '');

  useEffect(() => {
    if (!canCreateReceipts) {
      setActiveTab('receipts');
      return;
    }
    
    loadOrganizations();
    loadPayments();
    generateReceiptNumber();
  }, []);

  const loadOrganizations = async () => {
    const result = await getAllOrganizations();
    if (result) {
      setOrganizations(result.filter(org => org.isActive));
    }
  };

  const loadPayments = async () => {
    const result = await getAllPayments();
    if (result) {
      // Only show completed payments that don't have receipts yet
      setPayments(result.filter(payment => payment.status === 'COMPLETED'));
    }
  };

  const generateReceiptNumber = () => {
    const timestamp = Date.now().toString().slice(-6);
    const randomStr = Math.random().toString(36).substring(2, 5).toUpperCase();
    setFormData(prev => ({
      ...prev,
      receiptNumber: `RCP-${timestamp}-${randomStr}`,
    }));
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.receiptNumber.trim()) {
      errors.receiptNumber = 'Receipt number is required';
    }

    if (!formData.organizationId) {
      errors.organizationId = 'Organization is required';
    }

    if (!formData.paymentId) {
      errors.paymentId = 'Payment is required';
    }

    if (!formData.amount || formData.amount <= 0) {
      errors.amount = 'Amount must be greater than 0';
    }

    if (!formData.issueDate) {
      errors.issueDate = 'Issue date is required';
    } else {
      const issueDate = new Date(formData.issueDate);
      const today = new Date();
      
      if (issueDate > today) {
        errors.issueDate = 'Issue date cannot be in the future';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'amount' ? parseFloat(value) || 0 : value,
    }));

    // Clear error for this field when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handlePaymentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const paymentId = e.target.value;
    const selectedPayment = payments.find(p => p.id === paymentId);
    
    setFormData(prev => ({
      ...prev,
      paymentId,
      organizationId: selectedPayment?.organizationId || prev.organizationId,
      amount: selectedPayment?.amount || prev.amount,
      currency: selectedPayment?.currency || prev.currency,
    }));

    // Clear errors
    if (formErrors.paymentId) {
      setFormErrors(prev => ({
        ...prev,
        paymentId: '',
        organizationId: '',
        amount: '',
      }));
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        setFormErrors(prev => ({
          ...prev,
          file: 'Only JPEG, PNG, GIF, and PDF files are allowed',
        }));
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        setFormErrors(prev => ({
          ...prev,
          file: 'File size must be less than 5MB',
        }));
        return;
      }

      setSelectedFile(file);
      
      // Create preview for images
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setFilePreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      } else {
        setFilePreview(null);
      }

      // Clear file error
      if (formErrors.file) {
        setFormErrors(prev => ({
          ...prev,
          file: '',
        }));
      }
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setFilePreview(null);
    setUploadProgress(0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Create FormData for file upload
    const submitData = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      submitData.append(key, value.toString());
    });

    if (selectedFile) {
      submitData.append('attachment', selectedFile);
    }

    const result = await createReceipt(submitData);
    if (result) {
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        onReceiptCreated?.();
        setActiveTab('receipts');
      }, 2000);
    }
  };

  const handleCancel = () => {
    setActiveTab('receipts');
  };

  const getPaymentDisplay = (payment: Payment) => {
    const org = organizations.find(o => o.id === payment.organizationId);
    return `${payment.reference} - ${org?.name || 'Unknown'} (${payment.amount.toLocaleString()} ${payment.currency})`;
  };

  if (!canCreateReceipts) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">You don't have permission to create receipts</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="h-8 w-8 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
            <Receipt className="h-5 w-5 text-[#2aa45c]" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-[#045024]">Create Receipt</h2>
            <p className="text-gray-600">Generate a new payment receipt</p>
          </div>
        </div>
        <button
          onClick={handleCancel}
          className="text-gray-500 hover:text-gray-700 transition-colors"
        >
          <X size={24} />
        </button>
      </div>

      {/* Success Message */}
      {showSuccess && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircle className="text-green-500 mr-2" size={20} />
            <span className="text-green-700">Receipt created successfully!</span>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={20} />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Form */}
      <div className="bg-white rounded-lg shadow-md">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Receipt Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Receipt Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="receiptNumber" className="block text-sm font-medium text-gray-700 mb-2">
                  Receipt Number *
                </label>
                <div className="relative">
                  <Receipt className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    id="receiptNumber"
                    name="receiptNumber"
                    value={formData.receiptNumber}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                      formErrors.receiptNumber ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="RCP-123456-ABC"
                  />
                </div>
                {formErrors.receiptNumber && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.receiptNumber}</p>
                )}
              </div>

              <div>
                <label htmlFor="issueDate" className="block text-sm font-medium text-gray-700 mb-2">
                  Issue Date *
                </label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="date"
                    id="issueDate"
                    name="issueDate"
                    value={formData.issueDate}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                      formErrors.issueDate ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                </div>
                {formErrors.issueDate && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.issueDate}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label htmlFor="paymentId" className="block text-sm font-medium text-gray-700 mb-2">
                  Payment *
                </label>
                <div className="relative">
                  <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <select
                    id="paymentId"
                    name="paymentId"
                    value={formData.paymentId}
                    onChange={handlePaymentChange}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                      formErrors.paymentId ? 'border-red-300' : 'border-gray-300'
                    }`}
                  >
                    <option value="">Select Payment</option>
                    {payments.map((payment) => (
                      <option key={payment.id} value={payment.id}>
                        {getPaymentDisplay(payment)}
                      </option>
                    ))}
                  </select>
                </div>
                {formErrors.paymentId && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.paymentId}</p>
                )}
              </div>
            </div>
          </div>

          {/* Amount Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Amount Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                  Amount *
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="number"
                    id="amount"
                    name="amount"
                    value={formData.amount}
                    onChange={handleInputChange}
                    step="0.01"
                    min="0"
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                      formErrors.amount ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="0.00"
                  />
                </div>
                {formErrors.amount && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.amount}</p>
                )}
              </div>

              <div>
                <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-2">
                  Currency *
                </label>
                <select
                  id="currency"
                  name="currency"
                  value={formData.currency}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                >
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="NGN">NGN</option>
                </select>
              </div>

              <div>
                <label htmlFor="organizationId" className="block text-sm font-medium text-gray-700 mb-2">
                  Organization *
                </label>
                <div className="relative">
                  <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <select
                    id="organizationId"
                    name="organizationId"
                    value={formData.organizationId}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                      formErrors.organizationId ? 'border-red-300' : 'border-gray-300'
                    }`}
                    disabled={!!formData.paymentId} // Disabled if payment is selected
                  >
                    <option value="">Select Organization</option>
                    {organizations.map((org) => (
                      <option key={org.id} value={org.id}>
                        {org.name}
                      </option>
                    ))}
                  </select>
                </div>
                {formErrors.organizationId && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.organizationId}</p>
                )}
              </div>
            </div>
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 text-gray-400" size={20} />
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                placeholder="Enter receipt description or notes"
              />
            </div>
          </div>

          {/* File Upload */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Attachment (Optional)</h3>
            
            {!selectedFile ? (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                <div className="text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-4">
                    <label htmlFor="file-upload" className="cursor-pointer">
                      <span className="mt-2 block text-sm font-medium text-gray-900">
                        Upload receipt document or image
                      </span>
                      <span className="mt-1 block text-sm text-gray-500">
                        PNG, JPG, GIF, PDF up to 5MB
                      </span>
                    </label>
                    <input
                      id="file-upload"
                      name="file-upload"
                      type="file"
                      className="sr-only"
                      accept="image/*,.pdf"
                      onChange={handleFileSelect}
                    />
                  </div>
                  <div className="mt-4">
                    <button
                      type="button"
                      onClick={() => document.getElementById('file-upload')?.click()}
                      className="bg-[#2aa45c] text-white px-4 py-2 rounded-lg hover:bg-[#076934] transition-colors"
                    >
                      Choose File
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="border border-gray-300 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {filePreview ? (
                      <img src={filePreview} alt="Preview" className="h-16 w-16 object-cover rounded" />
                    ) : (
                      <div className="h-16 w-16 bg-gray-100 rounded flex items-center justify-center">
                        <File className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                    <div>
                      <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                      <p className="text-sm text-gray-500">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={handleRemoveFile}
                    className="text-red-600 hover:text-red-800 transition-colors"
                  >
                    <X size={20} />
                  </button>
                </div>
              </div>
            )}
            
            {formErrors.file && (
              <p className="mt-1 text-sm text-red-600">{formErrors.file}</p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="bg-[#2aa45c] text-white px-6 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Creating...</span>
                </>
              ) : (
                <>
                  <Save size={16} />
                  <span>Create Receipt</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateReceipt;
