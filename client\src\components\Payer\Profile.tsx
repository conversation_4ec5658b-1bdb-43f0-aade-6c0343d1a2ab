// import React, { useState } from 'react';
// import { User, Building2, Edit, Save, X } from 'lucide-react';
// import type { LucideIcon } from 'lucide-react';

// // Types
// interface UserProfile {
//   name: string;
//   email: string;
//   phone: string;
// }

// interface PayerProfile {
//   organizationName: string;
//   contractAddress: string;
//   email: string;
//   phone: string;
// }

// interface ProfileProps {
//   userProfile?: UserProfile;
//   payerProfile?: PayerProfile;
//   onUserProfileChange?: (profile: UserProfile) => void;
//   onPayerProfileChange?: (profile: PayerProfile) => void;
// }

// // Default data
// const defaultUserProfile: UserProfile = {
//   name: 'John <PERSON>',
//   email: '<EMAIL>',
//   phone: '+****************'
// };

// const defaultPayerProfile: PayerProfile = {
//   organizationName: 'ABC Corporation',
//   contractAddress: '123 Business Ave, Suite 100\nBusiness City, BC 12345',
//   email: '<EMAIL>',
//   phone: '+****************'
// };

// // Profile Form Component
// const ProfileForm: React.FC<{
//   title: string;
//   icon: LucideIcon;
//   profile: UserProfile | PayerProfile;
//   isEditing: boolean;
//   onToggleEdit: () => void;
//   onSave: () => void;
//   onProfileChange: (profile: UserProfile | PayerProfile) => void;
//   isUserProfile?: boolean;
// }> = ({ title, icon: Icon, profile, isEditing, onToggleEdit, onSave, onProfileChange, isUserProfile = false }) => {
//   return (
//     <div className="bg-white rounded-lg shadow-md p-6">
//       <div className="flex items-center justify-between mb-4">
//         <h3 className="text-lg font-semibold flex items-center gap-2" style={{ color: '#045024' }}>
//           <Icon size={20} />
//           {title}
//         </h3>
//         <button
//           onClick={onToggleEdit}
//           className="flex items-center gap-2 px-3 py-1 rounded-lg transition-colors"
//           style={{ 
//             color: '#076934',
//             backgroundColor: isEditing ? 'transparent' : '#f0f9ff'
//           }}
//         >
//           {isEditing ? <X size={16} /> : <Edit size={16} />}
//           {isEditing ? 'Cancel' : 'Edit'}
//         </button>
//       </div>

//       {isUserProfile ? (
//         <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
//             <input
//               type="text"
//               value={(profile as UserProfile).name}
//               disabled={!isEditing}
//               onChange={(e) => onProfileChange({ ...profile, name: e.target.value } as UserProfile)}
//               className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent disabled:bg-gray-50"
//               style={{ '--tw-ring-color': '#2aa45c' } as React.CSSProperties}
//             />
//           </div>
//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
//             <input
//               type="email"
//               value={(profile as UserProfile).email}
//               disabled
//               className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
//             />
//           </div>
//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
//             <input
//               type="tel"
//               value={(profile as UserProfile).phone}
//               disabled={!isEditing}
//               onChange={(e) => onProfileChange({ ...profile, phone: e.target.value } as UserProfile)}
//               className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent disabled:bg-gray-50"
//               style={{ '--tw-ring-color': '#2aa45c' } as React.CSSProperties}
//             />
//           </div>
//         </div>
//       ) : (
//         <div className="space-y-4">
//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-1">Organization Name</label>
//             <input
//               type="text"
//               value={(profile as PayerProfile).organizationName}
//               disabled
//               className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
//             />
//           </div>
//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-1">Contract Address</label>
//             <textarea
//               value={(profile as PayerProfile).contractAddress}
//               disabled={!isEditing}
//               onChange={(e) => onProfileChange({ ...profile, contractAddress: e.target.value } as PayerProfile)}
//               rows={3}
//               className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent disabled:bg-gray-50"
//               style={{ '--tw-ring-color': '#2aa45c' } as React.CSSProperties}
//             />
//           </div>
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
//               <input
//                 type="email"
//                 value={(profile as PayerProfile).email}
//                 disabled={!isEditing}
//                 onChange={(e) => onProfileChange({ ...profile, email: e.target.value } as PayerProfile)}
//                 className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent disabled:bg-gray-50"
//                 style={{ '--tw-ring-color': '#2aa45c' } as React.CSSProperties}
//               />
//             </div>
//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
//               <input
//                 type="tel"
//                 value={(profile as PayerProfile).phone}
//                 disabled={!isEditing}
//                 onChange={(e) => onProfileChange({ ...profile, phone: e.target.value } as PayerProfile)}
//                 className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent disabled:bg-gray-50"
//                 style={{ '--tw-ring-color': '#2aa45c' } as React.CSSProperties}
//               />
//             </div>
//           </div>
//         </div>
//       )}

//       {isEditing && (
//         <div className="flex justify-end mt-4">
//           <button
//             onClick={onSave}
//             className="flex items-center gap-2 px-4 py-2 text-white rounded-lg transition-colors"
//             style={{ backgroundColor: '#076934' }}
//           >
//             <Save size={16} />
//             Save Changes
//           </button>
//         </div>
//       )}
//     </div>
//   );
// };

// // Main Profile Component
// const Profile: React.FC<ProfileProps> = ({ 
//   userProfile = defaultUserProfile,
//   payerProfile = defaultPayerProfile,
//   onUserProfileChange,
//   onPayerProfileChange
// }) => {
//   const [editingUserProfile, setEditingUserProfile] = useState(false);
//   const [editingPayerProfile, setEditingPayerProfile] = useState(false);
//   const [currentUserProfile, setCurrentUserProfile] = useState(userProfile);
//   const [currentPayerProfile, setCurrentPayerProfile] = useState(payerProfile);

//   const handleUserProfileChange = (profile: UserProfile) => {
//     setCurrentUserProfile(profile);
//     if (onUserProfileChange) {
//       onUserProfileChange(profile);
//     }
//   };

//   const handlePayerProfileChange = (profile: PayerProfile) => {
//     setCurrentPayerProfile(profile);
//     if (onPayerProfileChange) {
//       onPayerProfileChange(profile);
//     }
//   };

//   return (
//     <div className="space-y-6">
//       <ProfileForm
//         title="User Profile"
//         icon={User}
//         profile={currentUserProfile}
//         isEditing={editingUserProfile}
//         onToggleEdit={() => setEditingUserProfile(!editingUserProfile)}
//         onSave={() => setEditingUserProfile(false)}
//         onProfileChange={(profile) => handleUserProfileChange(profile as UserProfile)}
//         isUserProfile={true}
//       />
//       <ProfileForm
//         title="Organization Profile"
//         icon={Building2}
//         profile={currentPayerProfile}
//         isEditing={editingPayerProfile}
//         onToggleEdit={() => setEditingPayerProfile(!editingPayerProfile)}
//         onSave={() => setEditingPayerProfile(false)}
//         onProfileChange={(profile) => handlePayerProfileChange(profile as PayerProfile)}
//         isUserProfile={false}
//       />
//     </div>
//   );
// };

// export default Profile;

import React, { useState } from 'react';
import { Building2, Edit, Save, X, Mail, Phone, MapPin, Bell } from 'lucide-react';

// Types
interface OrganizationProfile {
  organizationName: string;
  contractAddress: string;
  email: string;
  phone: string;
}

interface ProfileProps {
  profile?: OrganizationProfile;
  onProfileChange?: (profile: OrganizationProfile) => void;
  onNotificationSent?: (email: string) => void;
}

// Default data
const defaultProfile: OrganizationProfile = {
  organizationName: 'ABC Corporation',
  contractAddress: '123 Business Ave, Suite 100\nBusiness City, BC 12345',
  email: '<EMAIL>',
  phone: '+****************'
};

const OrganizationProfile: React.FC<ProfileProps> = ({ 
  profile = defaultProfile,
  onProfileChange,
  onNotificationSent
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [currentProfile, setCurrentProfile] = useState(profile);
  const [showNotification, setShowNotification] = useState(false);

  const handleProfileChange = (field: keyof OrganizationProfile, value: string) => {
    const updatedProfile = { ...currentProfile, [field]: value };
    setCurrentProfile(updatedProfile);
  };

  const handleSave = () => {
    setIsEditing(false);
    if (onProfileChange) {
      onProfileChange(currentProfile);
    }
    if (onNotificationSent) {
      onNotificationSent(currentProfile.email);
    }
    // Show notification confirmation
    setShowNotification(true);
    setTimeout(() => setShowNotification(false), 3000);
  };

  const handleCancel = () => {
    setCurrentProfile(profile);
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      {/* Notification Banner */}
      {showNotification && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
          <Bell className="text-green-600" size={20} />
          <div>
            <p className="text-green-800 font-medium">Profile Updated Successfully</p>
            <p className="text-green-600 text-sm">Notification sent to {currentProfile.email}</p>
          </div>
        </div>
      )}

      {/* Organization Profile Card */}
      <div className="bg-white rounded-lg border border-gray-200">
        {/* Header */}
        <div className="bg-[#045024] px-6 py-4 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Building2 className="text-white" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-white">Organization Profile</h2>
                <p className="text-green-100 text-sm">Manage your organization details</p>
              </div>
            </div>
            <button
              onClick={isEditing ? handleCancel : () => setIsEditing(true)}
              className="flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors border border-white/20"
            >
              {isEditing ? <X size={16} /> : <Edit size={16} />}
              {isEditing ? 'Cancel' : 'Edit Profile'}
            </button>
          </div>
        </div>

        {/* Form Content */}
        <div className="p-6 space-y-6">
          {/* Organization Name */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Building2 size={16} className="text-gray-500" />
              <label className="text-sm font-medium text-gray-700">Organization Name</label>
            </div>
            <div className="relative">
              <input
                type="text"
                value={currentProfile.organizationName}
                disabled={true}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-600"
              />
              <span className="absolute right-3 top-3 text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                Read-only
              </span>
            </div>
          </div>

          {/* Contract Address */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <MapPin size={16} className="text-gray-500" />
              <label className="text-sm font-medium text-gray-700">Contract Address</label>
            </div>
            <textarea
              value={currentProfile.contractAddress}
              disabled={!isEditing}
              onChange={(e) => handleProfileChange('contractAddress', e.target.value)}
              rows={3}
              className={`w-full px-4 py-3 border rounded-lg transition-colors ${
                isEditing 
                  ? 'border-green-300 focus:border-green-500 focus:ring-2 focus:ring-green-200 bg-white focus:outline-none' 
                  : 'border-gray-200 bg-gray-50 text-gray-600'
              }`}
              placeholder="Enter your organization's contract address"
            />
          </div>

          {/* Email and Phone in Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Email Address */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Mail size={16} className="text-gray-500" />
                <label className="text-sm font-medium text-gray-700">Email Address</label>
              </div>
              <input
                type="email"
                value={currentProfile.email}
                disabled={!isEditing}
                onChange={(e) => handleProfileChange('email', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg transition-colors ${
                  isEditing 
                    ? 'border-green-300 focus:border-green-500 focus:ring-2 focus:ring-green-200 bg-white focus:outline-none' 
                    : 'border-gray-200 bg-gray-50 text-gray-600'
                }`}
                placeholder="Enter email address"
              />
              {isEditing && (
                <p className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                  <Bell size={12} />
                  Notifications will be sent to this email
                </p>
              )}
            </div>

            {/* Phone Number */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Phone size={16} className="text-gray-500" />
                <label className="text-sm font-medium text-gray-700">Phone Number</label>
              </div>
              <input
                type="tel"
                value={currentProfile.phone}
                disabled={!isEditing}
                onChange={(e) => handleProfileChange('phone', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg transition-colors ${
                  isEditing 
                    ? 'border-green-300 focus:border-green-500 focus:ring-2 focus:ring-green-200 bg-white focus:outline-none' 
                    : 'border-gray-200 bg-gray-50 text-gray-600'
                }`}
                placeholder="Enter phone number"
              />
            </div>
          </div>

          {/* Save Button */}
          {isEditing && (
            <div className="flex justify-end pt-4 border-t border-gray-100">
              <button
                onClick={handleSave}
                className="flex items-center gap-2 px-6 py-3 bg-[#045024] hover:bg-green-700 text-white rounded-lg transition-colors font-medium"
              >
                <Save size={16} />
                Save Changes
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrganizationProfile;