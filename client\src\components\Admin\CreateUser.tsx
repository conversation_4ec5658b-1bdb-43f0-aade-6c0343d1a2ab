import React, { useState } from "react";
import { ChevronLeft, Mail, Loader2 } from "lucide-react";
import { API_BASE_URL } from "../../config/api";
import UserInvitations from "./UserInvitation";
interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: "primary" | "secondary" | "danger";
  size?: "sm" | "md";
  disabled?: boolean;
}

interface CreateUserProps {
  setActiveTab: (tab: string) => void;
}

interface InvitationData {
  email: string;
  role: string;
  organizationId: string;
  authType: number;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  children,
  onClick,
  variant = "primary",
  size = "md",
  disabled = false,
}) => {
  const baseClasses =
    "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white",
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${
        disabled ? "opacity-50 cursor-not-allowed" : ""
      }`}
    >
      {children}
    </button>
  );
};

const CreateUser: React.FC<CreateUserProps> = ({ setActiveTab }) => {
  const [email, setEmail] = useState("");
  const [role, setRole] = useState("admin");
  const [organizationId, setOrganizationId] = useState("");
  const [authType, setAuthType] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);

  const sendInvitation = async () => {
    if (!email.trim()) {
      setMessage({ type: "error", text: "Email is required" });
      return;
    }

    if (!organizationId.trim()) {
      setMessage({ type: "error", text: "Organization ID is required" });
      return;
    }

    setIsLoading(true);
    setMessage(null);

    try {
      // Get the JWT token from localStorage or wherever you store it
      const token = localStorage.getItem("auth_token");

      const invitationData: InvitationData = {
        email: email.trim(),
        role: role,
        organizationId: organizationId.trim(),
        authType: authType,
      };

      const response = await fetch(`${API_BASE_URL}/user-invitations/invite`, {
        method: "POST",
        headers: {
          accept: "*/*",
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(invitationData),
      });

      if (response.ok) {
        const result = await response.json();
        setMessage({
          type: "success",
          text: `Invitation sent successfully to ${email}!`,
        });

        // Reset form after successful invitation
        setTimeout(() => {
          setEmail("");
          setRole("admin");
          setOrganizationId("org-001");
          setAuthType(1);
          setMessage(null);
        }, 2000);
      } else {
        const errorData = await response.json().catch(() => ({}));
        setMessage({
          type: "error",
          text:
            errorData.message ||
            `Failed to send invitation. Status: ${response.status}`,
        });
      }
    } catch (error) {
      console.error("Error sending invitation:", error);
      setMessage({
        type: "error",
        text: "Network error. Please check your connection and try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center gap-4 mb-6">
            <button
              onClick={() => setActiveTab("user-list")}
              className="text-[#2aa45c] hover:text-[#045024]"
            >
              <ChevronLeft size={24} />
            </button>
            <h2 className="text-xl font-semibold text-[#045024]">
              Invite New User
            </h2>
          </div>

          <div className="max-w-2xl">
            <div className="space-y-6">
              {/* Message Display */}
              {message && (
                <div
                  className={`p-3 rounded-lg ${
                    message.type === "success"
                      ? "bg-green-100 text-green-800 border border-green-300"
                      : "bg-red-100 text-red-800 border border-red-300"
                  }`}
                >
                  {message.text}
                </div>
              )}

              {/* Email Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter user's email"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  required
                />
              </div>

              {/* Role Selection and Auth Type */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Role <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={role}
                    onChange={(e) => setRole(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                    required
                  >
                    <option value="admin">Admin</option>
                    <option value="payer">Payer</option>
                    <option value="finance_officer">Finance Officer</option>
                    <option value="senior_finance_officer">
                      Senior Finance Officer
                    </option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Authentication Type <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={authType}
                    onChange={(e) => setAuthType(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                    required
                  >
                    <option value={0}>Microsoft</option>
                    <option value={1}>Local</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Organization ID <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={organizationId}
                    onChange={(e) => setOrganizationId(e.target.value)}
                    placeholder="e.g., org-001"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                    required
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-4">
                <ActionButton
                  onClick={sendInvitation}
                  disabled={
                    !email.trim() || !organizationId.trim() || isLoading
                  }
                >
                  {isLoading ? (
                    <>
                      <Loader2 size={16} className="animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail size={16} />
                      Send Invitation
                    </>
                  )}
                </ActionButton>
                <ActionButton
                  variant="secondary"
                  onClick={() => setActiveTab("user-list")}
                >
                  Cancel
                </ActionButton>
              </div>
            </div>
          </div>
        </div>
      </div>
      <UserInvitations />
    </div>
  );
};

export default CreateUser;

// import React, { useState } from "react";
// import { ChevronLeft, Search, Mail, Loader2 } from "lucide-react";
// import { API_BASE_URL } from "../../config/api";

// interface ActionButtonProps {
//   children: React.ReactNode;
//   onClick?: () => void;
//   variant?: "primary" | "secondary" | "danger";
//   size?: "sm" | "md";
//   disabled?: boolean;
// }

// interface CreateUserProps {
//   setActiveTab: (tab: string) => void;
// }

// interface InvitationData {
//   email: string;
//   role: string;
//   organizationId: string;
//   authType: number;
// }

// const ActionButton: React.FC<ActionButtonProps> = ({
//   children,
//   onClick,
//   variant = "primary",
//   size = "md",
//   disabled = false,
// }) => {
//   const baseClasses =
//     "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
//   const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
//   const variantClasses = {
//     primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
//     secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
//     danger: "bg-red-500 hover:bg-red-600 text-white",
//   };

//   return (
//     <button
//       onClick={onClick}
//       disabled={disabled}
//       className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${
//         disabled ? "opacity-50 cursor-not-allowed" : ""
//       }`}
//     >
//       {children}
//     </button>
//   );
// };

// const CreateUser: React.FC<CreateUserProps> = ({ setActiveTab }) => {
//   const [searchQuery, setSearchQuery] = useState("");
//   const [selectedUser, setSelectedUser] = useState<{
//     name: string;
//     email: string;
//   } | null>(null);
//   const [role, setRole] = useState("Payer");
//   const [organizationId, setOrganizationId] = useState("");
//   const [authType, setAuthType] = useState(1); // 0 = MICROSOFT, 1 = LOCAL
//   const [welcomeMessage, setWelcomeMessage] = useState("");
//   const [isLoading, setIsLoading] = useState(false);
//   const [message, setMessage] = useState<{
//     type: "success" | "error";
//     text: string;
//   } | null>(null);

//   // Mock search results - in real app, these would come from Azure AD API
//   const mockUsers = [
//     { name: "Alex Thompson", email: "<EMAIL>" },
//     { name: "Emma Davis", email: "<EMAIL>" },
//     { name: "John Smith", email: "<EMAIL>" },
//     { name: "Sarah Wilson", email: "<EMAIL>" },
//   ];

//   const filteredUsers = mockUsers.filter(
//     (user) =>
//       user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
//       user.email.toLowerCase().includes(searchQuery.toLowerCase())
//   );

//   const handleSelectUser = (user: { name: string; email: string }) => {
//     setSelectedUser(user);
//     setMessage(null);
//   };

//   const mapRoleToBackend = (frontendRole: string): string => {
//     const roleMapping: { [key: string]: string } = {
//       Payer: "payer",
//       "Finance Officer": "finance_officer",
//       "Senior Finance Officer": "senior_finance_officer",
//       Admin: "admin",
//     };
//     return roleMapping[frontendRole] || "payer";
//   };

//   const sendInvitation = async () => {
//     if (!selectedUser) {
//       setMessage({ type: "error", text: "Please select a user first" });
//       return;
//     }

//     if (!organizationId.trim()) {
//       setMessage({ type: "error", text: "Organization ID is required" });
//       return;
//     }

//     setIsLoading(true);
//     setMessage(null);

//     try {
//       // Get the JWT token from localStorage or wherever you store it
//       const token = localStorage.getItem("auth_token");

//       const invitationData: InvitationData = {
//         email: selectedUser.email,
//         role: mapRoleToBackend(role),
//         organizationId: organizationId.trim(),
//         authType: authType,
//       };

//       const response = await fetch(`${API_BASE_URL}/user-invitations/invite`, {
//         method: "POST",
//         headers: {
//           accept: "*/*",
//           Authorization: `Bearer ${token}`,
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify(invitationData),
//       });

//       if (response.ok) {
//         const result = await response.json();
//         setMessage({
//           type: "success",
//           text: `Invitation sent successfully to ${selectedUser.email}!`,
//         });

//         // Reset form after successful invitation
//         setTimeout(() => {
//           setSelectedUser(null);
//           setSearchQuery("");
//           setRole("Payer");
//           setOrganizationId("");
//           setAuthType(1);
//           setWelcomeMessage("");
//           setMessage(null);
//         }, 2000);
//       } else {
//         const errorData = await response.json().catch(() => ({}));
//         setMessage({
//           type: "error",
//           text:
//             errorData.message ||
//             `Failed to send invitation. Status: ${response.status}`,
//         });
//       }
//     } catch (error) {
//       console.error("Error sending invitation:", error);
//       setMessage({
//         type: "error",
//         text: "Network error. Please check your connection and try again.",
//       });
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   return (
//     <div className="space-y-6">
//       <div className="bg-white rounded-lg shadow-md p-6">
//         <div className="flex items-center gap-4 mb-6">
//           <button
//             onClick={() => setActiveTab("user-list")}
//             className="text-[#2aa45c] hover:text-[#045024]"
//           >
//             <ChevronLeft size={24} />
//           </button>
//           <h2 className="text-xl font-semibold text-[#045024]">
//             Invite New User
//           </h2>
//         </div>

//         <div className="max-w-2xl">
//           <div className="space-y-6">
//             {/* Message Display */}
//             {message && (
//               <div
//                 className={`p-3 rounded-lg ${
//                   message.type === "success"
//                     ? "bg-green-100 text-green-800 border border-green-300"
//                     : "bg-red-100 text-red-800 border border-red-300"
//                 }`}
//               >
//                 {message.text}
//               </div>
//             )}

//             {/* Search Section */}
//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-2">
//                 Search Azure AD Users
//               </label>
//               <div className="relative">
//                 <Search
//                   className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
//                   size={20}
//                 />
//                 <input
//                   type="text"
//                   value={searchQuery}
//                   onChange={(e) => setSearchQuery(e.target.value)}
//                   placeholder="Search by name or email..."
//                   className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                 />
//               </div>
//             </div>

//             {/* Selected User Display */}
//             {selectedUser && (
//               <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
//                 <h4 className="font-medium text-blue-800 mb-2">
//                   Selected User
//                 </h4>
//                 <div className="flex items-center justify-between">
//                   <div>
//                     <div className="font-medium text-blue-900">
//                       {selectedUser.name}
//                     </div>
//                     <div className="text-sm text-blue-700">
//                       {selectedUser.email}
//                     </div>
//                   </div>
//                   <button
//                     onClick={() => setSelectedUser(null)}
//                     className="text-blue-600 hover:text-blue-800 text-sm"
//                   >
//                     Change
//                   </button>
//                 </div>
//               </div>
//             )}

//             {/* Search Results */}
//             {searchQuery && !selectedUser && (
//               <div className="bg-[#dddeda] bg-opacity-30 rounded-lg p-4">
//                 <h4 className="font-medium text-[#045024] mb-3">
//                   Search Results
//                 </h4>
//                 <div className="space-y-2">
//                   {filteredUsers.length > 0 ? (
//                     filteredUsers.map((user, index) => (
//                       <div
//                         key={index}
//                         className="flex items-center justify-between p-3 bg-white rounded border"
//                       >
//                         <div>
//                           <div className="font-medium">{user.name}</div>
//                           <div className="text-sm text-gray-500">
//                             {user.email}
//                           </div>
//                         </div>
//                         <ActionButton
//                           size="sm"
//                           onClick={() => handleSelectUser(user)}
//                         >
//                           Select
//                         </ActionButton>
//                       </div>
//                     ))
//                   ) : (
//                     <div className="text-gray-500 text-center py-4">
//                       No users found
//                     </div>
//                   )}
//                 </div>
//               </div>
//             )}

//             {/* Role Selection and Auth Type */}
//             <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-2">
//                   Role
//                 </label>
//                 <select
//                   value={role}
//                   onChange={(e) => setRole(e.target.value)}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                 >
//                   <option value="Payer">Payer</option>
//                   <option value="Finance Officer">Finance Officer</option>
//                   <option value="Senior Finance Officer">
//                     Senior Finance Officer
//                   </option>
//                   <option value="Admin">Admin</option>
//                 </select>
//               </div>

//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-2">
//                   Authentication Type
//                 </label>
//                 <select
//                   value={authType}
//                   onChange={(e) => setAuthType(Number(e.target.value))}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                 >
//                   <option value={0}>Microsoft</option>
//                   <option value={1}>Local</option>
//                 </select>
//               </div>

//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-2">
//                   Organization ID <span className="text-red-500">*</span>
//                 </label>
//                 <input
//                   type="text"
//                   value={organizationId}
//                   onChange={(e) => setOrganizationId(e.target.value)}
//                   placeholder="e.g., org-001"
//                   className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                   required
//                 />
//               </div>
//             </div>

//             {/* Welcome Message */}
//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-2">
//                 Welcome Message (Optional)
//               </label>
//               <textarea
//                 rows={4}
//                 value={welcomeMessage}
//                 onChange={(e) => setWelcomeMessage(e.target.value)}
//                 placeholder="Add a personal welcome message for the new user..."
//                 className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//               />
//             </div>

//             {/* Action Buttons */}
//             <div className="flex gap-4">
//               <ActionButton
//                 onClick={sendInvitation}
//                 disabled={!selectedUser || !organizationId.trim() || isLoading}
//               >
//                 {isLoading ? (
//                   <>
//                     <Loader2 size={16} className="animate-spin" />
//                     Sending...
//                   </>
//                 ) : (
//                   <>
//                     <Mail size={16} />
//                     Send Invitation
//                   </>
//                 )}
//               </ActionButton>
//               <ActionButton
//                 variant="secondary"
//                 onClick={() => setActiveTab("user-list")}
//               >
//                 Cancel
//               </ActionButton>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default CreateUser;

// import React from 'react';
// import {
//   ChevronLeft,
//   Search,
//   Mail
// } from 'lucide-react';

// interface ActionButtonProps {
//   children: React.ReactNode;
//   onClick?: () => void;
//   variant?: 'primary' | 'secondary' | 'danger';
//   size?: 'sm' | 'md';
//   disabled?: boolean;
// }

// interface CreateUserProps {
//   setActiveTab: (tab: string) => void;
// }

// const ActionButton: React.FC<ActionButtonProps> = ({ children, onClick, variant = "primary", size = "md", disabled = false }) => {
//   const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
//   const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
//   const variantClasses = {
//     primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
//     secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
//     danger: "bg-red-500 hover:bg-red-600 text-white"
//   };

//   return (
//     <button
//       onClick={onClick}
//       disabled={disabled}
//       className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
//     >
//       {children}
//     </button>
//   );
// };

// const CreateUser: React.FC<CreateUserProps> = ({ setActiveTab }) => {
//   return (
//     <div className="space-y-6">
//       <div className="bg-white rounded-lg shadow-md p-6">
//         <div className="flex items-center gap-4 mb-6">
//           <button onClick={() => setActiveTab('user-list')} className="text-[#2aa45c] hover:text-[#045024]">
//             <ChevronLeft size={24} />
//           </button>
//           <h2 className="text-xl font-semibold text-[#045024]">Invite New User</h2>
//         </div>

//         <div className="max-w-2xl">
//           <div className="space-y-6">
//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-2">Search Azure AD Users</label>
//               <div className="relative">
//                 <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
//                 <input
//                   type="text"
//                   placeholder="Search by name or email..."
//                   className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                 />
//               </div>
//             </div>

//             <div className="bg-[#dddeda] bg-opacity-30 rounded-lg p-4">
//               <h4 className="font-medium text-[#045024] mb-3">Search Results</h4>
//               <div className="space-y-2">
//                 <div className="flex items-center justify-between p-3 bg-white rounded border">
//                   <div>
//                     <div className="font-medium">Alex Thompson</div>
//                     <div className="text-sm text-gray-500"><EMAIL></div>
//                   </div>
//                   <ActionButton size="sm">Select</ActionButton>
//                 </div>
//                 <div className="flex items-center justify-between p-3 bg-white rounded border">
//                   <div>
//                     <div className="font-medium">Emma Davis</div>
//                     <div className="text-sm text-gray-500"><EMAIL></div>
//                   </div>
//                   <ActionButton size="sm">Select</ActionButton>
//                 </div>
//               </div>
//             </div>

//             <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
//                 <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent">
//                   <option>Payer</option>
//                   <option>Finance Officer</option>
//                   <option>Senior Finance Officer</option>
//                   <option>Senior Finance Officer</option>S

//                 </select>
//               </div>
//               {/* <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
//                 <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent">
//                   <option>Finance</option>
//                   <option>IT</option>
//                   <option>HR</option>
//                   <option>Operations</option>
//                 </select>
//               </div> */}
//             </div>

//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-2">Welcome Message (Optional)</label>
//               <textarea
//                 rows={4}
//                 placeholder="Add a personal welcome message for the new user..."
//                 className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//               />
//             </div>

//             <div className="flex gap-4">
//               <ActionButton>
//                 <Mail size={16} />
//                 Send Invitation
//               </ActionButton>
//               <ActionButton variant="secondary" onClick={() => setActiveTab('user-list')}>
//                 Cancel
//               </ActionButton>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default CreateUser;
