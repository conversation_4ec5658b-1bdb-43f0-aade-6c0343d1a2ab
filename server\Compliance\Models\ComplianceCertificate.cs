using System;

namespace Final_E_Receipt.Compliance.Models
{
    public class ComplianceCertificate
    {
        public string Id { get; set; }
        public string CertificateNumber { get; set; }
        public string OrganizationId { get; set; }
        public string OrganizationName { get; set; }
        public string PaymentProfileId { get; set; }
        public string PaymentProfileName { get; set; }
        public string CertificateType { get; set; } // ANNUAL_LICENSE, QUARTERLY_COMPLIANCE, TAX_CLEARANCE, etc.
        public string Status { get; set; } // PENDING, GENERATED, ISSUED, REVOKED
        public decimal TotalAmount { get; set; }
        public string Currency { get; set; } = "NGN";
        public DateTime ValidFrom { get; set; }
        public DateTime ValidUntil { get; set; }
        public DateTime IssuedDate { get; set; }
        public string IssuedBy { get; set; }
        public string Description { get; set; }
        public string Terms { get; set; }
        public string CertificatePdfFileId { get; set; } // Link to generated PDF file
        public bool IsRevoked { get; set; } = false;
        public DateTime? RevokedDate { get; set; }
        public string RevokedBy { get; set; }
        public string RevokedReason { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
        
        // Additional metadata
        public string ComplianceYear { get; set; }
        public string CompliancePeriod { get; set; } // Q1, Q2, Q3, Q4, ANNUAL
        public string RegulatoryBody { get; set; }
        public string LicenseCategory { get; set; }
        public string Notes { get; set; }
    }
}
