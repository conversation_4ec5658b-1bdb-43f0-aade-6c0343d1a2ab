import React, { useState, useRef, useEffect } from "react";
import {
  Upload,
  Download,
  Eye,
  Trash2,
  Save,
  X,
  FileI<PERSON>,
  Settings,
  CheckCircle,
  AlertCircle,
  RefreshCw,
} from "lucide-react";

// Updated interfaces to match the API
interface ReceiptTemplate {
  id: string;
  name: string;
  description: string;
  fileName: string;
  fileSize: number;
  uploadedDate: string;
  isActive: boolean;
  isDefault: boolean;
  previewUrl: string;
  organizationId?: string;
  organizationName?: string;
  createdBy: string;
  updatedAt?: string;
}

interface CreateReceiptTemplateDTO {
  name: string;
  description: string;
  file: File;
  isDefault: boolean;
  organizationId?: string;
}

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: "primary" | "secondary" | "danger";
  size?: "sm" | "md";
  disabled?: boolean;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  children,
  onClick,
  variant = "primary",
  size = "md",
  disabled = false,
}) => {
  const baseClasses =
    "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white",
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${
        disabled ? "opacity-50 cursor-not-allowed" : ""
      }`}
    >
      {children}
    </button>
  );
};

// Mock API hook since we don't have the actual implementation yet
const useReceiptTemplatesApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = async <T,>(
    apiCall: () => Promise<T>
  ): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: any) {
      setError(err.message || "An error occurred");
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    getReceiptTemplates: (filters?: any) =>
      handleApiCall(async () => {
        // Mock API call - replace with actual API when ready
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return [];
      }),
    createReceiptTemplate: (data: CreateReceiptTemplateDTO) =>
      handleApiCall(async () => {
        await new Promise((resolve) => setTimeout(resolve, 2000));
        return {
          id: `template-${Date.now()}`,
          name: data.name,
          description: data.description,
          fileName: data.file.name,
          fileSize: data.file.size,
          uploadedDate: new Date().toISOString().split("T")[0],
          isActive: true,
          isDefault: data.isDefault,
          previewUrl: URL.createObjectURL(data.file),
          organizationName: "Joint Revenue Board",
          createdBy: "current-user",
        };
      }),
    updateReceiptTemplate: (id: string, data: any) =>
      handleApiCall(async () => {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return data;
      }),
    deleteReceiptTemplate: (id: string) =>
      handleApiCall(async () => {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return;
      }),
    setDefaultTemplate: (id: string) =>
      handleApiCall(async () => {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return {};
      }),
    toggleTemplateStatus: (id: string) =>
      handleApiCall(async () => {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return {};
      }),
    downloadTemplate: (id: string) =>
      handleApiCall(async () => {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return new Blob();
      }),
    getTemplatePreview: (id: string) =>
      handleApiCall(async () => {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return "";
      }),
  };
};

const ReceiptTemplateManagement: React.FC = () => {
  const {
    loading: apiLoading,
    error: apiError,
    getReceiptTemplates,
    createReceiptTemplate,
    updateReceiptTemplate,
    deleteReceiptTemplate,
    setDefaultTemplate,
    toggleTemplateStatus,
    downloadTemplate,
  } = useReceiptTemplatesApi();

  const [templates, setTemplates] = useState<ReceiptTemplate[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    useState<ReceiptTemplate | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [newTemplate, setNewTemplate] = useState({
    name: "",
    description: "",
    file: null as File | null,
    isDefault: false,
  });

  // Load templates on component mount
  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    const result = await getReceiptTemplates();
    if (result) {
      setTemplates(result);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        alert("Please select an image file (PNG, JPG, etc.)");
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert("File size must be less than 5MB");
        return;
      }

      setNewTemplate((prev) => ({ ...prev, file }));
    }
  };

  const handleUpload = async () => {
    if (!newTemplate.file || !newTemplate.name.trim()) {
      alert("Please provide template name and select a file");
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    // Simulate upload progress
    const progressInterval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 200);

    try {
      const templateData: CreateReceiptTemplateDTO = {
        name: newTemplate.name,
        description: newTemplate.description,
        file: newTemplate.file,
        isDefault: newTemplate.isDefault,
      };

      const result = await createReceiptTemplate(templateData);

      if (result) {
        setTemplates((prev) => [...prev, result]);
        setUploadProgress(100);

        setTimeout(() => {
          setShowUploadModal(false);
          setNewTemplate({
            name: "",
            description: "",
            file: null,
            isDefault: false,
          });
          setUploadProgress(0);
          setIsUploading(false);
        }, 500);
      } else {
        throw new Error("Upload failed");
      }
    } catch (error) {
      console.error("Upload failed:", error);
      alert("Upload failed. Please try again.");
      setIsUploading(false);
      setUploadProgress(0);
    }

    clearInterval(progressInterval);
  };

  const handleSetDefault = async (templateId: string) => {
    try {
      const result = await setDefaultTemplate(templateId);
      if (result !== null) {
        setTemplates((prev) =>
          prev.map((template) => ({
            ...template,
            isDefault: template.id === templateId,
          }))
        );
      }
    } catch (error) {
      console.error("Failed to set default template:", error);
    }
  };

  const handleToggleActive = async (templateId: string) => {
    try {
      const result = await toggleTemplateStatus(templateId);
      if (result !== null) {
        setTemplates((prev) =>
          prev.map((template) =>
            template.id === templateId
              ? { ...template, isActive: !template.isActive }
              : template
          )
        );
      }
    } catch (error) {
      console.error("Failed to toggle template status:", error);
    }
  };

  const handleDelete = async (templateId: string) => {
    if (window.confirm("Are you sure you want to delete this template?")) {
      try {
        const result = await deleteReceiptTemplate(templateId);
        if (result !== null) {
          setTemplates((prev) =>
            prev.filter((template) => template.id !== templateId)
          );
        }
      } catch (error) {
        console.error("Failed to delete template:", error);
      }
    }
  };

  const handleDownload = async (templateId: string) => {
    try {
      const result = await downloadTemplate(templateId);
      if (result) {
        // Handle download - create blob URL and trigger download
        const url = URL.createObjectURL(result as Blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `template-${templateId}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  if (apiLoading && templates.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2aa45c]"></div>
        <span className="ml-2">Loading templates...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-[#045024]">
            Receipt Template Management
          </h2>
          <p className="text-gray-600 mt-1">
            Manage JRB receipt templates for all tax services
          </p>
        </div>
        <ActionButton
          onClick={() => setShowUploadModal(true)}
          disabled={apiLoading}
        >
          <Upload size={16} />
          Upload New Template
        </ActionButton>
      </div>

      {/* Error Display */}
      {apiError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-red-800">
            <AlertCircle size={16} />
            <span>Error: {apiError}</span>
          </div>
        </div>
      )}

      {/* Template Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates.map((template) => (
          <div
            key={template.id}
            className="bg-white rounded-lg shadow-md overflow-hidden"
          >
            {/* Template Preview */}
            <div className="relative h-48 bg-gray-100 flex items-center justify-center">
              <FileImage size={48} className="text-gray-400" />
              {template.isDefault && (
                <div className="absolute top-2 right-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-[#2aa45c] text-white">
                    <CheckCircle size={12} className="mr-1" />
                    Default
                  </span>
                </div>
              )}
              <div className="absolute top-2 left-2">
                <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    template.isActive
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {template.isActive ? "Active" : "Inactive"}
                </span>
              </div>
            </div>

            {/* Template Info */}
            <div className="p-4">
              <h3 className="font-semibold text-[#045024] mb-1">
                {template.name}
              </h3>
              <p className="text-sm text-gray-600 mb-2">
                {template.description}
              </p>

              <div className="space-y-1 text-xs text-gray-500 mb-4">
                <div>File: {template.fileName}</div>
                <div>Size: {formatFileSize(template.fileSize)}</div>
                <div>Uploaded: {template.uploadedDate}</div>
                <div>Organization: {template.organizationName}</div>
              </div>

              {/* Actions */}
              <div className="flex flex-wrap gap-2">
                <ActionButton
                  size="sm"
                  variant="secondary"
                  onClick={() => {
                    setSelectedTemplate(template);
                    setShowPreview(true);
                  }}
                >
                  <Eye size={14} />
                  Preview
                </ActionButton>

                <ActionButton
                  size="sm"
                  variant="secondary"
                  onClick={() => handleDownload(template.id)}
                  disabled={apiLoading}
                >
                  <Download size={14} />
                  Download
                </ActionButton>

                {!template.isDefault && (
                  <ActionButton
                    size="sm"
                    variant="secondary"
                    onClick={() => handleSetDefault(template.id)}
                    disabled={apiLoading}
                  >
                    <Settings size={14} />
                    Set Default
                  </ActionButton>
                )}

                <ActionButton
                  size="sm"
                  variant="secondary"
                  onClick={() => handleToggleActive(template.id)}
                  disabled={apiLoading}
                >
                  {template.isActive ? (
                    <X size={14} />
                  ) : (
                    <CheckCircle size={14} />
                  )}
                  {template.isActive ? "Deactivate" : "Activate"}
                </ActionButton>

                <ActionButton
                  size="sm"
                  variant="danger"
                  onClick={() => handleDelete(template.id)}
                  disabled={apiLoading}
                >
                  <Trash2 size={14} />
                  Delete
                </ActionButton>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {templates.length === 0 && !apiLoading && (
        <div className="text-center py-12">
          <FileImage size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Templates Found
          </h3>
          <p className="text-gray-600 mb-4">
            Upload your first receipt template to get started.
          </p>
          <ActionButton onClick={() => setShowUploadModal(true)}>
            <Upload size={16} />
            Upload Template
          </ActionButton>
        </div>
      )}

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="flex items-center justify-between p-6 border-b">
              <h3 className="text-lg font-semibold text-[#045024]">
                Upload New Receipt Template
              </h3>
              <button
                onClick={() => setShowUploadModal(false)}
                className="text-gray-400 hover:text-gray-600"
                disabled={isUploading}
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template Name *
                </label>
                <input
                  type="text"
                  value={newTemplate.name}
                  onChange={(e) =>
                    setNewTemplate((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  placeholder="e.g., JRB Special Receipt Template"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  disabled={isUploading}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={newTemplate.description}
                  onChange={(e) =>
                    setNewTemplate((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Brief description of the template usage"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  disabled={isUploading}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template File *
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileSelect}
                    className="hidden"
                    disabled={isUploading}
                  />
                  {newTemplate.file ? (
                    <div className="space-y-2">
                      <FileImage size={32} className="mx-auto text-[#2aa45c]" />
                      <div className="text-sm">
                        <div className="font-medium">
                          {newTemplate.file.name}
                        </div>
                        <div className="text-gray-500">
                          {formatFileSize(newTemplate.file.size)}
                        </div>
                      </div>
                      <ActionButton
                        size="sm"
                        variant="secondary"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploading}
                      >
                        Change File
                      </ActionButton>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Upload size={32} className="mx-auto text-gray-400" />
                      <div className="text-sm text-gray-600">
                        <div>Click to upload or drag and drop</div>
                        <div className="text-xs text-gray-500">
                          PNG, JPG up to 5MB
                        </div>
                      </div>
                      <ActionButton
                        size="sm"
                        variant="secondary"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploading}
                      >
                        Select File
                      </ActionButton>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="setDefault"
                  checked={newTemplate.isDefault}
                  onChange={(e) =>
                    setNewTemplate((prev) => ({
                      ...prev,
                      isDefault: e.target.checked,
                    }))
                  }
                  className="mr-2"
                  disabled={isUploading}
                />
                <label htmlFor="setDefault" className="text-sm text-gray-700">
                  Set as default template
                </label>
              </div>

              {/* Upload Progress */}
              {isUploading && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Uploading...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#2aa45c] h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center justify-end gap-3 p-6 border-t">
              <ActionButton
                variant="secondary"
                onClick={() => setShowUploadModal(false)}
                disabled={isUploading}
              >
                Cancel
              </ActionButton>
              <ActionButton
                onClick={handleUpload}
                disabled={
                  isUploading || !newTemplate.file || !newTemplate.name.trim()
                }
              >
                {isUploading ? (
                  <>
                    <RefreshCw size={16} className="animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Save size={16} />
                    Upload Template
                  </>
                )}
              </ActionButton>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {showPreview && selectedTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b">
              <div>
                <h3 className="text-lg font-semibold text-[#045024]">
                  Template Preview
                </h3>
                <p className="text-sm text-gray-600">{selectedTemplate.name}</p>
              </div>
              <button
                onClick={() => setShowPreview(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-6">
              <div className="bg-gray-100 rounded-lg p-8 text-center">
                <FileImage size={64} className="mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">Template Preview</p>
                <div className="text-sm text-gray-500 space-y-1">
                  <div>File: {selectedTemplate.fileName}</div>
                  <div>Size: {formatFileSize(selectedTemplate.fileSize)}</div>
                  <div>Organization: {selectedTemplate.organizationName}</div>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium text-[#045024] mb-2">
                    Template Information
                  </h4>
                  <div className="space-y-1 text-gray-600">
                    <div>
                      <strong>Name:</strong> {selectedTemplate.name}
                    </div>
                    <div>
                      <strong>Description:</strong>{" "}
                      {selectedTemplate.description}
                    </div>
                    <div>
                      <strong>Status:</strong>{" "}
                      {selectedTemplate.isActive ? "Active" : "Inactive"}
                    </div>
                    <div>
                      <strong>Default:</strong>{" "}
                      {selectedTemplate.isDefault ? "Yes" : "No"}
                    </div>
                    <div>
                      <strong>Created By:</strong> {selectedTemplate.createdBy}
                    </div>
                    {selectedTemplate.updatedAt && (
                      <div>
                        <strong>Last Updated:</strong>{" "}
                        {selectedTemplate.updatedAt}
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-[#045024] mb-2">
                    Usage Guidelines
                  </h4>
                  <div className="space-y-1 text-gray-600">
                    <div>
                      • Template should be 2480 x 3508 pixels (A4 Portrait)
                    </div>
                    <div>• Leave blank spaces for dynamic text overlay</div>
                    <div>• Include JRB branding and official seals</div>
                    <div>• Use high resolution (300 DPI minimum)</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-end gap-3 p-6 border-t">
              <ActionButton
                variant="secondary"
                onClick={() => setShowPreview(false)}
              >
                Close
              </ActionButton>
              <ActionButton onClick={() => handleDownload(selectedTemplate.id)}>
                <Download size={16} />
                Download Template
              </ActionButton>
            </div>
          </div>
        </div>
      )}

      {/* Template Usage Instructions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-[#045024] mb-4">
          Template Requirements & Guidelines
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-[#045024] mb-2">
              Technical Requirements
            </h4>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>
                • <strong>Format:</strong> PNG or JPG (PNG recommended)
              </li>
              <li>
                • <strong>Size:</strong> 2480 x 3508 pixels (A4 Portrait at 300
                DPI)
              </li>
              <li>
                • <strong>File Size:</strong> Maximum 5MB
              </li>
              <li>
                • <strong>Background:</strong> White or transparent
              </li>
              <li>
                • <strong>Resolution:</strong> 300 DPI minimum for print quality
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-[#045024] mb-2">
              Design Guidelines
            </h4>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>• Include JRB logo and official branding</li>
              <li>
                • Leave blank spaces for dynamic text (receipt number, amount,
                etc.)
              </li>
              <li>• Use official JRB colors and fonts</li>
              <li>• Include security features (watermarks, seals)</li>
              <li>• Ensure readability and professional appearance</li>
            </ul>
          </div>
        </div>

        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <div className="flex items-start">
            <AlertCircle
              size={16}
              className="text-blue-600 mt-0.5 mr-2 flex-shrink-0"
            />
            <div className="text-sm text-blue-800">
              <strong>Important:</strong> The system will automatically overlay
              dynamic text (receipt numbers, amounts, dates, etc.) on your
              template. Ensure you leave appropriate blank spaces in your design
              for this information.
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-[#045024] mb-4">
          Quick Actions
        </h3>
        <div className="flex flex-wrap gap-3">
          <ActionButton
            variant="secondary"
            onClick={loadTemplates}
            disabled={apiLoading}
          >
            <RefreshCw size={16} className={apiLoading ? "animate-spin" : ""} />
            Refresh Templates
          </ActionButton>
          <ActionButton variant="secondary">
            <Download size={16} />
            Export All Templates
          </ActionButton>
          <ActionButton variant="secondary">
            <Settings size={16} />
            Template Settings
          </ActionButton>
        </div>
      </div>
    </div>
  );
};

export default ReceiptTemplateManagement;

// import React, { useState, useRef, useEffect } from 'react';
// import {
//   Upload,
//   Download,
//   Eye,
//   Edit,
//   Trash2,
//   Save,
//   X,
//   FileImage,
//   Settings,
//   CheckCircle,
//   AlertCircle,
//   RefreshCw
// } from 'lucide-react';
// import { useReceiptTemplateApi } from '../../hooks/api';

// // Using the existing ReceiptTemplate interface from the API hook
// // interface ReceiptTemplate {
// //   id: string;
// //   name: string;
// //   description?: string;
// //   templateData: any; // JSON template
// //   isActive: boolean;
// //   createdBy: string;
// //   createdAt: string;
// // }

// // Extended interface for UI display
// interface ReceiptTemplateDisplay {
//   id: string;
//   name: string;
//   description: string;
//   fileName: string;
//   fileSize: number;
//   uploadedDate: string;
//   isActive: boolean;
//   isDefault: boolean;
//   previewUrl: string;
//   organizationId?: string;
//   organizationName?: string;
// }

// interface ActionButtonProps {
//   children: React.ReactNode;
//   onClick?: () => void;
//   variant?: 'primary' | 'secondary' | 'danger';
//   size?: 'sm' | 'md';
//   disabled?: boolean;
// }

// const ActionButton: React.FC<ActionButtonProps> = ({
//   children,
//   onClick,
//   variant = "primary",
//   size = "md",
//   disabled = false
// }) => {
//   const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
//   const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
//   const variantClasses = {
//     primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
//     secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
//     danger: "bg-red-500 hover:bg-red-600 text-white"
//   };

//   return (
//     <button
//       onClick={onClick}
//       disabled={disabled}
//       className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${
//         disabled ? 'opacity-50 cursor-not-allowed' : ''
//       }`}
//     >
//       {children}
//     </button>
//   );
// };

// const ReceiptTemplateManagement: React.FC = () => {
//   const {
//     loading: apiLoading,
//     error: apiError,
//     getAllReceiptTemplates,
//     createReceiptTemplate,
//     updateReceiptTemplate,
//     deleteReceiptTemplate,
//     getActiveReceiptTemplates
//   } = useReceiptTemplateApi();

//   const [templates, setTemplates] = useState<ReceiptTemplateDisplay[]>([]);

//   // Load templates on component mount
//   useEffect(() => {
//     const loadTemplates = async () => {
//       try {
//         // For now, use dummy data since the API might not be fully implemented
//         const dummyTemplates: ReceiptTemplateDisplay[] = [
//           {
//             id: 'template-001',
//             name: 'JRB Default Receipt Template',
//             description: 'Standard JRB receipt template for all tax services',
//             fileName: 'jrb-receipt-template.png',
//             fileSize: 245760,
//             uploadedDate: '2024-01-15',
//             isActive: true,
//             isDefault: true,
//             previewUrl: '/templates/previews/jrb-receipt-preview.png',
//             organizationName: 'Joint Revenue Board'
//           },
//           {
//             id: 'template-002',
//             name: 'JRB Landscape Receipt',
//             description: 'Alternative landscape format for special receipts',
//             fileName: 'jrb-receipt-landscape-template.png',
//             fileSize: 198432,
//             uploadedDate: '2024-02-10',
//             isActive: true,
//             isDefault: false,
//             previewUrl: '/templates/previews/jrb-receipt-landscape-preview.png',
//             organizationName: 'Joint Revenue Board'
//           },
//           {
//             id: 'template-003',
//             name: 'Default Fallback Template',
//             description: 'System fallback template when JRB template is unavailable',
//             fileName: 'default-receipt-template.png',
//             fileSize: 156789,
//             uploadedDate: '2024-01-01',
//             isActive: true,
//             isDefault: false,
//             previewUrl: '/templates/previews/default-receipt-preview.png',
//             organizationName: 'System Default'
//           }
//         ];
//         setTemplates(dummyTemplates);

//         // Uncomment when API is ready:
//         // const result = await getAllReceiptTemplates();
//         // if (result) {
//         //   setTemplates(result);
//         // }
//       } catch (error) {
//         console.error('Failed to load templates:', error);
//       }
//     };

//     loadTemplates();
//   }, [getAllReceiptTemplates]);

//   const [showUploadModal, setShowUploadModal] = useState(false);
//   const [selectedTemplate, setSelectedTemplate] = useState<ReceiptTemplateDisplay | null>(null);
//   const [showPreview, setShowPreview] = useState(false);
//   const [uploadProgress, setUploadProgress] = useState(0);
//   const [isUploading, setIsUploading] = useState(false);
//   const fileInputRef = useRef<HTMLInputElement>(null);

//   const [newTemplate, setNewTemplate] = useState({
//     name: '',
//     description: '',
//     file: null as File | null,
//     isDefault: false
//   });

//   const formatFileSize = (bytes: number): string => {
//     if (bytes === 0) return '0 Bytes';
//     const k = 1024;
//     const sizes = ['Bytes', 'KB', 'MB', 'GB'];
//     const i = Math.floor(Math.log(bytes) / Math.log(k));
//     return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
//   };

//   const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
//     const file = event.target.files?.[0];
//     if (file) {
//       // Validate file type
//       if (!file.type.startsWith('image/')) {
//         alert('Please select an image file (PNG, JPG, etc.)');
//         return;
//       }

//       // Validate file size (max 5MB)
//       if (file.size > 5 * 1024 * 1024) {
//         alert('File size must be less than 5MB');
//         return;
//       }

//       setNewTemplate(prev => ({ ...prev, file }));
//     }
//   };

//   const handleUpload = async () => {
//     if (!newTemplate.file || !newTemplate.name.trim()) {
//       alert('Please provide template name and select a file');
//       return;
//     }

//     setIsUploading(true);
//     setUploadProgress(0);

//     // Simulate upload progress
//     const progressInterval = setInterval(() => {
//       setUploadProgress(prev => {
//         if (prev >= 90) {
//           clearInterval(progressInterval);
//           return 90;
//         }
//         return prev + 10;
//       });
//     }, 200);

//     try {
//       // For now, simulate the upload. When API is ready, use:
//       // const result = await createReceiptTemplate({
//       //   name: newTemplate.name,
//       //   description: newTemplate.description,
//       //   templateData: { file: newTemplate.file } // Adjust based on API requirements
//       // });

//       // Simulate API call
//       await new Promise(resolve => setTimeout(resolve, 2000));

//       const newTemplateData: ReceiptTemplateDisplay = {
//         id: `template-${Date.now()}`,
//         name: newTemplate.name,
//         description: newTemplate.description,
//         fileName: newTemplate.file.name,
//         fileSize: newTemplate.file.size,
//         uploadedDate: new Date().toISOString().split('T')[0],
//         isActive: true,
//         isDefault: newTemplate.isDefault,
//         previewUrl: URL.createObjectURL(newTemplate.file),
//         organizationName: 'Joint Revenue Board'
//       };

//       setTemplates(prev => [...prev, newTemplateData]);
//       setUploadProgress(100);

//       setTimeout(() => {
//         setShowUploadModal(false);
//         setNewTemplate({ name: '', description: '', file: null, isDefault: false });
//         setUploadProgress(0);
//         setIsUploading(false);
//       }, 500);

//     } catch (error) {
//       console.error('Upload failed:', error);
//       alert('Upload failed. Please try again.');
//       setIsUploading(false);
//       setUploadProgress(0);
//     }

//     clearInterval(progressInterval);
//   };

//   const handleSetDefault = (templateId: string) => {
//     setTemplates(prev => prev.map(template => ({
//       ...template,
//       isDefault: template.id === templateId
//     })));
//   };

//   const handleToggleActive = (templateId: string) => {
//     setTemplates(prev => prev.map(template =>
//       template.id === templateId
//         ? { ...template, isActive: !template.isActive }
//         : template
//     ));
//   };

//   const handleDelete = (templateId: string) => {
//     if (window.confirm('Are you sure you want to delete this template?')) {
//       setTemplates(prev => prev.filter(template => template.id !== templateId));
//     }
//   };

//   return (
//     <div className="space-y-6">
//       {/* Header */}
//       <div className="flex items-center justify-between">
//         <div>
//           <h2 className="text-2xl font-bold text-[#045024]">Receipt Template Management</h2>
//           <p className="text-gray-600 mt-1">Manage JRB receipt templates for all tax services</p>
//         </div>
//         <ActionButton onClick={() => setShowUploadModal(true)}>
//           <Upload size={16} />
//           Upload New Template
//         </ActionButton>
//       </div>

//       {/* Template Grid */}
//       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
//         {templates.map(template => (
//           <div key={template.id} className="bg-white rounded-lg shadow-md overflow-hidden">
//             {/* Template Preview */}
//             <div className="relative h-48 bg-gray-100 flex items-center justify-center">
//               <FileImage size={48} className="text-gray-400" />
//               {template.isDefault && (
//                 <div className="absolute top-2 right-2">
//                   <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-[#2aa45c] text-white">
//                     <CheckCircle size={12} className="mr-1" />
//                     Default
//                   </span>
//                 </div>
//               )}
//               <div className="absolute top-2 left-2">
//                 <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
//                   template.isActive
//                     ? 'bg-green-100 text-green-800'
//                     : 'bg-red-100 text-red-800'
//                 }`}>
//                   {template.isActive ? 'Active' : 'Inactive'}
//                 </span>
//               </div>
//             </div>

//             {/* Template Info */}
//             <div className="p-4">
//               <h3 className="font-semibold text-[#045024] mb-1">{template.name}</h3>
//               <p className="text-sm text-gray-600 mb-2">{template.description}</p>

//               <div className="space-y-1 text-xs text-gray-500 mb-4">
//                 <div>File: {template.fileName}</div>
//                 <div>Size: {formatFileSize(template.fileSize)}</div>
//                 <div>Uploaded: {template.uploadedDate}</div>
//                 <div>Organization: {template.organizationName}</div>
//               </div>

//               {/* Actions */}
//               <div className="flex flex-wrap gap-2">
//                 <ActionButton
//                   size="sm"
//                   variant="secondary"
//                   onClick={() => {
//                     setSelectedTemplate(template);
//                     setShowPreview(true);
//                   }}
//                 >
//                   <Eye size={14} />
//                   Preview
//                 </ActionButton>

//                 {!template.isDefault && (
//                   <ActionButton
//                     size="sm"
//                     variant="secondary"
//                     onClick={() => handleSetDefault(template.id)}
//                   >
//                     <Settings size={14} />
//                     Set Default
//                   </ActionButton>
//                 )}

//                 <ActionButton
//                   size="sm"
//                   variant="secondary"
//                   onClick={() => handleToggleActive(template.id)}
//                 >
//                   {template.isActive ? <X size={14} /> : <CheckCircle size={14} />}
//                   {template.isActive ? 'Deactivate' : 'Activate'}
//                 </ActionButton>

//                 <ActionButton
//                   size="sm"
//                   variant="danger"
//                   onClick={() => handleDelete(template.id)}
//                 >
//                   <Trash2 size={14} />
//                   Delete
//                 </ActionButton>
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>

//       {/* Upload Modal */}
//       {showUploadModal && (
//         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//           <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
//             <div className="flex items-center justify-between p-6 border-b">
//               <h3 className="text-lg font-semibold text-[#045024]">Upload New Receipt Template</h3>
//               <button
//                 onClick={() => setShowUploadModal(false)}
//                 className="text-gray-400 hover:text-gray-600"
//               >
//                 <X size={20} />
//               </button>
//             </div>

//             <div className="p-6 space-y-4">
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-2">
//                   Template Name *
//                 </label>
//                 <input
//                   type="text"
//                   value={newTemplate.name}
//                   onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
//                   placeholder="e.g., JRB Special Receipt Template"
//                   className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                 />
//               </div>

//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-2">
//                   Description
//                 </label>
//                 <textarea
//                   value={newTemplate.description}
//                   onChange={(e) => setNewTemplate(prev => ({ ...prev, description: e.target.value }))}
//                   placeholder="Brief description of the template usage"
//                   rows={3}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                 />
//               </div>

//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-2">
//                   Template File *
//                 </label>
//                 <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
//                   <input
//                     ref={fileInputRef}
//                     type="file"
//                     accept="image/*"
//                     onChange={handleFileSelect}
//                     className="hidden"
//                   />
//                   {newTemplate.file ? (
//                     <div className="space-y-2">
//                       <FileImage size={32} className="mx-auto text-[#2aa45c]" />
//                       <div className="text-sm">
//                         <div className="font-medium">{newTemplate.file.name}</div>
//                         <div className="text-gray-500">{formatFileSize(newTemplate.file.size)}</div>
//                       </div>
//                       <ActionButton
//                         size="sm"
//                         variant="secondary"
//                         onClick={() => fileInputRef.current?.click()}
//                       >
//                         Change File
//                       </ActionButton>
//                     </div>
//                   ) : (
//                     <div className="space-y-2">
//                       <Upload size={32} className="mx-auto text-gray-400" />
//                       <div className="text-sm text-gray-600">
//                         <div>Click to upload or drag and drop</div>
//                         <div className="text-xs text-gray-500">PNG, JPG up to 5MB</div>
//                       </div>
//                       <ActionButton
//                         size="sm"
//                         variant="secondary"
//                         onClick={() => fileInputRef.current?.click()}
//                       >
//                         Select File
//                       </ActionButton>
//                     </div>
//                   )}
//                 </div>
//               </div>

//               <div className="flex items-center">
//                 <input
//                   type="checkbox"
//                   id="setDefault"
//                   checked={newTemplate.isDefault}
//                   onChange={(e) => setNewTemplate(prev => ({ ...prev, isDefault: e.target.checked }))}
//                   className="mr-2"
//                 />
//                 <label htmlFor="setDefault" className="text-sm text-gray-700">
//                   Set as default template
//                 </label>
//               </div>

//               {/* Upload Progress */}
//               {isUploading && (
//                 <div className="space-y-2">
//                   <div className="flex items-center justify-between text-sm">
//                     <span>Uploading...</span>
//                     <span>{uploadProgress}%</span>
//                   </div>
//                   <div className="w-full bg-gray-200 rounded-full h-2">
//                     <div
//                       className="bg-[#2aa45c] h-2 rounded-full transition-all duration-300"
//                       style={{ width: `${uploadProgress}%` }}
//                     />
//                   </div>
//                 </div>
//               )}
//             </div>

//             <div className="flex items-center justify-end gap-3 p-6 border-t">
//               <ActionButton
//                 variant="secondary"
//                 onClick={() => setShowUploadModal(false)}
//                 disabled={isUploading}
//               >
//                 Cancel
//               </ActionButton>
//               <ActionButton
//                 onClick={handleUpload}
//                 disabled={isUploading || !newTemplate.file || !newTemplate.name.trim()}
//               >
//                 {isUploading ? (
//                   <>
//                     <RefreshCw size={16} className="animate-spin" />
//                     Uploading...
//                   </>
//                 ) : (
//                   <>
//                     <Save size={16} />
//                     Upload Template
//                   </>
//                 )}
//               </ActionButton>
//             </div>
//           </div>
//         </div>
//       )}

//       {/* Preview Modal */}
//       {showPreview && selectedTemplate && (
//         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//           <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
//             <div className="flex items-center justify-between p-6 border-b">
//               <div>
//                 <h3 className="text-lg font-semibold text-[#045024]">Template Preview</h3>
//                 <p className="text-sm text-gray-600">{selectedTemplate.name}</p>
//               </div>
//               <button
//                 onClick={() => setShowPreview(false)}
//                 className="text-gray-400 hover:text-gray-600"
//               >
//                 <X size={20} />
//               </button>
//             </div>

//             <div className="p-6">
//               <div className="bg-gray-100 rounded-lg p-8 text-center">
//                 <FileImage size={64} className="mx-auto text-gray-400 mb-4" />
//                 <p className="text-gray-600 mb-4">Template Preview</p>
//                 <div className="text-sm text-gray-500 space-y-1">
//                   <div>File: {selectedTemplate.fileName}</div>
//                   <div>Size: {formatFileSize(selectedTemplate.fileSize)}</div>
//                   <div>Organization: {selectedTemplate.organizationName}</div>
//                 </div>
//               </div>

//               <div className="mt-6 grid grid-cols-2 gap-4 text-sm">
//                 <div>
//                   <h4 className="font-medium text-[#045024] mb-2">Template Information</h4>
//                   <div className="space-y-1 text-gray-600">
//                     <div><strong>Name:</strong> {selectedTemplate.name}</div>
//                     <div><strong>Description:</strong> {selectedTemplate.description}</div>
//                     <div><strong>Status:</strong> {selectedTemplate.isActive ? 'Active' : 'Inactive'}</div>
//                     <div><strong>Default:</strong> {selectedTemplate.isDefault ? 'Yes' : 'No'}</div>
//                   </div>
//                 </div>
//                 <div>
//                   <h4 className="font-medium text-[#045024] mb-2">Usage Guidelines</h4>
//                   <div className="space-y-1 text-gray-600">
//                     <div>• Template should be 2480 x 3508 pixels (A4 Portrait)</div>
//                     <div>• Leave blank spaces for dynamic text overlay</div>
//                     <div>• Include JRB branding and official seals</div>
//                     <div>• Use high resolution (300 DPI minimum)</div>
//                   </div>
//                 </div>
//               </div>
//             </div>

//             <div className="flex items-center justify-end gap-3 p-6 border-t">
//               <ActionButton variant="secondary" onClick={() => setShowPreview(false)}>
//                 Close
//               </ActionButton>
//               <ActionButton>
//                 <Download size={16} />
//                 Download Template
//               </ActionButton>
//             </div>
//           </div>
//         </div>
//       )}

//       {/* Template Usage Instructions */}
//       <div className="bg-white rounded-lg shadow-md p-6">
//         <h3 className="text-lg font-semibold text-[#045024] mb-4">Template Requirements & Guidelines</h3>
//         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//           <div>
//             <h4 className="font-medium text-[#045024] mb-2">Technical Requirements</h4>
//             <ul className="space-y-1 text-sm text-gray-600">
//               <li>• <strong>Format:</strong> PNG or JPG (PNG recommended)</li>
//               <li>• <strong>Size:</strong> 2480 x 3508 pixels (A4 Portrait at 300 DPI)</li>
//               <li>• <strong>File Size:</strong> Maximum 5MB</li>
//               <li>• <strong>Background:</strong> White or transparent</li>
//               <li>• <strong>Resolution:</strong> 300 DPI minimum for print quality</li>
//             </ul>
//           </div>
//           <div>
//             <h4 className="font-medium text-[#045024] mb-2">Design Guidelines</h4>
//             <ul className="space-y-1 text-sm text-gray-600">
//               <li>• Include JRB logo and official branding</li>
//               <li>• Leave blank spaces for dynamic text (receipt number, amount, etc.)</li>
//               <li>• Use official JRB colors and fonts</li>
//               <li>• Include security features (watermarks, seals)</li>
//               <li>• Ensure readability and professional appearance</li>
//             </ul>
//           </div>
//         </div>

//         <div className="mt-4 p-4 bg-blue-50 rounded-lg">
//           <div className="flex items-start">
//             <AlertCircle size={16} className="text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
//             <div className="text-sm text-blue-800">
//               <strong>Important:</strong> The system will automatically overlay dynamic text (receipt numbers, amounts, dates, etc.)
//               on your template. Ensure you leave appropriate blank spaces in your design for this information.
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default ReceiptTemplateManagement;
