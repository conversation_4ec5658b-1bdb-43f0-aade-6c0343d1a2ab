namespace Final_E_Receipt.Common.Models
{
    public class AuditLog
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string UserId { get; set; }
        public string UserName { get; set; }
        public string UserEmail { get; set; }
        public string Action { get; set; }
        public string EntityType { get; set; }
        public string EntityId { get; set; }
        public string? OldValues { get; set; }
        public string? NewValues { get; set; }
        public string? AdditionalDetails { get; set; }
        public string IpAddress { get; set; }
        public string UserAgent { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string OrganizationId { get; set; }
    }

    public static class AuditActions
    {
        // User Management
        public const string USER_CREATED = "USER_CREATED";
        public const string USER_UPDATED = "USER_UPDATED";
        public const string USER_DELETED = "USER_DELETED";
        public const string USER_LOGIN = "USER_LOGIN";
        public const string USER_LOGOUT = "USER_LOGOUT";
        public const string USER_INVITED = "USER_INVITED";
        public const string USER_INVITATION_ACCEPTED = "USER_INVITATION_ACCEPTED";

        // Payment Management
        public const string PAYMENT_CREATED = "PAYMENT_CREATED";
        public const string PAYMENT_UPDATED = "PAYMENT_UPDATED";
        public const string PAYMENT_ACKNOWLEDGED = "PAYMENT_ACKNOWLEDGED";
        public const string PAYMENT_APPROVED = "PAYMENT_APPROVED";
        public const string PAYMENT_REJECTED = "PAYMENT_REJECTED";
        public const string PAYMENT_CANCELLED = "PAYMENT_CANCELLED";

        // Payment Schedule Management
        public const string PAYMENT_SCHEDULE_CREATED = "PAYMENT_SCHEDULE_CREATED";
        public const string PAYMENT_SCHEDULE_UPDATED = "PAYMENT_SCHEDULE_UPDATED";
        public const string PAYMENT_SCHEDULE_DELETED = "PAYMENT_SCHEDULE_DELETED";

        // Organization Management
        public const string ORGANIZATION_CREATED = "ORGANIZATION_CREATED";
        public const string ORGANIZATION_UPDATED = "ORGANIZATION_UPDATED";
        public const string ORGANIZATION_DELETED = "ORGANIZATION_DELETED";

        // Payment Profile Management
        public const string PAYMENT_PROFILE_CREATED = "PAYMENT_PROFILE_CREATED";
        public const string PAYMENT_PROFILE_UPDATED = "PAYMENT_PROFILE_UPDATED";
        public const string PAYMENT_PROFILE_DELETED = "PAYMENT_PROFILE_DELETED";

        // Payment Type Management
        public const string PAYMENT_TYPE_CREATED = "PAYMENT_TYPE_CREATED";
        public const string PAYMENT_TYPE_UPDATED = "PAYMENT_TYPE_UPDATED";
        public const string PAYMENT_TYPE_DEACTIVATED = "PAYMENT_TYPE_DEACTIVATED";

        // Receipt Management
        public const string RECEIPT_GENERATED = "RECEIPT_GENERATED";
        public const string RECEIPT_DOWNLOADED = "RECEIPT_DOWNLOADED";
        public const string RECEIPT_REVOKED = "RECEIPT_REVOKED";
        public const string RECEIPT_TEMPLATE_CREATED = "RECEIPT_TEMPLATE_CREATED";
        public const string RECEIPT_TEMPLATE_UPDATED = "RECEIPT_TEMPLATE_UPDATED";

        // Email Configuration
        public const string EMAIL_CONFIG_CREATED = "EMAIL_CONFIG_CREATED";
        public const string EMAIL_CONFIG_UPDATED = "EMAIL_CONFIG_UPDATED";
        public const string EMAIL_CONFIG_DELETED = "EMAIL_CONFIG_DELETED";

        // System Configuration
        public const string SYSTEM_CONFIG_UPDATED = "SYSTEM_CONFIG_UPDATED";
        public const string ADMIN_SETUP_COMPLETED = "ADMIN_SETUP_COMPLETED";

        // Security Events
        public const string UNAUTHORIZED_ACCESS_ATTEMPT = "UNAUTHORIZED_ACCESS_ATTEMPT";
        public const string PASSWORD_RESET = "PASSWORD_RESET";
        public const string ROLE_CHANGED = "ROLE_CHANGED";
    }

    public static class EntityTypes
    {
        public const string USER = "User";
        public const string PAYMENT = "Payment";
        public const string PAYMENT_SCHEDULE = "PaymentSchedule";
        public const string ORGANIZATION = "Organization";
        public const string PAYMENT_PROFILE = "PaymentProfile";
        public const string PAYMENT_TYPE = "PaymentType";
        public const string RECEIPT = "Receipt";
        public const string RECEIPT_TEMPLATE = "ReceiptTemplate";
        public const string EMAIL_CONFIGURATION = "EmailConfiguration";
        public const string EMAIL_TEMPLATE = "EmailTemplate";
        public const string USER_INVITATION = "UserInvitation";
        public const string SYSTEM_CONFIGURATION = "SystemConfiguration";
    }
}
