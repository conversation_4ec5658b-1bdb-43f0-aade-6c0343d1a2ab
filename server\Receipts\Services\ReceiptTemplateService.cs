using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Final_E_Receipt.Receipts.Models;
using Final_E_Receipt.Services;

namespace Final_E_Receipt.Receipts.Services
{
    public class ReceiptTemplateService
    {
        private readonly IDatabaseService _dbService;

        public ReceiptTemplateService(IDatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<ReceiptTemplate> CreateReceiptTemplate(ReceiptTemplate template)
        {
            var parameters = new
            {
                Id = template.Id ?? Guid.NewGuid().ToString(),
                Name = template.Name,
                Description = template.Description,
                TemplateContent = template.TemplateContent,
                IsDefault = template.IsDefault,
                OrganizationId = template.OrganizationId,
                CreatedBy = template.CreatedBy
            };

            return await _dbService.QueryFirstOrDefaultAsync<ReceiptTemplate>("CreateReceiptTemplate", parameters);
        }

        public async Task<ReceiptTemplate> GetReceiptTemplateById(string id)
        {
            var parameters = new { Id = id };
            return await _dbService.QueryFirstOrDefaultAsync<ReceiptTemplate>("GetReceiptTemplateById", parameters);
        }

        public async Task<List<ReceiptTemplate>> GetReceiptTemplatesByOrganization(string organizationId)
        {
            var parameters = new { OrganizationId = organizationId };
            var templates = await _dbService.QueryAsync<ReceiptTemplate>("GetReceiptTemplatesByOrganization", parameters);
            return templates.ToList();
        }

        public async Task<ReceiptTemplate> UpdateReceiptTemplate(ReceiptTemplate template)
        {
            var parameters = new
            {
                Id = template.Id,
                Name = template.Name,
                Description = template.Description,
                TemplateContent = template.TemplateContent,
                IsDefault = template.IsDefault,
                UpdatedBy = template.UpdatedBy
            };

            return await _dbService.QueryFirstOrDefaultAsync<ReceiptTemplate>("UpdateReceiptTemplate", parameters);
        }

        public async Task<bool> DeleteReceiptTemplate(string id)
        {
            var parameters = new { Id = id };
            await _dbService.ExecuteAsync("DeleteReceiptTemplate", parameters);
            return true;
        }

        public async Task<ReceiptTemplate> GetDefaultReceiptTemplate(string organizationId)
        {
            var parameters = new { OrganizationId = organizationId };
            return await _dbService.QueryFirstOrDefaultAsync<ReceiptTemplate>("GetDefaultReceiptTemplate", parameters);
        }
    }
}
