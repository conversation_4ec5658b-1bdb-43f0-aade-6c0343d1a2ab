import React from 'react';
import { 
  Users, 
  CreditCard, 
  FileText, 
  DollarSign, 
  Plus, 
  Search, 
  Bell,
  AlertCircle,
} from 'lucide-react';

interface Notification {
  id: number;
  message: string;
  type: string;
  time: string;
}

interface StatsCardProps {
  title: string;
  value: string;
  icon: React.ComponentType<any>;
  trend?: string;
  color?: string;
}

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md';
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, icon: Icon, trend, color = "bg-[#2aa45c]" }) => (
  <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-[#2aa45c]">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className="text-2xl font-bold text-[#045024] mt-1">{value}</p>
        {trend && <p className="text-sm text-[#2aa45c] mt-1">{trend}</p>}
      </div>
      <div className={`${color} p-3 rounded-full`}>
        <Icon className="text-white" size={24} />
      </div>
    </div>
  </div>
);

const ActionButton: React.FC<ActionButtonProps> = ({ children, onClick, variant = "primary", size = "md" }) => {
  const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white"
  };

  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]}`}
    >
      {children}
    </button>
  );
};

// const formatCurrency = (amount: number) => `₦${amount.toLocaleString('en-NG', { minimumFractionDigits: 2 })}`;

interface OverviewProps {
  setActiveTab: (tab: string) => void;
}

const Overview: React.FC<OverviewProps> = ({ setActiveTab }) => {
  const notifications: Notification[] = [
    { id: 1, message: "New payment reported from Acme Corp", type: "payment", time: "2 min ago" },
    { id: 2, message: "Payment profile ABC-2025 needs approval", type: "approval", time: "15 min ago" },
    { id: 3, message: "Overdue payment reminder sent to 5 payers", type: "reminder", time: "1 hour ago" }
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard title="Active Payment Types" value="12" icon={CreditCard} trend="+2 this month" />
        <StatsCard title="Total Payers" value="156" icon={Users} trend="+8 this week" />
        <StatsCard title="Outstanding Amount" value="₦45,100,000,000" icon={DollarSign} trend="-12% from last month" />
        <StatsCard title="Pending Approvals" value="7" icon={AlertCircle} color="bg-orange-500" />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-[#045024]">Recent Notifications</h3>
            <Bell className="text-[#2aa45c]" size={20} />
          </div>
          <div className="space-y-3">
            {notifications.map(notification => (
              <div key={notification.id} className="flex items-start gap-3 p-3 bg-[#dddeda] bg-opacity-30 rounded-lg">
                <div className="w-2 h-2 bg-[#2aa45c] rounded-full mt-2 flex-shrink-0"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-800">{notification.message}</p>
                  <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-[#045024] mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-3">
            <ActionButton onClick={() => setActiveTab('payment-types')}>
              <Plus size={16} />
              New Payment Type
            </ActionButton>
            <ActionButton onClick={() => setActiveTab('payer-profiles')}>
              <Users size={16} />
              Add Payer
            </ActionButton>
            <ActionButton onClick={() => setActiveTab('payment-profiles')}>
              <FileText size={16} />
              Create Profile
            </ActionButton>
            <ActionButton onClick={() => setActiveTab('tracking')}>
              <Search size={16} />
              Track Payments
            </ActionButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Overview;