-- Test Email Configuration Data
-- This script inserts sample email configurations for testing

-- First, check if the EmailConfigurations table exists
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'EmailConfigurations')
BEGIN
    PRINT 'EmailConfigurations table does not exist. Please run the NotificationProcedures.sql first.'
    RETURN
END

-- Clear existing test data (optional - comment out if you want to keep existing data)
-- DELETE FROM EmailConfigurations WHERE CreatedBy = 'TEST_SYSTEM'

-- Insert test email configurations
PRINT 'Inserting test email configurations...'

-- SYSTEM organization default configuration (for admin functions)
IF NOT EXISTS (SELECT 1 FROM EmailConfigurations WHERE OrganizationId = 'SYSTEM' AND IsDefault = 1)
BEGIN
    INSERT INTO EmailConfigurations (
        Id, 
        OrganizationId, 
        SmtpServer, 
        Port, 
        Username, 
        Password, 
        EnableSsl, 
        IsDefault, 
        SenderName, 
        Sender<PERSON>mail, 
        CreatedBy
    )
    VALUES (
        NEWID(),
        'SYSTEM',
        'smtp.gmail.com',
        587,
        '<EMAIL>',
        'test_password_123',
        1, -- EnableSsl = true
        1, -- IsDefault = true
        'Payment Management System',
        '<EMAIL>',
        'TEST_SYSTEM'
    )
    PRINT 'Inserted SYSTEM default email configuration'
END
ELSE
BEGIN
    PRINT 'SYSTEM default email configuration already exists'
END

-- Test organization configuration
IF NOT EXISTS (SELECT 1 FROM EmailConfigurations WHERE OrganizationId = 'TEST_ORG_001')
BEGIN
    INSERT INTO EmailConfigurations (
        Id, 
        OrganizationId, 
        SmtpServer, 
        Port, 
        Username, 
        Password, 
        EnableSsl, 
        IsDefault, 
        SenderName, 
        SenderEmail, 
        CreatedBy
    )
    VALUES (
        NEWID(),
        'TEST_ORG_001',
        'smtp.outlook.com',
        587,
        '<EMAIL>',
        'test_org_password',
        1, -- EnableSsl = true
        1, -- IsDefault = true
        'Test Organization',
        '<EMAIL>',
        'TEST_SYSTEM'
    )
    PRINT 'Inserted TEST_ORG_001 email configuration'
END
ELSE
BEGIN
    PRINT 'TEST_ORG_001 email configuration already exists'
END

-- Additional test configuration (non-default)
IF NOT EXISTS (SELECT 1 FROM EmailConfigurations WHERE OrganizationId = 'SYSTEM' AND SenderName = 'Backup Email System')
BEGIN
    INSERT INTO EmailConfigurations (
        Id, 
        OrganizationId, 
        SmtpServer, 
        Port, 
        Username, 
        Password, 
        EnableSsl, 
        IsDefault, 
        SenderName, 
        SenderEmail, 
        CreatedBy
    )
    VALUES (
        NEWID(),
        'SYSTEM',
        'smtp.sendgrid.net',
        587,
        '<EMAIL>',
        'backup_password_456',
        1, -- EnableSsl = true
        0, -- IsDefault = false (backup configuration)
        'Backup Email System',
        '<EMAIL>',
        'TEST_SYSTEM'
    )
    PRINT 'Inserted SYSTEM backup email configuration'
END
ELSE
BEGIN
    PRINT 'SYSTEM backup email configuration already exists'
END

-- Verify the inserted data
PRINT ''
PRINT 'Current email configurations:'
SELECT 
    Id,
    OrganizationId,
    SmtpServer,
    Port,
    Username,
    SenderName,
    SenderEmail,
    EnableSsl,
    IsDefault,
    CreatedAt,
    CreatedBy
FROM EmailConfigurations
ORDER BY OrganizationId, IsDefault DESC, SenderName

PRINT ''
PRINT 'Test data insertion completed!'
PRINT 'You can now test the email configuration API endpoints.'
