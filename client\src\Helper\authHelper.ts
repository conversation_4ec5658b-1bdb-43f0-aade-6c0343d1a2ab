// authHelper.ts - Utility functions for authentication
export const getStoredToken = (): string | null => {
  return localStorage.getItem("auth_token");
};

export const getStoredUser = (): any | null => {
  const userData = localStorage.getItem("user_data");
  if (userData) {
    try {
      return JSON.parse(userData);
    } catch (error) {
      console.error("Error parsing stored user data:", error);
      return null;
    }
  }
  return null;
};

export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split(".")[1]));
    const currentTime = Date.now() / 1000;
    return payload.exp < currentTime;
  } catch (error) {
    console.error("Error checking token expiration:", error);
    return true; // Assume expired if we can't parse it
  }
};

export const clearAuthData = (): void => {
  localStorage.removeItem("auth_token");
  localStorage.removeItem("user_data");
};

export const hasValidAuth = (): boolean => {
  const token = getStoredToken();
  if (!token) return false;

  // Check if token is expired (if it's a JWT)
  if (token.includes(".")) {
    return !isTokenExpired(token);
  }

  // For non-JWT tokens, assume valid if it exists
  return true;
};
