using System;
using System.Collections.Generic;
using System.Drawing;

namespace Final_E_Receipt.Documents.Models
{
    public class DocumentConfiguration
    {
        public string TemplatePath { get; set; }
        public Dictionary<string, PointF> TextPositions { get; set; } = new Dictionary<string, PointF>();
        public Func<object, Dictionary<string, string>> DataMapper { get; set; }
        
        // Document-specific formatting
        public string FontFamily { get; set; } = "Arial";
        public float FontSize { get; set; } = 12f;
        public FontStyle FontStyle { get; set; } = FontStyle.Regular;
        public Color TextColor { get; set; } = Color.Black;
        public float TextRotation { get; set; } = 0f; // For different orientations
        public System.Drawing.Text.TextRenderingHint TextRenderingHint { get; set; } = System.Drawing.Text.TextRenderingHint.AntiAlias;
        
        // PDF-specific settings
        public PdfSettings PdfSettings { get; set; } = new PdfSettings();
    }

    public class PdfSettings
    {
        public PageOrientation Orientation { get; set; } = PageOrientation.Portrait;
        public PageSize PageSize { get; set; } = PageSize.A4;
        public int DPI { get; set; } = 300;
        public int Quality { get; set; } = 95;
    }

    public enum PageOrientation
    {
        Portrait,
        Landscape
    }

    public enum PageSize
    {
        A4,
        Letter,
        Legal
    }
}
