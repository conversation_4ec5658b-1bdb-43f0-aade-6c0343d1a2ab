import { useState, useCallback } from 'react';
import { apiService } from '../../services/apiService';

// Types for Reporting API
export interface PaymentReport {
  organizationId: string;
  organizationName: string;
  totalPayments: number;
  totalAmount: number;
  pendingPayments: number;
  pendingAmount: number;
  completedPayments: number;
  completedAmount: number;
  period: string;
}

export interface ComplianceReport {
  organizationId: string;
  organizationName: string;
  totalCertificates: number;
  activeCertificates: number;
  expiredCertificates: number;
  expiringCertificates: number;
  complianceScore: number;
  lastAuditDate?: string;
}

export interface FinancialSummary {
  totalRevenue: number;
  totalPayments: number;
  averagePaymentAmount: number;
  paymentsByMonth: Array<{
    month: string;
    amount: number;
    count: number;
  }>;
  topOrganizations: Array<{
    organizationId: string;
    organizationName: string;
    totalAmount: number;
    paymentCount: number;
  }>;
}

export interface YearOverYearComparison {
  currentYear: number;
  previousYear: number;
  currentYearData: {
    totalAmount: number;
    totalPayments: number;
    averageAmount: number;
  };
  previousYearData: {
    totalAmount: number;
    totalPayments: number;
    averageAmount: number;
  };
  growth: {
    amountGrowth: number;
    paymentGrowth: number;
    averageGrowth: number;
  };
}

export interface ReportFilters {
  organizationId?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  paymentType?: string;
}

// Payment Reporting API Hooks
export const usePaymentReportingApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An error occurred');
      }
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Payment reporting endpoints
  const getPaymentSummary = useCallback((filters?: ReportFilters) => 
    handleApiCall(() => apiService.get<PaymentReport[]>('/Reporting/payment-summary', filters)), [handleApiCall]);

  const getPaymentsByOrganization = useCallback((organizationId: string, filters?: ReportFilters) => 
    handleApiCall(() => apiService.get<PaymentReport>(`/Reporting/payments/${organizationId}`, filters)), [handleApiCall]);

  const getFinancialSummary = useCallback((filters?: ReportFilters) => 
    handleApiCall(() => apiService.get<FinancialSummary>('/Reporting/financial-summary', filters)), [handleApiCall]);

  const getMonthlyReport = useCallback((year: number, month: number) => 
    handleApiCall(() => apiService.get(`/Reporting/monthly/${year}/${month}`)), [handleApiCall]);

  const getQuarterlyReport = useCallback((year: number, quarter: number) => 
    handleApiCall(() => apiService.get(`/Reporting/quarterly/${year}/${quarter}`)), [handleApiCall]);

  const getAnnualReport = useCallback((year: number) => 
    handleApiCall(() => apiService.get(`/Reporting/annual/${year}`)), [handleApiCall]);

  const getYearOverYearComparison = useCallback((organizationId: string, currentYear?: number, previousYear?: number) =>
    handleApiCall(() => apiService.get<YearOverYearComparison>(`/Reporting/year-over-year/${organizationId}`, { currentYear, previousYear })), [handleApiCall]);

  // Outstanding Balances Report
  const getOutstandingBalancesReport = useCallback((filters?: ReportFilters) =>
    handleApiCall(() => apiService.post('/reporting/outstanding-balances', { filter: filters })), [handleApiCall]);

  // Revoked Receipts Report
  const getRevokedReceiptsReport = useCallback((filters?: ReportFilters) =>
    handleApiCall(() => apiService.post('/reporting/revoked-receipts', { filter: filters })), [handleApiCall]);

  // Export functions
  const exportPaymentReport = useCallback((format: 'csv' | 'excel', filters?: ReportFilters) =>
    handleApiCall(() => apiService.post(`/reporting/payment-history/export`, { filter: filters, format: format.toUpperCase() })), [handleApiCall]);

  const exportOutstandingBalancesReport = useCallback((format: 'csv' | 'excel', filters?: ReportFilters) =>
    handleApiCall(() => apiService.post(`/reporting/outstanding-balances/export`, { filter: filters, format: format.toUpperCase() })), [handleApiCall]);

  const exportRevokedReceiptsReport = useCallback((format: 'csv' | 'excel', filters?: ReportFilters) =>
    handleApiCall(() => apiService.post(`/reporting/revoked-receipts/export`, { filter: filters, format: format.toUpperCase() })), [handleApiCall]);

  const exportFinancialSummary = useCallback((format: 'csv' | 'excel', filters?: ReportFilters) =>
    handleApiCall(() => apiService.get(`/Reporting/export/financial/${format}`, filters)), [handleApiCall]);

  return {
    loading,
    error,
    getPaymentSummary,
    getPaymentsByOrganization,
    getFinancialSummary,
    getMonthlyReport,
    getQuarterlyReport,
    getAnnualReport,
    getYearOverYearComparison,
    getOutstandingBalancesReport,
    getRevokedReceiptsReport,
    exportPaymentReport,
    exportOutstandingBalancesReport,
    exportRevokedReceiptsReport,
    exportFinancialSummary,
  };
};

// Compliance Reporting API Hooks
export const useComplianceReportingApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An error occurred');
      }
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Compliance reporting endpoints
  const getComplianceDashboard = useCallback(() => 
    handleApiCall(() => apiService.get('/ComplianceReporting/dashboard')), [handleApiCall]);

  const getOrganizationComplianceReport = useCallback((organizationId: string) => 
    handleApiCall(() => apiService.get<ComplianceReport>(`/ComplianceReporting/organization/${organizationId}`)), [handleApiCall]);

  const getComplianceMetrics = useCallback(() => {
    return handleApiCall(() => apiService.get('/ComplianceReporting/metrics'));
  }, [handleApiCall]);

  const getExpiringCertificatesReport = useCallback(() => {
    return handleApiCall(() => apiService.get('/ComplianceReporting/expiring-certificates'));
  }, [handleApiCall]);

  // Export functions
  const exportComplianceReport = useCallback((format: 'csv' | 'excel', organizationId?: string) =>
    handleApiCall(() => apiService.get(`/ComplianceReporting/export/${format}`, { organizationId })), [handleApiCall]);

  return {
    loading,
    error,
    getComplianceDashboard,
    getOrganizationComplianceReport,
    getComplianceMetrics,
    getExpiringCertificatesReport,
    exportComplianceReport,
  };
};
