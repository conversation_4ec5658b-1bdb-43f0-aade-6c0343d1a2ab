import React, { useState } from 'react';
import { Calendar } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import PaymentScheduleList from '../components/Payments/PaymentScheduleList';
import CreatePaymentSchedule from '../components/Payments/CreatePaymentSchedule';
import EditPaymentSchedule from '../components/Payments/EditPaymentSchedule';
import PaymentScheduleDetails from '../components/Payments/PaymentScheduleDetails';
import type { PaymentSchedule } from '../hooks/api';

const PaymentSchedulesPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<string>('payment-schedules');
  const [selectedPaymentSchedule, setSelectedPaymentSchedule] = useState<PaymentSchedule | null>(null);

  // Role-based permissions
  const canCreateSchedules = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');
  const canEditSchedules = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');

  const handlePaymentScheduleCreated = () => {
    // Refresh the payment schedule list when a new schedule is created
    setActiveTab('payment-schedules');
  };

  const handlePaymentScheduleUpdated = () => {
    // Refresh the payment schedule list when a schedule is updated
    setActiveTab('payment-schedules');
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'payment-schedules':
        return (
          <PaymentScheduleList
            setActiveTab={setActiveTab}
            setSelectedPaymentSchedule={setSelectedPaymentSchedule}
          />
        );
      
      case 'create-payment-schedule':
        return canCreateSchedules ? (
          <CreatePaymentSchedule
            setActiveTab={setActiveTab}
            onPaymentScheduleCreated={handlePaymentScheduleCreated}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">You don't have permission to create payment schedules</p>
          </div>
        );
      
      case 'edit-payment-schedule':
        return canEditSchedules && selectedPaymentSchedule ? (
          <EditPaymentSchedule
            paymentSchedule={selectedPaymentSchedule}
            setActiveTab={setActiveTab}
            setSelectedPaymentSchedule={setSelectedPaymentSchedule}
            onPaymentScheduleUpdated={handlePaymentScheduleUpdated}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {!canEditSchedules 
                ? "You don't have permission to edit payment schedules" 
                : "No payment schedule selected for editing"}
            </p>
          </div>
        );
      
      case 'payment-schedule-details':
        return selectedPaymentSchedule ? (
          <PaymentScheduleDetails
            paymentSchedule={selectedPaymentSchedule}
            setActiveTab={setActiveTab}
            setSelectedPaymentSchedule={setSelectedPaymentSchedule}
            onPaymentScheduleUpdated={handlePaymentScheduleUpdated}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">No payment schedule selected for viewing</p>
          </div>
        );
      
      default:
        return (
          <PaymentScheduleList
            setActiveTab={setActiveTab}
            setSelectedPaymentSchedule={setSelectedPaymentSchedule}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderContent()}
      </div>
    </div>
  );
};

export default PaymentSchedulesPage;
