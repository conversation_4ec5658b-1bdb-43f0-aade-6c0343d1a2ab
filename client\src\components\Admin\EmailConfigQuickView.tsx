import React, { useState, useEffect } from 'react';
import { useEmailConfigApi, type EmailConfiguration } from '../../hooks/api';
import { Server, Mail, Settings, AlertCircle, CheckCircle } from 'lucide-react';

interface EmailConfigQuickViewProps {
  organizationId?: string;
  showActions?: boolean;
}

const EmailConfigQuickView: React.FC<EmailConfigQuickViewProps> = ({ 
  organizationId,
  showActions = true 
}) => {
  const { loading, error, getDefaultEmailConfig } = useEmailConfigApi();
  const [config, setConfig] = useState<EmailConfiguration | null>(null);

  useEffect(() => {
    const loadConfig = async () => {
      if (organizationId) {
        const result = await getDefaultEmailConfig(organizationId);
        if (result) {
          setConfig(result);
        }
      }
    };

    loadConfig();
  }, [organizationId, getDefaultEmailConfig]);

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center space-x-3">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
          <span className="text-gray-600">Loading email configuration...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center space-x-3 text-red-600">
          <AlertCircle size={20} />
          <div>
            <h3 className="font-medium">Email Configuration Error</h3>
            <p className="text-sm text-red-500 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center space-x-3 text-yellow-600">
          <AlertCircle size={20} />
          <div>
            <h3 className="font-medium">No Email Configuration</h3>
            <p className="text-sm text-yellow-600 mt-1">
              No default email configuration found. Set up SMTP settings to enable email notifications.
            </p>
            {showActions && (
              <button className="mt-3 px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                Configure Email Settings
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Server className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Email Configuration</h3>
              <p className="text-sm text-gray-500">SMTP settings for email notifications</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <span className="text-sm font-medium text-green-600">Active</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                SMTP Server
              </label>
              <p className="text-sm font-medium text-gray-900 mt-1">
                {config.smtpServer}:{config.port}
              </p>
            </div>
            
            <div>
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                Security
              </label>
              <div className="flex items-center space-x-2 mt-1">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  config.enableSsl 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {config.enableSsl ? 'SSL Enabled' : 'SSL Disabled'}
                </span>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                Sender Information
              </label>
              <div className="mt-1">
                <p className="text-sm font-medium text-gray-900">{config.senderName}</p>
                <p className="text-sm text-gray-600">{config.senderEmail}</p>
              </div>
            </div>
            
            <div>
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                Username
              </label>
              <p className="text-sm text-gray-900 mt-1">
                {config.username}
              </p>
            </div>
          </div>
        </div>

        {showActions && (
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Mail size={16} />
              <span>Last updated: {new Date(config.updatedAt || config.createdAt).toLocaleDateString()}</span>
            </div>
            
            <div className="flex space-x-2">
              <button className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50">
                Test Connection
              </button>
              <button className="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-1">
                <Settings size={14} />
                <span>Configure</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailConfigQuickView;
