//import type { AuthContextType } from '../types/auth';

export type LoginOptions =
  | {
      authMethod: 'local';
      credentials: { email: string; password: string };
      // redirect never applies to local auth
      useRedirect?: false;
    }
  | {
      authMethod: 'microsoft';
      // no credentials supplied – handled by the Microsoft SDK
      credentials?: never;
      /**
       * Whether to trigger a full-page redirect instead of a pop-up.
       * Matches `useRedirect` flag in MSAL.
       */
      useRedirect?: boolean;
    };