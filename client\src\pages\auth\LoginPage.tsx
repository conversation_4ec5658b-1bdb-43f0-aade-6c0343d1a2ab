// import React, { useState, useEffect } from "react";
// import { useAuth } from "../../hooks/useAuth";
// import { useNavigate } from "react-router-dom";
// import EReceiptLogo from "../../assets/Ereceipt-logo.png";
// import CoatOfArmLogo from "../../assets/coat-of-arm-preview.png";
// import { authDetectionService } from "../../services/authDetectionService";

// type LoginStep = "email" | "password" | "microsoft" | "loading";

// const LoginPage: React.FC = () => {
//   const { user, isAuthenticated, login, isLoading } = useAuth();
//   const navigate = useNavigate();

//   const [step, setStep] = useState<LoginStep>("email");
//   const [email, setEmail] = useState("");
//   const [password, setPassword] = useState("");
//   const [authMethod, setAuthMethod] = useState<"microsoft" | "local" | null>(
//     null
//   );
//   const [error, setError] = useState<string | null>(null);

//   useEffect(() => {
//     if (isAuthenticated && user) {
//       switch (user.role) {
//         case "JTB_ADMIN":
//           navigate("/admin-dashboard");
//           break;
//         case "SENIOR_FINANCE_OFFICER":
//           navigate("/senior-finance-officer-dashboard");
//           break;
//         case "FINANCE_OFFICER":
//           navigate("/finance-officer-dashboard");
//           break;
//         case "PAYER":
//           navigate("/payer-dashboard");
//           break;
//         default:
//           navigate("/payer-dashboard");
//       }
//     }
//   }, [isAuthenticated, user, navigate]);

//   const handleEmailSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();

//     if (!email.trim()) {
//       setError("Please enter your email address");
//       return;
//     }

//     setError(null);
//     setStep("loading");

//     try {
//       const result = await authDetectionService.detectAuthMethod(email);

//       setAuthMethod(result.authMethod); // ✅ Fix applied here
//       localStorage.setItem("auth_type", result.authMethod || "unknown");

//       if (result.authMethod === "microsoft") {
//         setStep("microsoft");
//         // Optionally auto-trigger Microsoft login here
//         // await handleMicrosoftLogin();
//       } else {
//         setStep("password");
//       }
//     } catch (err) {
//       console.error("Auth detection failed", err);
//       setError("Unable to detect authentication method. Try again.");
//       setStep("email");
//     }
//   };

//   const handlePasswordSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();

//     if (!password) {
//       setError("Please enter your password");
//       return;
//     }

//     try {
//       setError(null);
//       await login({
//         authMethod: "local",
//         credentials: { email, password },
//       });
//     } catch (err) {
//       console.error("Login failed:", err);
//       setError("Incorrect email or password");
//     }
//   };

//   const handleMicrosoftLogin = async () => {
//     try {
//       setError(null);
//       await login({ authMethod: "microsoft" });
//     } catch (err) {
//       console.error("Microsoft login failed:", err);
//       setError("Microsoft login failed. Try again.");
//     }
//   };

//   const handleBackToEmail = () => {
//     setStep("email");
//     setPassword("");
//     setAuthMethod(null);
//     setError(null);
//   };

//   if (isLoading && step !== "email") {
//     return (
//       <div className="min-h-screen flex items-center justify-center">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
//           <p className="mt-4 text-gray-600">Signing you in...</p>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div
//       className="min-h-screen flex items-center justify-center px-4 py-12"
//       style={{
//         background:
//           "linear-gradient(135deg, #045024 0%, #076934 50%, #2aa45c 100%)",
//       }}
//     >
//       <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-xl">
//         <div className="text-center">
//           <h2 className="text-3xl font-bold text-gray-900">Sign In</h2>
//           <p className="mt-2 text-gray-600">
//             {step === "email" && "Enter your email to continue"}
//             {step === "password" && "Enter your password"}
//             {step === "microsoft" && "Redirecting to Microsoft..."}
//           </p>
//         </div>

//         {error && (
//           <div className="bg-red-50 border border-red-200 rounded-md p-4">
//             <p className="text-red-800 text-sm">{error}</p>
//           </div>
//         )}

//         {/* Email input step */}
//         {step === "email" && (
//           <form onSubmit={handleEmailSubmit} className="space-y-6">
//             <div>
//               <label
//                 htmlFor="email"
//                 className="block text-sm font-medium text-gray-700"
//               >
//                 Email Address
//               </label>
//               <input
//                 id="email"
//                 type="email"
//                 value={email}
//                 onChange={(e) => setEmail(e.target.value)}
//                 required
//                 className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
//                 placeholder="<EMAIL>"
//               />
//             </div>
//             <button
//               type="submit"
//               className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
//             >
//               Continue
//             </button>
//           </form>
//         )}

//         {/* Password step */}
//         {step === "password" && (
//           <div className="space-y-6">
//             <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
//               <strong>Email:</strong> {email}
//               <button
//                 onClick={handleBackToEmail}
//                 className="ml-2 text-blue-600 hover:text-blue-800"
//               >
//                 Change
//               </button>
//             </div>

//             <form onSubmit={handlePasswordSubmit} className="space-y-4">
//               <div>
//                 <label
//                   htmlFor="password"
//                   className="block text-sm font-medium text-gray-700"
//                 >
//                   Password
//                 </label>
//                 <input
//                   id="password"
//                   type="password"
//                   value={password}
//                   onChange={(e) => setPassword(e.target.value)}
//                   required
//                   className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
//                   placeholder="Enter your password"
//                 />
//               </div>
//               <button
//                 type="submit"
//                 className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700"
//               >
//                 Sign In
//               </button>
//             </form>
//           </div>
//         )}

//         {/* Microsoft auth step */}
//         {step === "microsoft" && (
//           <div className="space-y-6 text-center">
//             <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
//               <strong>Email:</strong> {email}
//               <button
//                 onClick={handleBackToEmail}
//                 className="ml-2 text-blue-600 hover:text-blue-800"
//               >
//                 Change
//               </button>
//             </div>

//             <p className="text-gray-600">
//               This email uses Microsoft authentication. <br />
//               You’ll be redirected to sign in with your Microsoft account.
//             </p>
//             <button
//               onClick={handleMicrosoftLogin}
//               className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
//             >
//               Continue to Microsoft
//             </button>
//           </div>
//         )}

//         {/* Footer */}
//         <div className="text-center">
//           <p className="text-xs text-gray-500">
//             {authMethod === "microsoft"
//               ? "For JTB staff and administrators"
//               : authMethod === "local"
//               ? "For taxpayers and external users"
//               : "Enter your email to get started"}
//           </p>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default LoginPage;

import React, { useState, useEffect } from "react";
import { useAuth } from "../../hooks/useAuth";
import { useNavigate } from "react-router-dom";
import EReceiptLogo from "../../assets/Ereceipt-logo.png";
import CoatOfArmLogo from "../../assets/coat-of-arm-preview.png";
import { authDetectionService } from "../../services/authDetectionService";

type LoginStep = "email" | "password" | "microsoft" | "loading";

const LoginPage: React.FC = () => {
  const { user, isAuthenticated, login, isLoading } = useAuth();
  const navigate = useNavigate();

  const [step, setStep] = useState<LoginStep>("email");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [authMethod, setAuthMethod] = useState<"microsoft" | "local" | null>(
    null
  );
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isAuthenticated && user) {
      switch (user.role) {
        case "JTB_ADMIN":
          navigate("/admin-dashboard");
          break;
        case "SENIOR_FINANCE_OFFICER":
          navigate("/senior-finance-officer-dashboard");
          break;
        case "FINANCE_OFFICER":
          navigate("/finance-officer-dashboard");
          break;
        case "PAYER":
          navigate("/payer-dashboard");
          break;
        default:
          navigate("/payer-dashboard");
      }
    }
  }, [isAuthenticated, user, navigate]);

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      setError("Please enter your email address");
      return;
    }

    setError(null);
    setStep("loading");

    try {
      const result = await authDetectionService.detectAuthMethod(email);

      setAuthMethod(result.authMethod);
      localStorage.setItem("auth_type", result.authMethod || "unknown");

      if (result.authMethod === "microsoft") {
        setStep("microsoft");
      } else {
        setStep("password");
      }
    } catch (err) {
      console.error("Auth detection failed", err);
      setError("Unable to detect authentication method. Try again.");
      setStep("email");
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!password) {
      setError("Please enter your password");
      return;
    }

    try {
      setError(null);
      await login({
        authMethod: "local",
        credentials: { email, password },
      });
    } catch (err) {
      console.error("Login failed:", err);
      setError("Incorrect email or password");
    }
  };

  const handleMicrosoftLogin = async () => {
    try {
      setError(null);
      await login({ authMethod: "microsoft" });
    } catch (err) {
      console.error("Microsoft login failed:", err);
      setError("Microsoft login failed. Try again.");
    }
  };

  const handleBackToEmail = () => {
    setStep("email");
    setPassword("");
    setAuthMethod(null);
    setError(null);
  };

  if (isLoading && step !== "email") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-emerald-800 via-emerald-700 to-emerald-600">
        <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-white/30 border-t-white mx-auto"></div>
          <p className="mt-6 text-white text-lg font-medium">
            Signing you in...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-12 bg-gradient-to-br from-emerald-800 via-emerald-700 to-emerald-600 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/3 rounded-full blur-3xl"></div>

        {/* Subtle grid pattern */}
        <div className="absolute inset-0 bg-grid-white/5 bg-[size:30px_30px] opacity-20"></div>
      </div>

      <div className="relative z-10 max-w-md w-full space-y-8">
        {/* Logo section */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-6 mb-6">
            <div className="flex-shrink-0">
              <img
                src={CoatOfArmLogo}
                alt="Coat of Arms"
                className="h-16 w-auto object-contain drop-shadow-lg"
              />
            </div>
            <div className="h-12 w-px bg-white/30"></div>
            <div className="flex-shrink-0">
              <img
                src={EReceiptLogo}
                alt="E-Receipt Logo"
                className="h-16 w-auto object-contain drop-shadow-lg"
              />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">
            Joint Tax Board
          </h1>
          <p className="text-emerald-100 text-sm">
            Electronic Receipt Management System
          </p>
        </div>

        {/* Main card */}
        <div className="bg-white/95 backdrop-blur-sm p-8 rounded-2xl shadow-2xl border border-white/20">
          <div className="text-center mb-6">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Welcome Back
            </h2>
            <p className="text-gray-600 text-sm">
              {step === "email" && "Enter your email address to continue"}
              {step === "password" && "Enter your password to sign in"}
              {step === "microsoft" &&
                "Redirecting to Microsoft authentication..."}
              {step === "loading" && "Processing your request..."}
            </p>
          </div>

          {/* Progress indicator */}
          <div className="flex items-center justify-center space-x-2 mb-6">
            <div
              className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                step === "email" ? "bg-emerald-600" : "bg-gray-300"
              }`}
            ></div>
            <div
              className={`w-8 h-0.5 transition-colors duration-300 ${
                step !== "email" ? "bg-emerald-600" : "bg-gray-300"
              }`}
            ></div>
            <div
              className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                step === "password" || step === "microsoft"
                  ? "bg-emerald-600"
                  : "bg-gray-300"
              }`}
            ></div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-red-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <p className="ml-3 text-red-800 text-sm font-medium">{error}</p>
              </div>
            </div>
          )}

          {/* Email input step */}
          {step === "email" && (
            <form onSubmit={handleEmailSubmit} className="space-y-6">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-semibold text-gray-700 mb-2"
                >
                  Email Address
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200"
                  placeholder="Enter your email address"
                />
              </div>
              <button
                type="submit"
                className="w-full bg-gradient-to-r from-emerald-600 to-emerald-700 text-white py-3 px-4 rounded-xl font-semibold hover:from-emerald-700 hover:to-emerald-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Continue
              </button>
            </form>
          )}

          {/* Password step */}
          {step === "password" && (
            <div className="space-y-6">
              <div className="text-sm text-gray-700 bg-gray-50 p-4 rounded-xl border border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <svg
                      className="h-4 w-4 text-gray-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                      />
                    </svg>
                    <span className="font-medium">{email}</span>
                  </div>
                  <button
                    onClick={handleBackToEmail}
                    className="text-emerald-600 hover:text-emerald-800 font-medium text-sm"
                  >
                    Change
                  </button>
                </div>
              </div>

              <form onSubmit={handlePasswordSubmit} className="space-y-6">
                <div>
                  <label
                    htmlFor="password"
                    className="block text-sm font-semibold text-gray-700 mb-2"
                  >
                    Password
                  </label>
                  <input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200"
                    placeholder="Enter your password"
                  />
                </div>
                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-emerald-600 to-emerald-700 text-white py-3 px-4 rounded-xl font-semibold hover:from-emerald-700 hover:to-emerald-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  Sign In
                </button>
              </form>
            </div>
          )}

          {/* Microsoft auth step */}
          {step === "microsoft" && (
            <div className="space-y-6 text-center">
              <div className="text-sm text-gray-700 bg-blue-50 p-4 rounded-xl border border-blue-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <svg
                      className="h-4 w-4 text-blue-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                      />
                    </svg>
                    <span className="font-medium">{email}</span>
                  </div>
                  <button
                    onClick={handleBackToEmail}
                    className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                  >
                    Change
                  </button>
                </div>
              </div>

              <div className="bg-blue-50 p-6 rounded-xl">
                <div className="flex items-center justify-center mb-4">
                  <svg
                    className="h-12 w-12 text-blue-600"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zm12.6 0H12.6V0H24v11.4z" />
                  </svg>
                </div>
                <p className="text-gray-700 mb-4">
                  This account uses <strong>Microsoft authentication</strong>.{" "}
                  <br />
                  You'll be redirected to sign in with your Microsoft account.
                </p>
              </div>

              <button
                onClick={handleMicrosoftLogin}
                className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 px-4 rounded-xl font-semibold hover:from-blue-700 hover:to-blue-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center space-x-2"
              >
                <svg
                  className="h-5 w-5"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zm12.6 0H12.6V0H24v11.4z" />
                </svg>
                <span>Continue with Microsoft</span>
              </button>
            </div>
          )}

          {/* Footer */}
          <div className="text-center mt-8 pt-6 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              {authMethod === "microsoft"
                ? "🏢 For JTB staff and administrators"
                : authMethod === "local"
                ? "👤 For taxpayers and external users"
                : "🔐 Secure authentication system"}
            </p>
          </div>
        </div>

        {/* Bottom text */}
        <div className="text-center">
          <p className="text-emerald-100 text-xs">
            © 2024 Joint Tax Board. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
