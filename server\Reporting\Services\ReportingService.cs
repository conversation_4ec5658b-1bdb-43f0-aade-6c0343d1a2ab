using System.Data;
using Final_E_Receipt.Reporting.Models;
using Final_E_Receipt.Services;
using Final_E_Receipt.Payments.Models;
using Final_E_Receipt.Receipts.Models;
using Microsoft.Extensions.Logging;
using Dapper;

namespace Final_E_Receipt.Reporting.Services
{
    public class ReportingService
    {
        private readonly IDatabaseService _dbService;
        private readonly ILogger<ReportingService> _logger;

        public ReportingService(IDatabaseService dbService, ILogger<ReportingService> logger)
        {
            _dbService = dbService;
            _logger = logger;
        }

        public async Task<DashboardSummary> GetDashboardSummary(string organizationId)
        {
            var parameters = new { OrganizationId = organizationId };
            var result = await _dbService.QueryFirstOrDefaultAsync<DashboardSummary>("GetDashboardSummary", parameters);
            return result ?? new DashboardSummary();
        }

        public async Task<List<MonthlyRevenue>> GetMonthlyRevenue(string organizationId, int year)
        {
            var parameters = new { OrganizationId = organizationId, Year = year };
            var result = await _dbService.QueryAsync<MonthlyRevenue>("GetMonthlyRevenue", parameters);
            return result.ToList();
        }

        public async Task<List<PaymentMethodSummary>> GetPaymentMethodSummary(string organizationId)
        {
            var parameters = new { OrganizationId = organizationId };
            var result = await _dbService.QueryAsync<PaymentMethodSummary>("GetPaymentMethodSummary", parameters);
            return result.ToList();
        }

        public async Task<List<CategorySummary>> GetCategorySummary(string organizationId)
        {
            var parameters = new { OrganizationId = organizationId };
            var result = await _dbService.QueryAsync<CategorySummary>("GetCategorySummary", parameters);
            return result.ToList();
        }

        public async Task<List<DailyRevenue>> GetDailyRevenue(string organizationId, DateTime startDate, DateTime endDate)
        {
            var parameters = new 
            { 
                OrganizationId = organizationId, 
                StartDate = startDate.Date, 
                EndDate = endDate.Date 
            };
            var result = await _dbService.QueryAsync<DailyRevenue>("GetDailyRevenue", parameters);
            return result.ToList();
        }

        public async Task<List<TopPayer>> GetTopPayers(string organizationId, int limit = 10)
        {
            var parameters = new { OrganizationId = organizationId, Limit = limit };
            var result = await _dbService.QueryAsync<TopPayer>("GetTopPayers", parameters);
            return result.ToList();
        }

        public async Task<List<YearOverYearComparison>> GetYearOverYearComparison(string organizationId, int currentYear, int previousYear)
        {
            var parameters = new 
            { 
                OrganizationId = organizationId, 
                CurrentYear = currentYear, 
                PreviousYear = previousYear 
            };
            var result = await _dbService.QueryAsync<YearOverYearComparison>("GetYearOverYearComparison", parameters);
            return result.ToList();
        }

        // ===== ENHANCED REPORTING METHODS =====

        /// <summary>
        /// Gets payment history by organization with filtering and sorting
        /// </summary>
        public async Task<PaymentHistoryReport> GetPaymentHistoryByOrganization(PaymentHistoryFilter filter)
        {
            try
            {
                var parameters = new
                {
                    filter.OrganizationId,
                    filter.StartDate,
                    filter.EndDate,
                    filter.PaymentStatus,
                    filter.PaymentMethod,
                    filter.MinAmount,
                    filter.MaxAmount,
                    filter.PageNumber,
                    filter.PageSize,
                    filter.SortBy,
                    filter.SortDirection
                };

                var payments = await _dbService.QueryAsync<PaymentHistoryItem>("GetPaymentHistoryByOrganization", parameters);

                var summaryParams = new
                {
                    filter.OrganizationId,
                    filter.StartDate,
                    filter.EndDate,
                    filter.PaymentStatus
                };

                var summary = await _dbService.QueryFirstOrDefaultAsync<PaymentSummary>("GetPaymentHistorySummary", summaryParams) 
                    ?? new PaymentSummary();

                return new PaymentHistoryReport
                {
                    OrganizationId = filter.OrganizationId,
                    Filter = filter,
                    Payments = payments.ToList(),
                    Summary = summary,
                    GeneratedAt = DateTime.Now,
                    TotalRecords = payments.Count()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating payment history report for organization {OrganizationId}", filter.OrganizationId);
                throw;
            }
        }

        /// <summary>
        /// Gets outstanding balances report with aging analysis
        /// </summary>
        public async Task<OutstandingBalancesReport> GetOutstandingBalancesReport(OutstandingBalancesFilter filter)
        {
            try
            {
                var parameters = new
                {
                    filter.OrganizationId,
                    filter.IncludeOverdue,
                    filter.MinAmount,
                    filter.AgingDays,
                    filter.SortBy,
                    filter.SortDirection
                };

                var balances = await _dbService.QueryAsync<OutstandingBalanceItem>("GetOutstandingBalances", parameters);
                var agingAnalysis = await _dbService.QueryAsync<AgingBucket>("GetOutstandingBalancesAging", parameters);

                return new OutstandingBalancesReport
                {
                    Filter = filter,
                    OutstandingBalances = balances.ToList(),
                    AgingAnalysis = agingAnalysis.ToList(),
                    TotalOutstanding = balances.Sum(b => b.OutstandingAmount),
                    TotalOverdue = balances.Where(b => b.IsOverdue).Sum(b => b.OutstandingAmount),
                    GeneratedAt = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating outstanding balances report");
                throw;
            }
        }

        /// <summary>
        /// Gets revoked receipts report with reasons and audit trail
        /// </summary>
        public async Task<RevokedReceiptsReport> GetRevokedReceiptsReport(RevokedReceiptsFilter filter)
        {
            try
            {
                var parameters = new
                {
                    filter.OrganizationId,
                    filter.StartDate,
                    filter.EndDate,
                    filter.RevokedBy,
                    filter.ReasonCategory,
                    filter.PageNumber,
                    filter.PageSize,
                    filter.SortBy,
                    filter.SortDirection
                };

                var revokedReceipts = await _dbService.QueryAsync<RevokedReceiptItem>("GetRevokedReceipts", parameters);

                var reasonSummaryResult = await _dbService.QueryAsync<dynamic>("GetRevokedReceiptsSummaryByReason", parameters);
                var reasonSummary = reasonSummaryResult.ToDictionary(
                    row => (string)row.RevokedReason,
                    row => (int)row.Count
                );

                return new RevokedReceiptsReport
                {
                    Filter = filter,
                    RevokedReceipts = revokedReceipts.ToList(),
                    ReasonSummary = reasonSummary,
                    TotalRevoked = revokedReceipts.Count(),
                    TotalAmount = revokedReceipts.Sum(r => r.Amount),
                    GeneratedAt = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating revoked receipts report");
                throw;
            }
        }
    }
}

