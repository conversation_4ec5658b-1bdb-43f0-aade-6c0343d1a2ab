using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Final_E_Receipt.Authentication.DTOs;
using Final_E_Receipt.Authentication.Models;
using Final_E_Receipt.Authentication.Services;
using System.Security.Claims;

namespace Final_E_Receipt.Authentication.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserController : ControllerBase
    {
        private readonly AuthenticationService _authService;
        private readonly ILogger<UserController> _logger;

        public UserController(AuthenticationService authService, ILogger<UserController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// Get all users in the system
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> GetAllUsers()
        {
            try
            {
                var users = await _authService.GetAllUsers();
                
                var userDtos = users.Select(u => new UserDTO
                {
                    Id = u.Id,
                    FirstName = u.FirstName,
                    LastName = u.LastName,
                    Email = u.Email,
                    Role = u.Role,
                    OrganizationId = u.OrganizationId,
                    IsActive = u.IsActive,
                    CreatedAt = u.CreatedAt,
                    LastLogin = u.LastLogin
                }).ToList();

                return Ok(userDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all users");
                return StatusCode(500, new { message = "Failed to get users" });
            }
        }

        /// <summary>
        /// Get user by ID
        /// </summary>
        [HttpGet("{id}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetUserById(string id)
        {
            try
            {
                var user = await _authService.GetUserById(id);
                
                if (user == null)
                    return NotFound(new { message = "User not found" });

                var userDto = new UserDTO
                {
                    Id = user.Id,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Email = user.Email,
                    Role = user.Role,
                    OrganizationId = user.OrganizationId,
                    IsActive = user.IsActive,
                    CreatedAt = user.CreatedAt,
                    LastLogin = user.LastLogin
                };

                return Ok(userDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user {UserId}", id);
                return StatusCode(500, new { message = "Failed to get user" });
            }
        }

        /// <summary>
        /// Update user details
        /// </summary>
        [HttpPut("{id}")]
        [Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> UpdateUser(string id, [FromBody] UpdateUserDTO dto)
        {
            try
            {
                var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                
                var existingUser = await _authService.GetUserById(id);
                if (existingUser == null)
                    return NotFound(new { message = "User not found" });

                // Update user properties
                existingUser.FirstName = dto.FirstName ?? existingUser.FirstName;
                existingUser.LastName = dto.LastName ?? existingUser.LastName;
                existingUser.Role = dto.Role ?? existingUser.Role;
                existingUser.OrganizationId = dto.OrganizationId ?? existingUser.OrganizationId;
                existingUser.IsActive = dto.IsActive ?? existingUser.IsActive;

                var updatedUser = await _authService.UpdateUser(existingUser, currentUserId);
                
                if (updatedUser == null)
                    return BadRequest(new { message = "Failed to update user" });

                var userDto = new UserDTO
                {
                    Id = updatedUser.Id,
                    FirstName = updatedUser.FirstName,
                    LastName = updatedUser.LastName,
                    Email = updatedUser.Email,
                    Role = updatedUser.Role,
                    OrganizationId = updatedUser.OrganizationId,
                    IsActive = updatedUser.IsActive,
                    CreatedAt = updatedUser.CreatedAt,
                    LastLogin = updatedUser.LastLogin
                };

                _logger.LogInformation("User {UserId} updated by {UpdatedBy}", id, currentUserId);
                return Ok(userDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId}", id);
                return StatusCode(500, new { message = "Failed to update user" });
            }
        }

        /// <summary>
        /// Partially update user details
        /// </summary>
        [HttpPatch("{id}")]
        [Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> PatchUser(string id, [FromBody] UpdateUserDTO dto)
        {
            // Use the same logic as PUT for simplicity
            return await UpdateUser(id, dto);
        }

        /// <summary>
        /// Deactivate/Activate user
        /// </summary>
        [HttpPatch("{id}/status")]
        [Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> UpdateUserStatus(string id, [FromBody] UpdateUserStatusDTO dto)
        {
            try
            {
                var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                
                var existingUser = await _authService.GetUserById(id);
                if (existingUser == null)
                    return NotFound(new { message = "User not found" });

                existingUser.IsActive = dto.IsActive;

                var updatedUser = await _authService.UpdateUser(existingUser, currentUserId);
                
                if (updatedUser == null)
                    return BadRequest(new { message = "Failed to update user status" });

                _logger.LogInformation("User {UserId} status changed to {Status} by {UpdatedBy}", 
                    id, dto.IsActive ? "Active" : "Inactive", currentUserId);
                
                return Ok(new { message = $"User {(dto.IsActive ? "activated" : "deactivated")} successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user status {UserId}", id);
                return StatusCode(500, new { message = "Failed to update user status" });
            }
        }

        /// <summary>
        /// Get users by organization
        /// </summary>
        [HttpGet("organization/{organizationId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetUsersByOrganization(string organizationId)
        {
            try
            {
                var users = await _authService.GetUsersByOrganization(organizationId);
                
                var userDtos = users.Select(u => new UserDTO
                {
                    Id = u.Id,
                    FirstName = u.FirstName,
                    LastName = u.LastName,
                    Email = u.Email,
                    Role = u.Role,
                    OrganizationId = u.OrganizationId,
                    IsActive = u.IsActive,
                    CreatedAt = u.CreatedAt,
                    LastLogin = u.LastLogin
                }).ToList();

                return Ok(userDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users for organization {OrganizationId}", organizationId);
                return StatusCode(500, new { message = "Failed to get users" });
            }
        }
    }
}
