using System;
using System.Collections.Generic;

namespace Final_E_Receipt.Reporting.Models
{
    // ===== PAYMENT HISTORY REPORT MODELS =====
    
    public class PaymentHistoryFilter
    {
        public string OrganizationId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string PaymentStatus { get; set; } // Completed, Pending, Failed
        public string PaymentMethod { get; set; } // Card, Bank Transfer, etc.
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public string PayerName { get; set; }
        public string PayerEmail { get; set; }
        public string Category { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public string SortBy { get; set; } = "CreatedAt";
        public string SortDirection { get; set; } = "DESC";
    }

    public class PaymentHistoryReport
    {
        public string OrganizationId { get; set; }
        public PaymentHistoryFilter Filter { get; set; }
        public List<PaymentHistoryItem> Payments { get; set; } = new List<PaymentHistoryItem>();
        public PaymentSummary Summary { get; set; }
        public DateTime GeneratedAt { get; set; }
        public int TotalRecords { get; set; }
    }

    public class PaymentHistoryItem
    {
        public string PaymentId { get; set; }
        public string TransactionReference { get; set; }
        public string PayerName { get; set; }
        public string PayerEmail { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; }
        public string PaymentMethod { get; set; }
        public string Status { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string ReceiptId { get; set; }
    }

    public class PaymentSummary
    {
        public int TotalPayments { get; set; }
        public decimal TotalAmount { get; set; }
        public int CompletedPayments { get; set; }
        public decimal CompletedAmount { get; set; }
        public int PendingPayments { get; set; }
        public decimal PendingAmount { get; set; }
        public int FailedPayments { get; set; }
        public decimal FailedAmount { get; set; }
        public double CompletionRate => TotalPayments > 0 ? (double)CompletedPayments / TotalPayments * 100 : 0;
        public double SuccessRate => TotalPayments > 0 ? (double)CompletedPayments / (CompletedPayments + FailedPayments) * 100 : 0;
    }

    // ===== OUTSTANDING BALANCES REPORT MODELS =====

    public class OutstandingBalancesFilter
    {
        public string OrganizationId { get; set; }
        public bool IncludeOverdue { get; set; } = true;
        public decimal? MinAmount { get; set; }
        public int AgingDays { get; set; } = 30;
        public string PaymentProfileId { get; set; }
        public string SortBy { get; set; } = "DaysOverdue";
        public string SortDirection { get; set; } = "DESC";
    }

    public class OutstandingBalancesReport
    {
        public OutstandingBalancesFilter Filter { get; set; }
        public List<OutstandingBalanceItem> OutstandingBalances { get; set; } = new List<OutstandingBalanceItem>();
        public List<AgingBucket> AgingAnalysis { get; set; } = new List<AgingBucket>();
        public decimal TotalOutstanding { get; set; }
        public decimal TotalOverdue { get; set; }
        public DateTime GeneratedAt { get; set; }
        public int TotalOrganizations => OutstandingBalances.Select(b => b.OrganizationId).Distinct().Count();
        public double OverduePercentage => TotalOutstanding > 0 ? (double)(TotalOverdue / TotalOutstanding) * 100 : 0;
    }

    public class OutstandingBalanceItem
    {
        public string OrganizationId { get; set; }
        public string OrganizationName { get; set; }
        public string PaymentScheduleId { get; set; }
        public string Description { get; set; }
        public DateTime DueDate { get; set; }
        public decimal OriginalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public int DaysOverdue { get; set; }
        public bool IsOverdue { get; set; }
        public string PaymentProfileId { get; set; }
        public string PaymentProfileName { get; set; }
        public string UrgencyLevel => DaysOverdue switch
        {
            > 90 => "CRITICAL",
            > 60 => "HIGH",
            > 30 => "MEDIUM",
            > 0 => "LOW",
            _ => "CURRENT"
        };
    }

    public class AgingBucket
    {
        public string AgeRange { get; set; } // "0-30 days", "31-60 days", etc.
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
        public double Percentage { get; set; }
    }

    // ===== REVOKED RECEIPTS REPORT MODELS =====

    public class RevokedReceiptsFilter
    {
        public string OrganizationId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string RevokedBy { get; set; }
        public string ReasonCategory { get; set; } // Error, Fraud, Duplicate, etc.
        public string PayerName { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public string SortBy { get; set; } = "RevokedDate";
        public string SortDirection { get; set; } = "DESC";
    }

    public class RevokedReceiptsReport
    {
        public RevokedReceiptsFilter Filter { get; set; }
        public List<RevokedReceiptItem> RevokedReceipts { get; set; } = new List<RevokedReceiptItem>();
        public Dictionary<string, int> ReasonSummary { get; set; } = new Dictionary<string, int>();
        public int TotalRevoked { get; set; }
        public decimal TotalAmount { get; set; }
        public DateTime GeneratedAt { get; set; }
        public int UniqueOrganizations => RevokedReceipts.Select(r => r.PayerEmail).Distinct().Count();
    }

    public class RevokedReceiptItem
    {
        public string ReceiptId { get; set; }
        public string ReceiptNumber { get; set; }
        public string PayerName { get; set; }
        public string PayerEmail { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; }
        public string PaymentMethod { get; set; }
        public DateTime OriginalDate { get; set; }
        public DateTime RevokedDate { get; set; }
        public string RevokedBy { get; set; }
        public string RevokedReason { get; set; }
        public string ReasonCategory { get; set; }
        public string PaymentId { get; set; }
        public int DaysBetweenIssueAndRevocation => (RevokedDate - OriginalDate).Days;
    }

    // ===== EXPORT MODELS =====

    public class ReportExportRequest
    {
        public string ReportType { get; set; } // PaymentHistory, OutstandingBalances, RevokedReceipts, ComplianceStatus
        public string Format { get; set; } // CSV, Excel, PDF
        public string OrganizationId { get; set; }
        public Dictionary<string, object> Filters { get; set; } = new Dictionary<string, object>();
        public bool IncludeCharts { get; set; } = false;
        public string FileName { get; set; }
    }

    public class ReportExportResult
    {
        public bool Success { get; set; }
        public string FileName { get; set; }
        public string ContentType { get; set; }
        public byte[] Content { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime GeneratedAt { get; set; }
        public long FileSizeBytes { get; set; }
    }

    // ===== COMPLIANCE STATUS REPORT MODELS =====

    public class ComplianceStatusFilter
    {
        public string OrganizationId { get; set; }
        public string ComplianceLevel { get; set; } // EXCELLENT, GOOD, SATISFACTORY, NEEDS_IMPROVEMENT, NON_COMPLIANT
        public bool? IsCompliant { get; set; }
        public int? MinComplianceScore { get; set; }
        public int? MaxComplianceScore { get; set; }
        public DateTime? LastCertificateFrom { get; set; }
        public DateTime? LastCertificateTo { get; set; }
        public bool IncludeExpiring { get; set; } = true;
        public int ExpiringWithinDays { get; set; } = 30;
        public string SortBy { get; set; } = "ComplianceScore";
        public string SortDirection { get; set; } = "DESC";
    }

    public class ComplianceStatusReport
    {
        public ComplianceStatusFilter Filter { get; set; }
        public List<OrganizationComplianceItem> Organizations { get; set; } = new List<OrganizationComplianceItem>();
        public ComplianceOverallSummary Summary { get; set; }
        public DateTime GeneratedAt { get; set; }
        public int TotalOrganizations { get; set; }
    }

    public class OrganizationComplianceItem
    {
        public string OrganizationId { get; set; }
        public string OrganizationName { get; set; }
        public bool IsCompliant { get; set; }
        public double ComplianceScore { get; set; }
        public string ComplianceLevel { get; set; }
        public int ActiveCertificates { get; set; }
        public int ExpiredCertificates { get; set; }
        public int ExpiringCertificates { get; set; }
        public DateTime? LastCertificateIssued { get; set; }
        public DateTime? NextExpiryDate { get; set; }
        public List<string> RequiredActions { get; set; } = new List<string>();
    }

    public class ComplianceOverallSummary
    {
        public int TotalOrganizations { get; set; }
        public int CompliantOrganizations { get; set; }
        public int NonCompliantOrganizations { get; set; }
        public double OverallComplianceRate { get; set; }
        public int OrganizationsWithExpiringCertificates { get; set; }
        public int CriticalComplianceIssues { get; set; }
        public Dictionary<string, int> ComplianceLevelDistribution { get; set; } = new Dictionary<string, int>();
    }

    // ===== COMMON FILTER BASE =====

    public abstract class BaseReportFilter
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public string SortBy { get; set; }
        public string SortDirection { get; set; } = "DESC";
    }
}
