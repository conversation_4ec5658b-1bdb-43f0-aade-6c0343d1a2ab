import React, { useState } from 'react';
import { CreditCard } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import PaymentList from '../components/Payments/PaymentList';
import CreatePayment from '../components/Payments/CreatePayment';
import PaymentDetails from '../components/Payments/PaymentDetails';
import type { Payment } from '../hooks/api';

const PaymentsPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<string>('payments');
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

  // Role-based permissions
  const canCreatePayments = ['JTB_ADMIN', 'FINANCE_OFFICER', 'SENIOR_FINANCE_OFFICER'].includes(user?.role || '');
  const canEditPayments = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');

  const handlePaymentCreated = () => {
    // Refresh the payment list when a new payment is created
    setActiveTab('payments');
  };

  const handlePaymentUpdated = () => {
    // Refresh the payment list when a payment is updated
    setActiveTab('payments');
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'payments':
        return (
          <PaymentList
            setActiveTab={setActiveTab}
            setSelectedPayment={setSelectedPayment}
          />
        );
      
      case 'create-payment':
        return canCreatePayments ? (
          <CreatePayment
            setActiveTab={setActiveTab}
            onPaymentCreated={handlePaymentCreated}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">You don't have permission to create payments</p>
          </div>
        );
      
      case 'edit-payment':
        return canEditPayments && selectedPayment ? (
          <div className="text-center py-8">
            <p className="text-gray-500">Edit payment functionality coming soon</p>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {!canEditPayments 
                ? "You don't have permission to edit payments" 
                : "No payment selected for editing"}
            </p>
          </div>
        );
      
      case 'payment-details':
        return selectedPayment ? (
          <PaymentDetails
            payment={selectedPayment}
            setActiveTab={setActiveTab}
            setSelectedPayment={setSelectedPayment}
            onPaymentUpdated={handlePaymentUpdated}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">No payment selected for viewing</p>
          </div>
        );
      
      default:
        return (
          <PaymentList
            setActiveTab={setActiveTab}
            setSelectedPayment={setSelectedPayment}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderContent()}
      </div>
    </div>
  );
};

export default PaymentsPage;
