
// import React, { useState } from 'react';
// import { 
//   Download, Mail, Upload, Check, Receipt, FileText, Search, 
//   Bell, Eye, X, AlertCircle, CheckCircle, Filter, Send, Plus
// } from 'lucide-react';

// interface PaymentProfile {
//   id: number;
//   name: string;
//   type: string;
//   year: string;
//   dueDate: string;
//   amount: string;
//   totalAmount: number;
//   payers: number;
//   paidCount: number;
//   status: 'Active' | 'Pending' | 'Inactive';
// }

// interface PayerPayment {
//   id: number;
//   payerName: string;
//   payerEmail: string;
//   paymentProfileId: number;
//   amountDue: number;
//   amountPaid: number;
//   dueDate: string;
//   status: 'Pending' | 'Proof Uploaded' | 'Acknowledged' | 'Awaiting Approval' | 'Approved' | 'Completed';
//   proofOfPayment?: string;
//   receiptSent: boolean;
//   complianceCertIssued: boolean;
//   lastNotificationSent?: string;
// }

// interface ActionButtonProps {
//   children: React.ReactNode;
//   onClick?: () => void;
//   variant?: 'primary' | 'secondary' | 'danger' | 'success';
//   size?: 'sm' | 'md';
//   disabled?: boolean;
// }

// interface ModalProps {
//   isOpen: boolean;
//   onClose: () => void;
//   title: string;
//   children: React.ReactNode;
// }

// const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
//   if (!isOpen) return null;

//   return (
//     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//       <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
//         <div className="flex justify-between items-center mb-4">
//           <h3 className="text-lg font-semibold text-[#045024]">{title}</h3>
//           <button onClick={onClose}>
//             <X size={20} />
//           </button>
//         </div>
//         {children}
//       </div>
//     </div>
//   );
// };

// const ActionButton: React.FC<ActionButtonProps> = ({ 
//   children, 
//   onClick, 
//   variant = "primary", 
//   size = "md",
//   disabled = false 
// }) => {
//   const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
//   const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
//   const variantClasses = {
//     primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
//     secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
//     danger: "bg-red-500 hover:bg-red-600 text-white",
//     success: "bg-green-500 hover:bg-green-600 text-white"
//   };

//   return (
//     <button
//       onClick={onClick}
//       disabled={disabled}
//       className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${
//         disabled ? 'opacity-50 cursor-not-allowed' : ''
//       }`}
//     >
//       {children}
//     </button>
//   );
// };

// const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
//   const statusStyles: { [key: string]: string } = {
//     'Pending': 'bg-yellow-100 text-yellow-800',
//     'Proof Uploaded': 'bg-blue-100 text-blue-800',
//     'Acknowledged': 'bg-purple-100 text-purple-800',
//     'Awaiting Approval': 'bg-orange-100 text-orange-800',
//     'Approved': 'bg-green-100 text-green-800',
//     'Completed': 'bg-gray-100 text-gray-800'
//   };

//   return (
//     <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusStyles[status] || 'bg-gray-100 text-gray-800'}`}>
//       {status}
//     </span>
//   );
// };

// const NotificationBadge: React.FC<{ count: number }> = ({ count }) => {
//   if (count === 0) return null;
  
//   return (
//     <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
//       {count > 9 ? '9+' : count}
//     </span>
//   );
// };

// const FinanceOfficerPaymentTracking: React.FC = () => {
//   const [selectedProfile, setSelectedProfile] = useState<number | null>(null);
//   const [searchTerm, setSearchTerm] = useState('');
//   const [showPaymentModal, setShowPaymentModal] = useState(false);
//   const [showNotificationModal, setShowNotificationModal] = useState(false);
//   const [showConfirmModal, setShowConfirmModal] = useState(false);
//   const [showReminderModal, setShowReminderModal] = useState(false);
//   const [confirmAction, setConfirmAction] = useState<{
//     type: string;
//     paymentId: number;
//     payerName: string;
//     title: string;
//     message: string;
//     action: () => void;
//   } | null>(null);
//   const [reminderMessage, setReminderMessage] = useState('');
//   const [selectedPaymentForReminder, setSelectedPaymentForReminder] = useState<PayerPayment | null>(null);

//   const [notifications] = useState([
//     { id: 1, message: "New payment proof uploaded by John Doe", timestamp: "2 mins ago", type: "payment" },
//     { id: 2, message: "Payment reminder sent to 15 payers", timestamp: "1 hour ago", type: "notification" },
//     { id: 3, message: "Payment approved by Senior Finance Officer", timestamp: "3 hours ago", type: "approval" }
//   ]);

//   const [payerPayments, setPayerPayments] = useState<PayerPayment[]>([
//     { id: 1, payerName: "John Doe", payerEmail: "<EMAIL>", paymentProfileId: 1, amountDue: 2500, amountPaid: 2500, dueDate: "2025-03-31", status: "Proof Uploaded", proofOfPayment: "receipt_001.pdf", receiptSent: false, complianceCertIssued: false },
//     { id: 2, payerName: "Jane Smith", payerEmail: "<EMAIL>", paymentProfileId: 1, amountDue: 2500, amountPaid: 2500, dueDate: "2025-03-31", status: "Approved", receiptSent: true, complianceCertIssued: false },
//     { id: 3, payerName: "Bob Johnson", payerEmail: "<EMAIL>", paymentProfileId: 1, amountDue: 2500, amountPaid: 0, dueDate: "2025-03-31", status: "Pending", receiptSent: false, complianceCertIssued: false, lastNotificationSent: "2025-06-01" },
//     { id: 4, payerName: "Alice Brown", payerEmail: "<EMAIL>", paymentProfileId: 2, amountDue: 1200, amountPaid: 1200, dueDate: "2025-04-15", status: "Completed", receiptSent: true, complianceCertIssued: true },
//   ]);

//   const paymentProfiles: PaymentProfile[] = [
//     { id: 1, name: "2025 Annual License", type: "Annual License Fee", year: "2025", dueDate: "2025-03-31", amount: "₦2,500", totalAmount: 2500, payers: 25, paidCount: 16, status: "Active" },
//     { id: 2, name: "Q1 Compliance", type: "Compliance Fee", year: "2025", dueDate: "2025-04-15", amount: "₦1,200", totalAmount: 1200, payers: 18, paidCount: 12, status: "Active" },
//     { id: 3, name: "New Registration", type: "Registration Fee", year: "2025", dueDate: "2025-05-01", amount: "₦500", totalAmount: 500, payers: 8, paidCount: 3, status: "Pending" }
//   ];

//   const updatePaymentStatus = (paymentId: number, newStatus: PayerPayment['status']) => {
//     setPayerPayments(prev => 
//       prev.map(payment => 
//         payment.id === paymentId 
//           ? { ...payment, status: newStatus }
//           : payment
//       )
//     );
//   };

//   const filteredPayments = payerPayments.filter(payment => {
//     const matchesSearch = payment.payerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
//                          payment.payerEmail.toLowerCase().includes(searchTerm.toLowerCase());
//     const matchesProfile = selectedProfile ? payment.paymentProfileId === selectedProfile : true;
//     return matchesSearch && matchesProfile;
//   });

//   const openConfirmModal = (type: string, paymentId: number, payerName: string, title: string, message: string, action: () => void) => {
//     setConfirmAction({ type, paymentId, payerName, title, message, action });
//     setShowConfirmModal(true);
//   };

//   const confirmAndExecute = () => {
//     if (confirmAction) {
//       confirmAction.action();
//       setShowConfirmModal(false);
//       setConfirmAction(null);
//     }
//   };

//   const downloadPaymentSchedule = (profileId: number) => {
//     const profile = paymentProfiles.find(p => p.id === profileId);
//     const profilePayments = getPaymentsByProfile(profileId);
    
//     if (!profile) return;
    
//     // Create CSV content
//     const csvContent = [
//       ['Payment Profile', profile.name],
//       ['Type', profile.type],
//       ['Due Date', profile.dueDate],
//       ['Total Amount', `₦${profile.totalAmount.toLocaleString()}`],
//       ['Total Payers', profile.payers],
//       ['Paid Count', profile.paidCount],
//       ['Outstanding Count', profile.payers - profile.paidCount],
//       [''],
//       ['Payer Name', 'Email', 'Amount Due', 'Amount Paid', 'Outstanding', 'Status', 'Due Date'],
//       ...profilePayments.map(payment => [
//         payment.payerName,
//         payment.payerEmail,
//         `₦${payment.amountDue.toLocaleString()}`,
//         `₦${payment.amountPaid.toLocaleString()}`,
//         `₦${(payment.amountDue - payment.amountPaid).toLocaleString()}`,
//         payment.status,
//         payment.dueDate
//       ])
//     ].map(row => row.join(',')).join('\n');
    
//     // Download CSV
//     const blob = new Blob([csvContent], { type: 'text/csv' });
//     const url = window.URL.createObjectURL(blob);
//     const a = document.createElement('a');
//     a.href = url;
//     a.download = `payment-schedule-${profile.name.replace(/\s+/g, '-').toLowerCase()}.csv`;
//     document.body.appendChild(a);
//     a.click();
//     document.body.removeChild(a);
//     window.URL.revokeObjectURL(url);
    
//     console.log('Downloaded payment schedule for:', profile.name);
//   };

//   const viewProofOfPayment = (paymentId: number) => {
//     const payment = payerPayments.find(p => p.id === paymentId);
//     if (payment?.proofOfPayment) {
//       window.open(`/uploads/proofs/${payment.proofOfPayment}`, '_blank');
//       console.log('Viewing proof of payment:', payment.proofOfPayment);
//     }
//   };

//   const handleAcknowledgePayment = (paymentId: number) => {
//     const payment = payerPayments.find(p => p.id === paymentId);
//     if (payment) {
//       openConfirmModal(
//         'acknowledge',
//         paymentId,
//         payment.payerName,
//         'Acknowledge Payment',
//         `Are you sure you want to acknowledge the payment from ${payment.payerName}? This will change the status to "Awaiting Approval" and forward to Senior Finance Officer.`,
//         () => {
//           updatePaymentStatus(paymentId, 'Awaiting Approval');
//           console.log('Payment acknowledged for:', payment.payerName);
//         }
//       );
//     }
//   };

//   const handleUploadProof = (paymentId: number) => {
//     const payment = payerPayments.find(p => p.id === paymentId);
//     if (payment) {
//       console.log('Uploading proof of payment on behalf of:', payment.payerName);
//       const fileInput = document.createElement('input');
//       fileInput.type = 'file';
//       fileInput.accept = '.pdf,.jpg,.jpeg,.png';
//       fileInput.onchange = (e) => {
//         const file = (e.target as HTMLInputElement).files?.[0];
//         if (file) {
//           updatePaymentStatus(paymentId, 'Proof Uploaded');
//           alert(`Proof of payment uploaded for ${payment.payerName}: ${file.name}`);
//         }
//       };
//       fileInput.click();
//     }
//   };

//   const handleSendReceipt = (paymentId: number) => {
//     const payment = payerPayments.find(p => p.id === paymentId);
//     if (payment) {
//       openConfirmModal(
//         'receipt',
//         paymentId,
//         payment.payerName,
//         'Send Receipt',
//         `Are you sure you want to send the payment receipt to ${payment.payerName} at ${payment.payerEmail}?`,
//         () => {
//           setPayerPayments(prev => 
//             prev.map(p => 
//               p.id === paymentId 
//                 ? { ...p, receiptSent: true }
//                 : p
//             )
//           );
//           console.log('Receipt sent to:', payment.payerName);
//         }
//       );
//     }
//   };

//   const handleIssueCertificate = (paymentId: number) => {
//     const payment = payerPayments.find(p => p.id === paymentId);
//     if (payment) {
//       openConfirmModal(
//         'certificate',
//         paymentId,
//         payment.payerName,
//         'Issue Compliance Certificate',
//         `Are you sure you want to issue a compliance certificate for ${payment.payerName}? This will mark the payment as completed.`,
//         () => {
//           setPayerPayments(prev => 
//             prev.map(p => 
//               p.id === paymentId 
//                 ? { ...p, complianceCertIssued: true, status: 'Completed' }
//                 : p
//             )
//           );
//           console.log('Compliance certificate issued for:', payment.payerName);
//         }
//       );
//     }
//   };

//   const openReminderModal = (paymentId: number) => {
//     const payment = payerPayments.find(p => p.id === paymentId);
//     if (payment) {
//       setSelectedPaymentForReminder(payment);
//       setReminderMessage(`Dear ${payment.payerName},\n\nThis is a friendly reminder that your payment of ₦${payment.amountDue.toLocaleString()} is due on ${payment.dueDate}.\n\nPlease ensure timely payment to avoid any penalties.\n\nBest regards,\nFinance Department`);
//       setShowReminderModal(true);
//     }
//   };

//   const handleSendReminder = () => {
//     if (selectedPaymentForReminder && reminderMessage.trim()) {
//       setPayerPayments(prev => 
//         prev.map(p => 
//           p.id === selectedPaymentForReminder.id 
//             ? { ...p, lastNotificationSent: new Date().toISOString().split('T')[0] }
//             : p
//         )
//       );
      
//       console.log('Reminder sent to:', selectedPaymentForReminder.payerName);
//       console.log('Message:', reminderMessage);
      
//       setShowReminderModal(false);
//       setSelectedPaymentForReminder(null);
//       setReminderMessage('');
      
//       alert(`Payment reminder sent to ${selectedPaymentForReminder.payerName} at ${selectedPaymentForReminder.payerEmail}`);
//     }
//   };

//   const handleSendNotification = (type: 'general' | 'specific', paymentId?: number) => {
//     if (type === 'general') {
//       console.log('Sending general payment reminders');
//       alert('Payment reminders sent to all payers with outstanding payments');
//     } else if (type === 'specific' && paymentId) {
//       openReminderModal(paymentId);
//     }
//   };

//   const getPaymentsByProfile = (profileId: number) => {
//     return payerPayments.filter(payment => payment.paymentProfileId === profileId);
//   };

//   return (
//     <div className="space-y-6">
//       {/* Header */}
//       <div className="flex justify-between items-center">
//         <h2 className="text-2xl font-bold text-[#045024]">Payment Tracking - Finance Officer</h2>
//         <div className="flex gap-2 items-center">
//           <div className="relative">
//             <ActionButton variant="secondary" onClick={() => setShowNotificationModal(true)}>
//               <Bell size={16} />
//               Notifications
//             </ActionButton>
//             <NotificationBadge count={notifications.length} />
//           </div>
//           <ActionButton variant="secondary">
//             <Download size={16} />
//             Export Schedule
//           </ActionButton>
//         </div>
//       </div>

//       {/* Search and Filter */}
//       <div className="bg-white rounded-lg shadow-md p-6">
//         <div className="flex gap-4 mb-4">
//           <div className="flex-1 relative">
//             <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
//             <input
//               type="text"
//               placeholder="Search payers by name or email..."
//               value={searchTerm}
//               onChange={(e) => setSearchTerm(e.target.value)}
//               className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//             />
//           </div>
//           <div className="relative">
//             <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
//             <select
//               value={selectedProfile || ''}
//               onChange={(e) => setSelectedProfile(e.target.value ? parseInt(e.target.value) : null)}
//               className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//             >
//               <option value="">All Payment Profiles</option>
//               {paymentProfiles.map(profile => (
//                 <option key={profile.id} value={profile.id}>{profile.name}</option>
//               ))}
//             </select>
//           </div>
//         </div>
//       </div>

//       {/* Payment Profiles Overview */}
//       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
//         {paymentProfiles.map(profile => {
//           const profilePayments = getPaymentsByProfile(profile.id);
//           const completedPayments = profilePayments.filter(p => p.status === 'Completed').length;
//           const pendingProofs = profilePayments.filter(p => p.status === 'Proof Uploaded').length;
//           const awaitingApproval = profilePayments.filter(p => p.status === 'Awaiting Approval').length;
          
//           return (
//             <div key={profile.id} className="bg-white rounded-lg shadow-md p-6">
//               <div className="flex justify-between items-start mb-4">
//                 <div>
//                   <h3 className="text-lg font-semibold text-[#045024]">{profile.name}</h3>
//                   <p className="text-sm text-gray-600">{profile.type}</p>
//                   <p className="text-sm text-gray-500">Due: {profile.dueDate}</p>
//                 </div>
//                 <StatusBadge status={profile.status} />
//               </div>
              
//               <div className="space-y-2 mb-4">
//                 <div className="flex justify-between text-sm">
//                   <span>Payment Progress</span>
//                   <span className="font-medium">{profile.paidCount}/{profile.payers}</span>
//                 </div>
//                 <div className="w-full bg-gray-200 rounded-full h-2">
//                   <div 
//                     className="bg-[#2aa45c] h-2 rounded-full" 
//                     style={{ width: `${(profile.paidCount / profile.payers) * 100}%` }}
//                   ></div>
//                 </div>
//               </div>
              
//               <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
//                 <div className="text-center p-2 bg-blue-50 rounded">
//                   <div className="font-medium text-[#045024]">{pendingProofs}</div>
//                   <div className="text-[#2aa45c]">Proofs to Review</div>
//                 </div>
//                 <div className="text-center p-2 bg-orange-50 rounded">
//                   <div className="font-medium text-[#045024]">{awaitingApproval}</div>
//                   <div className="text-[#2aa45c]">Awaiting Approval</div>
//                 </div>
//               </div>
              
//               <div className="flex gap-2">
//                 <ActionButton size="sm" onClick={() => setSelectedProfile(profile.id)}>
//                   <Eye size={14} />
//                   View Details
//                 </ActionButton>
//                 <ActionButton size="sm" variant="secondary" onClick={() => downloadPaymentSchedule(profile.id)}>
//                   <Download size={14} />
//                   Download Schedule
//                 </ActionButton>
//                 <ActionButton size="sm" variant="secondary" onClick={() => handleSendNotification('general')}>
//                   <Mail size={14} />
//                   Send Reminders
//                 </ActionButton>
//               </div>
//             </div>
//           );
//         })}
//       </div>

//       {/* Detailed Payment List */}
//       <div className="bg-white rounded-lg shadow-md p-6">
//         <h3 className="text-lg font-semibold text-[#045024] mb-4">Payment Details</h3>
//         <div className="overflow-x-auto">
//           <table className="w-full text-sm">
//             <thead className="bg-gray-50">
//               <tr>
//                 <th className="text-left p-3">Payer</th>
//                 <th className="text-left p-3">Payment Profile</th>
//                 <th className="text-left p-3">Amount</th>
//                 <th className="text-left p-3">Due Date</th>
//                 <th className="text-left p-3">Status</th>
//                 <th className="text-left p-3">Actions</th>
//               </tr>
//             </thead>
//             <tbody>
//               {filteredPayments.map(payment => {
//                 const profile = paymentProfiles.find(p => p.id === payment.paymentProfileId);
//                 return (
//                   <tr key={payment.id} className="border-b hover:bg-gray-50">
//                     <td className="p-3">
//                       <div>
//                         <div className="font-medium">{payment.payerName}</div>
//                         <div className="text-gray-500 text-xs">{payment.payerEmail}</div>
//                       </div>
//                     </td>
//                     <td className="p-3">{profile?.name}</td>
//                     <td className="p-3">
//                       <div>
//                         <div className="font-medium">₦{payment.amountPaid.toLocaleString()}</div>
//                         <div className="text-gray-500 text-xs">of ₦{payment.amountDue.toLocaleString()}</div>
//                       </div>
//                     </td>
//                     <td className="p-3">{payment.dueDate}</td>
//                     <td className="p-3">
//                       <StatusBadge status={payment.status} />
//                     </td>
//                     <td className="p-3">
//                       <div className="flex gap-1 flex-wrap">
//                         {payment.proofOfPayment && (
//                           <ActionButton size="sm" variant="secondary" onClick={() => viewProofOfPayment(payment.id)}>
//                             <Eye size={12} />
//                             View Proof
//                           </ActionButton>
//                         )}
//                         {payment.status === 'Proof Uploaded' && (
//                           <ActionButton size="sm" onClick={() => handleAcknowledgePayment(payment.id)}>
//                             <Check size={12} />
//                             Acknowledge
//                           </ActionButton>
//                         )}
//                         {payment.status === 'Pending' && (
//                           <ActionButton size="sm" variant="secondary" onClick={() => handleUploadProof(payment.id)}>
//                             <Upload size={12} />
//                             Upload Proof
//                           </ActionButton>
//                         )}
//                         {payment.status === 'Approved' && !payment.receiptSent && (
//                           <ActionButton size="sm" variant="success" onClick={() => handleSendReceipt(payment.id)}>
//                             <Receipt size={12} />
//                             Send Receipt
//                           </ActionButton>
//                         )}
//                         {payment.status === 'Approved' && payment.receiptSent && !payment.complianceCertIssued && (
//                           <ActionButton size="sm" variant="primary" onClick={() => handleIssueCertificate(payment.id)}>
//                             <FileText size={12} />
//                             Issue Certificate
//                           </ActionButton>
//                         )}
//                         <ActionButton size="sm" variant="secondary" onClick={() => handleSendNotification('specific', payment.id)}>
//                           <Send size={12} />
//                           Send Reminder
//                         </ActionButton>
//                       </div>
//                     </td>
//                   </tr>
//                 );
//               })}
//             </tbody>
//           </table>
//         </div>
//       </div>

//       {/* Quick Actions Panel */}
//       <div className="bg-white rounded-lg shadow-md p-6">
//         <h3 className="text-lg font-semibold text-[#045024] mb-4">Quick Actions</h3>
//         <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
//           <ActionButton onClick={() => setShowPaymentModal(true)}>
//             <Plus size={16} />
//             Record Payment
//           </ActionButton>
//           <ActionButton variant="secondary">
//             <Check size={16} />
//             Batch Acknowledge
//           </ActionButton>
//           <ActionButton variant="secondary">
//             <Receipt size={16} />
//             Bulk Receipts
//           </ActionButton>
//           <ActionButton variant="secondary">
//             <FileText size={16} />
//             Issue Certificates
//           </ActionButton>
//         </div>
//       </div>

//       {/* Confirmation Modal */}
//       <Modal 
//         isOpen={showConfirmModal} 
//         onClose={() => setShowConfirmModal(false)} 
//         title={confirmAction?.title || 'Confirm Action'}
//       >
//         <div className="space-y-4">
//           <div className="text-gray-600">
//             {confirmAction?.message}
//           </div>
//           <div className="flex gap-2 justify-end">
//             <ActionButton variant="secondary" onClick={() => setShowConfirmModal(false)}>
//               Cancel
//             </ActionButton>
//             <ActionButton onClick={confirmAndExecute}>
//               <Check size={16} />
//               Confirm
//             </ActionButton>
//           </div>
//         </div>
//       </Modal>

//       {/* Send Reminder Modal */}
//       <Modal 
//         isOpen={showReminderModal} 
//         onClose={() => setShowReminderModal(false)} 
//         title="Send Payment Reminder"
//       >
//         <div className="space-y-4">
//           <div className="text-gray-600">
//             Send payment reminder to <strong>{selectedPaymentForReminder?.payerName}</strong> at <strong>{selectedPaymentForReminder?.payerEmail}</strong>
//           </div>
//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-2">Message Content</label>
//             <textarea
//               className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2aa45c]"
//               rows={6}
//               value={reminderMessage}
//               onChange={(e) => setReminderMessage(e.target.value)}
//               placeholder="Enter your reminder message..."
//             />
//           </div>
//           <div className="flex gap-2 justify-end">
//             <ActionButton variant="secondary" onClick={() => setShowReminderModal(false)}>
//               Cancel
//             </ActionButton>
//             <ActionButton onClick={handleSendReminder} disabled={!reminderMessage.trim()}>
//               <Send size={16} />
//               Send Reminder
//             </ActionButton>
//           </div>
//         </div>
//       </Modal>

//       {/* Notifications Modal */}
//       <Modal 
//         isOpen={showNotificationModal} 
//         onClose={() => setShowNotificationModal(false)} 
//         title="Notifications"
//       >
//         <div className="space-y-3">
//           {notifications.map(notification => (
//             <div key={notification.id} className="p-3 bg-gray-50 rounded-lg">
//               <div className="flex items-start gap-3">
//                 <div className="mt-1">
//                   {notification.type === 'payment' && <AlertCircle size={16} className="text-blue-500" />}
//                   {notification.type === 'approval' && <CheckCircle size={16} className="text-green-500" />}
//                   {notification.type === 'notification' && <Mail size={16} className="text-gray-500" />}
//                 </div>
//                 <div className="flex-1">
//                   <p className="text-sm">{notification.message}</p>
//                   <p className="text-xs text-gray-500 mt-1">{notification.timestamp}</p>
//                 </div>
//               </div>
//             </div>
//           ))}
//         </div>
//       </Modal>
//     </div>
//   );
// };

// export default FinanceOfficerPaymentTracking;

import React, { useState } from 'react';
import { 
  Download, Mail, Upload, Check, Receipt, FileText, Search, 
  Bell, Eye, X, AlertCircle, CheckCircle, Filter, Send, Plus, XCircle
} from 'lucide-react';

interface PaymentProfile {
  id: number;
  name: string;
  type: string;
  year: string;
  dueDate: string;
  amount: string;
  totalAmount: number;
  payers: number;
  paidCount: number;
  status: 'Active' | 'Pending' | 'Inactive';
}

interface PayerPayment {
  id: number;
  payerName: string;
  payerEmail: string;
  paymentProfileId: number;
  amountDue: number;
  amountPaid: number;
  dueDate: string;
  status: 'Pending' | 'Proof Uploaded' | 'Acknowledged' | 'Awaiting Approval' | 'Approved' | 'Rejected' | 'Completed';
  proofOfPayment?: string;
  receiptSent: boolean;
  complianceCertIssued: boolean;
  lastNotificationSent?: string;
  rejectionReason?: string;
  rejectionNoticeSent?: boolean;
}

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger' | 'success';
  size?: 'sm' | 'md';
  disabled?: boolean;
}

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-[#045024]">{title}</h3>
          <button onClick={onClose}>
            <X size={20} />
          </button>
        </div>
        {children}
      </div>
    </div>
  );
};

const ActionButton: React.FC<ActionButtonProps> = ({ 
  children, 
  onClick, 
  variant = "primary", 
  size = "md",
  disabled = false 
}) => {
  const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white",
    success: "bg-green-500 hover:bg-green-600 text-white"
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      }`}
    >
      {children}
    </button>
  );
};

const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const statusStyles: { [key: string]: string } = {
    'Pending': 'bg-yellow-100 text-yellow-800',
    'Proof Uploaded': 'bg-blue-100 text-blue-800',
    'Acknowledged': 'bg-purple-100 text-purple-800',
    'Awaiting Approval': 'bg-orange-100 text-orange-800',
    'Approved': 'bg-green-100 text-green-800',
    'Rejected': 'bg-red-100 text-red-800',
    'Completed': 'bg-gray-100 text-gray-800'
  };

  return (
    <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusStyles[status] || 'bg-gray-100 text-gray-800'}`}>
      {status}
    </span>
  );
};

const NotificationBadge: React.FC<{ count: number }> = ({ count }) => {
  if (count === 0) return null;
  
  return (
    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
      {count > 9 ? '9+' : count}
    </span>
  );
};

const FinanceOfficerPaymentTracking: React.FC = () => {
  const [selectedProfile, setSelectedProfile] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showNotificationModal, setShowNotificationModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showReminderModal, setShowReminderModal] = useState(false);
  const [confirmAction, setConfirmAction] = useState<{
    type: string;
    paymentId: number;
    payerName: string;
    title: string;
    message: string;
    action: () => void;
  } | null>(null);
  const [reminderMessage, setReminderMessage] = useState('');
  const [selectedPaymentForReminder, setSelectedPaymentForReminder] = useState<PayerPayment | null>(null);

  const [notifications] = useState([
    { id: 1, message: "New payment proof uploaded by John Doe", timestamp: "2 mins ago", type: "payment" },
    { id: 2, message: "Payment reminder sent to 15 payers", timestamp: "1 hour ago", type: "notification" },
    { id: 3, message: "Payment approved by Senior Finance Officer", timestamp: "3 hours ago", type: "approval" }
  ]);

  const [payerPayments, setPayerPayments] = useState<PayerPayment[]>([
    { id: 1, payerName: "John Doe", payerEmail: "<EMAIL>", paymentProfileId: 1, amountDue: 2500, amountPaid: 2500, dueDate: "2025-03-31", status: "Proof Uploaded", proofOfPayment: "receipt_001.pdf", receiptSent: false, complianceCertIssued: false },
    { id: 2, payerName: "Jane Smith", payerEmail: "<EMAIL>", paymentProfileId: 1, amountDue: 2500, amountPaid: 2500, dueDate: "2025-03-31", status: "Approved", receiptSent: true, complianceCertIssued: false },
    { id: 3, payerName: "Bob Johnson", payerEmail: "<EMAIL>", paymentProfileId: 1, amountDue: 2500, amountPaid: 0, dueDate: "2025-03-31", status: "Pending", receiptSent: false, complianceCertIssued: false, lastNotificationSent: "2025-06-01" },
    { id: 4, payerName: "Alice Brown", payerEmail: "<EMAIL>", paymentProfileId: 2, amountDue: 1200, amountPaid: 1200, dueDate: "2025-04-15", status: "Completed", receiptSent: true, complianceCertIssued: true },
    { id: 5, payerName: "Charlie Wilson", payerEmail: "<EMAIL>", paymentProfileId: 1, amountDue: 2500, amountPaid: 2500, dueDate: "2025-03-31", status: "Rejected", receiptSent: false, complianceCertIssued: false, rejectionReason: "Insufficient proof of payment", rejectionNoticeSent: false },
  ]);

  const paymentProfiles: PaymentProfile[] = [
    { id: 1, name: "2025 Annual License", type: "Annual License Fee", year: "2025", dueDate: "2025-03-31", amount: "₦2,500", totalAmount: 2500, payers: 25, paidCount: 16, status: "Active" },
    { id: 2, name: "Q1 Compliance", type: "Compliance Fee", year: "2025", dueDate: "2025-04-15", amount: "₦1,200", totalAmount: 1200, payers: 18, paidCount: 12, status: "Active" },
    { id: 3, name: "New Registration", type: "Registration Fee", year: "2025", dueDate: "2025-05-01", amount: "₦500", totalAmount: 500, payers: 8, paidCount: 3, status: "Pending" }
  ];

  const updatePaymentStatus = (paymentId: number, newStatus: PayerPayment['status'], additionalData?: Partial<PayerPayment>) => {
    setPayerPayments(prev => 
      prev.map(payment => 
        payment.id === paymentId 
          ? { ...payment, status: newStatus, ...additionalData }
          : payment
      )
    );
  };

  const filteredPayments = payerPayments.filter(payment => {
    const matchesSearch = payment.payerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.payerEmail.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesProfile = selectedProfile ? payment.paymentProfileId === selectedProfile : true;
    return matchesSearch && matchesProfile;
  });

  const openConfirmModal = (type: string, paymentId: number, payerName: string, title: string, message: string, action: () => void) => {
    setConfirmAction({ type, paymentId, payerName, title, message, action });
    setShowConfirmModal(true);
  };

  const confirmAndExecute = () => {
    if (confirmAction) {
      confirmAction.action();
      setShowConfirmModal(false);
      setConfirmAction(null);
    }
  };

  const downloadPaymentSchedule = (profileId: number) => {
    const profile = paymentProfiles.find(p => p.id === profileId);
    const profilePayments = getPaymentsByProfile(profileId);
    
    if (!profile) return;
    
    // Create CSV content
    const csvContent = [
      ['Payment Profile', profile.name],
      ['Type', profile.type],
      ['Due Date', profile.dueDate],
      ['Total Amount', `₦${profile.totalAmount.toLocaleString()}`],
      ['Total Payers', profile.payers],
      ['Paid Count', profile.paidCount],
      ['Outstanding Count', profile.payers - profile.paidCount],
      [''],
      ['Payer Name', 'Email', 'Amount Due', 'Amount Paid', 'Outstanding', 'Status', 'Due Date'],
      ...profilePayments.map(payment => [
        payment.payerName,
        payment.payerEmail,
        `₦${payment.amountDue.toLocaleString()}`,
        `₦${payment.amountPaid.toLocaleString()}`,
        `₦${(payment.amountDue - payment.amountPaid).toLocaleString()}`,
        payment.status,
        payment.dueDate
      ])
    ].map(row => row.join(',')).join('\n');
    
    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `payment-schedule-${profile.name.replace(/\s+/g, '-').toLowerCase()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    console.log('Downloaded payment schedule for:', profile.name);
  };

  const viewProofOfPayment = (paymentId: number) => {
    const payment = payerPayments.find(p => p.id === paymentId);
    if (payment?.proofOfPayment) {
      window.open(`/uploads/proofs/${payment.proofOfPayment}`, '_blank');
      console.log('Viewing proof of payment:', payment.proofOfPayment);
    }
  };

  const handleAcknowledgePayment = (paymentId: number) => {
    const payment = payerPayments.find(p => p.id === paymentId);
    if (payment) {
      openConfirmModal(
        'acknowledge',
        paymentId,
        payment.payerName,
        'Acknowledge Payment',
        `Are you sure you want to acknowledge the payment from ${payment.payerName}? This will change the status to "Awaiting Approval" and forward to Senior Finance Officer.`,
        () => {
          updatePaymentStatus(paymentId, 'Awaiting Approval');
          console.log('Payment acknowledged for:', payment.payerName);
        }
      );
    }
  };

  const handleUploadProof = (paymentId: number) => {
    const payment = payerPayments.find(p => p.id === paymentId);
    if (payment) {
      console.log('Uploading proof of payment on behalf of:', payment.payerName);
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.accept = '.pdf,.jpg,.jpeg,.png';
      fileInput.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          updatePaymentStatus(paymentId, 'Proof Uploaded');
          alert(`Proof of payment uploaded for ${payment.payerName}: ${file.name}`);
        }
      };
      fileInput.click();
    }
  };

  const handleSendReceipt = (paymentId: number) => {
    const payment = payerPayments.find(p => p.id === paymentId);
    if (payment) {
      openConfirmModal(
        'receipt',
        paymentId,
        payment.payerName,
        'Send Receipt',
        `Are you sure you want to send the payment receipt to ${payment.payerName} at ${payment.payerEmail}?`,
        () => {
          setPayerPayments(prev => 
            prev.map(p => 
              p.id === paymentId 
                ? { ...p, receiptSent: true }
                : p
            )
          );
          console.log('Receipt sent to:', payment.payerName);
        }
      );
    }
  };

  const handleIssueCertificate = (paymentId: number) => {
    const payment = payerPayments.find(p => p.id === paymentId);
    if (payment) {
      openConfirmModal(
        'certificate',
        paymentId,
        payment.payerName,
        'Issue Compliance Certificate',
        `Are you sure you want to issue a compliance certificate for ${payment.payerName}? This will mark the payment as completed.`,
        () => {
          setPayerPayments(prev => 
            prev.map(p => 
              p.id === paymentId 
                ? { ...p, complianceCertIssued: true, status: 'Completed' }
                : p
            )
          );
          console.log('Compliance certificate issued for:', payment.payerName);
        }
      );
    }
  };

  const handleSendRejectionNotice = (paymentId: number) => {
    const payment = payerPayments.find(p => p.id === paymentId);
    if (payment) {
      openConfirmModal(
        'rejection_notice',
        paymentId,
        payment.payerName,
        'Send Rejection Notice',
        `Are you sure you want to send a rejection notice to ${payment.payerName}? This will change the status to "Pending" and mark that a rejection notice has been sent.`,
        () => {
          updatePaymentStatus(paymentId, 'Pending', { 
            rejectionNoticeSent: true, 
            lastNotificationSent: new Date().toISOString().split('T')[0] 
          });
          console.log('Rejection notice sent to:', payment.payerName);
        }
      );
    }
  };

  const openReminderModal = (paymentId: number) => {
    const payment = payerPayments.find(p => p.id === paymentId);
    if (payment) {
      setSelectedPaymentForReminder(payment);
      
      let defaultMessage = '';
      
      if (payment.status === 'Completed') {
        defaultMessage = `Dear ${payment.payerName},\n\nWe are pleased to inform you that your payment of ₦${payment.amountDue.toLocaleString()} has been successfully processed and approved.\n\nYour compliance certificate has been issued and is available for download from your dashboard.\n\nThank you for your prompt payment.\n\nBest regards,\nFinance Department`;
      } else if (payment.status === 'Approved') {
        defaultMessage = `Dear ${payment.payerName},\n\nWe are pleased to inform you that your payment of ₦${payment.amountDue.toLocaleString()} has been approved by our Senior Finance Officer.\n\nYour payment receipt has been processed and you will receive your compliance certificate shortly.\n\nThank you for your payment.\n\nBest regards,\nFinance Department`;
      } else if (payment.status === 'Rejected') {
        defaultMessage = `Dear ${payment.payerName},\n\nWe regret to inform you that your payment submission has been rejected.\n\nReason: ${payment.rejectionReason || 'Please contact our office for details'}\n\nPlease resubmit your payment with the correct documentation by ${payment.dueDate}.\n\nFor assistance, please contact our finance department.\n\nBest regards,\nFinance Department`;
      } else {
        defaultMessage = `Dear ${payment.payerName},\n\nThis is a friendly reminder that your payment of ₦${payment.amountDue.toLocaleString()} is due on ${payment.dueDate}.\n\nPlease ensure timely payment to avoid any penalties.\n\nBest regards,\nFinance Department`;
      }
      
      setReminderMessage(defaultMessage);
      setShowReminderModal(true);
    }
  };

  const handleSendReminder = () => {
    if (selectedPaymentForReminder && reminderMessage.trim()) {
      setPayerPayments(prev => 
        prev.map(p => 
          p.id === selectedPaymentForReminder.id 
            ? { ...p, lastNotificationSent: new Date().toISOString().split('T')[0] }
            : p
        )
      );
      
      console.log('Reminder sent to:', selectedPaymentForReminder.payerName);
      console.log('Message:', reminderMessage);
      
      setShowReminderModal(false);
      setSelectedPaymentForReminder(null);
      setReminderMessage('');
      
      const messageType = selectedPaymentForReminder.status === 'Completed' ? 'completion notice' : 
                         selectedPaymentForReminder.status === 'Approved' ? 'approval notice' : 
                         selectedPaymentForReminder.status === 'Rejected' ? 'rejection notice' : 'reminder';
      
      alert(`Payment ${messageType} sent to ${selectedPaymentForReminder.payerName} at ${selectedPaymentForReminder.payerEmail}`);
    }
  };

  const handleSendNotification = (type: 'general' | 'specific', paymentId?: number) => {
    if (type === 'general') {
      console.log('Sending general payment reminders');
      alert('Payment reminders sent to all payers with outstanding payments');
    } else if (type === 'specific' && paymentId) {
      openReminderModal(paymentId);
    }
  };

  const getPaymentsByProfile = (profileId: number) => {
    return payerPayments.filter(payment => payment.paymentProfileId === profileId);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-[#045024]">Payment Tracking - Finance Officer</h2>
        <div className="flex gap-2 items-center">
          <div className="relative">
            <ActionButton variant="secondary" onClick={() => setShowNotificationModal(true)}>
              <Bell size={16} />
              Notifications
            </ActionButton>
            <NotificationBadge count={notifications.length} />
          </div>
          <ActionButton variant="secondary">
            <Download size={16} />
            Export Schedule
          </ActionButton>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex gap-4 mb-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <input
              type="text"
              placeholder="Search payers by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
            />
          </div>
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <select
              value={selectedProfile || ''}
              onChange={(e) => setSelectedProfile(e.target.value ? parseInt(e.target.value) : null)}
              className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
            >
              <option value="">All Payment Profiles</option>
              {paymentProfiles.map(profile => (
                <option key={profile.id} value={profile.id}>{profile.name}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Payment Profiles Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {paymentProfiles.map(profile => {
          const profilePayments = getPaymentsByProfile(profile.id);
          const completedPayments = profilePayments.filter(p => p.status === 'Completed').length;
          const pendingProofs = profilePayments.filter(p => p.status === 'Proof Uploaded').length;
          const awaitingApproval = profilePayments.filter(p => p.status === 'Awaiting Approval').length;
          const rejectedPayments = profilePayments.filter(p => p.status === 'Rejected').length;
          
          return (
            <div key={profile.id} className="bg-white rounded-lg shadow-md p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-[#045024]">{profile.name}</h3>
                  <p className="text-sm text-gray-600">{profile.type}</p>
                  <p className="text-sm text-gray-500">Due: {profile.dueDate}</p>
                </div>
                <StatusBadge status={profile.status} />
              </div>
              
              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span>Payment Progress</span>
                  <span className="font-medium">{profile.paidCount}/{profile.payers}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-[#2aa45c] h-2 rounded-full" 
                    style={{ width: `${(profile.paidCount / profile.payers) * 100}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div className="text-center p-2 bg-blue-50 rounded">
                  <div className="font-medium text-[#045024]">{pendingProofs}</div>
                  <div className="text-[#2aa45c]">Proofs to Review</div>
                </div>
                <div className="text-center p-2 bg-orange-50 rounded">
                  <div className="font-medium text-[#045024]">{awaitingApproval}</div>
                  <div className="text-[#2aa45c]">Awaiting Approval</div>
                </div>
                <div className="text-center p-2 bg-red-50 rounded col-span-2">
                  <div className="font-medium text-[#045024]">{rejectedPayments}</div>
                  <div className="text-[#2aa45c]">Rejected Payments</div>
                </div>
              </div>
              
              <div className="flex gap-2">
                <ActionButton size="sm" onClick={() => setSelectedProfile(profile.id)}>
                  <Eye size={14} />
                  View Details
                </ActionButton>
                <ActionButton size="sm" variant="secondary" onClick={() => downloadPaymentSchedule(profile.id)}>
                  <Download size={14} />
                  Download Schedule
                </ActionButton>
                <ActionButton size="sm" variant="secondary" onClick={() => handleSendNotification('general')}>
                  <Mail size={14} />
                  Send Reminders
                </ActionButton>
              </div>
            </div>
          );
        })}
      </div>

      {/* Detailed Payment List */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-[#045024] mb-4">Payment Details</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left p-3">Payer</th>
                <th className="text-left p-3">Payment Profile</th>
                <th className="text-left p-3">Amount</th>
                <th className="text-left p-3">Due Date</th>
                <th className="text-left p-3">Status</th>
                <th className="text-left p-3">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredPayments.map(payment => {
                const profile = paymentProfiles.find(p => p.id === payment.paymentProfileId);
                return (
                  <tr key={payment.id} className="border-b hover:bg-gray-50">
                    <td className="p-3">
                      <div>
                        <div className="font-medium">{payment.payerName}</div>
                        <div className="text-gray-500 text-xs">{payment.payerEmail}</div>
                        {payment.rejectionNoticeSent && (
                          <div className="text-red-500 text-xs mt-1 flex items-center gap-1">
                            <XCircle size={12} />
                            Rejection notice sent
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="p-3">{profile?.name}</td>
                    <td className="p-3">
                      <div>
                        <div className="font-medium">₦{payment.amountPaid.toLocaleString()}</div>
                        <div className="text-gray-500 text-xs">of ₦{payment.amountDue.toLocaleString()}</div>
                      </div>
                    </td>
                    <td className="p-3">{payment.dueDate}</td>
                    <td className="p-3">
                      <StatusBadge status={payment.status} />
                    </td>
                    <td className="p-3">
                      <div className="flex gap-1 flex-wrap">
                        {payment.proofOfPayment && (
                          <ActionButton size="sm" variant="secondary" onClick={() => viewProofOfPayment(payment.id)}>
                            <Eye size={12} />
                            View Proof
                          </ActionButton>
                        )}
                        {payment.status === 'Proof Uploaded' && (
                          <ActionButton size="sm" onClick={() => handleAcknowledgePayment(payment.id)}>
                            <Check size={12} />
                            Acknowledge
                          </ActionButton>
                        )}
                        {(payment.status === 'Pending' || payment.status === 'Rejected') && (
                          <ActionButton size="sm" variant="secondary" onClick={() => handleUploadProof(payment.id)}>
                            <Upload size={12} />
                            Upload Proof
                          </ActionButton>
                        )}
                        {payment.status === 'Rejected' && !payment.rejectionNoticeSent && (
                          <ActionButton size="sm" variant="danger" onClick={() => handleSendRejectionNotice(payment.id)}>
                            <XCircle size={12} />
                            Send Rejection Notice
                          </ActionButton>
                        )}
                        {payment.status === 'Approved' && !payment.receiptSent && (
                          <ActionButton size="sm" variant="success" onClick={() => handleSendReceipt(payment.id)}>
                            <Receipt size={12} />
                            Send Receipt
                          </ActionButton>
                        )}
                        {payment.status === 'Approved' && payment.receiptSent && !payment.complianceCertIssued && (
                          <ActionButton size="sm" variant="primary" onClick={() => handleIssueCertificate(payment.id)}>
                                                      <FileText size={12} />
                            Issue Certificate
                          </ActionButton>
                        )}
                        <ActionButton size="sm" variant="secondary" onClick={() => openReminderModal(payment.id)}>
                          <Mail size={12} />
                          Send Notice
                        </ActionButton>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modals */}
      <Modal isOpen={showNotificationModal} onClose={() => setShowNotificationModal(false)} title="Notifications">
        <div className="space-y-4">
          {notifications.map(notification => (
            <div key={notification.id} className="border-b pb-3">
              <p className="text-gray-800">{notification.message}</p>
              <p className="text-xs text-gray-500">{notification.timestamp}</p>
            </div>
          ))}
        </div>
      </Modal>

      <Modal isOpen={showConfirmModal} onClose={() => setShowConfirmModal(false)} title={confirmAction?.title || 'Confirm Action'}>
        {confirmAction && (
          <div className="space-y-4">
            <p>{confirmAction.message}</p>
            <div className="flex justify-end gap-2">
              <ActionButton variant="secondary" onClick={() => setShowConfirmModal(false)}>
                Cancel
              </ActionButton>
              <ActionButton variant="primary" onClick={confirmAndExecute}>
                Confirm
              </ActionButton>
            </div>
          </div>
        )}
      </Modal>

      <Modal isOpen={showReminderModal} onClose={() => setShowReminderModal(false)} title="Send Payment Notice">
        {selectedPaymentForReminder && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Recipient</label>
              <p className="text-sm">{selectedPaymentForReminder.payerName} &lt;{selectedPaymentForReminder.payerEmail}&gt;</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Message</label>
              <textarea
                value={reminderMessage}
                onChange={(e) => setReminderMessage(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-lg h-40"
              />
            </div>
            <div className="flex justify-end gap-2">
              <ActionButton variant="secondary" onClick={() => setShowReminderModal(false)}>
                Cancel
              </ActionButton>
              <ActionButton variant="primary" onClick={handleSendReminder}>
                <Send size={14} />
                Send Notice
              </ActionButton>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default FinanceOfficerPaymentTracking;