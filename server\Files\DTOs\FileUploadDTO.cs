using System;

namespace Final_E_Receipt.Files.DTOs
{
    public class FileUploadDTO
    {
        public string Id { get; set; }
        public string FileName { get; set; }
        public string OriginalFileName { get; set; }
        public string ContentType { get; set; }
        public long FileSize { get; set; }
        public string UploadedBy { get; set; }
        public string OrganizationId { get; set; }
        public string RelatedEntityType { get; set; }
        public string RelatedEntityId { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsActive { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
    }

    public class CreateFileUploadDTO
    {
        public string RelatedEntityType { get; set; }
        public string RelatedEntityId { get; set; }
        public string OrganizationId { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
    }

    public class FileUploadResponseDTO
    {
        public string Id { get; set; }
        public string FileName { get; set; }
        public string OriginalFileName { get; set; }
        public string ContentType { get; set; }
        public long FileSize { get; set; }
        public string RelatedEntityType { get; set; }
        public string RelatedEntityId { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public bool IsScanned { get; set; }
        public string ScanResult { get; set; }
    }
}


