using System.ComponentModel.DataAnnotations;

namespace Final_E_Receipt.Notifications.DTOs
{
    public class CreateEmailConfigurationDTO
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } // e.g., "Gmail Configuration"

        [StringLength(500)]
        public string Description { get; set; } // e.g., "Gmail SMTP for company emails"

        [Required]
        [StringLength(255)]
        public string SmtpServer { get; set; }

        [Required]
        [Range(1, 65535)]
        public int Port { get; set; }

        [Required]
        [StringLength(255)]
        public string Username { get; set; }

        [Required]
        [StringLength(255)]
        public string Password { get; set; }

        public bool EnableSsl { get; set; } = true;

        public bool IsDefault { get; set; } = false;

        public bool? IsActive { get; set; } = true;

        [Required]
        [StringLength(100)]
        public string SenderName { get; set; }

        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string SenderEmail { get; set; }
    }

    public class UpdateEmailConfigurationDTO
    {
        [StringLength(100)]
        public string Name { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [StringLength(255)]
        public string SmtpServer { get; set; }

        [Range(1, 65535)]
        public int? Port { get; set; }

        [StringLength(255)]
        public string Username { get; set; }

        [StringLength(255)]
        public string Password { get; set; }

        public bool? EnableSsl { get; set; }

        public bool? IsDefault { get; set; }

        public bool? IsActive { get; set; }

        [StringLength(100)]
        public string SenderName { get; set; }

        [EmailAddress]
        [StringLength(255)]
        public string SenderEmail { get; set; }
    }

    public class EmailConfigurationSelectionDTO
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsDefault { get; set; }
        public bool IsActive { get; set; }
        public string SenderName { get; set; }
        public string SenderEmail { get; set; }
    }
}
