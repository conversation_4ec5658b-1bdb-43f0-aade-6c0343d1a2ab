import React, { useState } from 'react';
import { Download, FileText, FileSpreadsheet, Loader2 } from 'lucide-react';

interface ExportButtonsProps {
  onExportCSV: () => Promise<void>;
  onExportExcel: () => Promise<void>;
  disabled?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'buttons' | 'dropdown';
}

const ExportButtons: React.FC<ExportButtonsProps> = ({
  onExportCSV,
  onExportExcel,
  disabled = false,
  className = '',
  size = 'md',
  variant = 'buttons',
}) => {
  const [exportingCSV, setExportingCSV] = useState(false);
  const [exportingExcel, setExportingExcel] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  const handleExportCSV = async () => {
    try {
      setExportingCSV(true);
      await onExportCSV();
    } catch (error) {
      console.error('CSV export failed:', error);
    } finally {
      setExportingCSV(false);
      setShowDropdown(false);
    }
  };

  const handleExportExcel = async () => {
    try {
      setExportingExcel(true);
      await onExportExcel();
    } catch (error) {
      console.error('Excel export failed:', error);
    } finally {
      setExportingExcel(false);
      setShowDropdown(false);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm';
      case 'lg':
        return 'px-6 py-3 text-base';
      default:
        return 'px-4 py-2 text-sm';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 14;
      case 'lg':
        return 20;
      default:
        return 16;
    }
  };

  const isLoading = exportingCSV || exportingExcel;

  if (variant === 'dropdown') {
    return (
      <div className={`relative ${className}`}>
        <button
          onClick={() => setShowDropdown(!showDropdown)}
          disabled={disabled || isLoading}
          className={`inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#2aa45c] disabled:opacity-50 disabled:cursor-not-allowed bg-[#2aa45c] text-white hover:bg-[#076934] ${getSizeClasses()}`}
        >
          {isLoading ? (
            <Loader2 size={getIconSize()} className="animate-spin mr-2" />
          ) : (
            <Download size={getIconSize()} className="mr-2" />
          )}
          Export
        </button>

        {showDropdown && (
          <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
            <div className="py-1">
              <button
                onClick={handleExportCSV}
                disabled={disabled || isLoading}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {exportingCSV ? (
                  <Loader2 size={16} className="animate-spin mr-3" />
                ) : (
                  <FileText size={16} className="mr-3 text-green-600" />
                )}
                Export as CSV
              </button>
              <button
                onClick={handleExportExcel}
                disabled={disabled || isLoading}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {exportingExcel ? (
                  <Loader2 size={16} className="animate-spin mr-3" />
                ) : (
                  <FileSpreadsheet size={16} className="mr-3 text-blue-600" />
                )}
                Export as Excel
              </button>
            </div>
          </div>
        )}

        {/* Backdrop to close dropdown */}
        {showDropdown && (
          <div
            className="fixed inset-0 z-0"
            onClick={() => setShowDropdown(false)}
          />
        )}
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <button
        onClick={handleExportCSV}
        disabled={disabled || isLoading}
        className={`inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed bg-green-600 text-white hover:bg-green-700 ${getSizeClasses()}`}
      >
        {exportingCSV ? (
          <Loader2 size={getIconSize()} className="animate-spin mr-2" />
        ) : (
          <FileText size={getIconSize()} className="mr-2" />
        )}
        CSV
      </button>

      <button
        onClick={handleExportExcel}
        disabled={disabled || isLoading}
        className={`inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed bg-blue-600 text-white hover:bg-blue-700 ${getSizeClasses()}`}
      >
        {exportingExcel ? (
          <Loader2 size={getIconSize()} className="animate-spin mr-2" />
        ) : (
          <FileSpreadsheet size={getIconSize()} className="mr-2" />
        )}
        Excel
      </button>
    </div>
  );
};

export default ExportButtons;
