import React, { useState, useEffect } from 'react';
import { Upload, Download, Trash2, Eye, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { fileUploadService, FileListItem, FileUploadResponse } from '../../services/fileUploadService';
import FileUpload from './FileUpload';

interface PaymentProofUploadProps {
  paymentId: string;
  canUpload?: boolean;
  canValidate?: boolean;
  canDelete?: boolean;
  onProofUploaded?: (file: FileUploadResponse) => void;
  onProofValidated?: (fileId: string, isValid: boolean) => void;
}

export const PaymentProofUpload: React.FC<PaymentProofUploadProps> = ({
  paymentId,
  canUpload = true,
  canValidate = false,
  canDelete = false,
  onProofUploaded,
  onProofValidated
}) => {
  const [files, setFiles] = useState<FileListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showUpload, setShowUpload] = useState(false);
  const [validatingFile, setValidatingFile] = useState<string | null>(null);

  useEffect(() => {
    loadFiles();
  }, [paymentId]);

  const loadFiles = async () => {
    try {
      setLoading(true);
      const proofFiles = await fileUploadService.getPaymentProofFiles(paymentId);
      setFiles(proofFiles);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load files');
    } finally {
      setLoading(false);
    }
  };

  const handleUploadSuccess = async (file: FileUploadResponse) => {
    setShowUpload(false);
    await loadFiles(); // Reload files to get updated list
    onProofUploaded?.(file);
  };

  const handleUploadError = (error: string) => {
    setError(error);
  };

  const handleDownload = async (file: FileListItem) => {
    try {
      const blob = await fileUploadService.downloadPaymentProof(paymentId, file.id);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.originalFileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to download file');
    }
  };

  const handleDelete = async (file: FileListItem) => {
    if (!confirm(`Are you sure you want to delete ${file.originalFileName}?`)) {
      return;
    }

    try {
      await fileUploadService.deletePaymentProof(paymentId, file.id);
      await loadFiles(); // Reload files
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete file');
    }
  };

  const handleValidate = async (file: FileListItem, isValid: boolean) => {
    const comments = prompt(`Please provide comments for ${isValid ? 'approving' : 'rejecting'} this proof:`);
    if (comments === null) return; // User cancelled

    try {
      setValidatingFile(file.id);
      await fileUploadService.validatePaymentProof(paymentId, file.id, isValid, comments);
      await loadFiles(); // Reload files to get updated status
      onProofValidated?.(file.id, isValid);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to validate proof');
    } finally {
      setValidatingFile(null);
    }
  };

  const getStatusIcon = (file: FileListItem) => {
    if (file.scanResult === 'VALIDATED_APPROVED') {
      return <CheckCircle size={16} className="text-green-500" />;
    } else if (file.scanResult === 'VALIDATED_REJECTED') {
      return <XCircle size={16} className="text-red-500" />;
    } else if (file.isScanned) {
      return <AlertCircle size={16} className="text-yellow-500" />;
    }
    return null;
  };

  const getStatusText = (file: FileListItem) => {
    if (file.scanResult === 'VALIDATED_APPROVED') {
      return 'Approved';
    } else if (file.scanResult === 'VALIDATED_REJECTED') {
      return 'Rejected';
    } else if (file.isScanned) {
      return 'Pending Review';
    }
    return 'Uploaded';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading files...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Payment Proof Documents</h3>
        {canUpload && (
          <button
            onClick={() => setShowUpload(!showUpload)}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Upload size={16} className="mr-2" />
            Upload Proof
          </button>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-400 hover:text-red-600"
            >
              <XCircle size={16} />
            </button>
          </div>
        </div>
      )}

      {showUpload && canUpload && (
        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="text-md font-medium text-gray-900 mb-3">Upload Payment Proof</h4>
          <FileUpload
            onUploadSuccess={handleUploadSuccess}
            onUploadError={handleUploadError}
            accept=".pdf,.jpg,.jpeg,.png"
            maxSize={10}
            multiple={false}
          />
        </div>
      )}

      {files.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <Upload size={48} className="mx-auto mb-4 text-gray-300" />
          <p>No payment proof documents uploaded yet.</p>
          {canUpload && (
            <p className="text-sm mt-2">Click "Upload Proof" to add documents.</p>
          )}
        </div>
      ) : (
        <div className="space-y-3">
          {files.map((file) => (
            <div key={file.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  {getStatusIcon(file)}
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {file.originalFileName}
                  </p>
                  <p className="text-xs text-gray-500">
                    {fileUploadService.formatFileSize(file.fileSize)} • 
                    Uploaded {new Date(file.createdAt).toLocaleDateString()} • 
                    {getStatusText(file)}
                  </p>
                  {file.description && (
                    <p className="text-xs text-gray-600 mt-1">{file.description}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleDownload(file)}
                  className="text-blue-600 hover:text-blue-800"
                  title="Download"
                >
                  <Download size={16} />
                </button>

                {canValidate && !file.scanResult?.startsWith('VALIDATED_') && (
                  <>
                    <button
                      onClick={() => handleValidate(file, true)}
                      disabled={validatingFile === file.id}
                      className="text-green-600 hover:text-green-800 disabled:opacity-50"
                      title="Approve"
                    >
                      <CheckCircle size={16} />
                    </button>
                    <button
                      onClick={() => handleValidate(file, false)}
                      disabled={validatingFile === file.id}
                      className="text-red-600 hover:text-red-800 disabled:opacity-50"
                      title="Reject"
                    >
                      <XCircle size={16} />
                    </button>
                  </>
                )}

                {canDelete && (
                  <button
                    onClick={() => handleDelete(file)}
                    className="text-red-600 hover:text-red-800"
                    title="Delete"
                  >
                    <Trash2 size={16} />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default PaymentProofUpload;
