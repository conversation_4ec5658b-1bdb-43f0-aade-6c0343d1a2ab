using System;

namespace Final_E_Receipt.Receipts.Models
{
    public class Receipt
    {
        public string Id { get; set; }
        public string PayerId { get; set; }
        public string PayerName { get; set; }
        public string PayerEmail { get; set; }
        public string ReceiptNumber { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "NGN";
        public DateTime PaymentDate { get; set; }
        public string PaymentMethod { get; set; }
        public string Status { get; set; } // Pending, Completed, Failed
        public string Description { get; set; }
        public string Category { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public string OrganizationId { get; set; }
        public string PaymentId { get; set; } // Link to the payment this receipt is for
        public bool IsRevoked { get; set; } = false;
        public DateTime? RevokedDate { get; set; }
        public string RevokedReason { get; set; }
        public string RevokedBy { get; set; }
        public string ReasonCategory { get; set; } // ADD IF NEEDED
        public bool NotificationSent { get; set; } = false;
        public DateTime? NotificationSentDate { get; set; }
    }
}


