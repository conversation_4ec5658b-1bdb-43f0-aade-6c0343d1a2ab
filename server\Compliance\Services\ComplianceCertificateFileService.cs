using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Final_E_Receipt.Compliance.Models;
using Final_E_Receipt.Compliance.DTOs;
using Final_E_Receipt.Files.Services;
using Final_E_Receipt.Files.Models;
using Final_E_Receipt.Files.DTOs;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Compliance.Services
{
    public class ComplianceCertificateFileService
    {
        private readonly ComplianceCertificateService _certificateService;
        private readonly FileService _fileService;
        private readonly ILogger<ComplianceCertificateFileService> _logger;

        public ComplianceCertificateFileService(
            ComplianceCertificateService certificateService,
            FileService fileService,
            ILogger<ComplianceCertificateFileService> logger)
        {
            _certificateService = certificateService;
            _fileService = fileService;
            _logger = logger;
        }

        /// <summary>
        /// Creates a compliance certificate with file integration
        /// </summary>
        public async Task<ComplianceCertificateWithFilesDTO> CreateCertificateWithFiles(CreateComplianceCertificateDTO dto, string createdBy)
        {
            try
            {
                // Create the certificate
                var certificate = await _certificateService.CreateComplianceCertificate(dto, createdBy);
                if (certificate == null)
                    return null;

                // Get supporting files (if any exist from related payments)
                var supportingFiles = await GetCertificateSupportingFiles(certificate.Id);
                var stats = await GetCertificateFileStats(certificate.Id);

                return new ComplianceCertificateWithFilesDTO
                {
                    Certificate = MapToResponseDTO(certificate),
                    SupportingDocuments = supportingFiles.Select(MapFileToListDTO).ToList(),
                    Stats = stats
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating certificate with files");
                return null;
            }
        }

        /// <summary>
        /// Gets certificate with all associated files
        /// </summary>
        public async Task<ComplianceCertificateWithFilesDTO> GetCertificateWithFiles(string certificateId)
        {
            try
            {
                var certificate = await _certificateService.GetComplianceCertificateById(certificateId);
                if (certificate == null)
                    return null;

                var supportingFiles = await GetCertificateSupportingFiles(certificateId);
                var certificatePdf = await GetCertificatePdfFile(certificate.CertificatePdfFileId);
                var stats = await GetCertificateFileStats(certificateId);

                return new ComplianceCertificateWithFilesDTO
                {
                    Certificate = MapToResponseDTO(certificate),
                    SupportingDocuments = supportingFiles.Select(MapFileToListDTO).ToList(),
                    CertificatePdf = certificatePdf != null ? MapFileToListDTO(certificatePdf) : null,
                    Stats = stats
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting certificate with files for {CertificateId}", certificateId);
                return null;
            }
        }

        /// <summary>
        /// Uploads supporting document for a certificate
        /// </summary>
        public async Task<FileUpload> UploadSupportingDocument(
            string certificateId,
            IFormFile file,
            string uploadedBy,
            string organizationId,
            string description = null,
            string category = null)
        {
            try
            {
                var certificate = await _certificateService.GetComplianceCertificateById(certificateId);
                if (certificate == null)
                    throw new ArgumentException("Certificate not found");

                var uploadedFile = await _fileService.UploadFile(
                    file,
                    "CERTIFICATE",
                    certificateId,
                    uploadedBy,
                    organizationId,
                    description ?? "Certificate supporting document",
                    category ?? "SUPPORTING_DOCUMENT"
                );

                _logger.LogInformation(
                    "Supporting document uploaded for certificate {CertificateNumber}: {FileName}",
                    certificate.CertificateNumber, uploadedFile.OriginalFileName);

                return uploadedFile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading supporting document for certificate {CertificateId}", certificateId);
                throw;
            }
        }

        /// <summary>
        /// Stores generated certificate PDF
        /// </summary>
        public async Task<FileUpload> StoreCertificatePdf(
            string certificateId,
            byte[] pdfContent,
            string fileName,
            string uploadedBy,
            string organizationId)
        {
            try
            {
                var certificate = await _certificateService.GetComplianceCertificateById(certificateId);
                if (certificate == null)
                    throw new ArgumentException("Certificate not found");

                // Create a temporary file from byte array
                var tempFilePath = Path.GetTempFileName();
                await File.WriteAllBytesAsync(tempFilePath, pdfContent);

                // Create IFormFile from temp file
                using var stream = new FileStream(tempFilePath, FileMode.Open);
                var formFile = new FormFile(stream, 0, stream.Length, "file", fileName)
                {
                    Headers = new Microsoft.AspNetCore.Http.HeaderDictionary(),
                    ContentType = "application/pdf"
                };

                var uploadedFile = await _fileService.UploadFile(
                    formFile,
                    "CERTIFICATE",
                    certificateId,
                    uploadedBy,
                    organizationId,
                    $"Generated certificate PDF for {certificate.CertificateNumber}",
                    "CERTIFICATE_PDF"
                );

                // Update certificate with PDF file ID
                await _certificateService.UpdateCertificatePdfFile(certificateId, uploadedFile.Id, uploadedBy);

                // Clean up temp file
                File.Delete(tempFilePath);

                _logger.LogInformation(
                    "Certificate PDF stored for {CertificateNumber}: {FileId}",
                    certificate.CertificateNumber, uploadedFile.Id);

                return uploadedFile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error storing certificate PDF for {CertificateId}", certificateId);
                throw;
            }
        }

        /// <summary>
        /// Gets all supporting files for a certificate
        /// </summary>
        public async Task<List<FileUpload>> GetCertificateSupportingFiles(string certificateId)
        {
            try
            {
                return await _fileService.GetFilesByEntity("CERTIFICATE", certificateId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supporting files for certificate {CertificateId}", certificateId);
                return new List<FileUpload>();
            }
        }

        /// <summary>
        /// Gets the certificate PDF file
        /// </summary>
        public async Task<FileUpload> GetCertificatePdfFile(string fileId)
        {
            if (string.IsNullOrEmpty(fileId))
                return null;

            try
            {
                return await _fileService.GetFileById(fileId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting certificate PDF file {FileId}", fileId);
                return null;
            }
        }

        /// <summary>
        /// Downloads certificate PDF
        /// </summary>
        public async Task<byte[]> DownloadCertificatePdf(string certificateId)
        {
            try
            {
                var certificate = await _certificateService.GetComplianceCertificateById(certificateId);
                if (certificate == null || string.IsNullOrEmpty(certificate.CertificatePdfFileId))
                    throw new FileNotFoundException("Certificate PDF not found");

                return await _fileService.DownloadFile(certificate.CertificatePdfFileId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading certificate PDF for {CertificateId}", certificateId);
                throw;
            }
        }

        /// <summary>
        /// Deletes a supporting document
        /// </summary>
        public async Task<bool> DeleteSupportingDocument(string fileId, string certificateId)
        {
            try
            {
                var file = await _fileService.GetFileById(fileId);
                if (file == null || file.RelatedEntityId != certificateId)
                    return false;

                return await _fileService.DeleteFile(fileId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting supporting document {FileId} for certificate {CertificateId}", fileId, certificateId);
                return false;
            }
        }

        /// <summary>
        /// Gets file statistics for a certificate
        /// </summary>
        public async Task<ComplianceCertificateStatsDTO> GetCertificateFileStats(string certificateId)
        {
            try
            {
                var supportingFiles = await GetCertificateSupportingFiles(certificateId);
                var certificate = await _certificateService.GetComplianceCertificateById(certificateId);

                return new ComplianceCertificateStatsDTO
                {
                    TotalSupportingFiles = supportingFiles.Count,
                    TotalFileSize = supportingFiles.Sum(f => f.FileSize),
                    HasCertificatePdf = !string.IsNullOrEmpty(certificate?.CertificatePdfFileId),
                    PaymentProofFiles = supportingFiles.Count(f => f.Category == "PROOF"),
                    AdditionalDocuments = supportingFiles.Count(f => f.Category == "SUPPORTING_DOCUMENT"),
                    LastFileUpload = supportingFiles.Any() ? supportingFiles.Max(f => f.CreatedAt) : null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting file stats for certificate {CertificateId}", certificateId);
                return new ComplianceCertificateStatsDTO();
            }
        }

        /// <summary>
        /// Links payment proof files to certificate
        /// </summary>
        public async Task<bool> LinkPaymentProofsToCertificate(string certificateId, List<string> paymentIds)
        {
            try
            {
                var certificate = await _certificateService.GetComplianceCertificateById(certificateId);
                if (certificate == null)
                    return false;

                foreach (var paymentId in paymentIds)
                {
                    var paymentFiles = await _fileService.GetFilesByEntity("PAYMENT", paymentId);
                    _logger.LogInformation(
                        "Linked {FileCount} payment proof files from payment {PaymentId} to certificate {CertificateNumber}",
                        paymentFiles.Count, paymentId, certificate.CertificateNumber);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error linking payment proofs to certificate {CertificateId}", certificateId);
                return false;
            }
        }

        private ComplianceCertificateResponseDTO MapToResponseDTO(ComplianceCertificate certificate)
        {
            return new ComplianceCertificateResponseDTO
            {
                Id = certificate.Id,
                CertificateNumber = certificate.CertificateNumber,
                OrganizationId = certificate.OrganizationId,
                OrganizationName = certificate.OrganizationName,
                PaymentProfileId = certificate.PaymentProfileId,
                PaymentProfileName = certificate.PaymentProfileName,
                CertificateType = certificate.CertificateType,
                Status = certificate.Status,
                TotalAmount = certificate.TotalAmount,
                Currency = certificate.Currency,
                ValidFrom = certificate.ValidFrom,
                ValidUntil = certificate.ValidUntil,
                IssuedDate = certificate.IssuedDate,
                IssuedBy = certificate.IssuedBy,
                Description = certificate.Description,
                Terms = certificate.Terms,
                CertificatePdfFileId = certificate.CertificatePdfFileId,
                IsRevoked = certificate.IsRevoked,
                RevokedDate = certificate.RevokedDate,
                RevokedBy = certificate.RevokedBy,
                RevokedReason = certificate.RevokedReason,
                CreatedAt = certificate.CreatedAt,
                CreatedBy = certificate.CreatedBy,
                ComplianceYear = certificate.ComplianceYear,
                CompliancePeriod = certificate.CompliancePeriod,
                RegulatoryBody = certificate.RegulatoryBody,
                LicenseCategory = certificate.LicenseCategory,
                Notes = certificate.Notes
            };
        }

        private FileListDTO MapFileToListDTO(FileUpload file)
        {
            return new FileListDTO
            {
                Id = file.Id,
                FileName = file.FileName,
                OriginalFileName = file.OriginalFileName,
                ContentType = file.ContentType,
                FileSize = file.FileSize,
                RelatedEntityType = file.RelatedEntityType,
                RelatedEntityId = file.RelatedEntityId,
                CreatedAt = file.CreatedAt,
                Description = file.Description,
                Category = file.Category,
                IsScanned = file.IsScanned,
                ScanResult = file.ScanResult,
                UploadedBy = file.UploadedBy
            };
        }
 
   }
}








