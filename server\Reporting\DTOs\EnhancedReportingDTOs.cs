namespace Final_E_Receipt.Reporting.DTOs
{
    public class ComplianceStatusFilterDTO
    {
        public string OrganizationId { get; set; }
        public string ComplianceLevel { get; set; }
        public bool? IsCompliant { get; set; }
        public int? MinComplianceScore { get; set; }
        public int? MaxComplianceScore { get; set; }
        public DateTime? LastCertificateFrom { get; set; }
        public DateTime? LastCertificateTo { get; set; }
        public bool IncludeExpiring { get; set; } = true;
        public int ExpiringWithinDays { get; set; } = 30;
        public string SortBy { get; set; } = "ComplianceScore";
        public string SortDirection { get; set; } = "DESC";
    }

    public class ReportExportRequestDTO
    {
        public string ReportType { get; set; }
        public string Format { get; set; }
        public string OrganizationId { get; set; }
        public Dictionary<string, object> Filters { get; set; } = new Dictionary<string, object>();
        public bool IncludeCharts { get; set; } = false;
        public string FileName { get; set; }
    }
}