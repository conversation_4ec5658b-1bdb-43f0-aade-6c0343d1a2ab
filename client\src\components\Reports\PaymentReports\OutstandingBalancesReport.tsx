import React, { useState, useEffect } from 'react';
import { AlertTriangle, Clock, DollarSign, TrendingDown, RefreshCw, Calendar } from 'lucide-react';
import { usePaymentReportingApi, useOrganizationApi } from '../../../hooks/api';
import ReportFilters, { type FilterOptions } from '../Common/ReportFilters';
import ReportTable, { type TableColumn } from '../Common/ReportTable';
import ReportSummaryCard from '../Common/ReportSummaryCard';
import ExportButtons from '../Common/ExportButtons';

interface OutstandingBalance {
  organizationId: string;
  organizationName: string;
  paymentProfileId: string;
  paymentProfileName: string;
  outstandingAmount: number;
  currency: string;
  dueDate: string;
  daysOverdue: number;
  urgencyLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  payerName: string;
  payerEmail: string;
  lastPaymentDate?: string;
  agingBucket: 'CURRENT' | '1_30_DAYS' | '31_60_DAYS' | '61_90_DAYS' | 'OVER_90_DAYS';
}

interface OutstandingBalancesSummary {
  totalOutstanding: number;
  totalCount: number;
  agingAnalysis: {
    current: { count: number; amount: number };
    days1_30: { count: number; amount: number };
    days31_60: { count: number; amount: number };
    days61_90: { count: number; amount: number };
    over90Days: { count: number; amount: number };
  };
  urgencyBreakdown: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
}

const OutstandingBalancesReport: React.FC = () => {
  const { getOutstandingBalancesReport, exportOutstandingBalancesReport, loading } = usePaymentReportingApi();
  const { getAllOrganizations } = useOrganizationApi();
  
  const [filters, setFilters] = useState<FilterOptions>({});
  const [balanceData, setBalanceData] = useState<OutstandingBalance[]>([]);
  const [summary, setSummary] = useState<OutstandingBalancesSummary | null>(null);
  const [organizations, setOrganizations] = useState<Array<{ id: string; name: string }>>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    pageSize: 50,
    totalCount: 0,
  });
  const [sortBy, setSortBy] = useState<string>('daysOverdue');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    loadOrganizations();
    loadOutstandingBalances();
  }, [filters, pagination.currentPage, sortBy, sortDirection]);

  const loadOrganizations = async () => {
    const orgs = await getAllOrganizations();
    if (orgs) {
      setOrganizations(orgs.map(org => ({ id: org.id, name: org.name })));
    }
  };

  const loadOutstandingBalances = async () => {
    // Mock API call - replace with actual backend call
    const result = await getOutstandingBalancesReport({
      ...filters,
      sortBy,
      sortDirection,
      pageNumber: pagination.currentPage,
      pageSize: pagination.pageSize,
    });

    if (result) {
      setBalanceData(result.data || []);
      setSummary(result.summary || null);
      setPagination(prev => ({
        ...prev,
        totalPages: result.totalPages || 1,
        totalCount: result.totalCount || 0,
      }));
    }
  };

  const handleSort = (column: string, direction: 'asc' | 'desc') => {
    setSortBy(column);
    setSortDirection(direction);
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  const handleExportCSV = async () => {
    await exportOutstandingBalancesReport('csv', filters);
  };

  const handleExportExcel = async () => {
    await exportOutstandingBalancesReport('excel', filters);
  };

  const getUrgencyBadge = (urgency: string) => {
    const urgencyConfig = {
      LOW: { bg: 'bg-green-100', text: 'text-green-800', label: 'Low' },
      MEDIUM: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Medium' },
      HIGH: { bg: 'bg-orange-100', text: 'text-orange-800', label: 'High' },
      CRITICAL: { bg: 'bg-red-100', text: 'text-red-800', label: 'Critical' },
    };

    const config = urgencyConfig[urgency as keyof typeof urgencyConfig] || urgencyConfig.LOW;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const getAgingBadge = (bucket: string) => {
    const agingConfig = {
      CURRENT: { bg: 'bg-green-100', text: 'text-green-800', label: 'Current' },
      '1_30_DAYS': { bg: 'bg-yellow-100', text: 'text-yellow-800', label: '1-30 Days' },
      '31_60_DAYS': { bg: 'bg-orange-100', text: 'text-orange-800', label: '31-60 Days' },
      '61_90_DAYS': { bg: 'bg-red-100', text: 'text-red-800', label: '61-90 Days' },
      'OVER_90_DAYS': { bg: 'bg-red-200', text: 'text-red-900', label: '90+ Days' },
    };

    const config = agingConfig[bucket as keyof typeof agingConfig] || agingConfig.CURRENT;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const formatCurrency = (amount: number, currency: string = 'NGN') => {
    return `${currency === 'NGN' ? '₦' : currency} ${amount.toLocaleString()}`;
  };

  const formatDaysOverdue = (days: number) => {
    if (days <= 0) return 'Current';
    if (days === 1) return '1 day';
    return `${days} days`;
  };

  const columns: TableColumn[] = [
    {
      key: 'organizationName',
      label: 'Organization',
      sortable: true,
    },
    {
      key: 'paymentProfileName',
      label: 'Payment Profile',
      sortable: true,
    },
    {
      key: 'payerName',
      label: 'Payer',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm text-gray-500">{row.payerEmail}</div>
        </div>
      ),
    },
    {
      key: 'outstandingAmount',
      label: 'Outstanding Amount',
      sortable: true,
      render: (value, row) => (
        <span className="font-medium text-red-600">{formatCurrency(value, row.currency)}</span>
      ),
    },
    {
      key: 'dueDate',
      label: 'Due Date',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'daysOverdue',
      label: 'Days Overdue',
      sortable: true,
      render: (value) => (
        <span className={`font-medium ${value > 0 ? 'text-red-600' : 'text-green-600'}`}>
          {formatDaysOverdue(value)}
        </span>
      ),
    },
    {
      key: 'agingBucket',
      label: 'Aging',
      sortable: true,
      render: (value) => getAgingBadge(value),
    },
    {
      key: 'urgencyLevel',
      label: 'Urgency',
      sortable: true,
      render: (value) => getUrgencyBadge(value),
    },
  ];

  const urgencyOptions = [
    { value: 'LOW', label: 'Low' },
    { value: 'MEDIUM', label: 'Medium' },
    { value: 'HIGH', label: 'High' },
    { value: 'CRITICAL', label: 'Critical' },
  ];

  const agingOptions = [
    { value: 'CURRENT', label: 'Current' },
    { value: '1_30_DAYS', label: '1-30 Days' },
    { value: '31_60_DAYS', label: '31-60 Days' },
    { value: '61_90_DAYS', label: '61-90 Days' },
    { value: 'OVER_90_DAYS', label: '90+ Days' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 rounded-lg bg-red-100 flex items-center justify-center">
            <AlertTriangle className="h-5 w-5 text-red-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Outstanding Balances Report</h2>
            <p className="text-gray-600">Track overdue payments with aging analysis and urgency levels</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={loadOutstandingBalances}
            disabled={loading}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2 disabled:opacity-50"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
          <ExportButtons
            onExportCSV={handleExportCSV}
            onExportExcel={handleExportExcel}
            disabled={loading}
            variant="dropdown"
          />
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <ReportSummaryCard
            title="Total Outstanding"
            value={`₦${summary.totalOutstanding.toLocaleString()}`}
            icon={DollarSign}
            color="red"
            loading={loading}
          />
          <ReportSummaryCard
            title="Total Accounts"
            value={summary.totalCount}
            icon={Clock}
            color="yellow"
            loading={loading}
          />
          <ReportSummaryCard
            title="Critical Cases"
            value={summary.urgencyBreakdown.critical}
            icon={AlertTriangle}
            color="red"
            loading={loading}
          />
          <ReportSummaryCard
            title="90+ Days Overdue"
            value={`₦${summary.agingAnalysis.over90Days.amount.toLocaleString()}`}
            icon={TrendingDown}
            color="red"
            loading={loading}
          />
        </div>
      )}

      {/* Aging Analysis */}
      {summary && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Aging Analysis</h3>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {[
              { key: 'current', label: 'Current', color: 'green' },
              { key: 'days1_30', label: '1-30 Days', color: 'yellow' },
              { key: 'days31_60', label: '31-60 Days', color: 'orange' },
              { key: 'days61_90', label: '61-90 Days', color: 'red' },
              { key: 'over90Days', label: '90+ Days', color: 'red' },
            ].map((bucket) => {
              const data = summary.agingAnalysis[bucket.key as keyof typeof summary.agingAnalysis];
              return (
                <div key={bucket.key} className="text-center">
                  <div className={`text-2xl font-bold text-${bucket.color}-600`}>
                    {data.count}
                  </div>
                  <div className="text-sm text-gray-500">{bucket.label}</div>
                  <div className="text-sm font-medium text-gray-900">
                    ₦{data.amount.toLocaleString()}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Filters */}
      <ReportFilters
        filters={filters}
        onFiltersChange={setFilters}
        organizations={organizations}
        statusOptions={urgencyOptions}
        categoryOptions={agingOptions}
        showStatusFilter={true}
        showCategoryFilter={true}
        showPayerFilter={true}
      />

      {/* Data Table */}
      <ReportTable
        columns={columns}
        data={balanceData}
        loading={loading}
        sortBy={sortBy}
        sortDirection={sortDirection}
        onSort={handleSort}
        pagination={{
          ...pagination,
          onPageChange: handlePageChange,
        }}
        emptyMessage="No outstanding balances found for the selected filters"
      />
    </div>
  );
};

export default OutstandingBalancesReport;
