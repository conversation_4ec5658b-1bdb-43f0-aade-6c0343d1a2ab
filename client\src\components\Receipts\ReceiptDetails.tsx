import React, { useState, useEffect } from 'react';
import {
  Receipt,
  Edit,
  ArrowLeft,
  DollarSign,
  Calendar,
  Building2,
  CreditCard,
  CheckCircle,
  XCircle,
  AlertCircle,
  Activity,
  Download,
  FileText,
  Paperclip,
  Ban,
  Eye,
  Printer,
} from 'lucide-react';
import { useReceiptApi, useOrganizationApi, usePaymentApi } from '../../hooks/api';
import { useAuth } from '../../contexts/AuthContext';
import type { Receipt as ReceiptType, Organization, Payment } from '../../hooks/api';

interface ReceiptDetailsProps {
  receipt: ReceiptType;
  setActiveTab: (tab: string) => void;
  setSelectedReceipt: (receipt: ReceiptType | null) => void;
  onReceiptUpdated?: () => void;
}

const ReceiptDetails: React.FC<ReceiptDetailsProps> = ({
  receipt,
  setActiveTab,
  setSelectedReceipt,
  onReceiptUpdated,
}) => {
  const { 
    loading, 
    error, 
    revokeReceipt,
    generateReceiptPDF 
  } = useReceiptApi();
  const { getAllOrganizations } = useOrganizationApi();
  const { getAllPayments } = usePaymentApi();
  const { user } = useAuth();
  
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [payment, setPayment] = useState<Payment | null>(null);
  const [activeSection, setActiveSection] = useState<'overview' | 'activity'>('overview');
  const [showRevokeModal, setShowRevokeModal] = useState(false);
  const [revokeReason, setRevokeReason] = useState('');

  // Role-based permissions
  const canEditReceipts = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');
  const canRevokeReceipts = ['JTB_ADMIN', 'SENIOR_FINANCE_OFFICER'].includes(user?.role || '');

  useEffect(() => {
    loadOrganization();
    loadPayment();
  }, [receipt.organizationId, receipt.paymentId]);

  const loadOrganization = async () => {
    const result = await getAllOrganizations();
    if (result) {
      const org = result.find(o => o.id === receipt.organizationId);
      setOrganization(org || null);
    }
  };

  const loadPayment = async () => {
    const result = await getAllPayments();
    if (result) {
      const pay = result.find(p => p.id === receipt.paymentId);
      setPayment(pay || null);
    }
  };

  const handleEdit = () => {
    setSelectedReceipt(receipt);
    setActiveTab('edit-receipt');
  };

  const handleBack = () => {
    setSelectedReceipt(null);
    setActiveTab('receipts');
  };

  const handleRevoke = async () => {
    if (!revokeReason.trim()) {
      return;
    }
    
    const result = await revokeReceipt(receipt.id, revokeReason);
    if (result) {
      setShowRevokeModal(false);
      setRevokeReason('');
      onReceiptUpdated?.();
    }
  };

  const handleDownloadPDF = async () => {
    try {
      const pdfBlob = await generateReceiptPDF(receipt.id);
      if (pdfBlob) {
        const url = window.URL.createObjectURL(pdfBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `receipt-${receipt.receiptNumber}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Error downloading PDF:', error);
    }
  };

  const handlePrintReceipt = () => {
    window.print();
  };

  const getStatusBadge = (isRevoked: boolean) => {
    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
        isRevoked 
          ? 'bg-red-100 text-red-800' 
          : 'bg-green-100 text-green-800'
      }`}>
        {isRevoked ? (
          <>
            <XCircle className="w-4 h-4 mr-2" />
            Revoked
          </>
        ) : (
          <>
            <CheckCircle className="w-4 h-4 mr-2" />
            Active
          </>
        )}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleBack}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <ArrowLeft size={24} />
          </button>
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
              <Receipt className="h-6 w-6 text-[#2aa45c]" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-[#045024]">{receipt.receiptNumber}</h2>
              <div className="flex items-center space-x-2">
                {getStatusBadge(receipt.isRevoked)}
                <span className="text-gray-500">•</span>
                <span className="text-sm text-gray-500">
                  Issued {new Date(receipt.issueDate).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleDownloadPDF}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Download size={16} />
            <span>Download PDF</span>
          </button>
          <button
            onClick={handlePrintReceipt}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
          >
            <Printer size={16} />
            <span>Print</span>
          </button>
          {canRevokeReceipts && !receipt.isRevoked && (
            <button
              onClick={() => setShowRevokeModal(true)}
              disabled={loading}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
            >
              <Ban size={16} />
              <span>Revoke</span>
            </button>
          )}
          {canEditReceipts && !receipt.isRevoked && (
            <button
              onClick={handleEdit}
              className="bg-[#2aa45c] text-white px-4 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2"
            >
              <Edit size={16} />
              <span>Edit</span>
            </button>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={20} />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: Receipt },
            { id: 'activity', label: 'Activity', icon: Activity },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveSection(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeSection === tab.id
                  ? 'border-[#2aa45c] text-[#2aa45c]'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content Sections */}
      {activeSection === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Receipt Information */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Receipt Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Receipt Number</label>
                  <p className="mt-1 text-sm text-gray-900">{receipt.receiptNumber}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Issue Date</label>
                  <div className="flex items-center mt-1">
                    <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                    <p className="text-sm text-gray-900">
                      {new Date(receipt.issueDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Amount</label>
                  <div className="flex items-center mt-1">
                    <DollarSign className="h-4 w-4 text-gray-400 mr-1" />
                    <p className="text-sm font-medium text-gray-900">
                      {receipt.amount.toLocaleString()} {receipt.currency}
                    </p>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Status</label>
                  <div className="mt-1">
                    {getStatusBadge(receipt.isRevoked)}
                  </div>
                </div>
                {receipt.description && (
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-500">Description</label>
                    <p className="mt-1 text-sm text-gray-900">{receipt.description}</p>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Organization & Payment</h3>
              <div className="space-y-4">
                {organization && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Organization</label>
                    <div className="flex items-center space-x-3 mt-1">
                      <div className="h-8 w-8 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
                        <Building2 className="h-4 w-4 text-[#2aa45c]" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{organization.name}</p>
                        <p className="text-sm text-gray-500">{organization.contactEmail}</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {payment && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Payment</label>
                    <div className="flex items-center space-x-3 mt-1">
                      <div className="h-8 w-8 rounded-lg bg-blue-100 flex items-center justify-center">
                        <CreditCard className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{payment.reference}</p>
                        <p className="text-sm text-gray-500">{payment.paymentMethod}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {receipt.attachmentUrl && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Attachment</h3>
                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 bg-gray-100 rounded flex items-center justify-center">
                      <Paperclip className="h-5 w-5 text-gray-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Receipt Attachment</p>
                      <p className="text-sm text-gray-500">Click to view or download</p>
                    </div>
                  </div>
                  <button
                    onClick={() => window.open(receipt.attachmentUrl, '_blank')}
                    className="text-[#2aa45c] hover:text-[#076934] transition-colors"
                  >
                    <Eye size={20} />
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Status and System Information */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">System Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Created By</label>
                  <p className="mt-1 text-sm text-gray-900">{receipt.createdBy}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Created Date</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(receipt.createdAt).toLocaleDateString()}
                  </p>
                </div>
                {receipt.updatedAt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Last Updated</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(receipt.updatedAt).toLocaleDateString()}
                    </p>
                  </div>
                )}
                {receipt.isRevoked && receipt.revokedBy && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Revoked By</label>
                      <p className="mt-1 text-sm text-gray-900">{receipt.revokedBy}</p>
                    </div>
                    {receipt.revokedDate && (
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Revoked Date</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {new Date(receipt.revokedDate).toLocaleDateString()}
                        </p>
                      </div>
                    )}
                    {receipt.revokeReason && (
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Revoke Reason</label>
                        <p className="mt-1 text-sm text-gray-900">{receipt.revokeReason}</p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeSection === 'activity' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Receipt Activity</h3>
          <div className="text-center py-8">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Activity tracking coming soon</p>
          </div>
        </div>
      )}

      {/* Revoke Modal */}
      {showRevokeModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <Ban className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4 text-center">Revoke Receipt</h3>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reason for revocation *
                </label>
                <textarea
                  value={revokeReason}
                  onChange={(e) => setRevokeReason(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  placeholder="Please provide a reason for revoking this receipt..."
                />
              </div>
              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowRevokeModal(false)}
                  className="flex-1 px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300"
                >
                  Cancel
                </button>
                <button
                  onClick={handleRevoke}
                  disabled={!revokeReason.trim() || loading}
                  className="flex-1 px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
                >
                  {loading ? 'Revoking...' : 'Revoke Receipt'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReceiptDetails;
