-- File Uploads Table
CREATE TABLE FileUploads (
    Id NVARCHAR(50) PRIMARY KEY,
    FileName NVARCHAR(255) NOT NULL,
    OriginalFileName NVARCHAR(255) NOT NULL,
    FilePath NVARCHAR(500) NOT NULL,
    ContentType NVARCHAR(100) NOT NULL,
    FileSize BIGINT NOT NULL,
    FileHash NVARCHAR(64) NOT NULL,
    UploadedBy NVARCHAR(50) NOT NULL,
    OrganizationId NVARCHAR(50),
    RelatedEntityType NVARCHAR(50), -- PAYMENT, RECEIPT, CERTIFICATE
    RelatedEntityId NVARCHAR(50),
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    IsActive BIT NOT NULL DEFAULT 1,
    IsScanned BIT NOT NULL DEFAULT 0,
    ScanResult NVARCHAR(100),
    ScannedAt DATETIME NULL,
    Description NVARCHAR(500),
    Category NVARCHAR(100)
);

-- Create indexes for better performance
CREATE INDEX IX_FileUploads_RelatedEntity ON FileUploads(RelatedEntityType, RelatedEntityId);
CREATE INDEX IX_FileUploads_Organization ON FileUploads(OrganizationId);
CREATE INDEX IX_FileUploads_UploadedBy ON FileUploads(UploadedBy);
CREATE INDEX IX_FileUploads_CreatedAt ON FileUploads(CreatedAt);

-- Stored Procedures for File Uploads
CREATE PROCEDURE CreateFileUpload
    @Id NVARCHAR(50),
    @FileName NVARCHAR(255),
    @OriginalFileName NVARCHAR(255),
    @FilePath NVARCHAR(500),
    @ContentType NVARCHAR(100),
    @FileSize BIGINT,
    @FileHash NVARCHAR(64),
    @UploadedBy NVARCHAR(50),
    @OrganizationId NVARCHAR(50),
    @RelatedEntityType NVARCHAR(50),
    @RelatedEntityId NVARCHAR(50),
    @Description NVARCHAR(500),
    @Category NVARCHAR(100)
AS
BEGIN
    INSERT INTO FileUploads (
        Id, FileName, OriginalFileName, FilePath, ContentType, FileSize, FileHash,
        UploadedBy, OrganizationId, RelatedEntityType, RelatedEntityId, Description, Category
    )
    VALUES (
        @Id, @FileName, @OriginalFileName, @FilePath, @ContentType, @FileSize, @FileHash,
        @UploadedBy, @OrganizationId, @RelatedEntityType, @RelatedEntityId, @Description, @Category
    )
    
    SELECT * FROM FileUploads WHERE Id = @Id
END;

CREATE PROCEDURE GetFileUploadById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM FileUploads WHERE Id = @Id AND IsActive = 1
END;

CREATE PROCEDURE GetFileUploadsByEntity
    @RelatedEntityType NVARCHAR(50),
    @RelatedEntityId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM FileUploads 
    WHERE RelatedEntityType = @RelatedEntityType 
    AND RelatedEntityId = @RelatedEntityId 
    AND IsActive = 1
    ORDER BY CreatedAt DESC
END;

CREATE PROCEDURE GetFileUploadsByOrganization
    @OrganizationId NVARCHAR(50),
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT * FROM FileUploads 
    WHERE OrganizationId = @OrganizationId AND IsActive = 1
    ORDER BY CreatedAt DESC
    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END;

CREATE PROCEDURE UpdateFileScanResult
    @Id NVARCHAR(50),
    @ScanResult NVARCHAR(100)
AS
BEGIN
    UPDATE FileUploads 
    SET IsScanned = 1, ScanResult = @ScanResult, ScannedAt = GETDATE()
    WHERE Id = @Id
    
    SELECT * FROM FileUploads WHERE Id = @Id
END;

CREATE PROCEDURE DeleteFileUpload
    @Id NVARCHAR(50)
AS
BEGIN
    UPDATE FileUploads SET IsActive = 0 WHERE Id = @Id
    SELECT @@ROWCOUNT as RowsAffected
END;

CREATE PROCEDURE GetFileUploadsByUser
    @UploadedBy NVARCHAR(50),
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT * FROM FileUploads 
    WHERE UploadedBy = @UploadedBy AND IsActive = 1
    ORDER BY CreatedAt DESC
    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END;
