# File Upload System

This module provides secure file upload functionality for the Payment Management System, specifically designed for handling payment proof documents and other related files.

## Features

- **Secure File Upload**: Validates file types, sizes, and content
- **Payment Proof Integration**: Specialized endpoints for payment proof documents
- **File Management**: Upload, download, delete, and list files
- **Access Control**: Role-based permissions for file operations
- **File Validation**: Automatic validation of file types and sizes
- **Audit Trail**: Tracks who uploaded files and when
- **Virus Scanning Ready**: Framework for integrating virus scanning

## Architecture

### Models
- `FileUpload`: Core file metadata model
- Tracks file information, relationships, and scan status

### Services
- `FileService`: Core file operations (upload, download, delete)
- `PaymentProofService`: Specialized service for payment proof documents

### Controllers
- `FileController`: Generic file operations API
- `PaymentProofController`: Payment-specific file operations API

### Database
- `FileUploads` table with proper indexing
- Stored procedures for all operations
- Foreign key relationships to related entities

## Configuration

Add to `appsettings.json`:

```json
{
  "FileUpload": {
    "UploadPath": "uploads",
    "MaxFileSize": 10485760,
    "AllowedExtensions": [".pdf", ".jpg", ".jpeg", ".png", ".doc", ".docx"],
    "AllowedContentTypes": [
      "application/pdf",
      "image/jpeg", 
      "image/png",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ]
  }
}
```

## API Endpoints

### Payment Proof Endpoints
- `POST /api/payments/{paymentId}/proof/upload` - Upload payment proof
- `GET /api/payments/{paymentId}/proof` - List payment proof files
- `GET /api/payments/{paymentId}/proof/{fileId}/download` - Download proof file
- `DELETE /api/payments/{paymentId}/proof/{fileId}` - Delete proof file
- `POST /api/payments/{paymentId}/proof/{fileId}/validate` - Validate proof file

### Generic File Endpoints
- `POST /api/file/upload` - Upload any file
- `GET /api/file/{id}` - Get file metadata
- `GET /api/file/download/{id}` - Download file
- `GET /api/file/entity/{entityType}/{entityId}` - List files by entity
- `DELETE /api/file/{id}` - Delete file

## Security Features

1. **File Type Validation**: Only allows specific file types
2. **File Size Limits**: Configurable maximum file size
3. **Content Type Checking**: Validates MIME types
4. **Access Control**: Role-based permissions
5. **Path Traversal Protection**: Secure file path handling
6. **File Hash Calculation**: SHA256 hashing for integrity

## Usage Examples

### Upload Payment Proof (Frontend)
```typescript
import { fileUploadService } from '../services/fileUploadService';

const uploadProof = async (paymentId: string, file: File) => {
  try {
    const result = await fileUploadService.uploadPaymentProof(
      paymentId, 
      file, 
      'Payment proof for invoice #123'
    );
    console.log('Upload successful:', result);
  } catch (error) {
    console.error('Upload failed:', error);
  }
};
```

### Validate Payment Proof (Finance Officer)
```typescript
const validateProof = async (paymentId: string, fileId: string, isValid: boolean) => {
  try {
    await fileUploadService.validatePaymentProof(
      paymentId, 
      fileId, 
      isValid, 
      'Proof document verified and approved'
    );
  } catch (error) {
    console.error('Validation failed:', error);
  }
};
```

## Database Schema

```sql
CREATE TABLE FileUploads (
    Id NVARCHAR(50) PRIMARY KEY,
    FileName NVARCHAR(255) NOT NULL,
    OriginalFileName NVARCHAR(255) NOT NULL,
    FilePath NVARCHAR(500) NOT NULL,
    ContentType NVARCHAR(100) NOT NULL,
    FileSize BIGINT NOT NULL,
    FileHash NVARCHAR(64) NOT NULL,
    UploadedBy NVARCHAR(50) NOT NULL,
    OrganizationId NVARCHAR(50),
    RelatedEntityType NVARCHAR(50),
    RelatedEntityId NVARCHAR(50),
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    IsActive BIT NOT NULL DEFAULT 1,
    IsScanned BIT NOT NULL DEFAULT 0,
    ScanResult NVARCHAR(100),
    ScannedAt DATETIME NULL,
    Description NVARCHAR(500),
    Category NVARCHAR(100)
);
```

## File Storage Structure

```
uploads/
├── payment/
│   ├── 2024/
│   │   ├── 01/
│   │   │   ├── file1.pdf
│   │   │   └── file2.jpg
│   │   └── 02/
│   └── 2023/
├── receipt/
└── certificate/
```

## Integration Points

1. **Payment Workflow**: Automatically updates payment status when proof is uploaded
2. **Receipt Generation**: Links uploaded files to generated receipts
3. **Compliance Certificates**: Supports certificate-related document uploads
4. **Audit Logging**: Integrates with system audit trail
5. **Email Notifications**: Can trigger notifications on file upload/validation

## Testing

Use the `FileUploadTest` component to test the functionality:

1. Start the backend server
2. Navigate to the test component
3. Try uploading various file types
4. Test validation and download features
5. Check file storage in the uploads directory

## Future Enhancements

1. **Virus Scanning**: Integrate with antivirus services
2. **Cloud Storage**: Support for AWS S3, Azure Blob Storage
3. **Image Processing**: Thumbnail generation for images
4. **OCR Integration**: Extract text from uploaded documents
5. **Digital Signatures**: Support for digitally signed documents
6. **Bulk Upload**: Support for uploading multiple files at once
7. **File Versioning**: Track file versions and changes
