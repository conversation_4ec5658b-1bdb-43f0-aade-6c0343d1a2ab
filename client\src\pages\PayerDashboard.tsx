import React from 'react';
import { 
  User, 
  CreditCard, 
  Receipt, 
  DollarSign
} from 'lucide-react';
import type { LucideIcon } from 'lucide-react';
import Navbar from '../components/NavBaar';

// Import your separated components
import Overview from '../components/Payer/Overview';
import PayerProfiles from '../components/Payer/Profile';
import PaymentTypes from '../components/Payer/PaymentProfile';
import ReceiptComponent from '../components/Payer/Receipts';
import { Navigate, Route, Routes } from 'react-router-dom';
import { NavLink } from 'react-router-dom';
import Footer from '../components/Footer';

interface TabButtonProps {
  id: string;
  label: string;
  icon: LucideIcon;
}


// Tab Button Component
const TabButton: React.FC<TabButtonProps> = ({ id, label, icon: Icon }) => (
    <NavLink
    to={`/payerdashboard/${id}`}
      className={({isActive})=> `flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all ${
        isActive 
          ? 'bg-[#045024] text-white shadow-md' 
          : 'text-[#045024] hover:bg-[#2aa45c] hover:bg-opacity-10'
      }`}
    >
      <Icon size={18} />
      {label}
    </NavLink>
  );

// Main Dashboard Component
const PayerDashboard: React.FC = () => {
  return (
    <>
      <Navbar />
      <div className="min-h-screen pt-16" style={{ backgroundColor: '#dddeda' }}>
        <div className="max-w-7xl mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2" style={{ color: '#045024' }}>
              Payer Dashboard
            </h1>
            <p className="text-gray-600">Manage your payments, profiles, and receipts</p>
          </div>
          
          {/* Navigation Tabs */}
          <div >
            <div className="mb-8">
              <div className="flex flex-wrap gap-2">
                <TabButton
                  id="overview"
                  label="Overview"
                  icon={CreditCard}
                                  />
                <TabButton
                  id="profile"
                  label="Profile"
                  icon={User}
                  
                />
                <TabButton
                  id="payments"
                  label="Payments"
                  icon={DollarSign}
                />
                <TabButton
                  id="receipts"
                  label="Receipts"
                  icon={Receipt}
                />
              </div>
            </div>
          </div>

          {/* Dynamic Content */}
          <div className="min-h-96">
            <Routes>   
             <Route  path='' element={ <Navigate to={'overview'}/>}/>
              <Route  path='overview' element={ <Overview/>}/>
              <Route  path='profile' element={ <PayerProfiles/>}/>
              <Route  path='payments' element={ <PaymentTypes/>}/>
              <Route  path='receipts' element={ <ReceiptComponent/>}/>
            </Routes>
          </div>
        </div>
      </div>
      <Footer/>
    </>
  );
};

export default PayerDashboard;