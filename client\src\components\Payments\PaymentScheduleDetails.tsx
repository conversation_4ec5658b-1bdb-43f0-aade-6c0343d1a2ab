import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Edit,
  ArrowLeft,
  DollarSign,
  Building2,
  User,
  CheckCircle,
  XCircle,
  AlertCircle,
  Activity,
  RefreshCw,
  Clock,
  CreditCard,
  Play,
  Pause,
} from 'lucide-react';
import { usePaymentScheduleApi, useOrganizationApi, usePaymentProfileApi } from '../../hooks/api';
import { useAuth } from '../../contexts/AuthContext';
import type { PaymentSchedule, Organization, PaymentProfile } from '../../hooks/api';

interface PaymentScheduleDetailsProps {
  paymentSchedule: PaymentSchedule;
  setActiveTab: (tab: string) => void;
  setSelectedPaymentSchedule: (schedule: PaymentSchedule | null) => void;
  onPaymentScheduleUpdated?: () => void;
}

const PaymentScheduleDetails: React.FC<PaymentScheduleDetailsProps> = ({
  paymentSchedule,
  setActiveTab,
  setSelectedPaymentSchedule,
  onPaymentScheduleUpdated,
}) => {
  const { 
    loading, 
    error, 
    activatePaymentSchedule, 
    deactivatePaymentSchedule 
  } = usePaymentScheduleApi();
  const { getAllOrganizations } = useOrganizationApi();
  const { getAllPaymentProfiles } = usePaymentProfileApi();
  const { user } = useAuth();
  
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [paymentProfile, setPaymentProfile] = useState<PaymentProfile | null>(null);
  const [activeSection, setActiveSection] = useState<'overview' | 'payments' | 'activity'>('overview');
  const [upcomingPayments, setUpcomingPayments] = useState<string[]>([]);

  // Role-based permissions
  const canEditSchedules = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');
  const canActivateSchedules = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');

  useEffect(() => {
    loadOrganization();
    loadPaymentProfile();
    calculateUpcomingPayments();
  }, [paymentSchedule]);

  const loadOrganization = async () => {
    const result = await getAllOrganizations();
    if (result) {
      const org = result.find(o => o.id === paymentSchedule.organizationId);
      setOrganization(org || null);
    }
  };

  const loadPaymentProfile = async () => {
    const result = await getAllPaymentProfiles();
    if (result) {
      const profile = result.find(p => p.id === paymentSchedule.paymentProfileId);
      setPaymentProfile(profile || null);
    }
  };

  const calculateUpcomingPayments = () => {
    if (!paymentSchedule.nextPaymentDate) {
      setUpcomingPayments([]);
      return;
    }

    const dates: string[] = [];
    let currentDate = new Date(paymentSchedule.nextPaymentDate);
    const endDate = paymentSchedule.endDate ? new Date(paymentSchedule.endDate) : null;

    // Calculate next 10 payment dates
    for (let i = 0; i < 10; i++) {
      if (endDate && currentDate > endDate) break;
      
      dates.push(currentDate.toLocaleDateString());
      
      switch (paymentSchedule.frequency) {
        case 'MONTHLY':
          currentDate.setMonth(currentDate.getMonth() + 1);
          break;
        case 'QUARTERLY':
          currentDate.setMonth(currentDate.getMonth() + 3);
          break;
        case 'ANNUALLY':
          currentDate.setFullYear(currentDate.getFullYear() + 1);
          break;
      }
    }

    setUpcomingPayments(dates);
  };

  const handleEdit = () => {
    setSelectedPaymentSchedule(paymentSchedule);
    setActiveTab('edit-payment-schedule');
  };

  const handleBack = () => {
    setSelectedPaymentSchedule(null);
    setActiveTab('payment-schedules');
  };

  const handleActivate = async () => {
    const result = await activatePaymentSchedule(paymentSchedule.id);
    if (result) {
      onPaymentScheduleUpdated?.();
    }
  };

  const handleDeactivate = async () => {
    const result = await deactivatePaymentSchedule(paymentSchedule.id);
    if (result) {
      onPaymentScheduleUpdated?.();
    }
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
        isActive 
          ? 'bg-green-100 text-green-800' 
          : 'bg-red-100 text-red-800'
      }`}>
        {isActive ? (
          <>
            <CheckCircle className="w-4 h-4 mr-2" />
            Active
          </>
        ) : (
          <>
            <XCircle className="w-4 h-4 mr-2" />
            Inactive
          </>
        )}
      </span>
    );
  };

  const getFrequencyBadge = (frequency: string) => {
    const colors = {
      MONTHLY: 'bg-blue-100 text-blue-800',
      QUARTERLY: 'bg-purple-100 text-purple-800',
      ANNUALLY: 'bg-orange-100 text-orange-800',
    };

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
        colors[frequency as keyof typeof colors] || 'bg-gray-100 text-gray-800'
      }`}>
        <RefreshCw className="w-4 h-4 mr-2" />
        {frequency}
      </span>
    );
  };

  const getNextPaymentStatus = () => {
    if (!paymentSchedule.nextPaymentDate) return 'Not scheduled';
    
    const nextDate = new Date(paymentSchedule.nextPaymentDate);
    const today = new Date();
    const diffTime = nextDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return { text: `Overdue by ${Math.abs(diffDays)} days`, color: 'text-red-600' };
    } else if (diffDays === 0) {
      return { text: 'Due today', color: 'text-orange-600' };
    } else if (diffDays <= 7) {
      return { text: `Due in ${diffDays} days`, color: 'text-yellow-600' };
    } else {
      return { text: `Due in ${diffDays} days`, color: 'text-green-600' };
    }
  };

  const nextPaymentStatus = getNextPaymentStatus();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleBack}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <ArrowLeft size={24} />
          </button>
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
              <Calendar className="h-6 w-6 text-[#2aa45c]" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-[#045024]">{paymentSchedule.name}</h2>
              <div className="flex items-center space-x-2">
                {getStatusBadge(paymentSchedule.isActive)}
                {getFrequencyBadge(paymentSchedule.frequency)}
                <span className="text-gray-500">•</span>
                <span className="text-sm text-gray-500">
                  Created {new Date(paymentSchedule.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {canActivateSchedules && (
            <button
              onClick={paymentSchedule.isActive ? handleDeactivate : handleActivate}
              disabled={loading}
              className={`${
                paymentSchedule.isActive 
                  ? 'bg-red-600 hover:bg-red-700' 
                  : 'bg-green-600 hover:bg-green-700'
              } text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2 disabled:opacity-50`}
            >
              {paymentSchedule.isActive ? <Pause size={16} /> : <Play size={16} />}
              <span>{paymentSchedule.isActive ? 'Deactivate' : 'Activate'}</span>
            </button>
          )}
          {canEditSchedules && (
            <button
              onClick={handleEdit}
              className="bg-[#2aa45c] text-white px-4 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2"
            >
              <Edit size={16} />
              <span>Edit Schedule</span>
            </button>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={20} />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: Calendar },
            { id: 'payments', label: 'Upcoming Payments', icon: CreditCard },
            { id: 'activity', label: 'Activity', icon: Activity },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveSection(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeSection === tab.id
                  ? 'border-[#2aa45c] text-[#2aa45c]'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content Sections */}
      {activeSection === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Schedule Information */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Schedule Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Schedule Name</label>
                  <p className="mt-1 text-sm text-gray-900">{paymentSchedule.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Frequency</label>
                  <p className="mt-1 text-sm text-gray-900">{paymentSchedule.frequency}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Amount</label>
                  <div className="flex items-center mt-1">
                    <DollarSign className="h-4 w-4 text-gray-400 mr-1" />
                    <p className="text-sm font-medium text-gray-900">
                      {paymentSchedule.amount.toLocaleString()} {paymentSchedule.currency}
                    </p>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Start Date</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(paymentSchedule.startDate).toLocaleDateString()}
                  </p>
                </div>
                {paymentSchedule.endDate && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">End Date</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(paymentSchedule.endDate).toLocaleDateString()}
                    </p>
                  </div>
                )}
                {paymentSchedule.description && (
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-500">Description</label>
                    <p className="mt-1 text-sm text-gray-900">{paymentSchedule.description}</p>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Organization & Profile</h3>
              <div className="space-y-4">
                {organization && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Organization</label>
                    <div className="flex items-center space-x-3 mt-1">
                      <div className="h-8 w-8 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
                        <Building2 className="h-4 w-4 text-[#2aa45c]" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{organization.name}</p>
                        <p className="text-sm text-gray-500">{organization.contactEmail}</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {paymentProfile && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Payment Profile</label>
                    <div className="flex items-center space-x-3 mt-1">
                      <div className="h-8 w-8 rounded-lg bg-blue-100 flex items-center justify-center">
                        <User className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{paymentProfile.name}</p>
                        <p className="text-sm text-gray-500">{paymentProfile.paymentTypes.length} payment types</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Status and Next Payment */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Next Payment</h3>
              <div className="space-y-4">
                {paymentSchedule.nextPaymentDate ? (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Due Date</label>
                      <p className="mt-1 text-sm text-gray-900">
                        {new Date(paymentSchedule.nextPaymentDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Status</label>
                      <p className={`mt-1 text-sm font-medium ${nextPaymentStatus.color}`}>
                        {nextPaymentStatus.text}
                      </p>
                    </div>
                  </>
                ) : (
                  <p className="text-sm text-gray-500">No upcoming payments scheduled</p>
                )}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Schedule Stats</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">Total Payments</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">-</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">Total Amount</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">-</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <RefreshCw className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">Frequency</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{paymentSchedule.frequency}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeSection === 'payments' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Upcoming Payments</h3>
          {upcomingPayments.length > 0 ? (
            <div className="space-y-3">
              {upcomingPayments.map((date, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-between p-4 border rounded-lg ${
                    index === 0 ? 'border-[#2aa45c] bg-[#2aa45c] bg-opacity-5' : 'border-gray-200'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                      index === 0 ? 'bg-[#2aa45c] text-white' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Payment {index + 1}</p>
                      <p className="text-sm text-gray-500">{date}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {paymentSchedule.amount.toLocaleString()} {paymentSchedule.currency}
                    </p>
                    {index === 0 && (
                      <p className="text-xs text-[#2aa45c]">Next payment</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500">No upcoming payments scheduled</p>
          )}
        </div>
      )}

      {activeSection === 'activity' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Schedule Activity</h3>
          <div className="text-center py-8">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Activity tracking coming soon</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentScheduleDetails;
