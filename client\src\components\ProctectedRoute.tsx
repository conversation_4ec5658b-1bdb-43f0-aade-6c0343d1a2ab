// src/components/ProtectedRoute.tsx
import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

import type { UserRole } from "../types";

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: UserRole[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  allowedRoles,
}) => {
  const { isAuthenticated, user, isLoading } = useAuth();
  const location = useLocation();

  // Show loading spinner while authentication is being verified
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        <p className="ml-4 text-gray-600">Verifying authentication...</p>
      </div>
    );
  }

  // If not authenticated, always redirect to main login page
  if (!isAuthenticated) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // If user doesn't exist (shouldn't happen if isAuthenticated is true)
  if (!user) {
    console.error("User is null but isAuthenticated is true - this is a bug");
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // Check if user has required role
  if (allowedRoles && !allowedRoles.includes(user.role as UserRole)) {
    // Get the appropriate dashboard for the user's role
    const getUserDashboard = (role: string) => {
      switch (role) {
        case "JTB_ADMIN":
          return "/admin-dashboard";
        case "FINANCE_OFFICER":
          return "/finance-officer-dashboard";
        case "SENIOR_FINANCE_OFFICER":
          return "/senior-finance-officer-dashboard";
        case "PAYER":
          return "/payerdashboard";
        default:
          console.error(`Unknown role: ${role}`);
          return "/";
      }
    };

    const userDashboard = getUserDashboard(user.role);

    // Only redirect if they're not already on their correct dashboard
    if (!location.pathname.startsWith(userDashboard)) {
      return <Navigate to={userDashboard} replace />;
    }

    // If they're on their correct dashboard but still hitting this,
    // it means the allowedRoles configuration is wrong
    console.error(
      `User with role ${user.role} is trying to access ${location.pathname} ` +
        `but allowedRoles is ${JSON.stringify(allowedRoles)}`
    );

    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">
            Access Denied
          </h1>
          <p className="text-gray-600 mb-4">
            You don't have permission to access this page.
          </p>
          <button
            onClick={() => (window.location.href = userDashboard)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Go to Your Dashboard
          </button>
        </div>
      </div>
    );
  }

  // User is authenticated and has the required role
  return <>{children}</>;
};

export default ProtectedRoute;
