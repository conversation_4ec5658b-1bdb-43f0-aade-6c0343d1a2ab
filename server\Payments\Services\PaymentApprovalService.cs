using System.Data;
using Final_E_Receipt.Services;
using Final_E_Receipt.Payments.Models;
using Final_E_Receipt.Payments.DTOs;
using Final_E_Receipt.Common.Services;
using Final_E_Receipt.Common.Models;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Payments.Services
{
    public class PaymentApprovalService
    {
        private readonly IDatabaseService _dbService;
        private readonly ILogger<PaymentApprovalService> _logger;
        private readonly AuditService _auditService;

        public PaymentApprovalService(IDatabaseService dbService, ILogger<PaymentApprovalService> logger, AuditService auditService)
        {
            _dbService = dbService;
            _logger = logger;
            _auditService = auditService;
        }

        /// <summary>
        /// Finance Officer acknowledges a payment
        /// </summary>
        public async Task<Payment> AcknowledgePayment(string paymentId, string financeOfficerId, string notes = null)
        {
            try
            {
                _logger.LogInformation("Finance Officer {FinanceOfficerId} acknowledging payment {PaymentId}", financeOfficerId, paymentId);

                var parameters = new
                {
                    PaymentId = paymentId,
                    AcknowledgedBy = financeOfficerId,
                    Notes = notes
                };

                var payment = await _dbService.QueryFirstOrDefaultAsync<Payment>("AcknowledgePayment", parameters);

                if (payment == null)
                {
                    _logger.LogWarning("Failed to acknowledge payment {PaymentId}", paymentId);
                    return null;
                }

                // Log payment acknowledgment
                await _auditService.LogAsync(
                    AuditActions.PAYMENT_ACKNOWLEDGED,
                    EntityTypes.PAYMENT,
                    paymentId,
                    new { Status = "Pending" },
                    new { Status = "Acknowledged", AcknowledgedBy = financeOfficerId },
                    $"Payment acknowledged by Finance Officer with notes: {notes ?? "No notes"}"
                );

                _logger.LogInformation("Payment {PaymentId} acknowledged successfully by {FinanceOfficerId}", paymentId, financeOfficerId);
                return payment;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error acknowledging payment {PaymentId} by {FinanceOfficerId}", paymentId, financeOfficerId);
                throw;
            }
        }

        /// <summary>
        /// Senior Finance Officer approves a payment
        /// </summary>
        public async Task<Payment> ApprovePayment(string paymentId, string seniorFinanceOfficerId, string notes = null)
        {
            try
            {
                _logger.LogInformation("Senior Finance Officer {SeniorFinanceOfficerId} approving payment {PaymentId}", seniorFinanceOfficerId, paymentId);

                var parameters = new
                {
                    PaymentId = paymentId,
                    ApprovedBy = seniorFinanceOfficerId,
                    Notes = notes
                };

                var payment = await _dbService.QueryFirstOrDefaultAsync<Payment>("ApprovePayment", parameters);

                if (payment == null)
                {
                    _logger.LogWarning("Failed to approve payment {PaymentId}", paymentId);
                    return null;
                }

                // Log payment approval
                await _auditService.LogAsync(
                    AuditActions.PAYMENT_APPROVED,
                    EntityTypes.PAYMENT,
                    paymentId,
                    new { Status = "Acknowledged" },
                    new { Status = "Approved", ApprovedBy = seniorFinanceOfficerId },
                    $"Payment approved by Senior Finance Officer with notes: {notes ?? "No notes"}"
                );

                _logger.LogInformation("Payment {PaymentId} approved successfully by {SeniorFinanceOfficerId}", paymentId, seniorFinanceOfficerId);
                return payment;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving payment {PaymentId} by {SeniorFinanceOfficerId}", paymentId, seniorFinanceOfficerId);
                throw;
            }
        }

        /// <summary>
        /// Reject a payment (Finance Officer or Senior Finance Officer)
        /// </summary>
        public async Task<Payment> RejectPayment(string paymentId, string rejectedBy, string reason, string notes = null)
        {
            try
            {
                _logger.LogInformation("User {RejectedBy} rejecting payment {PaymentId} with reason: {Reason}", rejectedBy, paymentId, reason);

                var parameters = new
                {
                    PaymentId = paymentId,
                    RejectedBy = rejectedBy,
                    Reason = reason,
                    Notes = notes
                };

                var payment = await _dbService.QueryFirstOrDefaultAsync<Payment>("RejectPayment", parameters);

                if (payment == null)
                {
                    _logger.LogWarning("Failed to reject payment {PaymentId}", paymentId);
                    return null;
                }

                _logger.LogInformation("Payment {PaymentId} rejected successfully by {RejectedBy}", paymentId, rejectedBy);
                return payment;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting payment {PaymentId} by {RejectedBy}", paymentId, rejectedBy);
                throw;
            }
        }

        /// <summary>
        /// Get payments pending acknowledgment (for Finance Officers)
        /// </summary>
        public async Task<List<Payment>> GetPaymentsPendingAcknowledgment(string organizationId = null)
        {
            try
            {
                var parameters = new { OrganizationId = organizationId };
                var payments = await _dbService.QueryAsync<Payment>("GetPaymentsPendingAcknowledgment", parameters);

                _logger.LogInformation("Retrieved {Count} payments pending acknowledgment for organization {OrganizationId}", 
                    payments.Count(), organizationId ?? "ALL");
                return payments.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payments pending acknowledgment for organization {OrganizationId}", organizationId);
                throw;
            }
        }

        /// <summary>
        /// Get payments pending approval (for Senior Finance Officers)
        /// </summary>
        public async Task<List<Payment>> GetPaymentsPendingApproval(string organizationId = null)
        {
            try
            {
                var parameters = new { OrganizationId = organizationId };
                var payments = await _dbService.QueryAsync<Payment>("GetPaymentsPendingApproval", parameters);

                _logger.LogInformation("Retrieved {Count} payments pending approval for organization {OrganizationId}", 
                    payments.Count(), organizationId ?? "ALL");
                return payments.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payments pending approval for organization {OrganizationId}", organizationId);
                throw;
            }
        }

        /// <summary>
        /// Get payment approval history
        /// </summary>
        public async Task<Payment> GetPaymentApprovalHistory(string paymentId)
        {
            try
            {
                var parameters = new { PaymentId = paymentId };
                var payment = await _dbService.QueryFirstOrDefaultAsync<Payment>("GetPaymentApprovalHistory", parameters);

                if (payment == null)
                {
                    _logger.LogWarning("Payment {PaymentId} not found", paymentId);
                    return null;
                }

                return payment;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approval history for payment {PaymentId}", paymentId);
                throw;
            }
        }

        /// <summary>
        /// Get payments by approval status with filters
        /// </summary>
        public async Task<List<Payment>> GetPaymentsByApprovalStatus(string status, string organizationId = null, string userId = null)
        {
            try
            {
                var parameters = new
                {
                    Status = status,
                    OrganizationId = organizationId,
                    UserId = userId
                };

                var payments = await _dbService.QueryAsync<Payment>("GetPaymentsByApprovalStatus", parameters);

                _logger.LogInformation("Retrieved {Count} payments with status {Status} for organization {OrganizationId} and user {UserId}", 
                    payments.Count(), status, organizationId ?? "ALL", userId ?? "ALL");
                return payments.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payments by approval status {Status}", status);
                throw;
            }
        }
    }
}

