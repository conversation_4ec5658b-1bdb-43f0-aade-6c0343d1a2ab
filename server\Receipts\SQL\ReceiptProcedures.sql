-- Receipts Table
CREATE TABLE Receipts (
    Id NVARCHAR(50) PRIMARY KEY,
    PayerId NVARCHAR(50),
    PayerName NVARCHAR(100),
    PayerEmail NVARCHAR(255),
    ReceiptNumber NVARCHAR(50) UNIQUE NOT NULL,
    Amount DECIMAL(18, 2) NOT NULL,
    Currency NVARCHAR(10) NOT NULL DEFAULT 'NGN',
    PaymentDate DATETIME NOT NULL,
    PaymentMethod NVARCHAR(50) NOT NULL,
    Status NVARCHAR(20) NOT NULL,
    Description NVARCHAR(500),
    Category NVARCHAR(100),
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50),
    OrganizationId NVARCHAR(50),
    PaymentId NVARCHAR(50), -- Link to payment
    IsRevoked BIT NOT NULL DEFAULT 0,
    RevokedDate DATETIME NULL,
    RevokedReason NVARCHAR(500) NULL,
    RevokedBy NVARCHAR(50) NULL,
    NotificationSent BIT NOT NULL DEFAULT 0,
    NotificationSentDate DATETIME NULL
);

-- Stored Procedures for Receipts
CREATE PROCEDURE CreateReceipt
    @Id NVARCHAR(50),
    @PayerId NVARCHAR(50),
    @PayerName NVARCHAR(100),
    @PayerEmail NVARCHAR(255),
    @ReceiptNumber NVARCHAR(50),
    @Amount DECIMAL(18, 2),
    @Currency NVARCHAR(10),
    @PaymentDate DATETIME,
    @PaymentMethod NVARCHAR(50),
    @Status NVARCHAR(20),
    @Description NVARCHAR(500),
    @Category NVARCHAR(100),
    @CreatedBy NVARCHAR(50),
    @OrganizationId NVARCHAR(50),
    @PaymentId NVARCHAR(50)
AS
BEGIN
    INSERT INTO Receipts (
        Id, PayerId, PayerName, PayerEmail, ReceiptNumber, Amount, Currency,
        PaymentDate, PaymentMethod, Status, Description, Category, CreatedBy, OrganizationId, PaymentId
    )
    VALUES (
        @Id, @PayerId, @PayerName, @PayerEmail, @ReceiptNumber, @Amount, @Currency,
        @PaymentDate, @PaymentMethod, @Status, @Description, @Category, @CreatedBy, @OrganizationId, @PaymentId
    )
    
    SELECT * FROM Receipts WHERE Id = @Id
END;

CREATE PROCEDURE GetReceiptById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Receipts WHERE Id = @Id
END;

CREATE PROCEDURE GetReceiptsByPayer
    @PayerId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Receipts WHERE PayerId = @PayerId ORDER BY CreatedAt DESC
END;

CREATE PROCEDURE GetReceiptsByOrganization
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Receipts WHERE OrganizationId = @OrganizationId ORDER BY CreatedAt DESC
END;

CREATE PROCEDURE GetReceiptsByDateRange
    @StartDate DATETIME,
    @EndDate DATETIME,
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Receipts 
    WHERE OrganizationId = @OrganizationId 
    AND PaymentDate BETWEEN @StartDate AND @EndDate
    ORDER BY PaymentDate DESC
END;

CREATE PROCEDURE RevokeReceipt
    @Id NVARCHAR(50),
    @RevokedReason NVARCHAR(500),
    @RevokedBy NVARCHAR(50)
AS
BEGIN
    UPDATE Receipts
    SET 
        IsRevoked = 1,
        RevokedDate = GETDATE(),
        RevokedReason = @RevokedReason,
        RevokedBy = @RevokedBy
    WHERE Id = @Id
    
    SELECT * FROM Receipts WHERE Id = @Id
END;

CREATE PROCEDURE MarkReceiptNotificationSent
    @Id NVARCHAR(50)
AS
BEGIN
    UPDATE Receipts
    SET 
        NotificationSent = 1,
        NotificationSentDate = GETDATE()
    WHERE Id = @Id
    
    SELECT * FROM Receipts WHERE Id = @Id
END;

CREATE PROCEDURE SearchReceipts
    @SearchTerm NVARCHAR(100),
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Receipts 
    WHERE OrganizationId = @OrganizationId
    AND (
        ReceiptNumber LIKE '%' + @SearchTerm + '%' OR
        PayerName LIKE '%' + @SearchTerm + '%' OR
        PayerEmail LIKE '%' + @SearchTerm + '%' OR
        Description LIKE '%' + @SearchTerm + '%' OR
        Category LIKE '%' + @SearchTerm + '%'
    )
    ORDER BY CreatedAt DESC
END;