// config/api.ts - Create this file to centralize your API URLs
export const API_BASE_URL = "http://localhost:5248/api";

export const API_ENDPOINTS = {
  LOGIN: `${API_BASE_URL}/api/auth/login`,
  MICROSOFT_AUTH: `${API_BASE_URL}/api/auth/microsoft`,
  LOGOUT: `${API_BASE_URL}/api/auth/logout`,
  VERIFY_TOKEN: `${API_BASE_URL}/api/auth/verify`,
};

// Or if you want to handle different environments:
export const getApiUrl = () => {
  // In production, this might be your production API URL
  if (process.env.NODE_ENV === "production") {
    return "https://your-production-api.com";
  }

  // In development, use localhost
  return "http://localhost:5248";
};
