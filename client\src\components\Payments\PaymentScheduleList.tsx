import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Search,
  Plus,
  Edit,
  Eye,
  Trash2,
  Filter,
  CheckCircle,
  XCircle,
  AlertCircle,
  Building2,
  User,
  Clock,
  DollarSign,
  RefreshCw,
} from 'lucide-react';
import { usePaymentScheduleApi, useOrganizationApi, usePaymentProfileApi } from '../../hooks/api';
import { useAuth } from '../../contexts/AuthContext';
import type { PaymentSchedule, Organization, PaymentProfile } from '../../hooks/api';

interface PaymentScheduleListProps {
  setActiveTab: (tab: string) => void;
  setSelectedPaymentSchedule: (schedule: PaymentSchedule | null) => void;
}

const PaymentScheduleList: React.FC<PaymentScheduleListProps> = ({
  setActiveTab,
  setSelectedPaymentSchedule,
}) => {
  const { loading, error, getAllPaymentSchedules, deletePaymentSchedule } = usePaymentScheduleApi();
  const { getAllOrganizations } = useOrganizationApi();
  const { getAllPaymentProfiles } = usePaymentProfileApi();
  const { user } = useAuth();
  
  const [paymentSchedules, setPaymentSchedules] = useState<PaymentSchedule[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [paymentProfiles, setPaymentProfiles] = useState<PaymentProfile[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [frequencyFilter, setFrequencyFilter] = useState<'all' | 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteModal, setShowDeleteModal] = useState<string | null>(null);
  const schedulesPerPage = 10;

  // Role-based permissions
  const canCreateSchedules = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');
  const canEditSchedules = ['JTB_ADMIN', 'FINANCE_OFFICER'].includes(user?.role || '');
  const canDeleteSchedules = user?.role === 'JTB_ADMIN';

  useEffect(() => {
    loadPaymentSchedules();
    loadOrganizations();
    loadPaymentProfiles();
  }, []);

  const loadPaymentSchedules = async () => {
    const result = await getAllPaymentSchedules();
    if (result) {
      setPaymentSchedules(result);
    }
  };

  const loadOrganizations = async () => {
    const result = await getAllOrganizations();
    if (result) {
      setOrganizations(result);
    }
  };

  const loadPaymentProfiles = async () => {
    const result = await getAllPaymentProfiles();
    if (result) {
      setPaymentProfiles(result);
    }
  };

  const handleDelete = async (id: string) => {
    const result = await deletePaymentSchedule(id);
    if (result) {
      loadPaymentSchedules();
      setShowDeleteModal(null);
    }
  };

  const handleViewDetails = (schedule: PaymentSchedule) => {
    setSelectedPaymentSchedule(schedule);
    setActiveTab('payment-schedule-details');
  };

  const handleEditSchedule = (schedule: PaymentSchedule) => {
    setSelectedPaymentSchedule(schedule);
    setActiveTab('edit-payment-schedule');
  };

  const getOrganizationName = (organizationId: string) => {
    const org = organizations.find(o => o.id === organizationId);
    return org?.name || 'Unknown Organization';
  };

  const getPaymentProfileName = (paymentProfileId: string) => {
    const profile = paymentProfiles.find(p => p.id === paymentProfileId);
    return profile?.name || 'Unknown Profile';
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        isActive 
          ? 'bg-green-100 text-green-800' 
          : 'bg-red-100 text-red-800'
      }`}>
        {isActive ? (
          <>
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </>
        ) : (
          <>
            <XCircle className="w-3 h-3 mr-1" />
            Inactive
          </>
        )}
      </span>
    );
  };

  const getFrequencyBadge = (frequency: string) => {
    const colors = {
      MONTHLY: 'bg-blue-100 text-blue-800',
      QUARTERLY: 'bg-purple-100 text-purple-800',
      ANNUALLY: 'bg-orange-100 text-orange-800',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        colors[frequency as keyof typeof colors] || 'bg-gray-100 text-gray-800'
      }`}>
        <RefreshCw className="w-3 h-3 mr-1" />
        {frequency}
      </span>
    );
  };

  const getNextPaymentDate = (schedule: PaymentSchedule) => {
    if (!schedule.nextPaymentDate) return 'Not scheduled';
    
    const nextDate = new Date(schedule.nextPaymentDate);
    const today = new Date();
    const diffTime = nextDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return `Overdue by ${Math.abs(diffDays)} days`;
    } else if (diffDays === 0) {
      return 'Due today';
    } else if (diffDays <= 7) {
      return `Due in ${diffDays} days`;
    } else {
      return nextDate.toLocaleDateString();
    }
  };

  // Filter schedules based on search, status, and frequency
  const filteredSchedules = paymentSchedules.filter(schedule => {
    const matchesSearch = schedule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         getOrganizationName(schedule.organizationId).toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && schedule.isActive) ||
                         (statusFilter === 'inactive' && !schedule.isActive);
    const matchesFrequency = frequencyFilter === 'all' || schedule.frequency === frequencyFilter;
    return matchesSearch && matchesStatus && matchesFrequency;
  });

  // Calculate pagination
  const totalPages = Math.ceil(filteredSchedules.length / schedulesPerPage);
  const startIndex = (currentPage - 1) * schedulesPerPage;
  const endIndex = startIndex + schedulesPerPage;
  const currentSchedules = filteredSchedules.slice(startIndex, endIndex);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2aa45c]"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-[#045024]">Payment Schedules</h2>
          <p className="text-gray-600">
            {canCreateSchedules ? 'Manage recurring payment schedules and automation' : 'View payment schedule information'}
          </p>
        </div>
        {canCreateSchedules && (
          <button
            onClick={() => setActiveTab('create-payment-schedule')}
            className="bg-[#2aa45c] text-white px-4 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2"
          >
            <Plus size={20} />
            <span>Add Schedule</span>
          </button>
        )}
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search payment schedules..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            <select
              value={frequencyFilter}
              onChange={(e) => setFrequencyFilter(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
            >
              <option value="all">All Frequencies</option>
              <option value="MONTHLY">Monthly</option>
              <option value="QUARTERLY">Quarterly</option>
              <option value="ANNUALLY">Annually</option>
            </select>
            <button
              onClick={() => {}}
              className="bg-[#2aa45c] text-white px-4 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2"
            >
              <Filter size={16} />
              <span>Filter</span>
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={20} />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Payment Schedules Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Schedule
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Organization
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Frequency
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Next Payment
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentSchedules.map((schedule) => (
                <tr key={schedule.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
                          <Calendar className="h-5 w-5 text-[#2aa45c]" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {schedule.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {getPaymentProfileName(schedule.paymentProfileId)}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Building2 className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900">
                        {getOrganizationName(schedule.organizationId)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 text-gray-400 mr-1" />
                      <span className="text-sm font-medium text-gray-900">
                        {schedule.amount.toLocaleString()} {schedule.currency}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getFrequencyBadge(schedule.frequency)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900">
                        {getNextPaymentDate(schedule)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(schedule.isActive)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleViewDetails(schedule)}
                        className="text-[#2aa45c] hover:text-[#076934] transition-colors"
                        title="View Details"
                      >
                        <Eye size={16} />
                      </button>
                      {canEditSchedules && (
                        <button
                          onClick={() => handleEditSchedule(schedule)}
                          className="text-blue-600 hover:text-blue-800 transition-colors"
                          title="Edit"
                        >
                          <Edit size={16} />
                        </button>
                      )}
                      {canDeleteSchedules && (
                        <button
                          onClick={() => setShowDeleteModal(schedule.id)}
                          className="text-red-600 hover:text-red-800 transition-colors"
                          title="Delete"
                        >
                          <Trash2 size={16} />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                  <span className="font-medium">{Math.min(endIndex, filteredSchedules.length)}</span> of{' '}
                  <span className="font-medium">{filteredSchedules.length}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        page === currentPage
                          ? 'z-10 bg-[#2aa45c] border-[#2aa45c] text-white'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <Trash2 className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Delete Payment Schedule</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete this payment schedule? This action cannot be undone.
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowDeleteModal(null)}
                    className="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => handleDelete(showDeleteModal)}
                    className="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentScheduleList;
