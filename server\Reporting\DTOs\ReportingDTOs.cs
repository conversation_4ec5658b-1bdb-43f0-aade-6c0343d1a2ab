using System;

namespace Final_E_Receipt.Reporting.DTOs
{
    public class DateRangeDTO
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    public class YearDTO
    {
        public int Year { get; set; }
    }

    public class YearComparisonDTO
    {
        public int CurrentYear { get; set; }
        public int PreviousYear { get; set; }
    }

    public class TopPayersDTO
    {
        public int Limit { get; set; } = 10;
    }

    public class ExportReportDTO
    {
        public string ReportType { get; set; } // "Monthly", "Daily", "PaymentMethod", "Category", "TopPayers"
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? Year { get; set; }
        public string ExportFormat { get; set; } = "CSV"; // "CSV", "Excel", "PDF"
    }
}