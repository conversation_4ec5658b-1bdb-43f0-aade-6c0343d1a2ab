import React, { useState } from 'react';
import { apiService } from '../services/apiService';

const AdminSetupTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testAdminSetup = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      const response = await apiService.post('/AdminSetup/initialize', {});
      setResult(response);
      console.log('Admin setup successful:', response);
    } catch (err: any) {
      setError(err.message || 'Failed to setup admin');
      console.error('Admin setup failed:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Admin Setup Test</h3>
      
      <button
        onClick={testAdminSetup}
        disabled={loading}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {loading ? 'Setting up Admin...' : 'Initialize Admin User'}
      </button>

      {result && (
        <div className="mt-4 p-3 bg-green-100 border border-green-400 rounded">
          <h4 className="font-semibold text-green-800">Success!</h4>
          <pre className="text-sm text-green-700 mt-2">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}

      {error && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 rounded">
          <h4 className="font-semibold text-red-800">Error!</h4>
          <p className="text-red-700">{error}</p>
        </div>
      )}
    </div>
  );
};

export default AdminSetupTest;
