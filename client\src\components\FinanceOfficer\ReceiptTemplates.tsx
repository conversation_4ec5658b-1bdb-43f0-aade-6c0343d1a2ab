import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { 
  FileText, 
  Plus, 
  Edit2, 
  Trash2, 
  Eye, 
  Check, 
  Save 
} from 'react-feather';
import axios from 'axios';
import { toast } from 'react-toastify';
import ActionButton from '../common/ActionButton';
import Modal from '../common/Modal';
import CodeEditor from '@uiw/react-textarea-code-editor';

interface ReceiptTemplate {
  id: string;
  name: string;
  description: string;
  templateContent: string;
  isDefault: boolean;
  organizationId: string;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

const ReceiptTemplates: React.FC = () => {
  const { organizationId } = useParams<{ organizationId: string }>();
  const [templates, setTemplates] = useState<ReceiptTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ReceiptTemplate | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    templateContent: '',
    isDefault: false
  });
  const [previewHtml, setPreviewHtml] = useState('');

  useEffect(() => {
    fetchTemplates();
  }, [organizationId]);

  const fetchTemplates = async () => {
    try {
      const response = await axios.get(`/api/ReceiptTemplate/organization/${organizationId}`);
      setTemplates(response.data);
    } catch (error) {
      toast.error('Failed to fetch receipt templates');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({ ...formData, [name]: checked });
  };

  const handleCodeChange = (value: string) => {
    setFormData({ ...formData, templateContent: value });
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      templateContent: '',
      isDefault: false
    });
  };

  const handleCreateTemplate = async () => {
    try {
      await axios.post('/api/ReceiptTemplate', {
        ...formData,
        organizationId
      });
      toast.success('Receipt template created successfully');
      setShowCreateModal(false);
      resetForm();
      fetchTemplates();
    } catch (error) {
      toast.error('Failed to create receipt template');
    }
  };

  const handleEditTemplate = async () => {
    if (!selectedTemplate) return;
    
    try {
      await axios.put(`/api/ReceiptTemplate/${selectedTemplate.id}`, formData);
      toast.success('Receipt template updated successfully');
      setShowEditModal(false);
      fetchTemplates();
    } catch (error) {
      toast.error('Failed to update receipt template');
    }
  };

  const handleDeleteTemplate = async () => {
    if (!selectedTemplate) return;
    
    try {
      await axios.delete(`/api/ReceiptTemplate/${selectedTemplate.id}`);
      toast.success('Receipt template deleted successfully');
      setShowDeleteModal(false);
      fetchTemplates();
    } catch (error) {
      toast.error('Failed to delete receipt template');
    }
  };

  const openEditModal = (template: ReceiptTemplate) => {
    setSelectedTemplate(template);
    setFormData({
      name: template.name,
      description: template.description,
      templateContent: template.templateContent,
      isDefault: template.isDefault
    });
    setShowEditModal(true);
  };

  const openDeleteModal = (template: ReceiptTemplate) => {
    setSelectedTemplate(template);
    setShowDeleteModal(true);
  };

  const openPreviewModal = (template: ReceiptTemplate) => {
    setSelectedTemplate(template);
    
    // Replace placeholders with sample data for preview
    const sampleData = {
      ReceiptNumber: 'REC-********-12345',
      PayerName: 'Sample Company Ltd',
      PayerEmail: '<EMAIL>',
      Amount: '5,000.00',
      Currency: 'NGN',
      PaymentDate: '2025-06-15',
      PaymentMethod: 'Bank Transfer',
      Description: 'Annual License Fee',
      Category: 'License',
      IssuedDate: new Date().toISOString().split('T')[0]
    };
    
    let previewContent = template.templateContent;
    Object.entries(sampleData).forEach(([key, value]) => {
      previewContent = previewContent.replace(new RegExp(`{{${key}}}`, 'g'), value);
    });
    
    setPreviewHtml(previewContent);
    setShowPreviewModal(true);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Receipt Templates</h1>
        <ActionButton onClick={() => setShowCreateModal(true)}>
          <Plus size={16} />
          Create New Template
        </ActionButton>
      </div>

      {templates.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <FileText size={48} className="mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-800 mb-2">No Templates Found</h3>
          <p className="text-gray-600 mb-4">Create your first receipt template to get started.</p>
          <ActionButton onClick={() => setShowCreateModal(true)}>
            <Plus size={16} />
            Create Template
          </ActionButton>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map(template => (
            <div key={template.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-800">{template.name}</h3>
                  {template.isDefault && (
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full flex items-center">
                      <Check size={12} className="mr-1" />
                      Default
                    </span>
                  )}
                </div>
                <p className="text-gray-600 mb-4">{template.description}</p>
                <div className="text-sm text-gray-500 mb-4">
                  Created: {new Date(template.createdAt).toLocaleDateString()}
                  {template.updatedAt && (
                    <div>Updated: {new Date(template.updatedAt).toLocaleDateString()}</div>
                  )}
                </div>
              </div>
              <div className="bg-gray-50 px-6 py-3 flex justify-between">
                <button
                  onClick={() => openPreviewModal(template)}
                  className="text-blue-600 hover:text-blue-800 flex items-center"
                >
                  <Eye size={16} className="mr-1" />
                  Preview
                </button>
                <div className="flex space-x-3">
                  <button
                    onClick={() => openEditModal(template)}
                    className="text-gray-600 hover:text-gray-800 flex items-center"
                  >
                    <Edit2 size={16} className="mr-1" />
                    Edit
                  </button>
                  <button
                    onClick={() => openDeleteModal(template)}
                    className="text-red-600 hover:text-red-800 flex items-center"
                  >
                    <Trash2 size={16} className="mr-1" />
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create Template Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="Create Receipt Template"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Template Name</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="e.g., Standard Receipt Template"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <input
              type="text"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="Brief description of this template"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">HTML Template</label>
            <div className="border border-gray-300 rounded-md overflow-hidden">
              <CodeEditor
                value={formData.templateContent || GetDefaultTemplateContent()}
                language="html"
                onChange={(evn) => handleCodeChange(evn.target.value)}
                padding={15}
                style={{
                  fontSize: 14,
                  backgroundColor: "#f5f5f5",
                  fontFamily: 'ui-monospace,SFMono-Regular,SF Mono,Consolas,Liberation Mono,Menlo,monospace',
                  minHeight: '300px'
                }}
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Use placeholders like {{PayerName}}, {{Amount}}, {{ReceiptNumber}}, etc.
            </p>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isDefault"
              name="isDefault"
              checked={formData.isDefault}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
            />
            <label htmlFor="isDefault" className="ml-2 block text-sm text-gray-700">
              Set as default template
            </label>
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <ActionButton variant="secondary" onClick={() => setShowCreateModal(false)}>
              Cancel
            </ActionButton>
            <ActionButton onClick={handleCreateTemplate}