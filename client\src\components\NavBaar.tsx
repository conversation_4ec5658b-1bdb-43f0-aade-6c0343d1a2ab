// import React, { useState } from 'react';
// import {
//   Receipt,
//   User,
//   Bell,
//   Search,
//   Menu,
//   X,
//   ChevronDown,
//   Settings,
//   LogOut,
//   HelpCircle
// } from 'lucide-react';

// interface NavbarProps {
//   userName?: string;
//   userEmail?: string;
//   userRole?: string;
//   onProfileClick?: () => void;
//   onSettingsClick?: () => void;
//   onLogoutClick?: () => void;
//   onHelpClick?: () => void;
//   notificationCount?: number;
// }

// const Navbar: React.FC<NavbarProps> = ({
//   userName = "Teksphere Global Service",
//   userEmail = "<EMAIL>",
//   userRole = "Finance officer",
//   onProfileClick,
//   onSettingsClick,
//   onLogoutClick,
//   onHelpClick,
//   notificationCount = 3
// }) => {
//   const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
//   const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
//   const [searchQuery, setSearchQuery] = useState('');

//   const handleSearch = (e: React.FormEvent) => {
//     e.preventDefault();
//     console.log('Search query:', searchQuery);
//   };

//   return (
//     <nav className="fixed top-0 left-0 w-full z-50 bg-[#045024] shadow-lg border-b-4 border-b-green-400">
//       <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
//         <div className="flex items-center justify-between h-16">
//           {/* Logo and Brand */}
//           <div className="flex items-center">
//             <div className="flex-shrink-0 flex items-center gap-3">
//               <div className="p-2 rounded-lg bg-white">
//                 <Receipt className="text-[#045024]" size={24} />
//               </div>
//               <div>
//                 <h1 className="text-xl font-bold text-white">
//                   E-Receipt
//                 </h1>
//                 <p className="text-xs text-green-100 hidden sm:block">
//                   Payment Management System
//                 </p>
//               </div>
//             </div>
//           </div>

//           {/* Search Bar - Hidden on mobile */}
//           {/* <div className="hidden md:block flex-1 max-w-md mx-8">
//             <div className="relative">
//               <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
//                 <Search className="text-gray-400" size={20} />
//               </div>
//               <input
//                 type="text"
//                 value={searchQuery}
//                 onChange={(e) => setSearchQuery(e.target.value)}
//                 onKeyPress={(e) => {
//                   if (e.key === 'Enter') {
//                     handleSearch(e);
//                   }
//                 }}
//                 placeholder="Search receipts, payments..."
//                 className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-400 focus:border-transparent text-sm bg-white text-gray-900"
//               />
//             </div>
//           </div> */}

//           {/* Right side items */}
//           <div className="flex items-center gap-4">
//             {/* Notifications */}
//             <div className="relative">
//               <button
//                 className="p-2 rounded-lg hover:bg-green-700 hover:bg-opacity-50 relative transition-colors"
//                 onClick={() => console.log('Notifications clicked')}
//               >
//                 <Bell className="text-white" size={20} />
//                 {notificationCount > 0 && (
//                   <span
//                     className="absolute -top-1 -right-1 h-5 w-5 rounded-full text-xs text-white flex items-center justify-center bg-red-500"
//                   >
//                     {notificationCount > 9 ? '9+' : notificationCount}
//                   </span>
//                 )}
//               </button>
//             </div>

//             {/* Profile Dropdown */}
//             <div className="relative">
//               <button
//                 onClick={() => setIsProfileDropdownOpen(!isProfileDropdownOpen)}
//                 className="flex items-center gap-3 p-2 rounded-lg hover:bg-green-700 hover:bg-opacity-50 transition-colors"
//               >
//                 <div className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium bg-green-800">
//                   {userName.split(' ').map(n => n[0]).join('').toUpperCase()}
//                 </div>
//                 <div className="hidden sm:block text-left">
//                   <p className="text-sm font-medium text-white">{userName}</p>
//                   <p className="text-xs text-green-100">{userEmail}</p>
//                   <p className="text-xs text-green-50">{userRole}</p>
//                 </div>
//                 <ChevronDown className="text-green-100" size={16} />
//               </button>

//               {/* Profile Dropdown Menu */}
//               {isProfileDropdownOpen && (
//                 <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
//                   <div className="px-4 py-2 border-b border-gray-100">
//                     <p className="text-sm font-medium text-gray-900">{userName}</p>
//                     <p className="text-xs text-gray-500">{userEmail}</p>
//                   </div>

//                   <button
//                     onClick={() => {
//                       onProfileClick?.();
//                       setIsProfileDropdownOpen(false);
//                     }}
//                     className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
//                   >
//                     <User size={16} />
//                     Profile Settings
//                   </button>

//                   <button
//                     onClick={() => {
//                       onSettingsClick?.();
//                       setIsProfileDropdownOpen(false);
//                     }}
//                     className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
//                   >
//                     <Settings size={16} />
//                     Account Settings
//                   </button>

//                   <button
//                     onClick={() => {
//                       onHelpClick?.();
//                       setIsProfileDropdownOpen(false);
//                     }}
//                     className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
//                   >
//                     <HelpCircle size={16} />
//                     Help & Support
//                   </button>

//                   <hr className="my-2" />

//                   <button
//                     onClick={() => {
//                       onLogoutClick?.();
//                       setIsProfileDropdownOpen(false);
//                     }}
//                     className="flex items-center gap-3 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
//                   >
//                     <LogOut size={16} />
//                     Sign Out
//                   </button>
//                 </div>
//               )}
//             </div>

//             {/* Mobile menu button */}
//             <button
//               onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
//               className="md:hidden p-2 rounded-lg hover:bg-green-700 hover:bg-opacity-50 transition-colors"
//             >
//               {isMobileMenuOpen ? (
//                 <X className="text-white" size={20} />
//               ) : (
//                 <Menu className="text-white" size={20} />
//               )}
//             </button>
//           </div>
//         </div>

//         {/* Mobile menu */}
//         {isMobileMenuOpen && (
//           <div className="md:hidden border-t border-green-600 py-4">
//             {/* Mobile Search */}
//             <div className="px-4 mb-4">
//               <div className="relative">
//                 <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
//                   <Search className="text-gray-400" size={20} />
//                 </div>
//                 <input
//                   type="text"
//                   value={searchQuery}
//                   onChange={(e) => setSearchQuery(e.target.value)}
//                   onKeyPress={(e) => {
//                     if (e.key === 'Enter') {
//                       handleSearch(e);
//                     }
//                   }}
//                   placeholder="Search receipts, payments..."
//                   className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-400 focus:border-transparent text-sm bg-white text-gray-900"
//                 />
//               </div>
//             </div>

//             {/* Mobile Navigation Links */}
//             <div className="space-y-2 px-4">
//               <button
//                 onClick={() => {
//                   onProfileClick?.();
//                   setIsMobileMenuOpen(false);
//                 }}
//                 className="flex items-center gap-3 w-full px-3 py-2 text-sm text-white hover:bg-green-700 hover:bg-opacity-50 rounded-lg transition-colors"
//               >
//                 <User size={16} />
//                 Profile Settings
//               </button>

//               <button
//                 onClick={() => {
//                   onSettingsClick?.();
//                   setIsMobileMenuOpen(false);
//                 }}
//                 className="flex items-center gap-3 w-full px-3 py-2 text-sm text-white hover:bg-green-700 hover:bg-opacity-50 rounded-lg transition-colors"
//               >
//                 <Settings size={16} />
//                 Account Settings
//               </button>

//               <button
//                 onClick={() => {
//                   onHelpClick?.();
//                   setIsMobileMenuOpen(false);
//                 }}
//                 className="flex items-center gap-3 w-full px-3 py-2 text-sm text-white hover:bg-green-700 hover:bg-opacity-50 rounded-lg transition-colors"
//               >
//                 <HelpCircle size={16} />
//                 Help & Support
//               </button>

//               <hr className="my-2 border-green-600" />

//               <button
//                 onClick={() => {
//                   onLogoutClick?.();
//                   setIsMobileMenuOpen(false);
//                 }}
//                 className="flex items-center gap-3 w-full px-3 py-2 text-sm text-red-300 hover:bg-red-900 hover:bg-opacity-30 rounded-lg transition-colors"
//               >
//                 <LogOut size={16} />
//                 Sign Out
//               </button>
//             </div>
//           </div>
//         )}
//       </div>

//       {/* Overlay for mobile dropdown */}
//       {isMobileMenuOpen && (
//         <div
//           className="fixed inset-0 bg-black bg-opacity-25 z-40 md:hidden"
//           onClick={() => setIsMobileMenuOpen(false)}
//         />
//       )}
//     </nav>
//   );
// };

// export default Navbar;

// import React, { useState } from "react";
// import {
//   Receipt,
//   User,
//   Bell,
//   Search,
//   Menu,
//   X,
//   ChevronDown,
//   Settings,
//   LogOut,
//   HelpCircle,
// } from "lucide-react";
// import { useAuth } from "../hooks/useAuth"; // Adjust path as needed

// interface NavbarProps {
//   onProfileClick?: () => void;
//   onSettingsClick?: () => void;
//   onHelpClick?: () => void;
//   notificationCount?: number;
// }

// const Navbar: React.FC<NavbarProps> = ({
//   onProfileClick,
//   onSettingsClick,
//   onHelpClick,
//   notificationCount = 0,
// }) => {
//   const { user, logout, isAuthenticated } = useAuth();
//   const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
//   const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
//   const [searchQuery, setSearchQuery] = useState("");

//   // Helper function to get user's full name
//   const getUserFullName = () => {
//     if (!user) return "Guest User";
//     return `${user.firstName} ${user.lastName}`.trim() || user.email;
//   };

//   // Helper function to get user initials
//   const getUserInitials = () => {
//     if (!user) return "GU";

//     const firstName = user.firstName || "";
//     const lastName = user.lastName || "";

//     if (firstName && lastName) {
//       return `${firstName[0]}${lastName[0]}`.toUpperCase();
//     } else if (firstName) {
//       return firstName.substring(0, 2).toUpperCase();
//     } else if (user.email) {
//       return user.email.substring(0, 2).toUpperCase();
//     }

//     return "U";
//   };

//   // Helper function to format user role for display
//   const formatUserRole = (role: string) => {
//     switch (role) {
//       case "JTB_ADMIN":
//         return "JTB Administrator";
//       case "SENIOR_FINANCE_OFFICER":
//         return "Senior Finance Officer";
//       case "FINANCE_OFFICER":
//         return "Finance Officer";
//       case "PAYER":
//         return "Taxpayer";
//       default:
//         return role
//           .replace(/_/g, " ")
//           .toLowerCase()
//           .replace(/\b\w/g, (l) => l.toUpperCase());
//     }
//   };

//   // Helper function to get role-based color
//   const getRoleColor = (role: string) => {
//     switch (role) {
//       case "JTB_ADMIN":
//         return "bg-purple-600";
//       case "SENIOR_FINANCE_OFFICER":
//         return "bg-blue-600";
//       case "FINANCE_OFFICER":
//         return "bg-indigo-600";
//       case "PAYER":
//         return "bg-green-600";
//       default:
//         return "bg-gray-600";
//     }
//   };

//   const handleSearch = (e: React.FormEvent) => {
//     e.preventDefault();
//     console.log("Search query:", searchQuery);
//     // TODO: Implement search functionality
//   };

//   const handleLogout = async () => {
//     try {
//       await logout();
//       setIsProfileDropdownOpen(false);
//       setIsMobileMenuOpen(false);
//       // Navigation will be handled by the auth context/login page
//     } catch (error) {
//       console.error("Logout failed:", error);
//     }
//   };

//   // Don't render navbar if user is not authenticated
//   if (!isAuthenticated || !user) {
//     return null;
//   }

//   return (
//     <nav className="fixed top-0 left-0 w-full z-50 bg-[#045024] shadow-lg border-b-4 border-b-green-400">
//       <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
//         <div className="flex items-center justify-between h-16">
//           {/* Logo and Brand */}
//           <div className="flex items-center">
//             <div className="flex-shrink-0 flex items-center gap-3">
//               <div className="p-2 rounded-lg bg-white">
//                 <Receipt className="text-[#045024]" size={24} />
//               </div>
//               <div>
//                 <h1 className="text-xl font-bold text-white">E-Receipt</h1>
//                 <p className="text-xs text-green-100 hidden sm:block">
//                   Payment Management System
//                 </p>
//               </div>
//             </div>
//           </div>

//           {/* Search Bar - Hidden on mobile */}
//           <div className="hidden md:block flex-1 max-w-md mx-8">
//             <form onSubmit={handleSearch}>
//               <div className="relative">
//                 <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
//                   <Search className="text-gray-400" size={20} />
//                 </div>
//                 <input
//                   type="text"
//                   value={searchQuery}
//                   onChange={(e) => setSearchQuery(e.target.value)}
//                   placeholder="Search receipts, payments..."
//                   className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-400 focus:border-transparent text-sm bg-white text-gray-900"
//                 />
//               </div>
//             </form>
//           </div>

//           {/* Right side items */}
//           <div className="flex items-center gap-4">
//             {/* Notifications */}
//             <div className="relative">
//               <button
//                 className="p-2 rounded-lg hover:bg-green-700 hover:bg-opacity-50 relative transition-colors"
//                 onClick={() => console.log("Notifications clicked")}
//                 title="Notifications"
//               >
//                 <Bell className="text-white" size={20} />
//                 {notificationCount > 0 && (
//                   <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full text-xs text-white flex items-center justify-center bg-red-500">
//                     {notificationCount > 9 ? "9+" : notificationCount}
//                   </span>
//                 )}
//               </button>
//             </div>

//             {/* Profile Dropdown */}
//             <div className="relative">
//               <button
//                 onClick={() => setIsProfileDropdownOpen(!isProfileDropdownOpen)}
//                 className="flex items-center gap-3 p-2 rounded-lg hover:bg-green-700 hover:bg-opacity-50 transition-colors"
//               >
//                 <div
//                   className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${getRoleColor(
//                     user.role
//                   )}`}
//                 >
//                   {getUserInitials()}
//                 </div>
//                 <div className="hidden sm:block text-left">
//                   <p className="text-sm font-medium text-white">
//                     {getUserFullName()}
//                   </p>
//                   <p className="text-xs text-green-100">{user.email}</p>
//                   <p className="text-xs text-green-50">
//                     {formatUserRole(user.role)}
//                   </p>
//                 </div>
//                 <ChevronDown
//                   className={`text-green-100 transition-transform duration-200 ${
//                     isProfileDropdownOpen ? "rotate-180" : ""
//                   }`}
//                   size={16}
//                 />
//               </button>

//               {/* Profile Dropdown Menu */}
//               {isProfileDropdownOpen && (
//                 <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
//                   <div className="px-4 py-3 border-b border-gray-100">
//                     <p className="text-sm font-medium text-gray-900">
//                       {getUserFullName()}
//                     </p>
//                     <p className="text-xs text-gray-500">{user.email}</p>
//                     <div className="flex items-center gap-2 mt-1">
//                       <span
//                         className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white ${getRoleColor(
//                           user.role
//                         )}`}
//                       >
//                         {formatUserRole(user.role)}
//                       </span>
//                       {user.isActive && (
//                         <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-green-800 bg-green-100">
//                           Active
//                         </span>
//                       )}
//                     </div>
//                     {user.lastLogin && (
//                       <p className="text-xs text-gray-400 mt-1">
//                         Last login:{" "}
//                         {new Date(user.lastLogin).toLocaleDateString()}
//                       </p>
//                     )}
//                   </div>

//                   <button
//                     onClick={() => {
//                       onProfileClick?.();
//                       setIsProfileDropdownOpen(false);
//                     }}
//                     className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
//                   >
//                     <User size={16} />
//                     Profile Settings
//                   </button>

//                   <button
//                     onClick={() => {
//                       onSettingsClick?.();
//                       setIsProfileDropdownOpen(false);
//                     }}
//                     className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
//                   >
//                     <Settings size={16} />
//                     Account Settings
//                   </button>

//                   <button
//                     onClick={() => {
//                       onHelpClick?.();
//                       setIsProfileDropdownOpen(false);
//                     }}
//                     className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
//                   >
//                     <HelpCircle size={16} />
//                     Help & Support
//                   </button>

//                   <hr className="my-2" />

//                   <button
//                     onClick={handleLogout}
//                     className="flex items-center gap-3 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
//                   >
//                     <LogOut size={16} />
//                     Sign Out
//                   </button>
//                 </div>
//               )}
//             </div>

//             {/* Mobile menu button */}
//             <button
//               onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
//               className="md:hidden p-2 rounded-lg hover:bg-green-700 hover:bg-opacity-50 transition-colors"
//             >
//               {isMobileMenuOpen ? (
//                 <X className="text-white" size={20} />
//               ) : (
//                 <Menu className="text-white" size={20} />
//               )}
//             </button>
//           </div>
//         </div>

//         {/* Mobile menu */}
//         {isMobileMenuOpen && (
//           <div className="md:hidden border-t border-green-600 py-4">
//             {/* Mobile User Info */}
//             <div className="px-4 mb-4 pb-4 border-b border-green-600">
//               <div className="flex items-center gap-3">
//                 <div
//                   className={`w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-medium ${getRoleColor(
//                     user.role
//                   )}`}
//                 >
//                   {getUserInitials()}
//                 </div>
//                 <div>
//                   <p className="text-sm font-medium text-white">
//                     {getUserFullName()}
//                   </p>
//                   <p className="text-xs text-green-100">{user.email}</p>
//                   <p className="text-xs text-green-50">
//                     {formatUserRole(user.role)}
//                   </p>
//                 </div>
//               </div>
//             </div>

//             {/* Mobile Search */}
//             <div className="px-4 mb-4">
//               <form onSubmit={handleSearch}>
//                 <div className="relative">
//                   <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
//                     <Search className="text-gray-400" size={20} />
//                   </div>
//                   <input
//                     type="text"
//                     value={searchQuery}
//                     onChange={(e) => setSearchQuery(e.target.value)}
//                     placeholder="Search receipts, payments..."
//                     className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-400 focus:border-transparent text-sm bg-white text-gray-900"
//                   />
//                 </div>
//               </form>
//             </div>

//             {/* Mobile Navigation Links */}
//             <div className="space-y-2 px-4">
//               <button
//                 onClick={() => {
//                   onProfileClick?.();
//                   setIsMobileMenuOpen(false);
//                 }}
//                 className="flex items-center gap-3 w-full px-3 py-2 text-sm text-white hover:bg-green-700 hover:bg-opacity-50 rounded-lg transition-colors"
//               >
//                 <User size={16} />
//                 Profile Settings
//               </button>

//               <button
//                 onClick={() => {
//                   onSettingsClick?.();
//                   setIsMobileMenuOpen(false);
//                 }}
//                 className="flex items-center gap-3 w-full px-3 py-2 text-sm text-white hover:bg-green-700 hover:bg-opacity-50 rounded-lg transition-colors"
//               >
//                 <Settings size={16} />
//                 Account Settings
//               </button>

//               <button
//                 onClick={() => {
//                   onHelpClick?.();
//                   setIsMobileMenuOpen(false);
//                 }}
//                 className="flex items-center gap-3 w-full px-3 py-2 text-sm text-white hover:bg-green-700 hover:bg-opacity-50 rounded-lg transition-colors"
//               >
//                 <HelpCircle size={16} />
//                 Help & Support
//               </button>

//               <hr className="my-2 border-green-600" />

//               <button
//                 onClick={handleLogout}
//                 className="flex items-center gap-3 w-full px-3 py-2 text-sm text-red-300 hover:bg-red-900 hover:bg-opacity-30 rounded-lg transition-colors"
//               >
//                 <LogOut size={16} />
//                 Sign Out
//               </button>
//             </div>
//           </div>
//         )}
//       </div>

//       {/* Click outside to close dropdowns */}
//       {(isProfileDropdownOpen || isMobileMenuOpen) && (
//         <div
//           className="fixed inset-0 z-40"
//           onClick={() => {
//             setIsProfileDropdownOpen(false);
//             setIsMobileMenuOpen(false);
//           }}
//         />
//       )}
//     </nav>
//   );
// };

// export default Navbar;

import React, { useState } from "react";
import { Receipt, Bell, Menu, X, LogOut } from "lucide-react";
import { useAuth } from "../hooks/useAuth"; // Adjust path as needed // Adjust path as needed

interface NavbarProps {
  notificationCount?: number;
}

const Navbar: React.FC<NavbarProps> = ({ notificationCount = 0 }) => {
  const { user, logout, isAuthenticated } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Helper function to get user's full name
  const getUserFullName = () => {
    if (!user) return "Guest User";
    return `${user.firstName} ${user.lastName}`.trim() || user.email;
  };

  // Helper function to get user initials
  const getUserInitials = () => {
    if (!user) return "GU";

    const firstName = user.firstName || "";
    const lastName = user.lastName || "";

    if (firstName && lastName) {
      return `${firstName[0]}${lastName[0]}`.toUpperCase();
    } else if (firstName) {
      return firstName.substring(0, 2).toUpperCase();
    } else if (user.email) {
      return user.email.substring(0, 2).toUpperCase();
    }

    return "U";
  };

  // Helper function to format user role for display
  const formatUserRole = (role: string) => {
    switch (role) {
      case "JTB_ADMIN":
        return "JTB Administrator";
      case "SENIOR_FINANCE_OFFICER":
        return "Senior Finance Officer";
      case "FINANCE_OFFICER":
        return "Finance Officer";
      case "PAYER":
        return "Taxpayer";
      default:
        return role
          .replace(/_/g, " ")
          .toLowerCase()
          .replace(/\b\w/g, (l) => l.toUpperCase());
    }
  };

  // Helper function to get role-based color
  const getRoleColor = (role: string) => {
    switch (role) {
      case "JTB_ADMIN":
        return "bg-purple-600";
      case "SENIOR_FINANCE_OFFICER":
        return "bg-blue-600";
      case "FINANCE_OFFICER":
        return "bg-indigo-600";
      case "PAYER":
        return "bg-green-600";
      default:
        return "bg-gray-600";
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      setIsMobileMenuOpen(false);
      // The logout function should handle navigation
    } catch (error) {
      console.error("Logout failed:", error);
      // Even if logout fails, clear local state and redirect
      window.location.href = "/login";
    }
  };

  // Don't render navbar if user is not authenticated
  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <nav className="fixed top-0 left-0 w-full z-50 bg-[#045024] shadow-lg border-b-4 border-b-green-400">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Brand */}
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center gap-3">
              <div className="p-2 rounded-lg bg-white">
                <Receipt className="text-[#045024]" size={24} />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">E-Receipt</h1>
                <p className="text-xs text-green-100 hidden sm:block">
                  Payment Management System
                </p>
              </div>
            </div>
          </div>

          {/* Right side items */}
          <div className="flex items-center gap-4">
            {/* Notifications */}
            <div className="relative">
              <button
                className="p-2 rounded-lg hover:bg-green-700 hover:bg-opacity-50 relative transition-colors"
                onClick={() => console.log("Notifications clicked")}
                title="Notifications"
              >
                <Bell className="text-white" size={20} />
                {notificationCount > 0 && (
                  <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full text-xs text-white flex items-center justify-center bg-red-500">
                    {notificationCount > 9 ? "9+" : notificationCount}
                  </span>
                )}
              </button>
            </div>

            {/* User Info Display */}
            <div className="flex items-center gap-3">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${getRoleColor(
                  user.role
                )}`}
              >
                {getUserInitials()}
              </div>
              <div className="hidden sm:block text-left">
                <p className="text-sm font-medium text-white">
                  {getUserFullName()}
                </p>
                <p className="text-xs text-green-100">{user.email}</p>
                <p className="text-xs text-green-50">
                  {formatUserRole(user.role)}
                </p>
              </div>
            </div>

            {/* Sign Out Button */}
            <button
              onClick={handleLogout}
              className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 text-sm font-medium"
              title="Sign Out"
            >
              <LogOut size={16} />
              <span className="hidden sm:inline">Sign Out</span>
            </button>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden p-2 rounded-lg hover:bg-green-700 hover:bg-opacity-50 transition-colors"
            >
              {isMobileMenuOpen ? (
                <X className="text-white" size={20} />
              ) : (
                <Menu className="text-white" size={20} />
              )}
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-green-600 py-4">
            {/* Mobile User Info */}
            <div className="px-4 mb-4 pb-4 border-b border-green-600">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-medium ${getRoleColor(
                      user.role
                    )}`}
                  >
                    {getUserInitials()}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-white">
                      {getUserFullName()}
                    </p>
                    <p className="text-xs text-green-100">{user.email}</p>
                    <p className="text-xs text-green-50">
                      {formatUserRole(user.role)}
                    </p>
                  </div>
                </div>

                {/* Mobile Sign Out Button */}
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-2 px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 text-sm font-medium"
                >
                  <LogOut size={16} />
                  <span>Sign Out</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Click outside to close mobile menu */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </nav>
  );
};

export default Navbar;
