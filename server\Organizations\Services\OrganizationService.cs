using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Final_E_Receipt.Organizations.Models;
using Final_E_Receipt.Services;
using Final_E_Receipt.Common.Services;
using Final_E_Receipt.Common.Models;

namespace Final_E_Receipt.Organizations.Services
{
    public class OrganizationService
    {
        private readonly IDatabaseService _dbService;
        private readonly ILogger<OrganizationService> _logger;
        private readonly AuditService _auditService;

        public OrganizationService(IDatabaseService dbService, ILogger<OrganizationService> logger, AuditService auditService)
        {
            _dbService = dbService;
            _logger = logger;
            _auditService = auditService;
        }

        public async Task<Organization> CreateOrganization(Organization organization)
        {
            _logger.LogInformation("Creating new organization: {OrganizationName}", organization.Name);

            try
            {
                var createdOrg = await _dbService.QueryFirstOrDefaultAsync<Organization>(
                    "CreateOrganization",
                    new
                    {
                        Id = Guid.NewGuid().ToString(),
                        organization.Name,
                        organization.Email,
                        organization.PhoneNumber,
                        organization.Address,
                        organization.City,
                        organization.State,
                        organization.Country,
                        organization.LogoUrl,
                        organization.Website,
                        IsActive = true,
                        organization.CreatedBy
                    }
                );

                // Log organization creation
                if (createdOrg != null)
                {
                    await _auditService.LogAsync(
                        AuditActions.ORGANIZATION_CREATED,
                        EntityTypes.ORGANIZATION,
                        createdOrg.Id,
                        null,
                        new {
                            createdOrg.Name,
                            createdOrg.Email,
                            createdOrg.PhoneNumber,
                            createdOrg.Address,
                            createdOrg.City,
                            createdOrg.State,
                            createdOrg.Country
                        },
                        $"Organization '{organization.Name}' created by {organization.CreatedBy}"
                    );
                }

                return createdOrg;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating organization");
                throw;
            }
        }

        // Get a single organization by ID
        public async Task<Organization> GetOrganizationById(string id)
        {
            _logger.LogInformation("Fetching organization with ID: {OrganizationId}", id);

            try
            {
                return await _dbService.QueryFirstOrDefaultAsync<Organization>(
                    "GetOrganizationById",
                    new { Id = id }
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving organization with ID {OrganizationId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<Organization>> GetAllOrganizations(bool? activeOnly = null)
        {
            _logger.LogInformation("Fetching all organizations (ActiveOnly: {ActiveOnly})", activeOnly);

            try
            {
                if (activeOnly.HasValue)
                {
                    return await _dbService.QueryAsync<Organization>(
                        "GetActiveOrganizations",
                        new { IsActive = activeOnly.Value }
                    );
                }

                return await _dbService.QueryAsync<Organization>("GetAllOrganizations");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving organizations");
                throw;
            }
        }

        public async Task<Organization> UpdateOrganization(Organization organization)
        {
            _logger.LogInformation("Updating organization with ID: {OrganizationId}", organization.Id);

            try
            {
                return await _dbService.QueryFirstOrDefaultAsync<Organization>(
                    "UpdateOrganization",
                    new
                    {
                        organization.Id,
                        organization.Name,
                        organization.Email,
                        organization.PhoneNumber,
                        organization.Address,
                        organization.City,
                        organization.State,
                        organization.Country,
                        organization.LogoUrl,
                        organization.Website
                    }
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating organization with ID {OrganizationId}", organization.Id);
                throw;
            }
        }

        public async Task<Organization> ToggleOrganizationStatus(string id, bool isActive)
        {
            _logger.LogInformation("Toggling status for organization ID: {OrganizationId} to {IsActive}", id, isActive);

            try
            {
                return await _dbService.QueryFirstOrDefaultAsync<Organization>(
                    "UpdateOrganizationStatus",
                    new { Id = id, IsActive = isActive }
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling status for organization ID {OrganizationId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<Organization>> SearchOrganizations(string query)
        {
            if (string.IsNullOrWhiteSpace(query))
                throw new ArgumentException("Search query cannot be empty");

            _logger.LogInformation("Searching organizations with query: {SearchQuery}", query);

            try
            {
                return await _dbService.QueryAsync<Organization>(
                    "SearchOrganizations",
                    new { SearchQuery = query }
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching organizations with query: {SearchQuery}", query);
                throw;
            }
        }
    }
}

