using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Final_E_Receipt.Payments.Models;
using Final_E_Receipt.Services;

namespace Final_E_Receipt.Payments.Services
{
    public class PaymentProfileService
    {
        private readonly IDatabaseService _dbService;
        private readonly PaymentTypeService _paymentTypeService;

        public PaymentProfileService(IDatabaseService dbService, PaymentTypeService paymentTypeService)
        {
            _dbService = dbService;
            _paymentTypeService = paymentTypeService;
        }

        public async Task<PaymentProfile> CreatePaymentProfile(PaymentProfile profile)
        {
            // Validate payment type if provided
            if (!string.IsNullOrEmpty(profile.PaymentTypeId))
            {
                if (!await _paymentTypeService.ValidatePaymentType(profile.PaymentTypeId))
                {
                    throw new InvalidOperationException("Invalid or inactive payment type");
                }
            }

            var parameters = new
            {
                Id = profile.Id ?? Guid.NewGuid().ToString(),
                Name = profile.Name,
                Description = profile.Description,
                Amount = profile.Amount,
                Currency = profile.Currency,
                Frequency = profile.Frequency,
                DueDay = profile.DueDay,
                IsFixedAmount = profile.IsFixedAmount,
                IsActive = profile.IsActive,
                PaymentTypeId = profile.PaymentTypeId,
                OrganizationId = profile.OrganizationId,
                CreatedBy = profile.CreatedBy
            };

            return await _dbService.QueryFirstOrDefaultAsync<PaymentProfile>("CreatePaymentProfile", parameters);
        }

        public async Task<PaymentProfile> GetPaymentProfileById(string id)
        {
            var parameters = new { Id = id };
            return await _dbService.QueryFirstOrDefaultAsync<PaymentProfile>("GetPaymentProfileById", parameters);
        }

        public async Task<List<PaymentProfile>> GetPaymentProfilesByOrganization(string organizationId)
        {
            var parameters = new { OrganizationId = organizationId };
            var profiles = await _dbService.QueryAsync<PaymentProfile>("GetPaymentProfilesByOrganization", parameters);
            return profiles.ToList();
        }

        public async Task<PaymentProfile> UpdatePaymentProfile(PaymentProfile profile)
        {
            // Validate payment type if provided
            if (!string.IsNullOrEmpty(profile.PaymentTypeId))
            {
                if (!await _paymentTypeService.ValidatePaymentType(profile.PaymentTypeId))
                {
                    throw new InvalidOperationException("Invalid or inactive payment type");
                }
            }

            var parameters = new
            {
                Id = profile.Id,
                Name = profile.Name,
                Description = profile.Description,
                Amount = profile.Amount,
                Currency = profile.Currency,
                Frequency = profile.Frequency,
                DueDay = profile.DueDay,
                IsFixedAmount = profile.IsFixedAmount,
                IsActive = profile.IsActive,
                PaymentTypeId = profile.PaymentTypeId,
                OrganizationId = profile.OrganizationId,
                UpdatedBy = profile.UpdatedBy
            };

            return await _dbService.QueryFirstOrDefaultAsync<PaymentProfile>("UpdatePaymentProfile", parameters);
        }

        public async Task<bool> DeletePaymentProfile(string id)
        {
            var parameters = new { Id = id };

            try
            {
                await _dbService.ExecuteAsync("DeletePaymentProfile", parameters);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<PaymentSchedule>> BulkCreatePaymentSchedules(string profileId, string createdBy)
        {
            var parameters = new
            {
                PaymentProfileId = profileId,
                CreatedBy = createdBy
            };

            var schedules = await _dbService.QueryAsync<PaymentSchedule>("BulkCreatePaymentSchedules", parameters);
            return schedules.ToList();
        }
    }
}
