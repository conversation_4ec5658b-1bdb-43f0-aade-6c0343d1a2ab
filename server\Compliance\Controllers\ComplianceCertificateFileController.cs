using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Final_E_Receipt.Compliance.Services;
using Final_E_Receipt.Compliance.DTOs;
using Final_E_Receipt.Files.DTOs;

namespace Final_E_Receipt.Compliance.Controllers
{
    [ApiController]
    [Route("api/certificates/{certificateId}/files")]
    [Authorize]
    public class ComplianceCertificateFileController : ControllerBase
    {
        private readonly ComplianceCertificateFileService _certificateFileService;
        private readonly ILogger<ComplianceCertificateFileController> _logger;

        public ComplianceCertificateFileController(
            ComplianceCertificateFileService certificateFileService,
            ILogger<ComplianceCertificateFileController> logger)
        {
            _certificateFileService = certificateFileService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetCertificateSupportingFiles(string certificateId)
        {
            try
            {
                var files = await _certificateFileService.GetCertificateSupportingFiles(certificateId);
                
                var response = files.Select(f => new FileListDTO
                {
                    Id = f.Id,
                    FileName = f.FileName,
                    OriginalFileName = f.OriginalFileName,
                    ContentType = f.ContentType,
                    FileSize = f.FileSize,
                    RelatedEntityType = f.RelatedEntityType,
                    RelatedEntityId = f.RelatedEntityId,
                    CreatedAt = f.CreatedAt,
                    Description = f.Description,
                    Category = f.Category,
                    IsScanned = f.IsScanned,
                    ScanResult = f.ScanResult,
                    UploadedBy = f.UploadedBy
                }).ToList();

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving supporting files for certificate {CertificateId}", certificateId);
                return StatusCode(500, new { message = "An error occurred while retrieving files" });
            }
        }

        //[HttpPost("upload")]


        [HttpPost("upload")]
        [Consumes("multipart/form-data")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        [RequestSizeLimit(10 * 1024 * 1024)] // 10MB limit
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<IActionResult> UploadSupportingDocument(
             string certificateId,
            IFormFile file,
             string? description = null,
             string? category = null)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var organizationId = User.FindFirst("OrganizationId")?.Value;

                if (file == null || file.Length == 0)
                    return BadRequest(new { message = "File is required" });

                if (string.IsNullOrEmpty(certificateId))
                    return BadRequest(new { message = "Certificate ID is required" });

                var uploadedFile = await _certificateFileService.UploadSupportingDocument(
                    certificateId,
                    file,
                    userId ?? "",
                    organizationId ?? "",
                    description,
                    category
                );

                var response = new FileUploadResponseDTO
                {
                    Id = uploadedFile.Id,
                    FileName = uploadedFile.FileName,
                    OriginalFileName = uploadedFile.OriginalFileName,
                    ContentType = uploadedFile.ContentType,
                    FileSize = uploadedFile.FileSize,
                    RelatedEntityType = uploadedFile.RelatedEntityType,
                    RelatedEntityId = uploadedFile.RelatedEntityId,
                    CreatedAt = uploadedFile.CreatedAt,
                    Description = uploadedFile.Description,
                    Category = uploadedFile.Category,
                    IsScanned = uploadedFile.IsScanned,
                    ScanResult = uploadedFile.ScanResult
                };

                return Ok(response);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading supporting document for certificate {CertificateId}", certificateId);
                return StatusCode(500, new { message = "An error occurred while uploading the file" });
            }
        }

        [HttpDelete("{fileId}")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> DeleteSupportingDocument(string certificateId, string fileId)
        {
            try
            {
                var success = await _certificateFileService.DeleteSupportingDocument(fileId, certificateId);
                
                if (!success)
                    return NotFound(new { message = "File not found or access denied" });

                return Ok(new { message = "Supporting document deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting supporting document {FileId} for certificate {CertificateId}", fileId, certificateId);
                return StatusCode(500, new { message = "An error occurred while deleting the file" });
            }
        }

        [HttpGet("stats")]
        public async Task<IActionResult> GetCertificateFileStats(string certificateId)
        {
            try
            {
                var stats = await _certificateFileService.GetCertificateFileStats(certificateId);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting file stats for certificate {CertificateId}", certificateId);
                return StatusCode(500, new { message = "An error occurred while retrieving file statistics" });
            }
        }

        [HttpPost("generate-pdf")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GenerateAndStoreCertificatePdf(string certificateId, [FromBody] GenerateCertificatePdfRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var organizationId = User.FindFirst("OrganizationId")?.Value;

                // This is a placeholder for PDF generation
                // In a real implementation, you would use a PDF generation library
                var pdfContent = await GenerateCertificatePdfContent(certificateId, request.TemplateId);
                var fileName = $"Certificate_{certificateId}_{DateTime.Now:yyyyMMdd}.pdf";

                var uploadedFile = await _certificateFileService.StoreCertificatePdf(
                    certificateId,
                    pdfContent,
                    fileName,
                    userId,
                    organizationId
                );

                var response = new FileUploadResponseDTO
                {
                    Id = uploadedFile.Id,
                    FileName = uploadedFile.FileName,
                    OriginalFileName = uploadedFile.OriginalFileName,
                    ContentType = uploadedFile.ContentType,
                    FileSize = uploadedFile.FileSize,
                    RelatedEntityType = uploadedFile.RelatedEntityType,
                    RelatedEntityId = uploadedFile.RelatedEntityId,
                    CreatedAt = uploadedFile.CreatedAt,
                    Description = uploadedFile.Description,
                    Category = uploadedFile.Category,
                    IsScanned = uploadedFile.IsScanned,
                    ScanResult = uploadedFile.ScanResult
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating certificate PDF for {CertificateId}", certificateId);
                return StatusCode(500, new { message = "An error occurred while generating the certificate PDF" });
            }
        }

        [HttpPost("link-payment-proofs")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> LinkPaymentProofsToCertificate(string certificateId, [FromBody] LinkPaymentProofsRequest request)
        {
            try
            {
                var success = await _certificateFileService.LinkPaymentProofsToCertificate(certificateId, request.PaymentIds);
                
                if (!success)
                    return BadRequest(new { message = "Failed to link payment proofs to certificate" });

                return Ok(new { message = "Payment proofs linked successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error linking payment proofs to certificate {CertificateId}", certificateId);
                return StatusCode(500, new { message = "An error occurred while linking payment proofs" });
            }
        }

        private async Task<byte[]> GenerateCertificatePdfContent(string certificateId, string templateId)
        {
            // Placeholder for PDF generation
            // In a real implementation, you would:
            // 1. Get certificate data
            // 2. Get template
            // 3. Merge data with template
            // 4. Generate PDF using a library like iTextSharp or PdfSharp
            
            var samplePdfContent = System.Text.Encoding.UTF8.GetBytes($"Sample Certificate PDF for {certificateId}");
            return await Task.FromResult(samplePdfContent);
        }
    }

    public class GenerateCertificatePdfRequest
    {
        public string TemplateId { get; set; }
        public Dictionary<string, string> CustomFields { get; set; } = new Dictionary<string, string>();
    }

    public class LinkPaymentProofsRequest
    {
        public List<string> PaymentIds { get; set; } = new List<string>();
    }
}
