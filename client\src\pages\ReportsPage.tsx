import React, { useState, useEffect } from 'react';
import {
  BarChart3,
  TrendingUp,
  FileText,
  AlertTriangle,
  DollarSign,
  Calendar,
  Receipt,
  Shield,
  RefreshCw
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { usePaymentReportingApi, useComplianceReportingApi } from '../hooks/api';
import PaymentReportsContainer from '../components/Reports/PaymentReports/PaymentReportsContainer';
import ComplianceReportsContainer from '../components/Reports/ComplianceReports/ComplianceReportsContainer';
import ReceiptReportsContainer from '../components/Reports/ReceiptReports/ReceiptReportsContainer';

const ReportsPage: React.FC = () => {
  const { user, hasRole } = useAuth();
  const { getFinancialSummary, loading: paymentLoading } = usePaymentReportingApi();
  const { getComplianceMetrics, loading: complianceLoading } = useComplianceReportingApi();
  
  const [activeSection, setActiveSection] = useState<'overview' | 'payments' | 'compliance' | 'receipts'>('overview');
  const [financialSummary, setFinancialSummary] = useState<any>(null);
  const [complianceMetrics, setComplianceMetrics] = useState<any>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Role-based permissions
  const canViewPaymentReports = hasRole(['JTB_ADMIN', 'FINANCE_OFFICER', 'SENIOR_FINANCE_OFFICER']);
  const canViewComplianceReports = hasRole(['JTB_ADMIN', 'SENIOR_FINANCE_OFFICER']);
  const canViewReceiptReports = hasRole(['JTB_ADMIN', 'FINANCE_OFFICER', 'SENIOR_FINANCE_OFFICER']);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setRefreshing(true);
      
      if (canViewPaymentReports) {
        const summary = await getFinancialSummary();
        setFinancialSummary(summary);
      }
      
      if (canViewComplianceReports) {
        const metrics = await getComplianceMetrics();
        setComplianceMetrics(metrics);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const reportSections = [
    {
      id: 'overview',
      label: 'Overview',
      icon: BarChart3,
      description: 'Dashboard overview and key metrics',
      available: true,
    },
    {
      id: 'payments',
      label: 'Payment Reports',
      icon: DollarSign,
      description: 'Payment history, outstanding balances, financial summaries',
      available: canViewPaymentReports,
    },
    {
      id: 'compliance',
      label: 'Compliance Reports',
      icon: Shield,
      description: 'Compliance status, certificates, organization metrics',
      available: canViewComplianceReports,
    },
    {
      id: 'receipts',
      label: 'Receipt Reports',
      icon: Receipt,
      description: 'Receipt analytics, revoked receipts, audit trails',
      available: canViewReceiptReports,
    },
  ].filter(section => section.available);

  const quickStats = [
    {
      title: 'Total Revenue',
      value: financialSummary?.totalRevenue ? `₦${financialSummary.totalRevenue.toLocaleString()}` : '₦0',
      icon: DollarSign,
      color: 'bg-green-500',
      loading: paymentLoading,
    },
    {
      title: 'Total Payments',
      value: financialSummary?.totalPayments?.toLocaleString() || '0',
      icon: TrendingUp,
      color: 'bg-blue-500',
      loading: paymentLoading,
    },
    {
      title: 'Average Payment',
      value: financialSummary?.averagePaymentAmount ? `₦${financialSummary.averagePaymentAmount.toLocaleString()}` : '₦0',
      icon: BarChart3,
      color: 'bg-purple-500',
      loading: paymentLoading,
    },
    {
      title: 'Compliance Score',
      value: complianceMetrics?.averageComplianceScore ? `${complianceMetrics.averageComplianceScore}%` : 'N/A',
      icon: Shield,
      color: 'bg-orange-500',
      loading: complianceLoading,
    },
  ];

  const quickActions = [
    {
      title: 'Payment History',
      description: 'View detailed payment history by organization',
      icon: FileText,
      action: () => setActiveSection('payments'),
      available: canViewPaymentReports,
    },
    {
      title: 'Outstanding Balances',
      description: 'Check overdue payments and aging analysis',
      icon: AlertTriangle,
      action: () => setActiveSection('payments'),
      available: canViewPaymentReports,
    },
    {
      title: 'Compliance Dashboard',
      description: 'Monitor organization compliance status',
      icon: Shield,
      action: () => setActiveSection('compliance'),
      available: canViewComplianceReports,
    },
    {
      title: 'Revoked Receipts',
      description: 'Audit trail for revoked receipts',
      icon: Receipt,
      action: () => setActiveSection('receipts'),
      available: canViewReceiptReports,
    },
  ].filter(action => action.available);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-[#045024]">Reports & Analytics</h1>
              <p className="text-gray-600 mt-2">Comprehensive reporting dashboard for payment management</p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={loadDashboardData}
                disabled={refreshing}
                className="bg-[#2aa45c] text-white px-4 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2 disabled:opacity-50"
              >
                <RefreshCw size={16} className={refreshing ? 'animate-spin' : ''} />
                <span>Refresh</span>
              </button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">{stat.title}</p>
                  <div className="mt-2">
                    {stat.loading ? (
                      <div className="animate-pulse">
                        <div className="h-8 bg-gray-200 rounded w-24"></div>
                      </div>
                    ) : (
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    )}
                  </div>
                </div>
                <div className={`h-12 w-12 rounded-lg ${stat.color} bg-opacity-10 flex items-center justify-center`}>
                  <stat.icon className={`h-6 w-6 ${stat.color.replace('bg-', 'text-')}`} />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {reportSections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeSection === section.id
                    ? 'border-[#2aa45c] text-[#2aa45c]'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <section.icon size={16} />
                <span>{section.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Content Area */}
        {activeSection === 'overview' && (
          <div className="space-y-8">
            {/* Quick Actions */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {quickActions.map((action, index) => (
                  <button
                    key={index}
                    onClick={action.action}
                    className="bg-white rounded-lg shadow-md p-6 text-left hover:shadow-lg transition-shadow"
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="h-10 w-10 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
                        <action.icon className="h-5 w-5 text-[#2aa45c]" />
                      </div>
                      <h3 className="font-medium text-gray-900">{action.title}</h3>
                    </div>
                    <p className="text-sm text-gray-500">{action.description}</p>
                  </button>
                ))}
              </div>
            </div>

            {/* Recent Activity */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Activity</h2>
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Recent activity tracking coming soon</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeSection === 'payments' && (
          <PaymentReportsContainer />
        )}

        {activeSection === 'compliance' && (
          <ComplianceReportsContainer />
        )}

        {activeSection === 'receipts' && (
          <ReceiptReportsContainer />
        )}
      </div>
    </div>
  );
};

export default ReportsPage;
