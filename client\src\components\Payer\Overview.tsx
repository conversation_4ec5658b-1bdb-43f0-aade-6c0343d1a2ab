import React from 'react';
import { DollarSign, Clock, XCircle, FileText } from 'lucide-react';

// Types
interface PaymentProfile {
  id: string;
  name: string;
  outstandingBalance: number;
  dueDate: string;
  status: 'pending' | 'overdue' | 'paid';
}

interface Payment {
  id: string;
  profileId: string;
  amount: number;
  date: string;
  status: 'pending' | 'approved' | 'rejected';
  receiptUrl?: string;
  proofUrl?: string;
}

interface OverviewProps {
  paymentProfiles?: PaymentProfile[];
  payments?: Payment[];
}

// Utility Components
const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'pending':
        return { icon: Clock, color: 'bg-yellow-100 text-yellow-800', label: 'Pending' };
      case 'approved':
        return { icon: Clock, color: 'bg-green-100 text-green-800', label: 'Approved' };
      case 'rejected':
        return { icon: XCircle, color: 'bg-red-100 text-red-800', label: 'Rejected' };
      case 'overdue':
        return { icon: XCircle, color: 'bg-red-100 text-red-800', label: 'Overdue' };
      case 'paid':
        return { icon: Clock, color: 'bg-green-100 text-green-800', label: 'Paid' };
      default:
        return { icon: Clock, color: 'bg-gray-100 text-gray-800', label: status };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
      <Icon size={12} />
      {config.label}
    </span>
  );
};

// Summary Cards Component
const SummaryCards: React.FC<{ 
  paymentProfiles: PaymentProfile[]; 
  payments: Payment[] 
}> = ({ paymentProfiles, payments }) => {
  const formatCurrency = (amount: number) => 
  `₦${amount.toLocaleString('en-NG', { minimumFractionDigits: 2 })}`;


  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="bg-white rounded-lg shadow-md p-6 border-l-4" style={{ borderLeftColor: '#2aa45c' }}>
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-600 text-sm">Total Outstanding</p>
            <p className="text-2xl font-bold" style={{ color: '#045024' }}>
              {formatCurrency(paymentProfiles.reduce((sum, p) => sum + p.outstandingBalance, 0))}
            </p>
          </div>
          <DollarSign className="text-green-600" size={32} />
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-600 text-sm">Pending Payments</p>
            <p className="text-2xl font-bold text-yellow-700">
              {payments.filter(p => p.status === 'pending').length}
            </p>
          </div>
          <Clock className="text-yellow-500" size={32} />
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-red-500">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-600 text-sm">Overdue Items</p>
            <p className="text-2xl font-bold text-red-700">
              {paymentProfiles.filter(p => p.status === 'overdue').length}
            </p>
          </div>
          <XCircle className="text-red-500" size={32} />
        </div>
      </div>
    </div>
  );
};

// Recent Activity Component
const RecentActivity: React.FC<{ 
  payments: Payment[]; 
  paymentProfiles: PaymentProfile[] 
}> = ({ payments, paymentProfiles }) => {
  const formatCurrency = (amount: number) => `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}`;
  const formatDate = (date: string) => new Date(date).toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  });

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold mb-4" style={{ color: '#045024' }}>Recent Activity</h3>
      <div className="space-y-3">
        {payments.slice(0, 3).map((payment) => {
          const profile = paymentProfiles.find(p => p.id === payment.profileId);
          return (
            <div key={payment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FileText style={{ color: '#2aa45c' }} size={20} />
                <div>
                  <p className="font-medium text-gray-900">{profile?.name}</p>
                  <p className="text-sm text-gray-600">{formatDate(payment.date)}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <span className="font-semibold text-gray-900">{formatCurrency(payment.amount)}</span>
                <StatusBadge status={payment.status} />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Main Overview Component
const Overview: React.FC<OverviewProps> = ({ 
  paymentProfiles = [], 
  payments = [] 
}) => {
  return (
    <div className="space-y-6">
      <SummaryCards paymentProfiles={paymentProfiles} payments={payments} />
      <RecentActivity payments={payments} paymentProfiles={paymentProfiles} />
    </div>
  );
};

export default Overview;