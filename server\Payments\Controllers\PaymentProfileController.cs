using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Final_E_Receipt.Payments.Models;
using Final_E_Receipt.Payments.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Final_E_Receipt.Payments.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PaymentProfileController : ControllerBase
    {
        private readonly PaymentProfileService _profileService;

        public PaymentProfileController(PaymentProfileService profileService)
        {
            _profileService = profileService;
        }

        [HttpPost]
        [Authorize(Roles = "ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> CreatePaymentProfile([FromBody] PaymentProfile profile)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            profile.CreatedBy = userId;

            var createdProfile = await _profileService.CreatePaymentProfile(profile);
            
            if (createdProfile == null)
                return BadRequest(new { message = "Failed to create payment profile" });

            return Ok(createdProfile);
        }

        [HttpGet("{id}")]
        [Authorize]
        public async Task<IActionResult> GetPaymentProfileById(string id)
        {
            var profile = await _profileService.GetPaymentProfileById(id);
            
            if (profile == null)
                return NotFound(new { message = "Payment profile not found" });

            return Ok(profile);
        }

        [HttpGet("organization/{organizationId}")]
        [Authorize]
        public async Task<IActionResult> GetPaymentProfilesByOrganization(string organizationId)
        {
            var profiles = await _profileService.GetPaymentProfilesByOrganization(organizationId);
            return Ok(profiles);
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> UpdatePaymentProfile(string id, [FromBody] PaymentProfile profile)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            profile.Id = id;
            profile.UpdatedBy = userId;

            var updatedProfile = await _profileService.UpdatePaymentProfile(profile);
            
            if (updatedProfile == null)
                return NotFound(new { message = "Payment profile not found" });

            return Ok(updatedProfile);
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> DeletePaymentProfile(string id)
        {
            var success = await _profileService.DeletePaymentProfile(id);
            
            if (!success)
                return BadRequest(new { message = "Failed to delete payment profile. It may be in use." });

            return Ok(new { message = "Payment profile deleted successfully" });
        }

        [HttpPost("{profileId}/schedules")]
        [Authorize(Roles = "ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> BulkCreatePaymentSchedules(string profileId)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            var schedules = await _profileService.BulkCreatePaymentSchedules(profileId, userId);
            
            if (schedules.Count == 0)
                return BadRequest(new { message = "Failed to create payment schedules" });

            return Ok(schedules);
        }
    }
}