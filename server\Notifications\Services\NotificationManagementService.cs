using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Final_E_Receipt.Notifications.Models;
using Final_E_Receipt.Notifications.DTOs;
using Microsoft.Extensions.Logging;
using Final_E_Receipt.Services;

namespace Final_E_Receipt.Notifications.Services
{
    public class NotificationManagementService
    {
        private readonly IDatabaseService _dbService;
        private readonly ILogger<NotificationManagementService> _logger;

        public NotificationManagementService(
            IDatabaseService dbService,
            ILogger<NotificationManagementService> logger)
        {
            _dbService = dbService;
            _logger = logger;
        }

        public async Task<Notification> CreateNotification(CreateNotificationDTO dto)
        {
            try
            {
                var parameters = new
                {
                    Id = Guid.NewGuid().ToString(),
                    UserId = dto.UserId,
                    OrganizationId = dto.OrganizationId,
                    Type = dto.Type,
                    Title = dto.Title,
                    Message = dto.Message,
                    Priority = dto.Priority ?? "MEDIUM",
                    RelatedEntityId = dto.RelatedEntityId,
                    RelatedEntityType = dto.RelatedEntityType,
                    ScheduledDate = dto.ScheduledDate
                };

                return await _dbService.QueryFirstOrDefaultAsync<Notification>(
                    "CreateNotification", parameters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating notification for user {UserId}", dto.UserId);
                throw;
            }
        }

        public async Task<Notification> GetNotificationById(string id)
        {
            try
            {
                var parameters = new { Id = id };
                return await _dbService.QueryFirstOrDefaultAsync<Notification>(
                    "GetNotificationById", parameters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notification {Id}", id);
                return null;
            }
        }

        public async Task<List<Notification>> GetUserNotifications(string userId, NotificationFiltersDTO filters = null)
        {
            try
            {
                var parameters = new
                {
                    UserId = userId,
                    Status = filters?.Status,
                    Priority = filters?.Priority,
                    FromDate = filters?.FromDate,
                    ToDate = filters?.ToDate,
                    Type = filters?.Type,
                    PageNumber = filters?.PageNumber ?? 1,
                    PageSize = filters?.PageSize ?? 50
                };

                var notifications = await _dbService.QueryAsync<Notification>(
                    "GetUserNotifications", parameters);

                return notifications.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notifications for user {UserId}", userId);
                return new List<Notification>();
            }
        }

        public async Task<List<Notification>> GetUnreadNotifications(string userId, int limit = 10)
        {
            try
            {
                var parameters = new
                {
                    UserId = userId,
                    Limit = limit
                };

                var notifications = await _dbService.QueryAsync<Notification>(
                    "GetUnreadNotifications", parameters);

                return notifications.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread notifications for user {UserId}", userId);
                return new List<Notification>();
            }
        }

        public async Task<NotificationStats> GetNotificationStats(string userId)
        {
            try
            {
                return await _dbService.QueryFirstOrDefaultAsync<NotificationStats>(
                    "GetNotificationStats", new { UserId = userId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notification stats for user {UserId}", userId);
                return new NotificationStats();
            }
        }

        public async Task<Notification> MarkAsRead(string id)
        {
            try
            {
                var parameters = new { Id = id };
                return await _dbService.QueryFirstOrDefaultAsync<Notification>(
                    "MarkNotificationAsRead", parameters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification {Id} as read", id);
                return null;
            }
        }

        public async Task<Notification> MarkAsUnread(string id)
        {
            try
            {
                var parameters = new { Id = id };
                return await _dbService.QueryFirstOrDefaultAsync<Notification>(
                    "MarkNotificationAsUnread", parameters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification {Id} as unread", id);
                return null;
            }
        }

        public async Task<Notification> ArchiveNotification(string id)
        {
            try
            {
                var parameters = new { Id = id };
                return await _dbService.QueryFirstOrDefaultAsync<Notification>(
                    "ArchiveNotification", parameters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error archiving notification {Id}", id);
                return null;
            }
        }

        public async Task<bool> DeleteNotification(string id)
        {
            try
            {
                var parameters = new { Id = id };
                await _dbService.ExecuteAsync("DeleteNotification", parameters);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting notification {Id}", id);
                return false;
            }
        }

        public async Task<bool> MarkMultipleAsRead(string[] notificationIds)
        {
            try
            {
                var parameters = new
                {
                    NotificationIds = string.Join(",", notificationIds)
                };
                await _dbService.ExecuteAsync("MarkMultipleAsRead", parameters);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking multiple notifications as read");
                return false;
            }
        }

        public async Task<bool> ArchiveMultiple(string[] notificationIds)
        {
            try
            {
                var parameters = new
                {
                    NotificationIds = string.Join(",", notificationIds)
                };
                await _dbService.ExecuteAsync("ArchiveMultipleNotifications", parameters);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error archiving multiple notifications");
                return false;
            }
        }

        public async Task<bool> DeleteMultiple(string[] notificationIds)
        {
            try
            {
                var parameters = new
                {
                    NotificationIds = string.Join(",", notificationIds)
                };
                await _dbService.ExecuteAsync("DeleteMultipleNotifications", parameters);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting multiple notifications");
                return false;
            }
        }
    }
}



