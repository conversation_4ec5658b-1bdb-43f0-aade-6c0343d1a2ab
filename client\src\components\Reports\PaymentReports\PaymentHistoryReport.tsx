import React, { useState, useEffect } from 'react';
import { History, DollarSign, TrendingUp, Users, RefreshCw } from 'lucide-react';
import { usePaymentReportingApi, useOrganizationApi } from '../../../hooks/api';
import ReportFilters, { type FilterOptions } from '../Common/ReportFilters';
import ReportTable, { type TableColumn } from '../Common/ReportTable';
import ReportSummaryCard from '../Common/ReportSummaryCard';
import ExportButtons from '../Common/ExportButtons';

interface PaymentHistoryData {
  paymentId: string;
  transactionReference: string;
  payerName: string;
  payerEmail: string;
  organizationName: string;
  amount: number;
  currency: string;
  status: string;
  paymentMethod: string;
  category: string;
  createdAt: string;
  completedAt?: string;
}

interface PaymentHistorySummary {
  totalPayments: number;
  totalAmount: number;
  completedPayments: number;
  pendingPayments: number;
  successRate: number;
  averageAmount: number;
}

const PaymentHistoryReport: React.FC = () => {
  const { getPaymentSummary, exportPaymentReport, loading } = usePaymentReportingApi();
  const { getAllOrganizations } = useOrganizationApi();
  
  const [filters, setFilters] = useState<FilterOptions>({});
  const [paymentData, setPaymentData] = useState<PaymentHistoryData[]>([]);
  const [summary, setSummary] = useState<PaymentHistorySummary | null>(null);
  const [organizations, setOrganizations] = useState<Array<{ id: string; name: string }>>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    pageSize: 50,
    totalCount: 0,
  });
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    loadOrganizations();
    loadPaymentHistory();
  }, [filters, pagination.currentPage, sortBy, sortDirection]);

  const loadOrganizations = async () => {
    const orgs = await getAllOrganizations();
    if (orgs) {
      setOrganizations(orgs.map(org => ({ id: org.id, name: org.name })));
    }
  };

  const loadPaymentHistory = async () => {
    const result = await getPaymentSummary({
      ...filters,
      sortBy,
      sortDirection,
      pageNumber: pagination.currentPage,
      pageSize: pagination.pageSize,
    });

    if (result) {
      // Mock data structure - adjust based on actual API response
      setPaymentData(result.payments || []);
      setSummary(result.summary || null);
      setPagination(prev => ({
        ...prev,
        totalPages: result.totalPages || 1,
        totalCount: result.totalCount || 0,
      }));
    }
  };

  const handleSort = (column: string, direction: 'asc' | 'desc') => {
    setSortBy(column);
    setSortDirection(direction);
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  const handleExportCSV = async () => {
    await exportPaymentReport('csv', filters);
  };

  const handleExportExcel = async () => {
    await exportPaymentReport('excel', filters);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      COMPLETED: { bg: 'bg-green-100', text: 'text-green-800', label: 'Completed' },
      PENDING: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
      ACKNOWLEDGED: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Acknowledged' },
      APPROVED: { bg: 'bg-purple-100', text: 'text-purple-800', label: 'Approved' },
      REJECTED: { bg: 'bg-red-100', text: 'text-red-800', label: 'Rejected' },
      FAILED: { bg: 'bg-red-100', text: 'text-red-800', label: 'Failed' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const formatCurrency = (amount: number, currency: string = 'NGN') => {
    return `${currency === 'NGN' ? '₦' : currency} ${amount.toLocaleString()}`;
  };

  const columns: TableColumn[] = [
    {
      key: 'transactionReference',
      label: 'Reference',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: 'payerName',
      label: 'Payer',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm text-gray-500">{row.payerEmail}</div>
        </div>
      ),
    },
    {
      key: 'organizationName',
      label: 'Organization',
      sortable: true,
    },
    {
      key: 'amount',
      label: 'Amount',
      sortable: true,
      render: (value, row) => (
        <span className="font-medium">{formatCurrency(value, row.currency)}</span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => getStatusBadge(value),
    },
    {
      key: 'paymentMethod',
      label: 'Method',
      sortable: true,
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString(),
    },
  ];

  const statusOptions = [
    { value: 'COMPLETED', label: 'Completed' },
    { value: 'PENDING', label: 'Pending' },
    { value: 'ACKNOWLEDGED', label: 'Acknowledged' },
    { value: 'APPROVED', label: 'Approved' },
    { value: 'REJECTED', label: 'Rejected' },
    { value: 'FAILED', label: 'Failed' },
  ];

  const paymentMethodOptions = [
    { value: 'BANK_TRANSFER', label: 'Bank Transfer' },
    { value: 'CARD', label: 'Card Payment' },
    { value: 'CASH', label: 'Cash' },
    { value: 'CHEQUE', label: 'Cheque' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
            <History className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Payment History Report</h2>
            <p className="text-gray-600">Detailed payment history with filtering and export options</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={loadPaymentHistory}
            disabled={loading}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2 disabled:opacity-50"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
          <ExportButtons
            onExportCSV={handleExportCSV}
            onExportExcel={handleExportExcel}
            disabled={loading}
            variant="dropdown"
          />
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <ReportSummaryCard
            title="Total Payments"
            value={summary.totalPayments}
            icon={Users}
            color="blue"
            loading={loading}
          />
          <ReportSummaryCard
            title="Total Amount"
            value={`₦${summary.totalAmount.toLocaleString()}`}
            icon={DollarSign}
            color="green"
            loading={loading}
          />
          <ReportSummaryCard
            title="Success Rate"
            value={`${summary.successRate.toFixed(1)}%`}
            icon={TrendingUp}
            color="purple"
            loading={loading}
          />
          <ReportSummaryCard
            title="Average Amount"
            value={`₦${summary.averageAmount.toLocaleString()}`}
            icon={DollarSign}
            color="yellow"
            loading={loading}
          />
        </div>
      )}

      {/* Filters */}
      <ReportFilters
        filters={filters}
        onFiltersChange={setFilters}
        organizations={organizations}
        statusOptions={statusOptions}
        paymentMethodOptions={paymentMethodOptions}
        showPaymentMethodFilter={true}
      />

      {/* Data Table */}
      <ReportTable
        columns={columns}
        data={paymentData}
        loading={loading}
        sortBy={sortBy}
        sortDirection={sortDirection}
        onSort={handleSort}
        pagination={{
          ...pagination,
          onPageChange: handlePageChange,
        }}
        emptyMessage="No payment history found for the selected filters"
      />
    </div>
  );
};

export default PaymentHistoryReport;
