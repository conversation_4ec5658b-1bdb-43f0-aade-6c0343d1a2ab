import React, { useState } from "react";
import { useAuth } from "../contexts/AuthContext";
import OrganizationList from "../components/Admin/OrganizationList";
import CreateOrganization from "../components/Admin/CreateOrganization";
import EditOrganization from "../components/Admin/EditOrganization";
import OrganizationDetails from "../components/Admin/OrganizationDetails";
import type { Organization } from "../hooks/api";

const OrganizationsPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<string>("organizations");
  const [selectedOrganization, setSelectedOrganization] =
    useState<Organization | null>(null);

  // Role-based permissions
  const canCreateOrganizations = user?.role === "JTB_ADMIN";
  const canEditOrganizations = user?.role === "JTB_ADMIN";

  const handleOrganizationCreated = () => {
    // Refresh the organization list when a new organization is created
    setActiveTab("organizations");
  };

  const handleOrganizationUpdated = () => {
    // Refresh the organization list when an organization is updated
    setActiveTab("organizations");
  };

  const renderContent = () => {
    switch (activeTab) {
      case "organizations":
        return (
          <OrganizationList
            setActiveTab={setActiveTab}
            setSelectedOrganization={setSelectedOrganization}
          />
        );

      case "create-organization":
        return canCreateOrganizations ? (
          <CreateOrganization
            setActiveTab={setActiveTab}
            onOrganizationCreated={handleOrganizationCreated}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">
              You don't have permission to create organizations
            </p>
          </div>
        );

      case "edit-organization":
        return canEditOrganizations && selectedOrganization ? (
          <EditOrganization
            organization={selectedOrganization}
            setActiveTab={setActiveTab}
            setSelectedOrganization={setSelectedOrganization}
            onOrganizationUpdated={handleOrganizationUpdated}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {!canEditOrganizations
                ? "You don't have permission to edit organizations"
                : "No organization selected for editing"}
            </p>
          </div>
        );

      case "organization-details":
        return selectedOrganization ? (
          <OrganizationDetails
            organization={selectedOrganization}
            setActiveTab={setActiveTab}
            setSelectedOrganization={setSelectedOrganization}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">
              No organization selected for viewing
            </p>
          </div>
        );

      default:
        return (
          <OrganizationList
            setActiveTab={setActiveTab}
            setSelectedOrganization={setSelectedOrganization}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderContent()}
      </div>
    </div>
  );
};

export default OrganizationsPage;
