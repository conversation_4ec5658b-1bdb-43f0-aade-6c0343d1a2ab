import React, { useState } from 'react';
import {
  Home,
  User,
  Building2,
  CreditCard,
  Receipt,
  FileText,
  Settings,
  LogOut,
  Menu,
  X,
  Search,
  ChevronDown
} from 'lucide-react';
import NotificationBell from './Notifications/NotificationBell';

interface NavbarProps {
  userRole: 'finance_officer' | 'payer';
  currentPage: string;
  onNavigate: (page: string) => void;
  userName?: string;
  organizationName?: string;
}

const Navbar: React.FC<NavbarProps> = ({ 
  userRole, 
  currentPage, 
  onNavigate, 
  userName = "John Doe",
  organizationName = "Acme Corporation"
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);

  const financeOfficerNavItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'payments', label: 'Payments', icon: CreditCard },
    { id: 'receipts', label: 'Receipts', icon: Receipt },
    { id: 'profiles', label: 'Profiles', icon: Building2 },
    { id: 'reports', label: 'Reports', icon: FileText },
  ];

  const payerNavItems = [
    { id: 'overview', label: 'Overview', icon: Home },
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'payments', label: 'Payments', icon: CreditCard },
    { id: 'receipts', label: 'Receipts', icon: Receipt },
  ];

  const navItems = userRole === 'finance_officer' ? financeOfficerNavItems : payerNavItems;

  const NavItem: React.FC<{ item: any; isMobile?: boolean }> = ({ item, isMobile = false }) => (
    <button
      onClick={() => {
        onNavigate(item.id);
        if (isMobile) setIsMobileMenuOpen(false);
      }}
      className={`flex items-center gap-2 px-3 py-2 rounded-lg font-medium transition-colors ${
        currentPage === item.id
          ? 'bg-green-700 text-white shadow-lg'
          : 'text-green-800 hover:bg-green-100'
      } ${isMobile ? 'w-full justify-start' : ''}`}
    >
      <item.icon size={18} />
      {item.label}
    </button>
  );

  return (
    <nav className="bg-white shadow-md border-b border-gray-200 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Brand */}
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-green-700 rounded-lg flex items-center justify-center">
              <CreditCard className="text-white" size={20} />
            </div>
            <div>
              <h1 className="text-xl font-bold text-green-800">FinanceSystem</h1>
              <p className="text-xs text-gray-600">
                {userRole === 'finance_officer' ? 'Finance Officer Portal' : 'Payer Dashboard'}
              </p>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-4">
            {navItems.map((item) => (
              <NavItem key={item.id} item={item} />
            ))}
          </div>

          {/* Right Side - Search, Notifications, Profile */}
          <div className="flex items-center gap-4">
            {/* Search */}
            <div className="hidden lg:flex items-center">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type="text"
                  placeholder="Search..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                />
              </div>
            </div>

            {/* Notifications */}
            <NotificationBell />

            {/* Profile Dropdown */}
            <div className="relative">
              <button
                onClick={() => setIsProfileDropdownOpen(!isProfileDropdownOpen)}
                className="flex items-center gap-2 p-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                  <User className="text-white" size={16} />
                </div>
                <div className="hidden sm:block text-left">
                  <p className="text-sm font-medium">{userName}</p>
                  <p className="text-xs text-gray-500">{organizationName}</p>
                </div>
                <ChevronDown size={16} />
              </button>

              {/* Profile Dropdown Menu */}
              {isProfileDropdownOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                  <div className="px-4 py-2 border-b border-gray-100">
                    <p className="text-sm font-medium text-gray-900">{userName}</p>
                    <p className="text-xs text-gray-500">{organizationName}</p>
                  </div>
                  <button
                    onClick={() => {
                      onNavigate('profile');
                      setIsProfileDropdownOpen(false);
                    }}
                    className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <User size={16} />
                    Profile Settings
                  </button>
                  <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <Settings size={16} />
                    Account Settings
                  </button>
                  <hr className="my-1" />
                  <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                    <LogOut size={16} />
                    Sign Out
                  </button>
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden p-2 text-gray-600 hover:bg-gray-100 rounded-lg"
            >
              {isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <div className="space-y-2">
              {navItems.map((item) => (
                <NavItem key={item.id} item={item} isMobile />
              ))}
            </div>
            
            {/* Mobile Search */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type="text"
                  placeholder="Search..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Overlay for dropdowns */}
      {(isProfileDropdownOpen || isMobileMenuOpen) && (
        <div 
          className="fixed inset-0 z-30" 
          onClick={() => {
            setIsProfileDropdownOpen(false);
            setIsMobileMenuOpen(false);
          }}
        />
      )}
    </nav>
  );
};

// Example usage component showing how to integrate with both dashboards
const FinanceSystemWithNavbar: React.FC = () => {
  const [currentUser] = useState<'finance_officer' | 'payer'>('finance_officer');
  const [currentPage, setCurrentPage] = useState('dashboard');

  const handleNavigation = (page: string) => {
    setCurrentPage(page);
    console.log(`Navigating to: ${page}`);
    // Here you would implement actual navigation logic
    // For example: router.push(`/${page}`) or setState to show different components
  };

  const renderPageContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <div className="p-8">
            <h2 className="text-2xl font-bold text-green-800 mb-4">Dashboard</h2>
            <p className="text-gray-600">Welcome to your {currentUser === 'finance_officer' ? 'Finance Officer' : 'Payer'} dashboard.</p>
          </div>
        );
      case 'payments':
        return (
          <div className="p-8">
            <h2 className="text-2xl font-bold text-green-800 mb-4">Payments</h2>
            <p className="text-gray-600">Manage your payments here.</p>
          </div>
        );
      case 'receipts':
        return (
          <div className="p-8">
            <h2 className="text-2xl font-bold text-green-800 mb-4">Receipts</h2>
            <p className="text-gray-600">View and download receipts.</p>
          </div>
        );
      case 'profile':
        return (
          <div className="p-8">
            <h2 className="text-2xl font-bold text-green-800 mb-4">Profile</h2>
            <p className="text-gray-600">Manage your profile information.</p>
          </div>
        );
      case 'profiles':
        return (
          <div className="p-8">
            <h2 className="text-2xl font-bold text-green-800 mb-4">Profiles Management</h2>
            <p className="text-gray-600">Manage payer profiles and organizations.</p>
          </div>
        );
      case 'reports':
        return (
          <div className="p-8">
            <h2 className="text-2xl font-bold text-green-800 mb-4">Reports</h2>
            <p className="text-gray-600">Generate and view financial reports.</p>
          </div>
        );
      default:
        return (
          <div className="p-8">
            <h2 className="text-2xl font-bold text-green-800 mb-4">Page Not Found</h2>
            <p className="text-gray-600">The requested page could not be found.</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar
        userRole={currentUser}
        currentPage={currentPage}
        onNavigate={handleNavigation}
        userName="John Doe"
        organizationName="Acme Corporation"
      />
      
      <main className="min-h-screen" style={{ backgroundColor: '#dddeda' }}>
        {renderPageContent()}
      </main>
    </div>
  );
};

export default FinanceSystemWithNavbar;