namespace Final_E_Receipt.Reporting.Models
{
    public class DashboardSummary
    {
        public int TotalReceipts { get; set; }
        public decimal TotalAmount { get; set; }
        public int CompletedPayments { get; set; }
        public int PendingPayments { get; set; }
        public int FailedPayments { get; set; }
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
    }

    public class MonthlyRevenue
    {
        public int Month { get; set; }
        public int Year { get; set; }
        public decimal TotalAmount { get; set; }
        public int ReceiptCount { get; set; }
    }

    public class PaymentMethodSummary
    {
        public string PaymentMethod { get; set; }
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class CategorySummary
    {
        public string Category { get; set; }
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class DailyRevenue
    {
        public DateTime Date { get; set; }
        public decimal TotalAmount { get; set; }
        public int ReceiptCount { get; set; }
    }

    public class TopPayer
    {
        public string PayerId { get; set; }
        public string PayerName { get; set; }
        public string PayerEmail { get; set; }
        public int TransactionCount { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class YearOverYearComparison
    {
        public string Period { get; set; } // "Current" or "Previous"
        public int Month { get; set; }
        public decimal TotalAmount { get; set; }
    }
}
