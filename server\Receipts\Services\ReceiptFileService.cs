using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Final_E_Receipt.Receipts.Models;
using Final_E_Receipt.Files.Services;
using Final_E_Receipt.Files.Models;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Receipts.Services
{
    public class ReceiptFileService
    {
        private readonly FileService _fileService;
        private readonly ReceiptService _receiptService;
        private readonly ILogger<ReceiptFileService> _logger;

        public ReceiptFileService(
            FileService fileService,
            ReceiptService receiptService,
            ILogger<ReceiptFileService> logger)
        {
            _fileService = fileService;
            _receiptService = receiptService;
            _logger = logger;
        }

        /// <summary>
        /// Gets all files associated with a receipt through its linked payment
        /// </summary>
        public async Task<List<FileUpload>> GetReceiptSupportingFiles(string receiptId)
        {
            try
            {
                var receipt = await _receiptService.GetReceiptById(receiptId);
                if (receipt == null || string.IsNullOrEmpty(receipt.PaymentId))
                {
                    return new List<FileUpload>();
                }

                // Get payment proof files
                var paymentFiles = await _fileService.GetFilesByEntity("PAYMENT", receipt.PaymentId);
                
                // Get receipt-specific files
                var receiptFiles = await _fileService.GetFilesByEntity("RECEIPT", receiptId);

                // Combine both lists
                var allFiles = new List<FileUpload>();
                allFiles.AddRange(paymentFiles);
                allFiles.AddRange(receiptFiles);

                return allFiles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supporting files for receipt {ReceiptId}", receiptId);
                return new List<FileUpload>();
            }
        }

        /// <summary>
        /// Links receipt to payment proof files when receipt is created
        /// </summary>
        public async Task<bool> LinkReceiptToPaymentFiles(string receiptId, string paymentId)
        {
            try
            {
                if (string.IsNullOrEmpty(paymentId))
                    return true; // No payment to link

                var paymentFiles = await _fileService.GetFilesByEntity("PAYMENT", paymentId);
                
                if (paymentFiles.Count > 0)
                {
                    _logger.LogInformation(
                        "Receipt {ReceiptId} linked to {FileCount} payment proof files from payment {PaymentId}",
                        receiptId, paymentFiles.Count, paymentId);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error linking receipt {ReceiptId} to payment files", receiptId);
                return false;
            }
        }

        /// <summary>
        /// Creates a receipt with file attachments
        /// </summary>
        public async Task<ReceiptWithFiles> CreateReceiptWithFiles(Receipt receipt)
        {
            try
            {
                // Create the receipt first
                var createdReceipt = await _receiptService.CreateReceipt(receipt);
                if (createdReceipt == null)
                    return null;

                // Link to payment files
                await LinkReceiptToPaymentFiles(createdReceipt.Id, receipt.PaymentId);

                // Get all supporting files
                var supportingFiles = await GetReceiptSupportingFiles(createdReceipt.Id);

                return new ReceiptWithFiles
                {
                    Receipt = createdReceipt,
                    SupportingFiles = supportingFiles
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating receipt with files");
                return null;
            }
        }

        /// <summary>
        /// Gets receipt details with all supporting files
        /// </summary>
        public async Task<ReceiptWithFiles> GetReceiptWithFiles(string receiptId)
        {
            try
            {
                var receipt = await _receiptService.GetReceiptById(receiptId);
                if (receipt == null)
                    return null;

                var supportingFiles = await GetReceiptSupportingFiles(receiptId);

                return new ReceiptWithFiles
                {
                    Receipt = receipt,
                    SupportingFiles = supportingFiles
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting receipt with files for {ReceiptId}", receiptId);
                return null;
            }
        }

        /// <summary>
        /// Attaches additional files directly to a receipt
        /// </summary>
        public async Task<FileUpload> AttachFileToReceipt(
            string receiptId,
            Microsoft.AspNetCore.Http.IFormFile file,
            string uploadedBy,
            string organizationId,
            string description = null)
        {
            try
            {
                var receipt = await _receiptService.GetReceiptById(receiptId);
                if (receipt == null)
                    throw new ArgumentException("Receipt not found");

                var uploadedFile = await _fileService.UploadFile(
                    file,
                    "RECEIPT",
                    receiptId,
                    uploadedBy,
                    organizationId,
                    description ?? "Receipt supporting document",
                    "ATTACHMENT"
                );

                _logger.LogInformation(
                    "File {FileName} attached to receipt {ReceiptId}",
                    uploadedFile.OriginalFileName, receiptId);

                return uploadedFile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error attaching file to receipt {ReceiptId}", receiptId);
                throw;
            }
        }

        /// <summary>
        /// Gets file statistics for a receipt
        /// </summary>
        public async Task<ReceiptFileStats> GetReceiptFileStats(string receiptId)
        {
            try
            {
                var supportingFiles = await GetReceiptSupportingFiles(receiptId);
                
                var stats = new ReceiptFileStats
                {
                    TotalFiles = supportingFiles.Count,
                    PaymentProofFiles = supportingFiles.Count(f => f.RelatedEntityType == "PAYMENT"),
                    ReceiptAttachments = supportingFiles.Count(f => f.RelatedEntityType == "RECEIPT"),
                    TotalFileSize = supportingFiles.Sum(f => f.FileSize),
                    HasPaymentProof = supportingFiles.Any(f => f.RelatedEntityType == "PAYMENT" && f.Category == "PROOF")
                };

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting file stats for receipt {ReceiptId}", receiptId);
                return new ReceiptFileStats();
            }
        }
    }

    /// <summary>
    /// Receipt with associated files
    /// </summary>
    public class ReceiptWithFiles
    {
        public Receipt Receipt { get; set; }
        public List<FileUpload> SupportingFiles { get; set; } = new List<FileUpload>();
    }

    /// <summary>
    /// File statistics for a receipt
    /// </summary>
    public class ReceiptFileStats
    {
        public int TotalFiles { get; set; }
        public int PaymentProofFiles { get; set; }
        public int ReceiptAttachments { get; set; }
        public long TotalFileSize { get; set; }
        public bool HasPaymentProof { get; set; }
    }
}
