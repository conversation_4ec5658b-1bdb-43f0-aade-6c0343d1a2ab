import { useState, useCallback } from 'react';
import { apiService } from '../../services/apiService';

// Types for Organization API (Updated to match backend OrganizationProcedures.sql)
export interface Organization {
  id: string;
  name: string;
  email: string;
  phoneNumber?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  logoUrl?: string;
  website?: string;
  isActive: boolean;
  createdAt: string;
  createdBy?: string;
}

export interface CreateOrganizationDTO {
  name: string;
  email: string;
  phoneNumber?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  logoUrl?: string;
  website?: string;
}

export interface UpdateOrganizationDTO {
  name?: string;
  email?: string;
  phoneNumber?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  logoUrl?: string;
  website?: string;
}

export interface OrganizationCompliance {
  organizationId: string;
  complianceStatus: string;
  lastAuditDate?: string;
  nextAuditDate?: string;
  certificatesCount: number;
  expiredCertificates: number;
  expiringCertificates: number;
}

// Organization API Hooks
export const useOrganizationApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Organization CRUD endpoints
  const getAllOrganizations = useCallback(() => 
    handleApiCall(() => apiService.get<Organization[]>('/Organization')), [handleApiCall]);

  const getOrganizationById = useCallback((id: string) => 
    handleApiCall(() => apiService.get<Organization>(`/Organization/${id}`)), [handleApiCall]);

  const createOrganization = useCallback((data: CreateOrganizationDTO) => 
    handleApiCall(() => apiService.post<Organization>('/Organization', data)), [handleApiCall]);

  const updateOrganization = useCallback((id: string, data: UpdateOrganizationDTO) => 
    handleApiCall(() => apiService.put<Organization>(`/Organization/${id}`, data)), [handleApiCall]);

  const deleteOrganization = useCallback((id: string) => 
    handleApiCall(() => apiService.delete(`/Organization/${id}`)), [handleApiCall]);

  // Organization search and filtering
  const searchOrganizations = useCallback((query: string) => 
    handleApiCall(() => apiService.get<Organization[]>('/Organization/search', { query })), [handleApiCall]);

  const getActiveOrganizations = useCallback(() => 
    handleApiCall(() => apiService.get<Organization[]>('/Organization/active')), [handleApiCall]);

  return {
    loading,
    error,
    getAllOrganizations,
    getOrganizationById,
    createOrganization,
    updateOrganization,
    deleteOrganization,
    searchOrganizations,
    getActiveOrganizations,
  };
};

// Organization Compliance API Hooks
export const useOrganizationComplianceApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Organization compliance endpoints
  const getOrganizationComplianceStatus = useCallback((id: string) => 
    handleApiCall(() => apiService.get<OrganizationCompliance>(`/organizations/${id}/compliance/status`)), [handleApiCall]);

  const getOrganizationComplianceSummary = useCallback((id: string) => 
    handleApiCall(() => apiService.get(`/organizations/${id}/compliance/summary`)), [handleApiCall]);

  const getAllOrganizationsCompliance = useCallback(() => 
    handleApiCall(() => apiService.get<OrganizationCompliance[]>('/compliance/organizations')), [handleApiCall]);

  const getComplianceAlerts = useCallback(() => 
    handleApiCall(() => apiService.get('/compliance/alerts')), [handleApiCall]);

  return {
    loading,
    error,
    getOrganizationComplianceStatus,
    getOrganizationComplianceSummary,
    getAllOrganizationsCompliance,
    getComplianceAlerts,
  };
};
