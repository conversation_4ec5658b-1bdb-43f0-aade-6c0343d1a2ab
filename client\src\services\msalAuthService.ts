import { msalInstance, loginRequest } from "../auth/msalConfig";

// Track login state to prevent multiple attempts
let isSigningIn = false;
let tokenRefreshTimer: NodeJS.Timeout | null = null;
let isRefreshing = false;

// Following Microsoft's official pattern
export async function signIn() {
  // Prevent multiple simultaneous sign-in attempts
  if (isSigningIn) {
    throw new Error("Sign-in already in progress");
  }

  try {
    isSigningIn = true;

    // Check if there's already an interaction in progress
    const accounts = msalInstance.getAllAccounts();

    // If we have accounts but no active account, set one
    if (accounts.length > 0 && !msalInstance.getActiveAccount()) {
      msalInstance.setActiveAccount(accounts[0]);
    }

    const loginResponse = await msalInstance.loginPopup(loginRequest);
    return loginResponse.account;
  } catch (error: any) {
    // Handle interaction_in_progress error specifically
    if (error.errorCode === 'interaction_in_progress') {
      console.log('Interaction already in progress, waiting...');
      // Wait a bit and try to get existing account
      await new Promise(resolve => setTimeout(resolve, 1000));
      const accounts = msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        return accounts[0];
      }
    }
    console.error("Login failed:", error);
    throw error;
  } finally {
    isSigningIn = false;
  }
}

export async function getAccessToken() {
  const accounts = msalInstance.getAllAccounts();
  if (accounts.length === 0) throw new Error("No accounts found");

  try {
    const response = await msalInstance.acquireTokenSilent({
      ...loginRequest,
      account: accounts[0],
    });

    // Schedule token refresh before expiration
    scheduleTokenRefresh(response.expiresOn);

    return response.accessToken;
  } catch (error: any) {
    // Handle interaction_in_progress error
    if (error.errorCode === 'interaction_in_progress') {
      // Wait and retry silent acquisition
      await new Promise(resolve => setTimeout(resolve, 1000));
      try {
        const retryResponse = await msalInstance.acquireTokenSilent({
          ...loginRequest,
          account: accounts[0],
        });
        scheduleTokenRefresh(retryResponse.expiresOn);
        return retryResponse.accessToken;
      } catch (retryError) {
        throw new Error("Token acquisition failed - interaction in progress");
      }
    }

    // For other errors, try popup
    try {
      const response = await msalInstance.acquireTokenPopup(loginRequest);
      scheduleTokenRefresh(response.expiresOn);
      return response.accessToken;
    } catch (popupError: any) {
      if (popupError.errorCode === 'interaction_in_progress') {
        throw new Error("Cannot acquire token - interaction already in progress");
      }
      throw popupError;
    }
  }
}

// Refresh token silently
export async function refreshAccessToken(): Promise<string | null> {
  if (isRefreshing) {
    console.log('Token refresh already in progress');
    return null;
  }

  const accounts = msalInstance.getAllAccounts();
  if (accounts.length === 0) {
    console.log('No accounts found for token refresh');
    return null;
  }

  try {
    isRefreshing = true;
    console.log('Refreshing access token...');

    const response = await msalInstance.acquireTokenSilent({
      ...loginRequest,
      account: accounts[0],
      forceRefresh: true, // Force refresh to get new token
    });

    // Schedule next refresh
    scheduleTokenRefresh(response.expiresOn);

    console.log('Token refreshed successfully');
    return response.accessToken;
  } catch (error: any) {
    console.error('Token refresh failed:', error);

    // If refresh fails, user needs to re-authenticate
    if (error.errorCode === 'consent_required' ||
        error.errorCode === 'interaction_required' ||
        error.errorCode === 'login_required') {
      console.log('User interaction required for token refresh');
      // Don't throw here, let the app handle re-authentication
      return null;
    }

    throw error;
  } finally {
    isRefreshing = false;
  }
}

// Schedule automatic token refresh
function scheduleTokenRefresh(expiresOn: Date | null) {
  // Clear existing timer
  if (tokenRefreshTimer) {
    clearTimeout(tokenRefreshTimer);
    tokenRefreshTimer = null;
  }

  if (!expiresOn) return;

  // Calculate time until token expires (refresh 5 minutes before expiration)
  const now = new Date().getTime();
  const expirationTime = expiresOn.getTime();
  const refreshTime = expirationTime - (5 * 60 * 1000); // 5 minutes before expiration
  const timeUntilRefresh = refreshTime - now;

  // Only schedule if we have at least 1 minute until refresh
  if (timeUntilRefresh > 60000) {
    console.log(`Token refresh scheduled in ${Math.round(timeUntilRefresh / 1000 / 60)} minutes`);

    tokenRefreshTimer = setTimeout(async () => {
      try {
        await refreshAccessToken();
      } catch (error) {
        console.error('Scheduled token refresh failed:', error);
        // Emit event for app to handle re-authentication
        window.dispatchEvent(new CustomEvent('tokenRefreshFailed', { detail: error }));
      }
    }, timeUntilRefresh);
  } else {
    console.log('Token expires soon, immediate refresh needed');
    // Token expires very soon, try immediate refresh
    setTimeout(() => refreshAccessToken(), 1000);
  }
}

// Clear token refresh timer
export function clearTokenRefreshTimer() {
  if (tokenRefreshTimer) {
    clearTimeout(tokenRefreshTimer);
    tokenRefreshTimer = null;
  }
}



export async function signOut(useRedirect: boolean = false) {
  try {
    console.log('Starting logout process...');

    // Clear token refresh timer
    clearTokenRefreshTimer();

    // Clear any pending interactions
    await clearInteractions();

    const accounts = msalInstance.getAllAccounts();
    if (accounts.length > 0) {
      const account = accounts[0];

      if (useRedirect) {
        // Use redirect logout
        await msalInstance.logoutRedirect({
          account: account,
          postLogoutRedirectUri: window.location.origin,
        });
      } else {
        // Use popup logout
        await msalInstance.logoutPopup({
          account: account,
          postLogoutRedirectUri: window.location.origin,
        });
      }
    }

    // Clear all local storage and session storage related to auth
    clearLocalAuthData();

    console.log('Logout completed successfully');
  } catch (error: any) {
    console.error('Logout error:', error);

    // Even if logout fails, clear local data
    clearLocalAuthData();

    // For certain errors, force clear and reload
    if (error.errorCode === 'interaction_in_progress') {
      console.log('Forcing logout due to interaction in progress');
      clearLocalAuthData();
      window.location.reload();
    }

    throw error;
  }
}

// Clear all local authentication data
function clearLocalAuthData() {
  try {
    // Clear MSAL active account
    msalInstance.setActiveAccount(null);

    // Clear any auth-related items from localStorage
    const authKeys = [
      'msal.account.keys',
      'msal.token.keys',
      'msal.cache.keys',
      'user',
      'authToken',
      'accessToken',
    ];

    authKeys.forEach(key => {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    });

    // Clear any items that start with 'msal.'
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('msal.')) {
        localStorage.removeItem(key);
      }
    });

    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith('msal.')) {
        sessionStorage.removeItem(key);
      }
    });

    console.log('Local auth data cleared');
  } catch (error) {
    console.error('Error clearing local auth data:', error);
  }
}

export function getCurrentAccount() {
  const accounts = msalInstance.getAllAccounts();
  return accounts.length > 0 ? accounts[0] : null;
}

export function isLoggedIn() {
  return msalInstance.getAllAccounts().length > 0;
}

export async function clearInteractions() {
  try {
    // Handle any pending redirect responses
    await msalInstance.handleRedirectPromise();

    // Reset all interaction flags
    isSigningIn = false;
    isRefreshing = false;

    // Wait a moment for any pending operations to complete
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('Cleared any pending interactions');
  } catch (error) {
    console.error('Error clearing interactions:', error);
    // Reset flags even if there's an error
    isSigningIn = false;
    isRefreshing = false;
  }
}
