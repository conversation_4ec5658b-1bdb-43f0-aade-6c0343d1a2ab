-- Organizations Table
CREATE TABLE Organizations (
    Id NVARCHAR(50) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    Email NVARCHAR(255),
    PhoneNumber NVARCHAR(20),
    Address NVARCHAR(255),
    City NVARCHAR(100),
    State NVARCHAR(100),
    Country NVARCHAR(100),
    LogoUrl NVARCHAR(255),
    Website NVARCHAR(255),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50)
);

-- Stored Procedures for Organizations
CREATE PROCEDURE CreateOrganization
    @Id NVARCHAR(50),
    @Name NVARCHAR(100),
    @Email NVARCHAR(255),
    @PhoneNumber NVARCHAR(20),
    @Address NVARCHAR(255),
    @City NVARCHAR(100),
    @State NVARCHAR(100),
    @Country NVARCHAR(100),
    @LogoUrl NVARCHAR(255),
    @Website NVARCHAR(255),
    @IsActive BIT,
    @CreatedBy NVARCHAR(50)
AS
BEGIN
    INSERT INTO Organizations (
        Id, Name, Email, PhoneNumber, Address, City, State, Country,
        LogoUrl, Website, IsActive, CreatedBy
    )
    VALUES (
        @Id, @Name, @Email, @PhoneNumber, @Address, @City, @State, @Country,
        @LogoUrl, @Website, @IsActive, @CreatedBy
    )
    
    SELECT * FROM Organizations WHERE Id = @Id
END;

CREATE PROCEDURE GetOrganizationById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Organizations WHERE Id = @Id
END;

CREATE PROCEDURE GetAllOrganizations
AS
BEGIN
    SELECT * FROM Organizations ORDER BY Name
END;

CREATE PROCEDURE UpdateOrganization
    @Id NVARCHAR(50),
    @Name NVARCHAR(100),
    @Email NVARCHAR(255),
    @PhoneNumber NVARCHAR(20),
    @Address NVARCHAR(255),
    @City NVARCHAR(100),
    @State NVARCHAR(100),
    @Country NVARCHAR(100),
    @LogoUrl NVARCHAR(255),
    @Website NVARCHAR(255)
AS
BEGIN
    UPDATE Organizations
    SET 
        Name = @Name,
        Email = @Email,
        PhoneNumber = @PhoneNumber,
        Address = @Address,
        City = @City,
        State = @State,
        Country = @Country,
        LogoUrl = @LogoUrl,
        Website = @Website
    WHERE Id = @Id
    
    SELECT * FROM Organizations WHERE Id = @Id
END;

CREATE PROCEDURE UpdateOrganizationStatus
    @Id NVARCHAR(50),
    @IsActive BIT
AS
BEGIN
    UPDATE Organizations
    SET IsActive = @IsActive
    WHERE Id = @Id

    SELECT * FROM Organizations WHERE Id = @Id
END;

CREATE PROCEDURE SearchOrganizations
    @SearchQuery NVARCHAR(255)
AS
BEGIN
    SELECT * FROM Organizations
    WHERE Name LIKE '%' + @SearchQuery + '%'
       OR Email LIKE '%' + @SearchQuery + '%'
       OR City LIKE '%' + @SearchQuery + '%'
       OR State LIKE '%' + @SearchQuery + '%'
       OR Country LIKE '%' + @SearchQuery + '%'
    ORDER BY Name
END;

CREATE PROCEDURE GetActiveOrganizations
AS
BEGIN
    SELECT * FROM Organizations
    WHERE IsActive = 1
    ORDER BY Name
END;