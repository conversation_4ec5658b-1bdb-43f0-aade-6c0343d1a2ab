-- Create AuditLogs Table
CREATE TABLE IF NOT EXISTS AuditLogs (
    Id NVARCHAR(50) PRIMARY KEY,
    UserId NVARCHAR(50) NOT NULL,
    UserName NVARCHAR(255) NOT NULL,
    UserEmail NVARCHAR(255) NOT NULL,
    Action NVARCHAR(100) NOT NULL,
    EntityType NVARCHAR(100) NOT NULL,
    EntityId NVARCHAR(50) NOT NULL,
    OldValues NVARCHAR(MAX) NULL,
    NewValues NVARCHAR(MAX) NULL,
    AdditionalDetails NVARCHAR(MAX) NULL,
    IpAddress NVARCHAR(50) NOT NULL,
    UserAgent NVARCHAR(500) NOT NULL,
    Timestamp DATETIME NOT NULL DEFAULT GETDATE(),
    OrganizationId NVARCHAR(50) NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS IX_AuditLogs_UserId ON AuditLogs(UserId);
CREATE INDEX IF NOT EXISTS IX_AuditLogs_Action ON AuditLogs(Action);
CREATE INDEX IF NOT EXISTS IX_AuditLogs_EntityType ON AuditLogs(EntityType);
CREATE INDEX IF NOT EXISTS IX_AuditLogs_EntityId ON AuditLogs(EntityId);
CREATE INDEX IF NOT EXISTS IX_AuditLogs_Timestamp ON AuditLogs(Timestamp);
CREATE INDEX IF NOT EXISTS IX_AuditLogs_OrganizationId ON AuditLogs(OrganizationId);

-- Create Audit Log
CREATE OR ALTER PROCEDURE CreateAuditLog
    @Id NVARCHAR(50),
    @UserId NVARCHAR(50),
    @UserName NVARCHAR(255),
    @UserEmail NVARCHAR(255),
    @Action NVARCHAR(100),
    @EntityType NVARCHAR(100),
    @EntityId NVARCHAR(50),
    @OldValues NVARCHAR(MAX) = NULL,
    @NewValues NVARCHAR(MAX) = NULL,
    @AdditionalDetails NVARCHAR(MAX) = NULL,
    @IpAddress NVARCHAR(50),
    @UserAgent NVARCHAR(500),
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    INSERT INTO AuditLogs (
        Id, UserId, UserName, UserEmail, Action, EntityType, EntityId,
        OldValues, NewValues, AdditionalDetails, IpAddress, UserAgent, 
        Timestamp, OrganizationId
    )
    VALUES (
        @Id, @UserId, @UserName, @UserEmail, @Action, @EntityType, @EntityId,
        @OldValues, @NewValues, @AdditionalDetails, @IpAddress, @UserAgent,
        GETDATE(), @OrganizationId
    )
END;

-- Get Audit Logs with filtering
CREATE OR ALTER PROCEDURE GetAuditLogs
    @UserId NVARCHAR(50) = NULL,
    @Action NVARCHAR(100) = NULL,
    @EntityType NVARCHAR(100) = NULL,
    @OrganizationId NVARCHAR(50) = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageSize INT = 50,
    @Offset INT = 0
AS
BEGIN
    SELECT 
        Id, UserId, UserName, UserEmail, Action, EntityType, EntityId,
        OldValues, NewValues, AdditionalDetails, IpAddress, UserAgent,
        Timestamp, OrganizationId
    FROM AuditLogs
    WHERE 
        (@UserId IS NULL OR UserId = @UserId)
        AND (@Action IS NULL OR Action = @Action)
        AND (@EntityType IS NULL OR EntityType = @EntityType)
        AND (@OrganizationId IS NULL OR OrganizationId = @OrganizationId)
        AND (@StartDate IS NULL OR Timestamp >= @StartDate)
        AND (@EndDate IS NULL OR Timestamp <= @EndDate)
    ORDER BY Timestamp DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY
END;

-- Get Audit Logs for specific entity
CREATE OR ALTER PROCEDURE GetEntityAuditLogs
    @EntityType NVARCHAR(100),
    @EntityId NVARCHAR(50)
AS
BEGIN
    SELECT 
        Id, UserId, UserName, UserEmail, Action, EntityType, EntityId,
        OldValues, NewValues, AdditionalDetails, IpAddress, UserAgent,
        Timestamp, OrganizationId
    FROM AuditLogs
    WHERE EntityType = @EntityType AND EntityId = @EntityId
    ORDER BY Timestamp DESC
END;

-- Get Audit Statistics
CREATE OR ALTER PROCEDURE GetAuditStats
    @StartDate DATETIME,
    @EndDate DATETIME
AS
BEGIN
    -- Total actions count
    SELECT 
        Action,
        COUNT(*) as Count
    FROM AuditLogs
    WHERE Timestamp BETWEEN @StartDate AND @EndDate
    GROUP BY Action
    ORDER BY Count DESC;

    -- Actions by entity type
    SELECT 
        EntityType,
        COUNT(*) as Count
    FROM AuditLogs
    WHERE Timestamp BETWEEN @StartDate AND @EndDate
    GROUP BY EntityType
    ORDER BY Count DESC;

    -- Most active users
    SELECT TOP 10
        UserId,
        UserName,
        UserEmail,
        COUNT(*) as ActionCount
    FROM AuditLogs
    WHERE Timestamp BETWEEN @StartDate AND @EndDate
    GROUP BY UserId, UserName, UserEmail
    ORDER BY ActionCount DESC;

    -- Daily activity
    SELECT 
        CAST(Timestamp AS DATE) as Date,
        COUNT(*) as ActionCount
    FROM AuditLogs
    WHERE Timestamp BETWEEN @StartDate AND @EndDate
    GROUP BY CAST(Timestamp AS DATE)
    ORDER BY Date DESC;
END;

-- Get Recent Audit Logs (for dashboard)
CREATE OR ALTER PROCEDURE GetRecentAuditLogs
    @OrganizationId NVARCHAR(50) = NULL,
    @TopCount INT = 20
AS
BEGIN
    SELECT TOP (@TopCount)
        Id, UserId, UserName, UserEmail, Action, EntityType, EntityId,
        AdditionalDetails, Timestamp, OrganizationId
    FROM AuditLogs
    WHERE (@OrganizationId IS NULL OR OrganizationId = @OrganizationId)
    ORDER BY Timestamp DESC
END;

-- Clean up old audit logs (for maintenance)
CREATE OR ALTER PROCEDURE CleanupOldAuditLogs
    @RetentionDays INT = 365
AS
BEGIN
    DELETE FROM AuditLogs
    WHERE Timestamp < DATEADD(DAY, -@RetentionDays, GETDATE())
END;
