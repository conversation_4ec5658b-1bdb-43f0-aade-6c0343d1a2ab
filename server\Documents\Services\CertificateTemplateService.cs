using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Final_E_Receipt.Documents.Models;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Documents.Services
{
    public class CertificateTemplateService
    {
        private readonly string _templateBasePath = "wwwroot/templates";
        private readonly ILogger<CertificateTemplateService> _logger;

        public CertificateTemplateService(ILogger<CertificateTemplateService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Get available templates for organization and certificate type
        /// </summary>
        public async Task<List<AvailableTemplate>> GetAvailableTemplates(string organizationId, string certificateType)
        {
            var templates = new List<AvailableTemplate>();

            try
            {
                // Get organization-specific templates
                var orgTemplates = GetOrganizationTemplates(organizationId, certificateType);
                templates.AddRange(orgTemplates);

                // Add default templates if no organization-specific ones exist
                if (!templates.Any())
                {
                    var defaultTemplates = GetDefaultTemplates(certificateType);
                    templates.AddRange(defaultTemplates);
                }

                return await Task.FromResult(templates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available templates for {OrganizationId}, {CertificateType}", organizationId, certificateType);
                return new List<AvailableTemplate>();
            }
        }

        /// <summary>
        /// Get organization-specific templates (Dynamic)
        /// </summary>
        private List<AvailableTemplate> GetOrganizationTemplates(string organizationId, string certificateType)
        {
            // Generate templates dynamically based on available template files
            return GetDynamicTemplatesForOrganization(organizationId, certificateType);
        }

        /// <summary>
        /// Get default templates (Dynamic based on certificate type)
        /// </summary>
        private List<AvailableTemplate> GetDefaultTemplates(string certificateType)
        {
            var certificateTypeName = FormatCertificateType(certificateType);
            var defaultOrientation = GetDefaultOrientation(certificateType);

            var templates = new List<AvailableTemplate>();

            // Add default template with appropriate orientation
            templates.Add(new AvailableTemplate
            {
                Id = $"default-{certificateType.ToLower()}",
                Name = $"Default {certificateTypeName}",
                Description = $"Standard {certificateTypeName.ToLower()} certificate template",
                PreviewImageUrl = "/templates/previews/default-certificate-preview.png",
                Orientation = defaultOrientation,
                IsDefault = true,
                OrganizationName = "Default",
                CertificateTypeName = certificateTypeName
            });

            // Add alternative orientation if different from default
            var alternativeOrientation = defaultOrientation == PageOrientation.Landscape
                ? PageOrientation.Portrait
                : PageOrientation.Landscape;

            templates.Add(new AvailableTemplate
            {
                Id = $"default-{certificateType.ToLower()}-{alternativeOrientation.ToString().ToLower()}",
                Name = $"Default {certificateTypeName} ({alternativeOrientation})",
                Description = $"Standard {certificateTypeName.ToLower()} certificate template - {alternativeOrientation.ToString().ToLower()} format",
                PreviewImageUrl = $"/templates/previews/default-certificate-{alternativeOrientation.ToString().ToLower()}-preview.png",
                Orientation = alternativeOrientation,
                IsDefault = false,
                OrganizationName = "Default",
                CertificateTypeName = certificateTypeName
            });

            return templates;
        }

        /// <summary>
        /// Generate available templates dynamically for any organization
        /// </summary>
        private Dictionary<string, List<AvailableTemplate>> GetOrganizationTemplateDefinitions()
        {
            // This would typically query the database for organizations
            // For now, return dynamic templates that work for any organization
            return new Dictionary<string, List<AvailableTemplate>>();
        }

        /// <summary>
        /// Generate JRB templates - generalized templates for all certificate types
        /// </summary>
        private List<AvailableTemplate> GetDynamicTemplatesForOrganization(string organizationId, string certificateType)
        {
            var templates = new List<AvailableTemplate>();
            var certificateTypeName = FormatCertificateType(certificateType);

            // JRB uses generalized templates based on orientation only
            var landscapeTemplate = Path.Combine(_templateBasePath, "certificates", "jrb-certificate-landscape-template.png");
            var portraitTemplate = Path.Combine(_templateBasePath, "certificates", "jrb-certificate-portrait-template.png");

            // Determine default orientation based on certificate type
            var defaultOrientation = GetDefaultOrientation(certificateType);

            // Add landscape option if template exists
            if (File.Exists(landscapeTemplate))
            {
                templates.Add(new AvailableTemplate
                {
                    Id = $"jrb-certificate-landscape",
                    Name = $"{certificateTypeName} (Landscape)",
                    Description = $"JRB {certificateTypeName} certificate - landscape format",
                    PreviewImageUrl = "/templates/previews/jrb-certificate-landscape-preview.png",
                    Orientation = PageOrientation.Landscape,
                    IsDefault = defaultOrientation == PageOrientation.Landscape,
                    OrganizationName = "Joint Revenue Board",
                    CertificateTypeName = certificateTypeName
                });
            }

            // Add portrait option if template exists
            if (File.Exists(portraitTemplate))
            {
                templates.Add(new AvailableTemplate
                {
                    Id = $"jrb-certificate-portrait",
                    Name = $"{certificateTypeName} (Portrait)",
                    Description = $"JRB {certificateTypeName} certificate - portrait format",
                    PreviewImageUrl = "/templates/previews/jrb-certificate-portrait-preview.png",
                    Orientation = PageOrientation.Portrait,
                    IsDefault = defaultOrientation == PageOrientation.Portrait,
                    OrganizationName = "Joint Revenue Board",
                    CertificateTypeName = certificateTypeName
                });
            }

            // If no JRB templates exist, use default templates
            if (templates.Count == 0)
            {
                templates.AddRange(GetFallbackTemplates(certificateType));
            }

            return templates;
        }

        /// <summary>
        /// Get default orientation for certificate type
        /// </summary>
        private PageOrientation GetDefaultOrientation(string certificateType)
        {
            return certificateType switch
            {
                "ANNUAL_LICENSE" => PageOrientation.Landscape, // Licenses typically landscape
                "TAX_CLEARANCE" => PageOrientation.Portrait,   // Clearances typically portrait
                "QUARTERLY_COMPLIANCE" => PageOrientation.Portrait,
                "PAYMENT_COMPLIANCE" => PageOrientation.Portrait,
                _ => PageOrientation.Portrait // Default to portrait
            };
        }

        /// <summary>
        /// Get organization display name (would query database in real implementation)
        /// </summary>
        private string GetOrganizationDisplayName(string organizationId)
        {
            // In a real implementation, this would query the Organizations table
            // For now, return a generic name
            return "Organization";
        }

        /// <summary>
        /// Get fallback templates when no organization-specific templates exist
        /// </summary>
        private List<AvailableTemplate> GetFallbackTemplates(string certificateType)
        {
            var certificateTypeName = FormatCertificateType(certificateType);
            var defaultOrientation = GetDefaultOrientation(certificateType);

            return new List<AvailableTemplate>
            {
                new AvailableTemplate
                {
                    Id = $"default-{certificateType.ToLower()}",
                    Name = $"Default {certificateTypeName}",
                    Description = $"Standard {certificateTypeName.ToLower()} certificate template",
                    PreviewImageUrl = "/templates/previews/default-certificate-preview.png",
                    Orientation = defaultOrientation,
                    IsDefault = true,
                    OrganizationName = "Default",
                    CertificateTypeName = certificateTypeName
                }
            };
        }

        /// <summary>
        /// Get template configuration by ID (Dynamic)
        /// </summary>
        public async Task<DocumentConfiguration> GetTemplateConfiguration(string templateId, string organizationId, string certificateType)
        {
            try
            {
                // Create dynamic template configuration based on template ID
                var configuration = CreateDynamicTemplateConfiguration(templateId, organizationId, certificateType);
                return await Task.FromResult(configuration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting template configuration for {TemplateId}", templateId);
                return null;
            }
        }

        /// <summary>
        /// Map template IDs to configurations (Dynamic)
        /// </summary>
        private Dictionary<string, DocumentConfiguration> GetTemplateIdMapping()
        {
            // This would be populated dynamically based on available template files
            // For now, return empty - will be handled by GetTemplateConfiguration method
            return new Dictionary<string, DocumentConfiguration>();
        }

        /// <summary>
        /// Create JRB template configuration from template ID
        /// </summary>
        private DocumentConfiguration CreateDynamicTemplateConfiguration(string templateId, string organizationId, string certificateType)
        {
            // JRB uses generalized templates based on orientation
            var isLandscape = templateId.Contains("-landscape");
            var orientation = isLandscape ? PageOrientation.Landscape : PageOrientation.Portrait;

            // Build JRB template path
            var templateFileName = isLandscape
                ? "jrb-certificate-landscape-template.png"
                : "jrb-certificate-portrait-template.png";

            var templatePath = Path.Combine(_templateBasePath, "certificates", templateFileName);

            // Fallback to default if JRB template doesn't exist
            if (!File.Exists(templatePath))
            {
                templatePath = Path.Combine(_templateBasePath, "certificates", "default-certificate-template.png");
            }

            return new DocumentConfiguration
            {
                TemplatePath = templatePath,
                FontFamily = "Arial",
                FontSize = 13f,
                FontStyle = System.Drawing.FontStyle.Bold,
                TextColor = System.Drawing.Color.Black,
                PdfSettings = new PdfSettings { Orientation = orientation },
                TextPositions = GetStandardTextPositions(orientation),
                DataMapper = (data) => MapCertificateData((Final_E_Receipt.Compliance.Models.ComplianceCertificate)data)
            };
        }

        /// <summary>
        /// Get standard text positions based on orientation
        /// </summary>
        private Dictionary<string, System.Drawing.PointF> GetStandardTextPositions(PageOrientation orientation)
        {
            if (orientation == PageOrientation.Landscape)
            {
                // Landscape positions (wider layout)
                return new Dictionary<string, System.Drawing.PointF>
                {
                    { "CertificateNumber", new System.Drawing.PointF(600, 150) },
                    { "OrganizationName", new System.Drawing.PointF(300, 250) },
                    { "CertificateType", new System.Drawing.PointF(300, 300) },
                    { "TotalAmount", new System.Drawing.PointF(500, 400) },
                    { "ValidFrom", new System.Drawing.PointF(200, 450) },
                    { "ValidUntil", new System.Drawing.PointF(500, 450) },
                    { "IssuedDate", new System.Drawing.PointF(300, 500) },
                    { "RegulatoryBody", new System.Drawing.PointF(300, 550) },
                    { "ComplianceYear", new System.Drawing.PointF(600, 200) },
                    { "PaymentProfileName", new System.Drawing.PointF(300, 350) }
                };
            }
            else
            {
                // Portrait positions (taller layout)
                return new Dictionary<string, System.Drawing.PointF>
                {
                    { "CertificateNumber", new System.Drawing.PointF(400, 120) },
                    { "OrganizationName", new System.Drawing.PointF(200, 220) },
                    { "CertificateType", new System.Drawing.PointF(200, 270) },
                    { "TotalAmount", new System.Drawing.PointF(400, 350) },
                    { "ValidFrom", new System.Drawing.PointF(200, 400) },
                    { "ValidUntil", new System.Drawing.PointF(400, 400) },
                    { "IssuedDate", new System.Drawing.PointF(200, 450) },
                    { "RegulatoryBody", new System.Drawing.PointF(200, 500) },
                    { "ComplianceYear", new System.Drawing.PointF(400, 170) },
                    { "PaymentProfileName", new System.Drawing.PointF(200, 320) }
                };
            }
        }

        /// <summary>
        /// Map certificate data to text dictionary (same as BrandedDocumentService)
        /// </summary>
        private Dictionary<string, string> MapCertificateData(Final_E_Receipt.Compliance.Models.ComplianceCertificate certificate)
        {
            return new Dictionary<string, string>
            {
                { "CertificateNumber", certificate.CertificateNumber ?? "" },
                { "OrganizationName", certificate.OrganizationName ?? "" },
                { "CertificateType", FormatCertificateType(certificate.CertificateType) },
                { "TotalAmount", $"{certificate.Currency} {certificate.TotalAmount:N2}" },
                { "ValidFrom", certificate.ValidFrom.ToString("MMMM dd, yyyy") },
                { "ValidUntil", certificate.ValidUntil.ToString("MMMM dd, yyyy") },
                { "IssuedDate", certificate.IssuedDate.ToString("MMMM dd, yyyy") },
                { "RegulatoryBody", certificate.RegulatoryBody ?? "" },
                { "ComplianceYear", certificate.ComplianceYear ?? "" },
                { "PaymentProfileName", certificate.PaymentProfileName ?? "" }
            };
        }

        /// <summary>
        /// Format certificate type for display
        /// </summary>
        private string FormatCertificateType(string certificateType)
        {
            return certificateType switch
            {
                "ANNUAL_LICENSE" => "Annual License",
                "TAX_CLEARANCE" => "Tax Clearance",
                "QUARTERLY_COMPLIANCE" => "Quarterly Compliance",
                _ => certificateType?.Replace("_", " ") ?? ""
            };
        }
    }
}
