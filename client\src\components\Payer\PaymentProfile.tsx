import React, { useState } from 'react';
import { Eye, Upload, CreditCard, Download, Clock, CheckCircle, XCircle, X } from 'lucide-react';

// Types
interface PaymentProfile {
  id: string;
  name: string;
  outstandingBalance: number;
  dueDate: string;
  status: 'pending' | 'overdue' | 'paid';
}

interface Payment {
  id: string;
  profileId: string;
  amount: number;
  date: string;
  status: 'pending' | 'approved' | 'rejected';
  receiptUrl?: string;
  proofUrl?: string;
}

// Utility Components
const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'pending':
        return { icon: Clock, color: 'bg-yellow-100 text-yellow-800', label: 'Pending' };
      case 'approved':
        return { icon: CheckCircle, color: 'bg-green-100 text-green-800', label: 'Approved' };
      case 'rejected':
        return { icon: XCircle, color: 'bg-red-100 text-red-800', label: 'Rejected' };
      case 'overdue':
        return { icon: XCircle, color: 'bg-red-100 text-red-800', label: 'Overdue' };
      case 'paid':
        return { icon: CheckCircle, color: 'bg-green-100 text-green-800', label: 'Paid' };
      default:
        return { icon: Clock, color: 'bg-gray-100 text-gray-800', label: status };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
      <Icon size={12} />
      {config.label}
    </span>
  );
};

// Payment Profiles Table Component
const PaymentProfilesTable: React.FC<{
  paymentProfiles: PaymentProfile[];
  onViewProfile: (id: string) => void;
  onUploadProof: (id: string) => void;
}> = ({ paymentProfiles, onViewProfile, onUploadProof }) => {
  const formatCurrency = (amount: number) => `₦${amount.toLocaleString('en-NG', { minimumFractionDigits: 2 })}`;
  const formatDate = (date: string) => new Date(date).toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  });

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="bg-[#045024] px-6 py-4 border-b border-gray-200 rounded-t-lg">
        <h2 className="text-xl font-semibold text-white">Payment Profiles</h2>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 uppercase tracking-wider">Payment Name</th>
              <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 uppercase tracking-wider">Outstanding Balance</th>
              <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
              <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {paymentProfiles.map((profile) => (
              <tr key={profile.id} className="hover:bg-gray-50">
                <td className="py-4 px-6 text-sm font-medium text-gray-900">{profile.name}</td>
                <td className="py-4 px-6 text-sm text-gray-900 font-medium">{formatCurrency(profile.outstandingBalance)}</td>
                <td className="py-4 px-6 text-sm text-gray-700">{formatDate(profile.dueDate)}</td>
                <td className="py-4 px-6">
                  <StatusBadge status={profile.status} />
                </td>
                <td className="py-4 px-6">
                  <div className="flex gap-2">
                    <button 
                      onClick={() => onViewProfile(profile.id)}
                      className="p-2 text-gray-400 hover:text-green-600 hover:bg-gray-100 rounded-lg transition-colors"
                      title="View details"
                    >
                      <Eye size={16} />
                    </button>
                    {profile.outstandingBalance > 0 && (
                      <button 
                        onClick={() => onUploadProof(profile.id)}
                        className="p-2 text-gray-400 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors"
                        title="Upload proof of payment"
                      >
                        <Upload size={16} />
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Payment History Component
const PaymentHistory: React.FC<{
  payments: Payment[];
  paymentProfiles: PaymentProfile[];
}> = ({ payments, paymentProfiles }) => {
  const formatCurrency = (amount: number) => `₦${amount.toLocaleString('en-NG', { minimumFractionDigits: 2 })}`;
  const formatDate = (date: string) => new Date(date).toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  });

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Payment History</h2>
      </div>
      <div className="p-6">
        <div className="space-y-4">
          {payments.map((payment) => {
            const profile = paymentProfiles.find(p => p.id === payment.profileId);
            return (
              <div key={payment.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center gap-4">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <CreditCard className="text-green-600" size={20} />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{profile?.name}</p>
                    <p className="text-sm text-gray-500">{formatDate(payment.date)}</p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <span className="font-semibold text-gray-900">{formatCurrency(payment.amount)}</span>
                  <StatusBadge status={payment.status} />
                  {payment.receiptUrl && (
                    <button 
                      className="p-2 text-gray-400 hover:text-green-600 hover:bg-gray-100 rounded-lg transition-colors"
                      title="Download receipt"
                    >
                      <Download size={16} />
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

// Upload Proof Modal Component
const UploadProofModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  profileId: string | null;
}> = ({ isOpen, onClose }) => {
  const [amount, setAmount] = useState('');
  const [dragActive, setDragActive] = useState(false);

  if (!isOpen) return null;

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    // Handle file drop logic here
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-md">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Upload Proof of Payment</h3>
          <button 
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} />
          </button>
        </div>
        
        <div className="p-6 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Amount Paid</label>
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="0.00"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Upload Proof</label>
            <div 
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
                dragActive 
                  ? 'border-green-400 bg-green-50' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="mx-auto text-gray-400 mb-3" size={32} />
              <p className="text-gray-600 font-medium mb-1">Click to upload or drag and drop</p>
              <p className="text-xs text-gray-500">PDF, JPG, PNG up to 10MB</p>
            </div>
          </div>
        </div>
        
        <div className="flex gap-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors"
          >
            Cancel
          </button>
          <button 
            className="flex-1 px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
          >
            Submit Payment
          </button>
        </div>
      </div>
    </div>
  );
};

// Main Payments Component
const Payments: React.FC = () => {
  const [payments] = useState<Payment[]>([
    { id: '1', profileId: '1', amount: 2500.00, date: '2025-06-18', status: 'pending', proofUrl: 'proof1.pdf' },
    { id: '2', profileId: '2', amount: 5000.00, date: '2025-06-15', status: 'approved', receiptUrl: 'receipt2.pdf' },
    { id: '3', profileId: '3', amount: 750.00, date: '2025-06-10', status: 'rejected', proofUrl: 'proof3.pdf' }
  ]);
  
  const [paymentProfiles] = useState<PaymentProfile[]>([
    { id: '1', name: 'Monthly Service Fee', outstandingBalance: 2500.00, dueDate: '2025-07-15', status: 'pending' },
    { id: '2', name: 'Annual License', outstandingBalance: 0, dueDate: '2025-12-31', status: 'paid' },
    { id: '3', name: 'Maintenance Contract', outstandingBalance: 750.00, dueDate: '2025-06-20', status: 'overdue' },
    { id: '4', name: 'Consulting Services', outstandingBalance: 1200.00, dueDate: '2025-08-01', status: 'pending' }
  ]);
  
  const [selectedPayment, setSelectedPayment] = useState<string | null>(null);
  const [uploadingProof, setUploadingProof] = useState<string | null>(null);

  return (
    <div className="space-y-6">
      <PaymentProfilesTable
        paymentProfiles={paymentProfiles}
        onViewProfile={setSelectedPayment}
        onUploadProof={setUploadingProof}
      />
      <PaymentHistory payments={payments} paymentProfiles={paymentProfiles} />
      <UploadProofModal
        isOpen={!!uploadingProof}
        onClose={() => setUploadingProof(null)}
        profileId={uploadingProof}
      />
    </div>
  );
};

export default Payments;