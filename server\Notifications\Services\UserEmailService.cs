using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Final_E_Receipt.Services;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Notifications.Services
{
    public class UserEmailService
    {
        private readonly IDatabaseService _dbService;
        private readonly ILogger<UserEmailService> _logger;

        public UserEmailService(
            IDatabaseService dbService,
            ILogger<UserEmailService> logger)
        {
            _dbService = dbService;
            _logger = logger;
        }

        /// <summary>
        /// Get user email by user ID
        /// </summary>
        public async Task<string> GetUserEmail(string userId)
        {
            try
            {
                var parameters = new { UserId = userId };
                var user = await _dbService.QueryFirstOrDefaultAsync<UserEmailResult>(
                    "GetUserById", parameters);
                
                if (user == null)
                {
                    _logger.LogWarning("User not found: {UserId}", userId);
                    return null;
                }
                
                return user.Email;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user email for {UserId}", userId);
                return null;
            }
        }

        /// <summary>
        /// Get multiple user emails by user IDs
        /// </summary>
        public async Task<Dictionary<string, string>> GetUserEmails(string[] userIds)
        {
            var emails = new Dictionary<string, string>();
            
            try
            {
                foreach (var userId in userIds)
                {
                    var email = await GetUserEmail(userId);
                    if (!string.IsNullOrEmpty(email))
                    {
                        emails[userId] = email;
                    }
                }
                
                return emails;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting multiple user emails");
                return emails;
            }
        }

        /// <summary>
        /// Get user details including email and name
        /// </summary>
        public async Task<UserEmailInfo> GetUserEmailInfo(string userId)
        {
            try
            {
                var parameters = new { UserId = userId };
                var user = await _dbService.QueryFirstOrDefaultAsync<UserEmailResult>(
                    "GetUserById", parameters);
                
                if (user == null)
                {
                    return null;
                }

                return new UserEmailInfo
                {
                    UserId = userId,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    FullName = $"{user.FirstName} {user.LastName}".Trim()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user email info for {UserId}", userId);
                return null;
            }
        }
    }

    public class UserEmailResult
    {
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
    }

    public class UserEmailInfo
    {
        public string UserId { get; set; }
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string FullName { get; set; }
    }
}

