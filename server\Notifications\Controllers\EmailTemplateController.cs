using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Final_E_Receipt.Notifications.DTOs;
using Final_E_Receipt.Notifications.Models;
using Final_E_Receipt.Notifications.Services;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Final_E_Receipt.Notifications.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class EmailTemplateController : ControllerBase
    {
        private readonly EmailTemplateService _emailTemplateService;

        public EmailTemplateController(EmailTemplateService emailTemplateService)
        {
            _emailTemplateService = emailTemplateService;
        }

        [HttpPost]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> CreateEmailTemplate([FromBody] CreateEmailTemplateDTO dto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            var template = new EmailTemplate
            {
                OrganizationId = dto.OrganizationId,
                Name = dto.Name,
                Description = dto.Description,
                Subject = dto.Subject,
                BodyContent = dto.BodyContent,
                Type = dto.Type,
                IsDefault = dto.IsDefault,
                CreatedBy = userId
            };

            var createdTemplate = await _emailTemplateService.CreateEmailTemplate(template);
            
            if (createdTemplate == null)
                return BadRequest(new { message = "Failed to create email template" });

            return Ok(createdTemplate);
        }

        [HttpGet("organization/{organizationId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetEmailTemplatesByOrganization(string organizationId)
        {
            var templates = await _emailTemplateService.GetEmailTemplatesByOrganization(organizationId);
            return Ok(templates);
        }

        [HttpGet("type/{organizationId}/{type}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetEmailTemplatesByType(string organizationId, string type)
        {
            var templates = await _emailTemplateService.GetEmailTemplatesByType(organizationId, type);
            return Ok(templates);
        }

        [HttpGet("{id}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetEmailTemplateById(string id)
        {
            var template = await _emailTemplateService.GetEmailTemplateById(id);
            
            if (template == null)
                return NotFound(new { message = "Email template not found" });

            return Ok(template);
        }

        [HttpGet("default/{organizationId}/{type}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetDefaultEmailTemplate(string organizationId, string type)
        {
            var template = await _emailTemplateService.GetDefaultEmailTemplate(organizationId, type);
            
            if (template == null)
                return NotFound(new { message = "Default email template not found" });

            return Ok(template);
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> UpdateEmailTemplate(string id, [FromBody] UpdateEmailTemplateDTO dto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            var existingTemplate = await _emailTemplateService.GetEmailTemplateById(id);
            if (existingTemplate == null)
                return NotFound(new { message = "Email template not found" });

            existingTemplate.Name = dto.Name;
            existingTemplate.Description = dto.Description;
            existingTemplate.Subject = dto.Subject;
            existingTemplate.BodyContent = dto.BodyContent;
            existingTemplate.Type = dto.Type;
            existingTemplate.IsDefault = dto.IsDefault;
            existingTemplate.UpdatedBy = userId;

            var updatedTemplate = await _emailTemplateService.UpdateEmailTemplate(existingTemplate);
            
            if (updatedTemplate == null)
                return BadRequest(new { message = "Failed to update email template" });

            return Ok(updatedTemplate);
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> DeleteEmailTemplate(string id)
        {
            var existingTemplate = await _emailTemplateService.GetEmailTemplateById(id);
            if (existingTemplate == null)
                return NotFound(new { message = "Email template not found" });

            var result = await _emailTemplateService.DeleteEmailTemplate(id);
            
            if (!result)
                return BadRequest(new { message = "Failed to delete email template" });

            return Ok(new { message = "Email template deleted successfully" });
        }
    }
}
