using System;

namespace Final_E_Receipt.Notifications.Models
{
    public class Notification
    {
        public string Id { get; set; }
        public string UserId { get; set; }
        public string OrganizationId { get; set; }
        public string Type { get; set; } // PAYMENT_DUE, PAYMENT_SCHEDULE_CREATED, etc.
        public string Title { get; set; }
        public string Message { get; set; }
        public string Priority { get; set; } // LOW, MEDIUM, HIGH, URGENT
        public string Status { get; set; } // UNREAD, READ, ARCHIVED
        public string RelatedEntityId { get; set; }
        public string RelatedEntityType { get; set; } // PAYMENT, PAYMENT_SCHEDULE, CERTIFICATE, etc.
        public DateTime? ScheduledDate { get; set; }
        public DateTime? SentDate { get; set; }
        public DateTime? ReadDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class NotificationPreference
    {
        public string Id { get; set; }
        public string UserId { get; set; }
        public string NotificationType { get; set; }
        public bool InAppEnabled { get; set; }
        public bool EmailEnabled { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class NotificationStats
    {
        public int TotalNotifications { get; set; }
        public int UnreadCount { get; set; }
        public int ReadCount { get; set; }
        public int ArchivedCount { get; set; }
    }
}
