import React, { useEffect, useState } from "react";
import {
  Users,
  Settings,
  UserPlus,
  Mail,
  Activity,
  Building2,
  CreditCard,
  User,
  Calendar,
  Receipt,
  BarChart3,
  Server,
} from "lucide-react";
import type { LucideIcon } from "lucide-react";
import Navbar from "../components/NavBaar";
import Overview from "../components/Admin/Overview";
import CreateUser from "../components/Admin/CreateUser";
import UserDetails from "../components/Admin/UserDetails";
import UserInvitations from "../components/Admin/UserInvitation";
import UserList from "../components/Admin/UserList";
import UserSettings from "../components/Admin/Settings";
import ReceiptTemplateManagement from "../components/Admin/ReceiptTemplateManagement";
import EmailConfigurationPage from "./EmailConfigurationPage";
import OrganizationsPage from "./OrganizationsPage";
import PaymentsPage from "./PaymentsPage";
import PaymentProfilesPage from "./PaymentProfilesPage";
import PaymentSchedulesPage from "./PaymentSchedulesPage";
import ReceiptsPage from "./ReceiptsPage";
import ReportsPage from "./ReportsPage";
import UserInvitationManager from "../components/Admin/UserInvitationManager";
import { NavLink, Navigate, Route, Routes } from "react-router-dom";
import Footer from "../components/Footer";
import { loginRequest, msalInstance } from "../auth/msalConfig";

// Define the User interface for selectedUser
interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  status: "Active" | "Inactive" | "Pending";
  lastLogin: string;
  invitedDate: string;
}

interface TabButtonProps {
  id: string;
  label: string;
  icon: LucideIcon;
}

const TabButton: React.FC<TabButtonProps> = ({ id, label, icon: Icon }) => (
  <NavLink
    to={`/admin-dashboard/${id}`}
    className={({ isActive }) =>
      `w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
        isActive
          ? "bg-[#2aa45c] text-white"
          : "text-gray-700 hover:bg-[#dddeda] hover:bg-opacity-50"
      }`
    }
  >
    <Icon size={18} />
    {label}
  </NavLink>
);

const AdminUserManagement: React.FC = () => {
  // State for selectedUser - this will be managed by UserList and passed to UserDetails
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  // Function to handle navigation programmatically from child components
  const handleTabChange = (tab: string) => {
    // This function can be used by child components to navigate
    // You might want to use useNavigate hook in child components instead
    console.log("Navigating to:", tab);
  };
  useEffect(() => {
    const activeAccounts = msalInstance.getAllAccounts();
    const activeAccount = activeAccounts[0];

    const logTOken = async () => {
      await msalInstance.initialize();
      const response = await msalInstance.acquireTokenSilent({
        ...loginRequest,
        account: activeAccount,
      });
      console.log(response);
    };
    logTOken();
  }, []);
  const renderSidebar = () => {
    return (
      <div className="bg-white shadow-md rounded-lg p-4">
        <h2 className="text-lg font-semibold text-[#045024] mb-4">
          Admin Panel
        </h2>
        <nav className="space-y-2">
          {[
            { id: "overview", label: "Overview", icon: Activity },
            { id: "organizations", label: "Organizations", icon: Building2 },
            // { id: "payments", label: "Payments", icon: CreditCard },
            // { id: "payment-profiles", label: "Payment Profiles", icon: User },
            // {
            //   id: "payment-schedules",
            //   label: "Payment Schedules",
            //   icon: Calendar,
            // },
            // { id: "receipts", label: "Receipts", icon: Receipt },
            // { id: "reports", label: "Reports", icon: BarChart3 },
            { id: "user-list", label: "User List", icon: Users },
            // { id: "create-user", label: "Invite User", icon: UserPlus },
            { id: "invitations", label: "Invitations", icon: Mail },
            {
              id: "receipt-templates",
              label: "Receipt Templates",
              icon: Receipt,
            },
            { id: "email-config", label: "Email Configuration", icon: Server },
            { id: "settings", label: "System Settings", icon: Settings },
          ].map(({ id, label, icon }) => (
            <TabButton key={id} id={id} label={label} icon={icon} />
          ))}
        </nav>
      </div>
    );
  };

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#dddeda] bg-opacity-20 pt-16">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="mb-6">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-[#045024]">
                  Admin Management Dashboard
                </h1>
                <p className="text-gray-600 mt-2">
                  Manage users, invitations, and system settings
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="lg:col-span-1">{renderSidebar()}</div>

            <div className="lg:col-span-3">
              <div className="min-h-96">
                <Routes>
                  <Route path="" element={<Navigate to="overview" />} />
                  <Route path="overview" element={<Overview />} />
                  <Route
                    path="organizations/*"
                    element={<OrganizationsPage />}
                  />
                  <Route path="payments/*" element={<PaymentsPage />} />
                  <Route
                    path="payment-profiles/*"
                    element={<PaymentProfilesPage />}
                  />
                  <Route
                    path="payment-schedules/*"
                    element={<PaymentSchedulesPage />}
                  />
                  <Route path="receipts/*" element={<ReceiptsPage />} />
                  <Route path="reports/*" element={<ReportsPage />} />
                  <Route
                    path="user-list"
                    element={
                      <UserList
                        setActiveTab={handleTabChange}
                        setSelectedUser={setSelectedUser}
                      />
                    }
                  />
                  <Route
                    path="create-user"
                    element={<CreateUser setActiveTab={handleTabChange} />}
                  />
                  {/* Updated route with user ID parameter */}
                  <Route
                    path="user-details/:userId"
                    element={
                      <UserDetails
                        selectedUser={selectedUser}
                        setActiveTab={handleTabChange}
                      />
                    }
                  />
                  <Route
                    path="invitations"
                    element={<UserInvitationManager />}
                  />
                  <Route
                    path="receipt-templates"
                    element={<ReceiptTemplateManagement />}
                  />
                  <Route
                    path="email-config"
                    element={<EmailConfigurationPage />}
                  />
                  <Route path="settings" element={<UserSettings />} />
                </Routes>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default AdminUserManagement;
