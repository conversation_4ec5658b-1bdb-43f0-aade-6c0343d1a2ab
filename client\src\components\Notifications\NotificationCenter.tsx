import React, { useState, useEffect } from 'react';
import { 
  Bell, 
  X, 
  Check, 
  Archive, 
  Trash2, 
  Filter, 
  MoreVertical,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
  Receipt,
  Shield,
  RefreshCw
} from 'lucide-react';
import { useNotificationsApi } from '../../hooks/api';
import { useAuth } from '../../hooks/useAuth';
import type { Notification, NotificationFilters } from '../../hooks/api/useNotifications';

interface NotificationCenterProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  isOpen,
  onClose,
  className = '',
}) => {
  const { user } = useAuth();
  const {
    getUserNotifications,
    getNotificationStats,
    markAsRead,
    markAsUnread,
    archiveNotification,
    deleteNotification,
    markMultipleAsRead,
    archiveMultiple,
    deleteMultiple,
    loading,
  } = useNotificationsApi();

  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [filters, setFilters] = useState<NotificationFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (isOpen && user?.id) {
      loadNotifications();
      loadStats();
    }
  }, [isOpen, user?.id, filters]);

  const loadNotifications = async () => {
    if (!user?.id) return;
    
    const result = await getUserNotifications(user.id, filters);
    if (result) {
      setNotifications(result);
    }
  };

  const loadStats = async () => {
    if (!user?.id) return;
    
    const result = await getNotificationStats(user.id);
    if (result) {
      setStats(result);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([loadNotifications(), loadStats()]);
    setRefreshing(false);
  };

  const handleMarkAsRead = async (notificationId: string) => {
    await markAsRead(notificationId);
    setNotifications(prev => prev.map(n => 
      n.id === notificationId ? { ...n, status: 'READ' as const, readDate: new Date().toISOString() } : n
    ));
    loadStats();
  };

  const handleMarkAsUnread = async (notificationId: string) => {
    await markAsUnread(notificationId);
    setNotifications(prev => prev.map(n => 
      n.id === notificationId ? { ...n, status: 'unread' as const, readDate: undefined } : n
    ));
    loadStats();
  };

  const handleArchive = async (notificationId: string) => {
    await archiveNotification(notificationId);
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
    loadStats();
  };

  const handleDelete = async (notificationId: string) => {
    await deleteNotification(notificationId);
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
    loadStats();
  };

  const handleBulkAction = async (action: 'read' | 'archive' | 'delete') => {
    if (selectedNotifications.length === 0) return;

    switch (action) {
      case 'read':
        await markMultipleAsRead(selectedNotifications);
        setNotifications(prev => prev.map(n => 
          selectedNotifications.includes(n.id) ? { ...n, status: 'read' as const, readDate: new Date().toISOString() } : n
        ));
        break;
      case 'archive':
        await archiveMultiple(selectedNotifications);
        setNotifications(prev => prev.filter(n => !selectedNotifications.includes(n.id)));
        break;
      case 'delete':
        await deleteMultiple(selectedNotifications);
        setNotifications(prev => prev.filter(n => !selectedNotifications.includes(n.id)));
        break;
    }

    setSelectedNotifications([]);
    loadStats();
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'PAYMENT_DUE':
      case 'PAYMENT_OVERDUE':
        return <DollarSign size={16} className="text-orange-500" />;
      case 'PAYMENT_SCHEDULE_CREATED':
        return <Clock size={16} className="text-blue-500" />;
      case 'PAYMENT_SCHEDULE_UPDATED':
        return <Clock size={16} className="text-yellow-500" />;
      case 'PAYMENT_SCHEDULE_DELETED':
        return <Clock size={16} className="text-red-500" />;
      case 'BULK_SCHEDULES_IMPORTED':
        return <DollarSign size={16} className="text-green-500" />;
      case 'CERTIFICATE_EXPIRING':
      case 'CERTIFICATE_EXPIRED':
        return <Shield size={16} className="text-red-500" />;
      case 'SYSTEM_ALERT':
        return <AlertCircle size={16} className="text-red-500" />;
      default:
        return <Bell size={16} className="text-blue-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'border-l-red-500 bg-red-50';
      case 'HIGH':
        return 'border-l-orange-500 bg-orange-50';
      case 'MEDIUM':
        return 'border-l-yellow-500 bg-yellow-50';
      default:
        return 'border-l-blue-500 bg-blue-50';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-0 right-0 h-full w-full max-w-md ml-auto bg-white shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Bell size={20} className="text-gray-600" />
            <h2 className="text-lg font-medium text-gray-900">Notifications</h2>
            {stats && (
              <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                {stats.unreadCount}
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
              title="Refresh"
            >
              <RefreshCw size={16} className={refreshing ? 'animate-spin' : ''} />
            </button>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="text-gray-400 hover:text-gray-600"
              title="Filters"
            >
              <Filter size={16} />
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Stats */}
        {stats && (
          <div className="p-4 bg-gray-50 border-b border-gray-200">
            <div className="grid grid-cols-4 gap-2 text-center">
              <div>
                <div className="text-lg font-bold text-gray-900">{stats.totalNotifications}</div>
                <div className="text-xs text-gray-500">Total</div>
              </div>
              <div>
                <div className="text-lg font-bold text-red-600">{stats.unreadCount}</div>
                <div className="text-xs text-gray-500">Unread</div>
              </div>
              <div>
                <div className="text-lg font-bold text-green-600">{stats.readCount}</div>
                <div className="text-xs text-gray-500">Read</div>
              </div>
              <div>
                <div className="text-lg font-bold text-gray-600">{stats.archivedCount}</div>
                <div className="text-xs text-gray-500">Archived</div>
              </div>
            </div>
          </div>
        )}

        {/* Bulk Actions */}
        {selectedNotifications.length > 0 && (
          <div className="p-4 bg-blue-50 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-800">
                {selectedNotifications.length} selected
              </span>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleBulkAction('read')}
                  className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                >
                  Mark Read
                </button>
                <button
                  onClick={() => handleBulkAction('archive')}
                  className="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                >
                  Archive
                </button>
                <button
                  onClick={() => handleBulkAction('delete')}
                  className="text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Notifications List */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2aa45c]"></div>
            </div>
          ) : notifications.length === 0 ? (
            <div className="text-center p-8">
              <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No notifications found</p>
            </div>
          ) : (
            <div className="space-y-1">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`
                    border-l-4 p-4 hover:bg-gray-50 cursor-pointer transition-colors
                    ${notification.status === 'UNREAD' ? 'bg-white' : 'bg-gray-50'}
                    ${getPriorityColor(notification.priority)}
                  `}
                >
                  <div className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      checked={selectedNotifications.includes(notification.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedNotifications(prev => [...prev, notification.id]);
                        } else {
                          setSelectedNotifications(prev => prev.filter(id => id !== notification.id));
                        }
                      }}
                      className="mt-1"
                    />
                    
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className={`text-sm ${notification.status === 'UNREAD' ? 'font-medium text-gray-900' : 'text-gray-700'}`}>
                            {notification.title}
                          </p>
                          <p className="text-sm text-gray-600 mt-1">
                            {notification.message}
                          </p>
                          <p className="text-xs text-gray-500 mt-2">
                            {formatTimeAgo(notification.createdAt)}
                          </p>
                        </div>
                        
                        <div className="flex items-center space-x-1 ml-2">
                          {notification.status === 'UNREAD' ? (
                            <button
                              onClick={() => handleMarkAsRead(notification.id)}
                              className="text-gray-400 hover:text-green-600"
                              title="Mark as read"
                            >
                              <Check size={14} />
                            </button>
                          ) : (
                            <button
                              onClick={() => handleMarkAsUnread(notification.id)}
                              className="text-gray-400 hover:text-blue-600"
                              title="Mark as unread"
                            >
                              <Clock size={14} />
                            </button>
                          )}
                          <button
                            onClick={() => handleArchive(notification.id)}
                            className="text-gray-400 hover:text-yellow-600"
                            title="Archive"
                          >
                            <Archive size={14} />
                          </button>
                          <button
                            onClick={() => handleDelete(notification.id)}
                            className="text-gray-400 hover:text-red-600"
                            title="Delete"
                          >
                            <Trash2 size={14} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationCenter;
