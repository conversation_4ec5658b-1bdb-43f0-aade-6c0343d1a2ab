import React, { useState, useEffect } from 'react';
import { useEmailConfigApi, type EmailConfiguration } from '../hooks/api';

interface EmailConfigDisplayProps {
  organizationId: string;
  showSensitiveData?: boolean;
}

const EmailConfigDisplay: React.FC<EmailConfigDisplayProps> = ({ 
  organizationId, 
  showSensitiveData = false 
}) => {
  const { loading, error, getDefaultEmailConfig } = useEmailConfigApi();
  const [config, setConfig] = useState<EmailConfiguration | null>(null);

  useEffect(() => {
    const loadConfig = async () => {
      if (organizationId) {
        const result = await getDefaultEmailConfig(organizationId);
        if (result) {
          setConfig(result);
        }
      }
    };

    loadConfig();
  }, [organizationId, getDefaultEmailConfig]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading email configuration...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Error loading email configuration
            </h3>
            <div className="mt-2 text-sm text-red-700">
              {error}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              No email configuration found
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              No default email configuration is set up for this organization.
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
          Email Configuration
        </h3>
        
        <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
          <div>
            <dt className="text-sm font-medium text-gray-500">SMTP Server</dt>
            <dd className="mt-1 text-sm text-gray-900">{config.smtpServer}</dd>
          </div>
          
          <div>
            <dt className="text-sm font-medium text-gray-500">Port</dt>
            <dd className="mt-1 text-sm text-gray-900">{config.port}</dd>
          </div>
          
          <div>
            <dt className="text-sm font-medium text-gray-500">Username</dt>
            <dd className="mt-1 text-sm text-gray-900">
              {showSensitiveData ? config.username : '••••••••'}
            </dd>
          </div>
          
          <div>
            <dt className="text-sm font-medium text-gray-500">SSL Enabled</dt>
            <dd className="mt-1 text-sm text-gray-900">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                config.enableSsl 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {config.enableSsl ? 'Yes' : 'No'}
              </span>
            </dd>
          </div>
          
          <div>
            <dt className="text-sm font-medium text-gray-500">Sender Name</dt>
            <dd className="mt-1 text-sm text-gray-900">{config.senderName}</dd>
          </div>
          
          <div>
            <dt className="text-sm font-medium text-gray-500">Sender Email</dt>
            <dd className="mt-1 text-sm text-gray-900">{config.senderEmail}</dd>
          </div>
          
          <div>
            <dt className="text-sm font-medium text-gray-500">Status</dt>
            <dd className="mt-1 text-sm text-gray-900">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Default Configuration
              </span>
            </dd>
          </div>
          
          <div>
            <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
            <dd className="mt-1 text-sm text-gray-900">
              {config.updatedAt 
                ? new Date(config.updatedAt).toLocaleDateString()
                : new Date(config.createdAt).toLocaleDateString()
              }
            </dd>
          </div>
        </dl>

        {showSensitiveData && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  <strong>Security Notice:</strong> Sensitive data is being displayed. 
                  Ensure this information is kept secure.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailConfigDisplay;
