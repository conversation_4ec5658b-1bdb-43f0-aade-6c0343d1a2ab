-- Enhanced Reporting SQL Procedures
-- Payment History, Outstanding Balances, and Revoked Receipts Reports

-- ===== PAYMENT HISTORY PROCEDURES =====

CREATE PROCEDURE GetPaymentHistoryByOrganization
    @OrganizationId NVARCHAR(50),
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PaymentStatus NVARCHAR(50) = NULL,
    @PaymentMethod NVARCHAR(50) = NULL,
    @MinAmount DECIMAL(18,2) = NULL,
    @MaxAmount DECIMAL(18,2) = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 50,
    @SortBy NVARCHAR(50) = 'CreatedAt',
    @SortDirection NVARCHAR(10) = 'DESC'
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    -- Dynamic ORDER BY clause
    DECLARE @OrderBy NVARCHAR(100) = @SortBy + ' ' + @SortDirection
    
    SELECT 
        p.Id,
        p.TransactionR<PERSON>erence,
        p.Payer<PERSON>,
        p.Payer<PERSON>mail,
        p.Amount,
        p.<PERSON>,
        p.PaymentMethod,
        p.Status,
        p.Description,
        p.Category,
        p.CreatedAt,
        p.CompletedAt,
        p.ReceiptId
    FROM Payments p
    WHERE p.OrganizationId = @OrganizationId
    AND (@StartDate IS NULL OR p.CreatedAt >= @StartDate)
    AND (@EndDate IS NULL OR p.CreatedAt <= @EndDate)
    AND (@PaymentStatus IS NULL OR p.Status = @PaymentStatus)
    AND (@PaymentMethod IS NULL OR p.PaymentMethod = @PaymentMethod)
    AND (@MinAmount IS NULL OR p.Amount >= @MinAmount)
    AND (@MaxAmount IS NULL OR p.Amount <= @MaxAmount)
    ORDER BY 
        CASE WHEN @SortBy = 'CreatedAt' AND @SortDirection = 'ASC' THEN p.CreatedAt END ASC,
        CASE WHEN @SortBy = 'CreatedAt' AND @SortDirection = 'DESC' THEN p.CreatedAt END DESC,
        CASE WHEN @SortBy = 'Amount' AND @SortDirection = 'ASC' THEN p.Amount END ASC,
        CASE WHEN @SortBy = 'Amount' AND @SortDirection = 'DESC' THEN p.Amount END DESC,
        CASE WHEN @SortBy = 'PayerName' AND @SortDirection = 'ASC' THEN p.PayerName END ASC,
        CASE WHEN @SortBy = 'PayerName' AND @SortDirection = 'DESC' THEN p.PayerName END DESC,
        p.CreatedAt DESC -- Default fallback
    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END;

CREATE PROCEDURE GetPaymentHistorySummary
    @OrganizationId NVARCHAR(50),
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PaymentStatus NVARCHAR(50) = NULL
AS
BEGIN
    SELECT 
        COUNT(*) as TotalPayments,
        ISNULL(SUM(Amount), 0) as TotalAmount,
        SUM(CASE WHEN Status = 'Completed' THEN 1 ELSE 0 END) as CompletedPayments,
        ISNULL(SUM(CASE WHEN Status = 'Completed' THEN Amount ELSE 0 END), 0) as CompletedAmount,
        SUM(CASE WHEN Status = 'Pending' THEN 1 ELSE 0 END) as PendingPayments,
        ISNULL(SUM(CASE WHEN Status = 'Pending' THEN Amount ELSE 0 END), 0) as PendingAmount,
        SUM(CASE WHEN Status = 'Failed' THEN 1 ELSE 0 END) as FailedPayments,
        ISNULL(SUM(CASE WHEN Status = 'Failed' THEN Amount ELSE 0 END), 0) as FailedAmount
    FROM Payments
    WHERE OrganizationId = @OrganizationId
    AND (@StartDate IS NULL OR CreatedAt >= @StartDate)
    AND (@EndDate IS NULL OR CreatedAt <= @EndDate)
    AND (@PaymentStatus IS NULL OR Status = @PaymentStatus)
END;

-- ===== OUTSTANDING BALANCES PROCEDURES =====

CREATE PROCEDURE GetOutstandingBalances
    @OrganizationId NVARCHAR(50) = NULL,
    @IncludeOverdue BIT = 1,
    @MinAmount DECIMAL(18,2) = NULL,
    @AgingDays INT = 30,
    @SortBy NVARCHAR(50) = 'DaysOverdue',
    @SortDirection NVARCHAR(10) = 'DESC'
AS
BEGIN
    SELECT 
        ps.OrganizationId,
        o.Name as OrganizationName,
        ps.Id as PaymentScheduleId,
        ps.Description,
        ps.DueDate,
        ps.Amount as OriginalAmount,
        ISNULL(ps.PaidAmount, 0) as PaidAmount,
        (ps.Amount - ISNULL(ps.PaidAmount, 0)) as OutstandingAmount,
        CASE 
            WHEN ps.DueDate < GETDATE() THEN DATEDIFF(DAY, ps.DueDate, GETDATE())
            ELSE 0
        END as DaysOverdue,
        CASE WHEN ps.DueDate < GETDATE() THEN 1 ELSE 0 END as IsOverdue,
        ps.PaymentProfileId,
        pp.Name as PaymentProfileName
    FROM PaymentSchedules ps
    LEFT JOIN Organizations o ON ps.OrganizationId = o.Id
    LEFT JOIN PaymentProfiles pp ON ps.PaymentProfileId = pp.Id
    WHERE ps.Status != 'PAID'
    AND (ps.Amount - ISNULL(ps.PaidAmount, 0)) > 0
    AND (@OrganizationId IS NULL OR ps.OrganizationId = @OrganizationId)
    AND (@MinAmount IS NULL OR (ps.Amount - ISNULL(ps.PaidAmount, 0)) >= @MinAmount)
    AND (@IncludeOverdue = 1 OR ps.DueDate >= GETDATE())
    ORDER BY 
        CASE WHEN @SortBy = 'DaysOverdue' AND @SortDirection = 'ASC' THEN 
            CASE WHEN ps.DueDate < GETDATE() THEN DATEDIFF(DAY, ps.DueDate, GETDATE()) ELSE 0 END END ASC,
        CASE WHEN @SortBy = 'DaysOverdue' AND @SortDirection = 'DESC' THEN 
            CASE WHEN ps.DueDate < GETDATE() THEN DATEDIFF(DAY, ps.DueDate, GETDATE()) ELSE 0 END END DESC,
        CASE WHEN @SortBy = 'OutstandingAmount' AND @SortDirection = 'ASC' THEN (ps.Amount - ISNULL(ps.PaidAmount, 0)) END ASC,
        CASE WHEN @SortBy = 'OutstandingAmount' AND @SortDirection = 'DESC' THEN (ps.Amount - ISNULL(ps.PaidAmount, 0)) END DESC,
        CASE WHEN @SortBy = 'DueDate' AND @SortDirection = 'ASC' THEN ps.DueDate END ASC,
        CASE WHEN @SortBy = 'DueDate' AND @SortDirection = 'DESC' THEN ps.DueDate END DESC,
        ps.DueDate ASC -- Default fallback
END;

CREATE PROCEDURE GetOutstandingBalancesAging
    @OrganizationId NVARCHAR(50) = NULL,
    @IncludeOverdue BIT = 1,
    @MinAmount DECIMAL(18,2) = NULL,
    @AgingDays INT = 30
AS
BEGIN
    WITH AgingData AS (
        SELECT 
            ps.Amount - ISNULL(ps.PaidAmount, 0) as OutstandingAmount,
            CASE 
                WHEN ps.DueDate >= GETDATE() THEN 'Current'
                WHEN DATEDIFF(DAY, ps.DueDate, GETDATE()) <= 30 THEN '1-30 days'
                WHEN DATEDIFF(DAY, ps.DueDate, GETDATE()) <= 60 THEN '31-60 days'
                WHEN DATEDIFF(DAY, ps.DueDate, GETDATE()) <= 90 THEN '61-90 days'
                ELSE '90+ days'
            END as AgeRange
        FROM PaymentSchedules ps
        WHERE ps.Status != 'PAID'
        AND (ps.Amount - ISNULL(ps.PaidAmount, 0)) > 0
        AND (@OrganizationId IS NULL OR ps.OrganizationId = @OrganizationId)
        AND (@MinAmount IS NULL OR (ps.Amount - ISNULL(ps.PaidAmount, 0)) >= @MinAmount)
        AND (@IncludeOverdue = 1 OR ps.DueDate >= GETDATE())
    ),
    AgingSummary AS (
        SELECT 
            AgeRange,
            COUNT(*) as Count,
            SUM(OutstandingAmount) as TotalAmount
        FROM AgingData
        GROUP BY AgeRange
    ),
    TotalSummary AS (
        SELECT SUM(TotalAmount) as GrandTotal FROM AgingSummary
    )
    SELECT 
        a.AgeRange,
        a.Count,
        a.TotalAmount,
        CASE WHEN t.GrandTotal > 0 THEN (a.TotalAmount / t.GrandTotal) * 100 ELSE 0 END as Percentage
    FROM AgingSummary a
    CROSS JOIN TotalSummary t
    ORDER BY 
        CASE a.AgeRange
            WHEN 'Current' THEN 1
            WHEN '1-30 days' THEN 2
            WHEN '31-60 days' THEN 3
            WHEN '61-90 days' THEN 4
            WHEN '90+ days' THEN 5
        END
END;

-- ===== REVOKED RECEIPTS PROCEDURES =====

CREATE PROCEDURE GetRevokedReceipts
    @OrganizationId NVARCHAR(50) = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @RevokedBy NVARCHAR(50) = NULL,
    @ReasonCategory NVARCHAR(50) = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 50,
    @SortBy NVARCHAR(50) = 'RevokedDate',
    @SortDirection NVARCHAR(10) = 'DESC'
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT 
        r.Id,
        r.ReceiptNumber,
        r.PayerName,
        r.PayerEmail,
        r.Amount,
        r.Currency,
        r.PaymentMethod,
        r.CreatedAt,
        r.RevokedDate,
        r.RevokedBy,
        r.RevokedReason,
        r.ReasonCategory,
        r.PaymentId
    FROM Receipts r
    WHERE r.IsRevoked = 1
    AND (@OrganizationId IS NULL OR r.OrganizationId = @OrganizationId)
    AND (@StartDate IS NULL OR r.RevokedDate >= @StartDate)
    AND (@EndDate IS NULL OR r.RevokedDate <= @EndDate)
    AND (@RevokedBy IS NULL OR r.RevokedBy = @RevokedBy)
    AND (@ReasonCategory IS NULL OR r.ReasonCategory = @ReasonCategory)
    ORDER BY 
        CASE WHEN @SortBy = 'RevokedDate' AND @SortDirection = 'ASC' THEN r.RevokedDate END ASC,
        CASE WHEN @SortBy = 'RevokedDate' AND @SortDirection = 'DESC' THEN r.RevokedDate END DESC,
        CASE WHEN @SortBy = 'Amount' AND @SortDirection = 'ASC' THEN r.Amount END ASC,
        CASE WHEN @SortBy = 'Amount' AND @SortDirection = 'DESC' THEN r.Amount END DESC,
        CASE WHEN @SortBy = 'PayerName' AND @SortDirection = 'ASC' THEN r.PayerName END ASC,
        CASE WHEN @SortBy = 'PayerName' AND @SortDirection = 'DESC' THEN r.PayerName END DESC,
        r.RevokedDate DESC -- Default fallback
    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END;

CREATE PROCEDURE GetRevokedReceiptsSummaryByReason
    @OrganizationId NVARCHAR(50) = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @RevokedBy NVARCHAR(50) = NULL,
    @ReasonCategory NVARCHAR(50) = NULL
AS
BEGIN
    SELECT 
        ISNULL(RevokedReason, 'Unknown') as RevokedReason,
        COUNT(*) as Count,
        SUM(Amount) as TotalAmount
    FROM Receipts
    WHERE IsRevoked = 1
    AND (@OrganizationId IS NULL OR OrganizationId = @OrganizationId)
    AND (@StartDate IS NULL OR RevokedDate >= @StartDate)
    AND (@EndDate IS NULL OR RevokedDate <= @EndDate)
    AND (@RevokedBy IS NULL OR RevokedBy = @RevokedBy)
    AND (@ReasonCategory IS NULL OR ReasonCategory = @ReasonCategory)
    GROUP BY RevokedReason
    ORDER BY Count DESC
END;

-- ===== COMPLIANCE STATUS PROCEDURES =====

CREATE PROCEDURE GetComplianceStatusReport
    @OrganizationId NVARCHAR(50) = NULL,
    @ComplianceLevel NVARCHAR(50) = NULL,
    @IsCompliant BIT = NULL,
    @MinComplianceScore INT = NULL,
    @MaxComplianceScore INT = NULL,
    @LastCertificateFrom DATETIME = NULL,
    @LastCertificateTo DATETIME = NULL,
    @ExpiringWithinDays INT = 30,
    @SortBy NVARCHAR(50) = 'ComplianceScore',
    @SortDirection NVARCHAR(10) = 'DESC'
AS
BEGIN
    WITH OrganizationCompliance AS (
        SELECT 
            o.Id as OrganizationId,
            o.Name as OrganizationName,
            COUNT(c.Id) as TotalCertificates,
            SUM(CASE WHEN c.IsRevoked = 0 AND c.ValidUntil > GETDATE() THEN 1 ELSE 0 END) as ActiveCertificates,
            SUM(CASE WHEN c.IsRevoked = 0 AND c.ValidUntil <= GETDATE() THEN 1 ELSE 0 END) as ExpiredCertificates,
            SUM(CASE WHEN c.IsRevoked = 0 AND c.ValidUntil > GETDATE() AND c.ValidUntil <= DATEADD(DAY, @ExpiringWithinDays, GETDATE()) THEN 1 ELSE 0 END) as ExpiringCertificates,
            MAX(CASE WHEN c.IsRevoked = 0 THEN c.IssuedDate END) as LastCertificateIssued,
            MIN(CASE WHEN c.IsRevoked = 0 AND c.ValidUntil > GETDATE() THEN c.ValidUntil END) as NextExpiryDate,
            CASE 
                WHEN COUNT(c.Id) = 0 THEN 0
                ELSE CAST(SUM(CASE WHEN c.IsRevoked = 0 AND c.ValidUntil > GETDATE() THEN 1 ELSE 0 END) AS FLOAT) / COUNT(c.Id) * 100
            END as ComplianceScore
        FROM Organizations o
        LEFT JOIN ComplianceCertificates c ON o.Id = c.OrganizationId
        WHERE (@OrganizationId IS NULL OR o.Id = @OrganizationId)
        GROUP BY o.Id, o.Name
    )
    SELECT 
        OrganizationId,
        OrganizationName,
        CASE WHEN ActiveCertificates > 0 THEN 1 ELSE 0 END as IsCompliant,
        ComplianceScore,
        CASE 
            WHEN ComplianceScore >= 90 THEN 'EXCELLENT'
            WHEN ComplianceScore >= 80 THEN 'GOOD'
            WHEN ComplianceScore >= 70 THEN 'SATISFACTORY'
            WHEN ComplianceScore >= 60 THEN 'NEEDS_IMPROVEMENT'
            ELSE 'NON_COMPLIANT'
        END as ComplianceLevel,
        ActiveCertificates,
        ExpiredCertificates,
        ExpiringCertificates,
        LastCertificateIssued,
        NextExpiryDate
    FROM OrganizationCompliance
    WHERE (@ComplianceLevel IS NULL OR 
           (@ComplianceLevel = 'EXCELLENT' AND ComplianceScore >= 90) OR
           (@ComplianceLevel = 'GOOD' AND ComplianceScore >= 80 AND ComplianceScore < 90) OR
           (@ComplianceLevel = 'SATISFACTORY' AND ComplianceScore >= 70 AND ComplianceScore < 80) OR
           (@ComplianceLevel = 'NEEDS_IMPROVEMENT' AND ComplianceScore >= 60 AND ComplianceScore < 70) OR
           (@ComplianceLevel = 'NON_COMPLIANT' AND ComplianceScore < 60))
    AND (@IsCompliant IS NULL OR (@IsCompliant = 1 AND ActiveCertificates > 0) OR (@IsCompliant = 0 AND ActiveCertificates = 0))
    AND (@MinComplianceScore IS NULL OR ComplianceScore >= @MinComplianceScore)
    AND (@MaxComplianceScore IS NULL OR ComplianceScore <= @MaxComplianceScore)
    AND (@LastCertificateFrom IS NULL OR LastCertificateIssued >= @LastCertificateFrom)
    AND (@LastCertificateTo IS NULL OR LastCertificateIssued <= @LastCertificateTo)
    ORDER BY 
        CASE WHEN @SortBy = 'ComplianceScore' AND @SortDirection = 'ASC' THEN ComplianceScore END ASC,
        CASE WHEN @SortBy = 'ComplianceScore' AND @SortDirection = 'DESC' THEN ComplianceScore END DESC,
        CASE WHEN @SortBy = 'OrganizationName' AND @SortDirection = 'ASC' THEN OrganizationName END ASC,
        CASE WHEN @SortBy = 'OrganizationName' AND @SortDirection = 'DESC' THEN OrganizationName END DESC,
        ComplianceScore DESC -- Default fallback
END;

