using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Final_E_Receipt.Files.Services;
using Final_E_Receipt.Files.DTOs;
using Final_E_Receipt.Files.Models;

namespace Final_E_Receipt.Files.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class FileController : ControllerBase
    {
        private readonly FileService _fileService;
        private readonly ILogger<FileController> _logger;

        public FileController(FileService fileService, ILogger<FileController> logger)
        {
            _fileService = fileService;
            _logger = logger;
        }

        [HttpPost("upload")]
        public async Task<IActionResult> UploadFile(IFormFile file, string relatedEntityType, string relatedEntityId, string? description = null, string? category = null)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var organizationId = User.FindFirst("OrganizationId")?.Value;

                if (file == null || file.Length == 0)
                    return BadRequest(new { message = "File is required" });

                if (string.IsNullOrEmpty(relatedEntityType) || string.IsNullOrEmpty(relatedEntityId))
                    return BadRequest(new { message = "RelatedEntityType and RelatedEntityId are required" });

                var uploadedFile = await _fileService.UploadFile(
                    file,
                    relatedEntityType,
                    relatedEntityId,
                    userId,
                    organizationId,
                    description,
                    category
                );

                var response = MapToFileUploadResponseDTO(uploadedFile);
                return Ok(response);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file");
                return StatusCode(500, new { message = "An error occurred while uploading the file" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetFileById(string id)
        {
            try
            {
                var file = await _fileService.GetFileById(id);
                if (file == null)
                    return NotFound(new { message = "File not found" });

                var response = MapToFileListDTO(file);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving file: {FileId}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the file" });
            }
        }

        [HttpGet("download/{id}")]
        public async Task<IActionResult> DownloadFile(string id)
        {
            try
            {
                var file = await _fileService.GetFileById(id);
                if (file == null)
                    return NotFound(new { message = "File not found" });

                // Check if user has access to this file
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var organizationId = User.FindFirst("OrganizationId")?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                // Allow access if user is admin, finance officer, or owns the file, or belongs to same organization
                if (userRole != "JTB_ADMIN" && userRole != "FINANCE_OFFICER" && userRole != "SENIOR_FINANCE_OFFICER" &&
                    file.UploadedBy != userId && file.OrganizationId != organizationId)
                {
                    return Forbid();
                }

                var fileContent = await _fileService.DownloadFile(id);
                
                return File(fileContent, file.ContentType, file.OriginalFileName);
            }
            catch (FileNotFoundException)
            {
                return NotFound(new { message = "File not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading file: {FileId}", id);
                return StatusCode(500, new { message = "An error occurred while downloading the file" });
            }
        }

        [HttpGet("entity/{entityType}/{entityId}")]
        public async Task<IActionResult> GetFilesByEntity(string entityType, string entityId)
        {
            try
            {
                var files = await _fileService.GetFilesByEntity(entityType, entityId);
                
                var response = files.Select(MapToFileListDTO).ToList();
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving files for entity: {EntityType}/{EntityId}", entityType, entityId);
                return StatusCode(500, new { message = "An error occurred while retrieving files" });
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> DeleteFile(string id)
        {
            try
            {
                var file = await _fileService.GetFileById(id);
                if (file == null)
                    return NotFound(new { message = "File not found" });

                // Check if user has permission to delete this file
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                // Allow deletion if user is admin, finance officer, or owns the file
                if (userRole != "JTB_ADMIN" && userRole != "FINANCE_OFFICER" && userRole != "SENIOR_FINANCE_OFFICER" &&
                    file.UploadedBy != userId)
                {
                    return Forbid();
                }

                var success = await _fileService.DeleteFile(id);
                if (!success)
                    return BadRequest(new { message = "Failed to delete file" });

                return Ok(new { message = "File deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file: {FileId}", id);
                return StatusCode(500, new { message = "An error occurred while deleting the file" });
            }
        }

        private FileUploadResponseDTO MapToFileUploadResponseDTO(FileUpload file)
        {
            return new FileUploadResponseDTO
            {
                Id = file.Id,
                FileName = file.FileName,
                OriginalFileName = file.OriginalFileName,
                ContentType = file.ContentType,
                FileSize = file.FileSize,
                RelatedEntityType = file.RelatedEntityType,
                RelatedEntityId = file.RelatedEntityId,
                CreatedAt = file.CreatedAt,
                Description = file.Description,
                Category = file.Category,
                IsScanned = file.IsScanned,
                ScanResult = file.ScanResult
            };
        }

        private FileListDTO MapToFileListDTO(FileUpload file)
        {
            return new FileListDTO
            {
                Id = file.Id,
                FileName = file.FileName,
                OriginalFileName = file.OriginalFileName,
                ContentType = file.ContentType,
                FileSize = file.FileSize,
                RelatedEntityType = file.RelatedEntityType,
                RelatedEntityId = file.RelatedEntityId,
                CreatedAt = file.CreatedAt,
                Description = file.Description,
                Category = file.Category,
                IsScanned = file.IsScanned,
                ScanResult = file.ScanResult,
                UploadedBy = file.UploadedBy
            };
        }
    }
}



