import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Eye, CheckCircle } from 'lucide-react';

interface AvailableTemplate {
  id: string;
  name: string;
  description: string;
  previewImageUrl: string;
  orientation: 'Portrait' | 'Landscape';
  isDefault: boolean;
  organizationName: string;
  certificateTypeName: string;
}

interface CertificateTemplateSelectorProps {
  organizationId: string;
  certificateType: string;
  selectedTemplateId?: string;
  onTemplateSelect: (templateId: string) => void;
  disabled?: boolean;
}

export const CertificateTemplateSelector: React.FC<CertificateTemplateSelectorProps> = ({
  organizationId,
  certificateType,
  selectedTemplateId,
  onTemplateSelect,
  disabled = false
}) => {
  const [templates, setTemplates] = useState<AvailableTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [previewTemplate, setPreviewTemplate] = useState<string | null>(null);

  useEffect(() => {
    loadAvailableTemplates();
  }, [organizationId, certificateType]);

  const loadAvailableTemplates = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        `/api/certificate-templates/available?organizationId=${organizationId}&certificateType=${certificateType}`
      );

      if (!response.ok) {
        throw new Error('Failed to load templates');
      }

      const data = await response.json();
      setTemplates(data.availableTemplates || []);

      // Auto-select default template if none selected
      if (!selectedTemplateId && data.availableTemplates?.length > 0) {
        const defaultTemplate = data.availableTemplates.find((t: AvailableTemplate) => t.isDefault) 
          || data.availableTemplates[0];
        onTemplateSelect(defaultTemplate.id);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (templateId: string) => {
    if (!disabled) {
      onTemplateSelect(templateId);
    }
  };

  const handlePreview = (templateId: string) => {
    setPreviewTemplate(templateId);
    // In a real implementation, you'd open a modal or navigate to preview
    console.log('Preview template:', templateId);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading certificate templates...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="text-center text-red-600">
            <p>Error loading templates: {error}</p>
            <Button 
              variant="outline" 
              onClick={loadAvailableTemplates}
              className="mt-4"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (templates.length === 0) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="text-center text-gray-500">
            <p>No templates available for this certificate type.</p>
            <p className="text-sm mt-2">The system will use the default template.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Select Certificate Template</CardTitle>
        <p className="text-sm text-gray-600">
          Choose a template for your {certificateType.replace('_', ' ').toLowerCase()} certificate
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {templates.map((template) => (
            <div
              key={template.id}
              className={`
                relative border rounded-lg p-4 cursor-pointer transition-all
                ${selectedTemplateId === template.id 
                  ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200' 
                  : 'border-gray-200 hover:border-gray-300'
                }
                ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
              `}
              onClick={() => handleTemplateSelect(template.id)}
            >
              {/* Selection indicator */}
              {selectedTemplateId === template.id && (
                <div className="absolute top-2 right-2">
                  <CheckCircle className="h-5 w-5 text-blue-600" />
                </div>
              )}

              {/* Template preview */}
              <div className="aspect-[4/3] bg-gray-100 rounded mb-3 flex items-center justify-center">
                <img
                  src={template.previewImageUrl}
                  alt={`${template.name} preview`}
                  className="max-w-full max-h-full object-contain"
                  onError={(e) => {
                    // Fallback to placeholder if image fails to load
                    (e.target as HTMLImageElement).src = '/placeholder-certificate.png';
                  }}
                />
              </div>

              {/* Template info */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-sm">{template.name}</h3>
                  {template.isDefault && (
                    <Badge variant="secondary" className="text-xs">Default</Badge>
                  )}
                </div>
                
                <p className="text-xs text-gray-600 line-clamp-2">
                  {template.description}
                </p>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{template.orientation}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePreview(template.id);
                    }}
                    className="h-6 px-2"
                  >
                    <Eye className="h-3 w-3 mr-1" />
                    Preview
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Selected template info */}
        {selectedTemplateId && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 text-blue-600 mr-2" />
              <span className="text-sm font-medium">Selected Template:</span>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              {templates.find(t => t.id === selectedTemplateId)?.name}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CertificateTemplateSelector;
