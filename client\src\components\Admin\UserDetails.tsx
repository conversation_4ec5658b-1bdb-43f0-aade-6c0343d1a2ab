import React, { useState, useEffect } from "react";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import {
  ChevronLeft,
  Save,
  Mail,
  UserX,
  CheckCircle,
  XCircle,
  Edit,
  Eye,
} from "lucide-react";

const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:5248/api";

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  organizationId: string;
  isActive: boolean;
  createdAt: string;
  lastLogin?: string;
}

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: "primary" | "secondary" | "danger";
  size?: "sm" | "md";
  disabled?: boolean;
}

interface UserDetailsProps {
  selectedUser: User | null;
  setActiveTab: (tab: string) => void;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  children,
  onClick,
  variant = "primary",
  size = "md",
  disabled = false,
}) => {
  const baseClasses =
    "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white",
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${
        disabled ? "opacity-50 cursor-not-allowed" : ""
      }`}
    >
      {children}
    </button>
  );
};

const UserDetails: React.FC<UserDetailsProps> = ({ selectedUser }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { userId } = useParams<{ userId: string }>();
  const [mode, setMode] = useState<"view" | "edit">("view");
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    role: "",
    isActive: true,
  });

  // Fetch user by ID from API
  const fetchUserById = async (id: string): Promise<User | null> => {
    try {
      const token = localStorage.getItem("auth_token");

      const response = await fetch(`${API_BASE_URL}/User/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Failed to fetch user: ${response.status}`);
      }

      const userData = await response.json();
      return userData;
    } catch (error) {
      console.error("Error fetching user:", error);
      throw error;
    }
  };

  useEffect(() => {
    const loadUser = async () => {
      setLoading(true);
      setError(null);

      try {
        // First try to use the selectedUser prop
        if (selectedUser && selectedUser.id === userId) {
          setCurrentUser(selectedUser);
        } else if (userId) {
          // If no selectedUser or ID mismatch, fetch from API
          const user = await fetchUserById(userId);
          setCurrentUser(user);
        } else {
          setCurrentUser(null);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load user");
      } finally {
        setLoading(false);
      }
    };

    loadUser();
  }, [userId, selectedUser]);

  useEffect(() => {
    // Get mode from navigation state
    const navMode = location.state?.mode || "view";
    setMode(navMode);

    // Initialize form data from current user
    if (currentUser) {
      setFormData({
        firstName: currentUser.firstName || "",
        lastName: currentUser.lastName || "",
        email: currentUser.email,
        role: currentUser.role,
        isActive: currentUser.isActive,
      });
    }
  }, [currentUser, location.state]);

  const getStatusIcon = (isActive: boolean) => {
    return isActive ? (
      <CheckCircle className="text-green-500" size={16} />
    ) : (
      <XCircle className="text-red-500" size={16} />
    );
  };

  const getStatusText = (isActive: boolean) => {
    return isActive ? "Active" : "Inactive";
  };

  const handleSaveChanges = async () => {
    if (!currentUser) return;

    try {
      const token = localStorage.getItem("auth_token");

      const updateData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        role: formData.role,
        isActive: formData.isActive,
      };

      const response = await fetch(`${API_BASE_URL}/User/${currentUser.id}`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error(`Failed to update user: ${response.status}`);
      }

      const updatedUser = await response.json();
      setCurrentUser(updatedUser);
      setMode("view");
    } catch (error) {
      console.error("Error updating user:", error);
      setError("Failed to save changes");
    }
  };

  const handleResendInvitation = async () => {
    if (!currentUser) return;

    try {
      const token = localStorage.getItem("auth_token");

      const invitationData = {
        email: currentUser.email,
        role: currentUser.role,
        organizationId: currentUser.organizationId,
        authType: 1, // Assuming Microsoft auth
      };

      const response = await fetch(`${API_BASE_URL}/user-invitations/invite`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(invitationData),
      });

      if (response.ok) {
        console.log("Invitation resent successfully to:", currentUser.email);
      } else {
        throw new Error("Failed to resend invitation");
      }
    } catch (error) {
      console.error("Error resending invitation:", error);
      setError("Failed to resend invitation");
    }
  };

  const handleDeactivateUser = async () => {
    if (!currentUser) return;

    if (window.confirm("Are you sure you want to deactivate this user?")) {
      try {
        const token = localStorage.getItem("auth_token");

        const response = await fetch(
          `${API_BASE_URL}/User/${currentUser.id}/status`,
          {
            method: "PATCH",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ isActive: false }),
          }
        );

        if (!response.ok) {
          throw new Error("Failed to deactivate user");
        }

        const updatedUser = await response.json();
        setCurrentUser(updatedUser);
        setFormData((prev) => ({ ...prev, isActive: false }));
      } catch (error) {
        console.error("Error deactivating user:", error);
        setError("Failed to deactivate user");
      }
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={() => navigate("/admin-dashboard/user-list")}
            className="text-[#2aa45c] hover:text-[#045024]"
          >
            <ChevronLeft size={24} />
          </button>
          <h2 className="text-xl font-semibold text-[#045024]">User Details</h2>
        </div>
        <div className="flex items-center justify-center py-8">
          <div className="text-gray-500">Loading user details...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={() => navigate("/admin-dashboard/user-list")}
            className="text-[#2aa45c] hover:text-[#045024]"
          >
            <ChevronLeft size={24} />
          </button>
          <h2 className="text-xl font-semibold text-[#045024]">User Details</h2>
        </div>
        <div className="text-center py-8">
          <p className="text-red-500 mb-4">{error}</p>
          <ActionButton
            onClick={() => navigate("/admin-dashboard/user-list")}
            variant="secondary"
          >
            Back to User List
          </ActionButton>
        </div>
      </div>
    );
  }

  if (!currentUser) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={() => navigate("/admin-dashboard/user-list")}
            className="text-[#2aa45c] hover:text-[#045024]"
          >
            <ChevronLeft size={24} />
          </button>
          <h2 className="text-xl font-semibold text-[#045024]">User Details</h2>
        </div>
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">User not found</p>
          <ActionButton
            onClick={() => navigate("/admin-dashboard/user-list")}
            variant="secondary"
          >
            Back to User List
          </ActionButton>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate("/admin-dashboard/user-list")}
              className="text-[#2aa45c] hover:text-[#045024]"
            >
              <ChevronLeft size={24} />
            </button>
            <h2 className="text-xl font-semibold text-[#045024]">
              {mode === "edit" ? "Edit User" : "User Details"}
            </h2>
          </div>
          <div className="flex items-center gap-2">
            {mode === "view" ? (
              <ActionButton onClick={() => setMode("edit")} variant="secondary">
                <Edit size={16} />
                Edit User
              </ActionButton>
            ) : (
              <ActionButton onClick={() => setMode("view")} variant="secondary">
                <Eye size={16} />
                View Mode
              </ActionButton>
            )}
          </div>
        </div>

        <div className="max-w-4xl">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      First Name
                    </label>
                    {mode === "edit" ? (
                      <input
                        type="text"
                        value={formData.firstName}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            firstName: e.target.value,
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                      />
                    ) : (
                      <div className="w-full px-3 py-2 bg-gray-50 rounded-lg text-gray-700">
                        {formData.firstName}
                      </div>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Last Name
                    </label>
                    {mode === "edit" ? (
                      <input
                        type="text"
                        value={formData.lastName}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            lastName: e.target.value,
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                      />
                    ) : (
                      <div className="w-full px-3 py-2 bg-gray-50 rounded-lg text-gray-700">
                        {formData.lastName}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  {mode === "edit" ? (
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          email: e.target.value,
                        }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                    />
                  ) : (
                    <div className="w-full px-3 py-2 bg-gray-50 rounded-lg text-gray-700">
                      {formData.email}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Role
                  </label>
                  {mode === "edit" ? (
                    <select
                      value={formData.role}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          role: e.target.value,
                        }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                    >
                      <option value="JTB_ADMIN">Admin</option>
                      <option value="FINANCE_OFFICER">Finance Officer</option>
                      <option value="SENIOR_FINANCE_OFFICER">
                        Senior Finance Officer
                      </option>
                      <option value="PAYER">Payer</option>
                    </select>
                  ) : (
                    <div className="w-full px-3 py-2 bg-gray-50 rounded-lg text-gray-700">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#dddeda] text-[#045024]">
                        {formData.role}
                      </span>
                    </div>
                  )}
                </div>

                {mode === "edit" && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Status
                    </label>
                    <div className="flex items-center gap-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="status"
                          value="true"
                          checked={formData.isActive === true}
                          onChange={(e) =>
                            setFormData((prev) => ({ ...prev, isActive: true }))
                          }
                          className="mr-2"
                        />
                        Active
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="status"
                          value="false"
                          checked={formData.isActive === false}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              isActive: false,
                            }))
                          }
                          className="mr-2"
                        />
                        Inactive
                      </label>
                    </div>
                  </div>
                )}

                <div className="flex gap-4">
                  {mode === "edit" ? (
                    <>
                      <ActionButton onClick={handleSaveChanges}>
                        <Save size={16} />
                        Save Changes
                      </ActionButton>
                      <ActionButton
                        onClick={handleResendInvitation}
                        variant="secondary"
                      >
                        <Mail size={16} />
                        Resend Invitation
                      </ActionButton>
                      <ActionButton
                        onClick={handleDeactivateUser}
                        variant="danger"
                      >
                        <UserX size={16} />
                        Deactivate User
                      </ActionButton>
                    </>
                  ) : (
                    <ActionButton
                      onClick={handleResendInvitation}
                      variant="secondary"
                    >
                      <Mail size={16} />
                      Resend Invitation
                    </ActionButton>
                  )}
                </div>
              </div>
            </div>

            <div>
              <div className="bg-[#dddeda] bg-opacity-30 rounded-lg p-4">
                <h4 className="font-medium text-[#045024] mb-3">
                  User Information
                </h4>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="text-gray-600">User ID:</span>
                    <span className="ml-2 font-medium">{currentUser.id}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Full Name:</span>
                    <span className="ml-2 font-medium">
                      {formData.firstName} {formData.lastName}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Created Date:</span>
                    <span className="ml-2 font-medium">
                      {new Date(currentUser.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Last Login:</span>
                    <span className="ml-2 font-medium">
                      {currentUser.lastLogin
                        ? new Date(currentUser.lastLogin).toLocaleDateString()
                        : "Never"}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-gray-600">Status:</span>
                    <div className="ml-2 flex items-center gap-1">
                      {getStatusIcon(formData.isActive)}
                      <span className="font-medium">
                        {getStatusText(formData.isActive)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDetails;
