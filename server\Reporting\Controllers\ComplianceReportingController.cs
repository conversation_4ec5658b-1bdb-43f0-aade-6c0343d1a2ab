using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Final_E_Receipt.Reporting.Services;

namespace Final_E_Receipt.Reporting.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ComplianceReportingController : ControllerBase
    {
        private readonly ComplianceReportingService _complianceReportingService;
        private readonly ReportExcelService _excelService;
        private readonly ILogger<ComplianceReportingController> _logger;

        public ComplianceReportingController(
            ComplianceReportingService complianceReportingService,
            ReportExcelService excelService,
            ILogger<ComplianceReportingController> logger)
        {
            _complianceReportingService = complianceReportingService;
            _excelService = excelService;
            _logger = logger;
        }

        [HttpGet("dashboard")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER,JTB_ADMIN")]
        public async Task<IActionResult> GetComplianceDashboard()
        {
            try
            {
                var dashboard = await _complianceReportingService.GetComplianceDashboard();
                return Ok(dashboard);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving compliance dashboard");
                return StatusCode(500, new { message = "An error occurred while retrieving the compliance dashboard" });
            }
        }

        [HttpGet("organization/{organizationId}")]
        public async Task<IActionResult> GetOrganizationComplianceReport(string organizationId)
        {
            try
            {
                // Check if user has access to this organization's data
                var userOrganizationId = User.FindFirst("OrganizationId")?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                if (userRole != "JTB_ADMIN" && userRole != "FINANCE_OFFICER" && userRole != "SENIOR_FINANCE_OFFICER" &&
                    userOrganizationId != organizationId)
                {
                    return Forbid();
                }

                var report = await _complianceReportingService.GetOrganizationComplianceReport(organizationId);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving organization compliance report for {OrganizationId}", organizationId);
                return StatusCode(500, new { message = "An error occurred while retrieving the compliance report" });
            }
        }

        [HttpGet("metrics")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER,JTB_ADMIN")]
        public async Task<IActionResult> GetComplianceMetrics()
        {
            try
            {
                var metrics = await _complianceReportingService.GetComplianceMetrics();
                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving compliance metrics");
                return StatusCode(500, new { message = "An error occurred while retrieving compliance metrics" });
            }
        }

        [HttpGet("issuance-stats")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER,JTB_ADMIN")]
        public async Task<IActionResult> GetCertificateIssuanceStats([FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate)
        {
            try
            {
                var from = fromDate ?? DateTime.Now.AddMonths(-12);
                var to = toDate ?? DateTime.Now;

                var stats = await _complianceReportingService.GetCertificateIssuanceStats(from, to);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving certificate issuance stats");
                return StatusCode(500, new { message = "An error occurred while retrieving issuance statistics" });
            }
        }

        [HttpGet("expiring-certificates")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER,JTB_ADMIN")]
        public async Task<IActionResult> GetExpiringCertificatesReport([FromQuery] int daysFromNow = 30)
        {
            try
            {
                var report = await _complianceReportingService.GetExpiringCertificatesReport(daysFromNow);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving expiring certificates report");
                return StatusCode(500, new { message = "An error occurred while retrieving the expiring certificates report" });
            }
        }

        [HttpGet("export/dashboard")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER,JTB_ADMIN")]
        public async Task<IActionResult> ExportDashboardReport([FromQuery] string format = "pdf")
        {
            try
            {
                var dashboard = await _complianceReportingService.GetComplianceDashboard();
                
                if (format.ToLower() == "excel")
                {
                    var excelContent = await GenerateExcelDashboardReport(dashboard);
                    var fileName = $"ComplianceDashboard_{DateTime.Now:yyyyMMdd}.xlsx";
                    return File(excelContent, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
                else
                {
                    var pdfContent = await GeneratePdfDashboardReport(dashboard);
                    var fileName = $"ComplianceDashboard_{DateTime.Now:yyyyMMdd}.pdf";
                    return File(pdfContent, "application/pdf", fileName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting dashboard report");
                return StatusCode(500, new { message = "An error occurred while exporting the dashboard report" });
            }
        }

        [HttpGet("export/organization/{organizationId}")]
        public async Task<IActionResult> ExportOrganizationReport(string organizationId, [FromQuery] string format = "pdf")
        {
            try
            {
                // Check access permissions
                var userOrganizationId = User.FindFirst("OrganizationId")?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                if (userRole != "JTB_ADMIN" && userRole != "FINANCE_OFFICER" && userRole != "SENIOR_FINANCE_OFFICER" &&
                    userOrganizationId != organizationId)
                {
                    return Forbid();
                }

                var report = await _complianceReportingService.GetOrganizationComplianceReport(organizationId);
                
                if (format.ToLower() == "excel")
                {
                    var excelContent = await GenerateExcelOrganizationReport(report);
                    var fileName = $"ComplianceReport_{organizationId}_{DateTime.Now:yyyyMMdd}.xlsx";
                    return File(excelContent, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
                else
                {
                    var pdfContent = await GeneratePdfOrganizationReport(report);
                    var fileName = $"ComplianceReport_{organizationId}_{DateTime.Now:yyyyMMdd}.pdf";
                    return File(pdfContent, "application/pdf", fileName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting organization report for {OrganizationId}", organizationId);
                return StatusCode(500, new { message = "An error occurred while exporting the organization report" });
            }
        }

        [HttpGet("export/expiring-certificates")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER,JTB_ADMIN")]
        public async Task<IActionResult> ExportExpiringCertificatesReport([FromQuery] int daysFromNow = 30, [FromQuery] string format = "excel")
        {
            try
            {
                var report = await _complianceReportingService.GetExpiringCertificatesReport(daysFromNow);
                
                if (format.ToLower() == "pdf")
                {
                    var pdfContent = await GeneratePdfExpiringCertificatesReport(report);
                    var fileName = $"ExpiringCertificates_{DateTime.Now:yyyyMMdd}.pdf";
                    return File(pdfContent, "application/pdf", fileName);
                }
                else
                {
                    var excelContent = await GenerateExcelExpiringCertificatesReport(report);
                    var fileName = $"ExpiringCertificates_{DateTime.Now:yyyyMMdd}.xlsx";
                    return File(excelContent, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting expiring certificates report");
                return StatusCode(500, new { message = "An error occurred while exporting the expiring certificates report" });
            }
        }

        // Report generation methods using ReportExcelService
        private async Task<byte[]> GenerateExcelDashboardReport(ComplianceDashboard dashboard)
        {
            return await _excelService.GenerateComplianceDashboardExcel(dashboard);
        }

        private async Task<byte[]> GeneratePdfDashboardReport(ComplianceDashboard dashboard)
        {
            // TODO: Implement PDF generation using iTextSharp or similar
            var placeholder = $"PDF Dashboard Report - {dashboard.GeneratedDate}";
            return System.Text.Encoding.UTF8.GetBytes(placeholder);
        }

        private async Task<byte[]> GenerateExcelOrganizationReport(ComplianceReport report)
        {
            return await _excelService.GenerateOrganizationComplianceExcel(report);
        }

        private async Task<byte[]> GeneratePdfOrganizationReport(ComplianceReport report)
        {
            // TODO: Implement PDF generation
            var placeholder = $"PDF Organization Report - {report.OrganizationId}";
            return System.Text.Encoding.UTF8.GetBytes(placeholder);
        }

        private async Task<byte[]> GenerateExcelExpiringCertificatesReport(ExpiringCertificatesReport report)
        {
            return await _excelService.GenerateExpiringCertificatesExcel(report);
        }

        private async Task<byte[]> GeneratePdfExpiringCertificatesReport(ExpiringCertificatesReport report)
        {
            // TODO: Implement PDF generation
            var placeholder = $"PDF Expiring Certificates Report - {report.ReportDate}";
            return System.Text.Encoding.UTF8.GetBytes(placeholder);
        }
    }
}
