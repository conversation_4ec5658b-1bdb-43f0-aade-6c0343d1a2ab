-- Receipt Templates Table
CREATE TABLE ReceiptTemplates (
    Id NVARCHAR(50) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500),
    TemplateContent NVARCHAR(MAX) NOT NULL,
    Is<PERSON>ef<PERSON> BIT NOT NULL DEFAULT 0,
    OrganizationId NVARCHAR(50) NOT NULL,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50),
    UpdatedAt DATETIME,
    UpdatedBy NVARCHAR(50)
);

-- Stored Procedures for Receipt Templates
CREATE PROCEDURE CreateReceiptTemplate
    @Id NVARCHAR(50),
    @Name NVARCHAR(100),
    @Description NVARCHAR(500),
    @TemplateContent NVARCHAR(MAX),
    @IsDefault BIT,
    @OrganizationId NVARCHAR(50),
    @CreatedBy NVARCHAR(50)
AS
BEGIN
    INSERT INTO ReceiptTemplates (
        Id, Name, Description, TemplateContent, IsDefault, OrganizationId, CreatedBy
    )
    VALUES (
        @Id, @Name, @Description, @TemplateContent, @IsDefault, @OrganizationId, @CreatedBy
    )
    
    SELECT * FROM ReceiptTemplates WHERE Id = @Id
END;

CREATE PROCEDURE GetReceiptTemplateById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM ReceiptTemplates WHERE Id = @Id
END;

CREATE PROCEDURE GetReceiptTemplatesByOrganization
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM ReceiptTemplates WHERE OrganizationId = @OrganizationId ORDER BY Name
END;

CREATE PROCEDURE UpdateReceiptTemplate
    @Id NVARCHAR(50),
    @Name NVARCHAR(100),
    @Description NVARCHAR(500),
    @TemplateContent NVARCHAR(MAX),
    @IsDefault BIT,
    @UpdatedBy NVARCHAR(50)
AS
BEGIN
    UPDATE ReceiptTemplates
    SET Name = @Name,
        Description = @Description,
        TemplateContent = @TemplateContent,
        IsDefault = @IsDefault,
        UpdatedAt = GETDATE(),
        UpdatedBy = @UpdatedBy
    WHERE Id = @Id
    
    SELECT * FROM ReceiptTemplates WHERE Id = @Id
END;

CREATE PROCEDURE DeleteReceiptTemplate
    @Id NVARCHAR(50)
AS
BEGIN
    DELETE FROM ReceiptTemplates WHERE Id = @Id
END;

CREATE PROCEDURE GetDefaultReceiptTemplate
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM ReceiptTemplates 
    WHERE OrganizationId = @OrganizationId AND IsDefault = 1
END;