import {useState} from 'react';
import { Receipt, Download, Bell } from 'lucide-react';

interface ReceiptData {
  id: string;
  profileName: string;
  amount: number;
  date: string;
  receiptUrl: string;
  isRecent: boolean;
}

const Receipts: React.FC= () => {
    const [receipts] = useState<ReceiptData[]>([
    { id: '1', profileName: 'Annual License', amount: 5000.00, date: '2025-06-15', receiptUrl: 'receipt1.pdf', isRecent: true },
    { id: '2', profileName: 'Monthly Service Fee', amount: 2500.00, date: '2025-05-15', receiptUrl: 'receipt2.pdf', isRecent: false },
    { id: '3', profileName: 'Consulting Services', amount: 1200.00, date: '2025-04-20', receiptUrl: 'receipt3.pdf', isRecent: false }
    ]);
  const formatCurrency = (amount: number) => `₦${amount.toLocaleString('en-NG', { minimumFractionDigits: 2 })}`;
  const formatDate = (date: string) => new Date(date).toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  });

  return (
    <div className="space-y-6">
      <div className=" bg-[#045024] flex items-center justify-between rounded-t-lg py-4 px-6">
        <h3 className="text-xl font-semibold text-white">Receipts</h3>
        <button 
          className="flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors border border-white/20"
          style={{ backgroundColor: '#076934' }}
        >
          <Download size={16} />
          Download All
        </button>
      </div>

      <div className="grid gap-4">
        {receipts.map((receipt) => (
          <div
            key={receipt.id}
            className={`bg-white rounded-lg shadow-md p-6 border-l-4 ${
              receipt.isRecent ? 'bg-green-50' : ''
            }`}
            style={{ borderLeftColor: receipt.isRecent ? '#2aa45c' : '#d1d5db' }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Receipt style={{ color: '#2aa45c' }} size={24} />
                <div>
                  <p className="font-medium text-gray-900">{receipt.profileName}</p>
                  <p className="text-sm text-gray-600">{formatDate(receipt.date)}</p>
                  {receipt.isRecent && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full mt-1">
                      <Bell size={10} />
                      Recently Paid
                    </span>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-4">
                <span className="font-semibold text-gray-900">{formatCurrency(receipt.amount)}</span>
                <button 
                  className="flex items-center gap-2 px-3 py-1 rounded-lg transition-colors"
                  style={{ color: '#076934', backgroundColor: '#f0f9ff' }}
                >
                  <Download size={16} />
                  Download
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Receipts;