using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Final_E_Receipt.Organizations.Models;
using Final_E_Receipt.Compliance.Services;
using Final_E_Receipt.Compliance.Models;
using Final_E_Receipt.Compliance.DTOs;
using Final_E_Receipt.Payments.Services;
using Final_E_Receipt.Services;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Organizations.Services
{
    public class OrganizationComplianceService
    {
        private readonly OrganizationService _organizationService;
        private readonly ComplianceCertificateService _certificateService;
        private readonly PaymentComplianceService _paymentComplianceService;
        private readonly IDatabaseService _dbService;
        private readonly ILogger<OrganizationComplianceService> _logger;

        public OrganizationComplianceService(
            OrganizationService organizationService,
            ComplianceCertificateService certificateService,
            PaymentComplianceService paymentComplianceService,
            IDatabaseService dbService,
            ILogger<OrganizationComplianceService> logger)
        {
            _organizationService = organizationService;
            _certificateService = certificateService;
            _paymentComplianceService = paymentComplianceService;
            _dbService = dbService;
            _logger = logger;
        }

        /// <summary>
        /// Gets comprehensive compliance status for an organization
        /// </summary>
        public async Task<OrganizationComplianceStatus> GetOrganizationComplianceStatus(string organizationId)
        {
            try
            {
                var organization = await _organizationService.GetOrganizationById(organizationId);
                if (organization == null)
                    return null;

                var certificates = await _certificateService.GetComplianceCertificatesByOrganization(organizationId, 1, 1000);
                
                var status = new OrganizationComplianceStatus
                {
                    OrganizationId = organizationId,
                    OrganizationName = organization.Name,
                    LastUpdated = DateTime.Now,
                    TotalCertificates = certificates.Count,
                    ActiveCertificates = certificates.Count(c => !c.IsRevoked && c.ValidUntil > DateTime.Now),
                    ExpiredCertificates = certificates.Count(c => !c.IsRevoked && c.ValidUntil <= DateTime.Now),
                    RevokedCertificates = certificates.Count(c => c.IsRevoked),
                    CertificatesExpiringSoon = certificates.Count(c => !c.IsRevoked && 
                        c.ValidUntil > DateTime.Now && c.ValidUntil <= DateTime.Now.AddDays(30)),
                    OverallComplianceScore = CalculateComplianceScore(certificates),
                    ComplianceLevel = DetermineComplianceLevel(certificates),
                    CertificatesByType = GroupCertificatesByType(certificates),
                    RecentCertificates = certificates.Where(c => c.IssuedDate >= DateTime.Now.AddDays(-90))
                        .OrderByDescending(c => c.IssuedDate).Take(5).ToList(),
                    UpcomingExpirations = certificates.Where(c => !c.IsRevoked && 
                        c.ValidUntil > DateTime.Now && c.ValidUntil <= DateTime.Now.AddDays(90))
                        .OrderBy(c => c.ValidUntil).Take(5).ToList(),
                    ComplianceHistory = await GetComplianceHistory(organizationId)
                };

                return status;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting compliance status for organization {OrganizationId}", organizationId);
                return null;
            }
        }

        /// <summary>
        /// Gets organization compliance summary for dashboard
        /// </summary>
        public async Task<OrganizationComplianceSummary> GetOrganizationComplianceSummary(string organizationId)
        {
            try
            {
                var certificates = await _certificateService.GetComplianceCertificatesByOrganization(organizationId, 1, 100);
                
                return new OrganizationComplianceSummary
                {
                    OrganizationId = organizationId,
                    IsCompliant = certificates.Any(c => !c.IsRevoked && c.ValidUntil > DateTime.Now),
                    ActiveCertificatesCount = certificates.Count(c => !c.IsRevoked && c.ValidUntil > DateTime.Now),
                    ExpiringCertificatesCount = certificates.Count(c => !c.IsRevoked && 
                        c.ValidUntil > DateTime.Now && c.ValidUntil <= DateTime.Now.AddDays(30)),
                    ComplianceScore = CalculateComplianceScore(certificates),
                    LastCertificateIssued = certificates.Where(c => !c.IsRevoked).OrderByDescending(c => c.IssuedDate).FirstOrDefault()?.IssuedDate,
                    NextExpiryDate = certificates.Where(c => !c.IsRevoked && c.ValidUntil > DateTime.Now)
                        .OrderBy(c => c.ValidUntil).FirstOrDefault()?.ValidUntil
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting compliance summary for organization {OrganizationId}", organizationId);
                return new OrganizationComplianceSummary { OrganizationId = organizationId };
            }
        }

        /// <summary>
        /// Gets all organizations with their compliance status
        /// </summary>
        public async Task<List<OrganizationComplianceSummary>> GetAllOrganizationsComplianceStatus()
        {
            try
            {
                var organizations = await _organizationService.GetAllOrganizations();
                var complianceSummaries = new List<OrganizationComplianceSummary>();

                foreach (var org in organizations)
                {
                    var summary = await GetOrganizationComplianceSummary(org.Id);
                    summary.OrganizationName = org.Name;
                    complianceSummaries.Add(summary);
                }

                return complianceSummaries.OrderByDescending(s => s.ComplianceScore).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all organizations compliance status");
                return new List<OrganizationComplianceSummary>();
            }
        }

        /// <summary>
        /// Gets organizations that need attention (expiring certificates, non-compliant, etc.)
        /// </summary>
        public async Task<OrganizationComplianceAlerts> GetOrganizationComplianceAlerts()
        {
            try
            {
                var allSummaries = await GetAllOrganizationsComplianceStatus();
                
                return new OrganizationComplianceAlerts
                {
                    GeneratedDate = DateTime.Now,
                    NonCompliantOrganizations = allSummaries.Where(s => !s.IsCompliant).ToList(),
                    OrganizationsWithExpiringCertificates = allSummaries.Where(s => s.ExpiringCertificatesCount > 0).ToList(),
                    LowComplianceScoreOrganizations = allSummaries.Where(s => s.ComplianceScore < 70).ToList(),
                    OrganizationsRequiringAction = allSummaries.Where(s => 
                        !s.IsCompliant || s.ExpiringCertificatesCount > 0 || s.ComplianceScore < 70).ToList()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting organization compliance alerts");
                return new OrganizationComplianceAlerts { GeneratedDate = DateTime.Now };
            }
        }

        /// <summary>
        /// Updates organization compliance status after certificate changes
        /// </summary>
        public async Task<bool> UpdateOrganizationComplianceStatus(string organizationId, string certificateId, string action)
        {
            try
            {
                // This could update a compliance status cache or trigger notifications
                var status = await GetOrganizationComplianceSummary(organizationId);
                
                _logger.LogInformation(
                    "Organization {OrganizationId} compliance status updated after certificate {Action}: Score {Score}",
                    organizationId, action, status.ComplianceScore);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating organization compliance status for {OrganizationId}", organizationId);
                return false;
            }
        }

        /// <summary>
        /// Gets certificate requirements for an organization
        /// </summary>
        public async Task<List<CertificateRequirement>> GetCertificateRequirements(string organizationId)
        {
            try
            {
                // This would typically be based on organization type, business category, etc.
                // For now, return standard requirements
                return new List<CertificateRequirement>
                {
                    new CertificateRequirement
                    {
                        CertificateType = "ANNUAL_LICENSE",
                        Description = "Annual Business License",
                        IsRequired = true,
                        ValidityPeriod = 365,
                        RenewalNoticeDays = 30
                    },
                    new CertificateRequirement
                    {
                        CertificateType = "TAX_CLEARANCE",
                        Description = "Tax Clearance Certificate",
                        IsRequired = true,
                        ValidityPeriod = 365,
                        RenewalNoticeDays = 60
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting certificate requirements for organization {OrganizationId}", organizationId);
                return new List<CertificateRequirement>();
            }
        }

        // Helper methods
        private double CalculateComplianceScore(List<ComplianceCertificate> certificates)
        {
            if (!certificates.Any()) return 0;

            var activeCertificates = certificates.Count(c => !c.IsRevoked && c.ValidUntil > DateTime.Now);
            var totalCertificates = certificates.Count;
            
            // Base score from active certificates
            var baseScore = (double)activeCertificates / totalCertificates * 100;
            
            // Penalty for expiring certificates
            var expiringCertificates = certificates.Count(c => !c.IsRevoked && 
                c.ValidUntil > DateTime.Now && c.ValidUntil <= DateTime.Now.AddDays(30));
            var expiryPenalty = expiringCertificates * 5; // 5 points per expiring certificate
            
            return Math.Max(0, baseScore - expiryPenalty);
        }

        private string DetermineComplianceLevel(List<ComplianceCertificate> certificates)
        {
            var score = CalculateComplianceScore(certificates);
            
            return score switch
            {
                >= 90 => "EXCELLENT",
                >= 80 => "GOOD",
                >= 70 => "SATISFACTORY",
                >= 60 => "NEEDS_IMPROVEMENT",
                _ => "NON_COMPLIANT"
            };
        }

        private Dictionary<string, int> GroupCertificatesByType(List<ComplianceCertificate> certificates)
        {
            return certificates
                .GroupBy(c => c.CertificateType)
                .ToDictionary(g => g.Key, g => g.Count());
        }

        private async Task<List<ComplianceHistoryEntry>> GetComplianceHistory(string organizationId)
        {
            try
            {
                // This would query historical compliance data
                // For now, return a placeholder
                return new List<ComplianceHistoryEntry>
                {
                    new ComplianceHistoryEntry
                    {
                        Date = DateTime.Now.AddDays(-30),
                        ComplianceScore = 85,
                        ActiveCertificates = 3,
                        Event = "Certificate renewed"
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting compliance history for organization {OrganizationId}", organizationId);
                return new List<ComplianceHistoryEntry>();
            }
        }
    }

    // Supporting classes
    public class OrganizationComplianceStatus
    {
        public string OrganizationId { get; set; }
        public string OrganizationName { get; set; }
        public DateTime LastUpdated { get; set; }
        public int TotalCertificates { get; set; }
        public int ActiveCertificates { get; set; }
        public int ExpiredCertificates { get; set; }
        public int RevokedCertificates { get; set; }
        public int CertificatesExpiringSoon { get; set; }
        public double OverallComplianceScore { get; set; }
        public string ComplianceLevel { get; set; }
        public Dictionary<string, int> CertificatesByType { get; set; } = new Dictionary<string, int>();
        public List<ComplianceCertificate> RecentCertificates { get; set; } = new List<ComplianceCertificate>();
        public List<ComplianceCertificate> UpcomingExpirations { get; set; } = new List<ComplianceCertificate>();
        public List<ComplianceHistoryEntry> ComplianceHistory { get; set; } = new List<ComplianceHistoryEntry>();
    }

    public class OrganizationComplianceSummary
    {
        public string OrganizationId { get; set; }
        public string OrganizationName { get; set; }
        public bool IsCompliant { get; set; }
        public int ActiveCertificatesCount { get; set; }
        public int ExpiringCertificatesCount { get; set; }
        public double ComplianceScore { get; set; }
        public DateTime? LastCertificateIssued { get; set; }
        public DateTime? NextExpiryDate { get; set; }
    }

    public class OrganizationComplianceAlerts
    {
        public DateTime GeneratedDate { get; set; }
        public List<OrganizationComplianceSummary> NonCompliantOrganizations { get; set; } = new List<OrganizationComplianceSummary>();
        public List<OrganizationComplianceSummary> OrganizationsWithExpiringCertificates { get; set; } = new List<OrganizationComplianceSummary>();
        public List<OrganizationComplianceSummary> LowComplianceScoreOrganizations { get; set; } = new List<OrganizationComplianceSummary>();
        public List<OrganizationComplianceSummary> OrganizationsRequiringAction { get; set; } = new List<OrganizationComplianceSummary>();
    }

    public class CertificateRequirement
    {
        public string CertificateType { get; set; }
        public string Description { get; set; }
        public bool IsRequired { get; set; }
        public int ValidityPeriod { get; set; } // in days
        public int RenewalNoticeDays { get; set; }
    }

    public class ComplianceHistoryEntry
    {
        public DateTime Date { get; set; }
        public double ComplianceScore { get; set; }
        public int ActiveCertificates { get; set; }
        public string Event { get; set; }
    }
}
