# 🔧 Admin Setup Guide - Creating the First Admin User

## 🎯 **How to Create the First Admin User**

Since our system uses invitation-based user creation, we need a special bootstrap process to create the very first admin user who can then invite other users.

## 📋 **Admin Setup Process:**

### **Step 1: Configure Allowed Admin Emails**

Edit `server/appsettings.json` and add the email addresses that are allowed to become the initial admin:

```json
{
  "AdminSetup": {
    "AllowedEmails": [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ]
  }
}
```

**Security Note:** Only emails in this list can become the initial admin. Remove this section after creating the first admin for maximum security.

### **Step 2: Deploy Database Schema**

Ensure the database is set up with all tables and procedures:

```sql
-- Deploy complete schema
server/SQL/CompleteDatabaseSchema.sql

-- Deploy authentication procedures
server/Authentication/SQL/AuthenticationProcedures.sql
```

### **Step 3: Start the API Server**

```bash
cd server
dotnet run
```

The API will be available at `http://localhost:5000` or `https://localhost:5001`

### **Step 4: Get Microsoft JWT Token**

The person who will become the first admin needs to:

1. **Login to Microsoft** using the Azure AD tenant configured in the system
2. **Get a JWT token** for the API audience
3. **Extract the token** from the authentication response

#### **Option A: Use Frontend Application**
If you have a React frontend set up:
1. Login through the React app
2. Open browser developer tools
3. Check localStorage/sessionStorage for the JWT token
4. Copy the token value

#### **Option B: Use Microsoft Authentication Directly**
Navigate to Microsoft login URL:
```
https://login.microsoftonline.com/{tenant-id}/oauth2/v2.0/authorize?
client_id={client-id}&
response_type=token&
redirect_uri={redirect-uri}&
scope=api://{client-id}/.default&
response_mode=fragment
```

Replace:
- `{tenant-id}`: Your Azure AD tenant ID
- `{client-id}`: Your application client ID  
- `{redirect-uri}`: Your configured redirect URI

### **Step 5: Call Admin Setup Endpoint**

Use the JWT token to call the admin setup endpoint:

```bash
curl -X POST "http://localhost:5000/api/adminsetup/initialize" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

#### **Expected Response (Success):**
```json
{
  "message": "Admin user created successfully",
  "user": {
    "id": "admin-user-id",
    "email": "<EMAIL>",
    "firstName": "Admin",
    "lastName": "User",
    "role": "ADMIN"
  }
}
```

#### **Expected Response (Already Exists):**
```json
{
  "message": "Admin users already exist. Cannot create initial admin."
}
```

#### **Expected Response (Not Allowed):**
```json
{
  "message": "Forbidden"
}
```

### **Step 6: Verify Admin User Creation**

Test that the admin user was created successfully:

```bash
curl -X GET "http://localhost:5000/api/auth/me" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

**Expected Response:**
```json
{
  "id": "admin-user-id",
  "email": "<EMAIL>",
  "firstName": "Admin",
  "lastName": "User",
  "role": "ADMIN",
  "isActive": true,
  "organizationId": null
}
```

### **Step 7: Remove Security Configuration (Optional)**

After creating the first admin, you can remove the `AdminSetup` section from `appsettings.json` for maximum security:

```json
{
  // Remove this section after first admin is created
  // "AdminSetup": {
  //   "AllowedEmails": [...]
  // }
}
```

## 🔄 **Complete Admin Workflow:**

### **1. First Admin Creation:**
```
Microsoft Login → JWT Token → Admin Setup Endpoint → First Admin Created
```

### **2. Subsequent User Creation:**
```
Admin Invites User → User Gets Invitation → User Logs in with Microsoft → User Created with Role
```

## 🛡️ **Security Features:**

### **✅ Email Whitelist:**
- Only pre-configured emails can become the initial admin
- Prevents unauthorized admin creation

### **✅ One-Time Setup:**
- Admin setup only works when no admin users exist
- Prevents multiple admin creation attempts

### **✅ Microsoft Authentication:**
- Requires valid Microsoft JWT token
- Ensures user is authenticated with Microsoft

### **✅ Proper Claims Extraction:**
- Extracts user info from JWT token claims
- Links admin user to Microsoft account

## 🧪 **Testing the Setup:**

### **Test 1: Valid Admin Creation**
```bash
# With valid email in AllowedEmails list
curl -X POST "http://localhost:5000/api/adminsetup/initialize" \
  -H "Authorization: Bearer VALID_JWT_TOKEN"
# Expected: 200 OK with admin user details
```

### **Test 2: Unauthorized Email**
```bash
# With email NOT in AllowedEmails list
curl -X POST "http://localhost:5000/api/adminsetup/initialize" \
  -H "Authorization: Bearer JWT_TOKEN_WITH_UNAUTHORIZED_EMAIL"
# Expected: 403 Forbidden
```

### **Test 3: Admin Already Exists**
```bash
# After admin is already created
curl -X POST "http://localhost:5000/api/adminsetup/initialize" \
  -H "Authorization: Bearer VALID_JWT_TOKEN"
# Expected: 400 Bad Request - "Admin users already exist"
```

### **Test 4: Invalid Token**
```bash
# With invalid or expired token
curl -X POST "http://localhost:5000/api/adminsetup/initialize" \
  -H "Authorization: Bearer INVALID_TOKEN"
# Expected: 401 Unauthorized
```

## 📋 **Troubleshooting:**

### **Issue: "Azure AD Object ID not found in token"**
**Solution:** Ensure the JWT token contains the `oid` claim. Check Azure AD app registration scopes.

### **Issue: "Email not found in token claims"**
**Solution:** Ensure the JWT token contains `preferred_username` or `upn` claims. Check Azure AD app registration.

### **Issue: "Forbidden"**
**Solution:** Check that the email in the JWT token is listed in `AdminSetup:AllowedEmails` in appsettings.json.

### **Issue: "Admin users already exist"**
**Solution:** An admin user has already been created. Use the existing admin to invite new users.

## ✅ **After First Admin is Created:**

### **Next Steps:**
1. **Login as Admin** using the Microsoft authentication
2. **Create Organizations** for your system
3. **Invite Users** with appropriate roles:
   - FINANCE_OFFICER
   - SENIOR_FINANCE_OFFICER  
   - PAYER
4. **Configure Payment Profiles** and schedules
5. **Set up compliance rules** if needed

### **Admin Capabilities:**
- ✅ **Invite all user types** (FINANCE_OFFICER, SENIOR_FINANCE_OFFICER, PAYER)
- ✅ **Manage organizations** (create, update, delete)
- ✅ **View all payments** across organizations
- ✅ **Access all reports** and analytics
- ✅ **Manage system configuration**

## 🚀 **Ready for Production:**

**Once the first admin is created, your Payment Management System is ready for full operation with:**

- ✅ **Secure admin bootstrap process**
- ✅ **Microsoft authentication integration**
- ✅ **Role-based access control**
- ✅ **Complete payment approval workflow**
- ✅ **Multi-tenant organization support**

**The system is now ready for users to be invited and start processing payments!** 🎉
