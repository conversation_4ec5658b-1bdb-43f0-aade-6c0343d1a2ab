import type { AuthStatus, User } from "../types/auth";
import { getAccessToken } from "./msalAuthService";

// API Configuration
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "https://localhost:7141/api";

// Local storage keys
const LOCAL_TOKEN_KEY = "local_auth_token";
const AUTH_TYPE_KEY = "auth_type";

// Helper function for API calls
async function fetchWithAuth<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const authType = localStorage.getItem(AUTH_TYPE_KEY);
  let token = null;

  if (authType === "local") {
    token = localStorage.getItem(LOCAL_TOKEN_KEY);
  } else {
    try {
      token = await getAccessToken();
    } catch (err) {
      console.error("Failed to get Microsoft token:", err);
    }
  }

  const headers = {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
    ...options.headers,
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
  });

  if (!response.ok) {
    throw new Error(`API call failed: ${response.statusText}`);
  }

  return response.json();
}

export interface CreateInvitationRequest {
  email: string;
  role: "ADMIN" | "FINANCE_OFFICER" | "SENIOR_FINANCE_OFFICER" | "PAYER";
  organizationId?: string;
  authType: "MICROSOFT" | "LOCAL";
}

export interface UserInvitation {
  id: string;
  email: string;
  role: string;
  organizationId?: string;
  invitedDate: string;
  status: "Pending" | "Accepted" | "Cancelled";
  invitedBy: string;
  expiryDate: string;
}

class AuthService {
  /**
   * Get current token based on auth type
   */
  async getCurrentToken(): Promise<string | null> {
    const authType = localStorage.getItem(AUTH_TYPE_KEY);
    console.log("Getting token for auth type:", authType);

    if (authType === "local") {
      const token = localStorage.getItem(LOCAL_TOKEN_KEY);
      if (!token) {
        console.error("No local token found but auth type is local");
        // Clear invalid state
        localStorage.removeItem(AUTH_TYPE_KEY);
        return null;
      }
      console.log("Using local token:", token.substring(0, 10) + "...");
      return token;
    }

    // Default to Microsoft token
    try {
      const msalToken = await getAccessToken();
      if (!msalToken) {
        console.error("No MSAL token available");
        return null;
      }
      console.log("Using MSAL token:", msalToken.substring(0, 10) + "...");
      return msalToken;
    } catch (error) {
      console.error("Failed to get MSAL token:", error);
      return null;
    }
  }

  /**
   * Local login
   */
  async localLogin(email: string, password: string): Promise<User> {
    // Explicitly use the local auth endpoint
    const response = await fetchWithAuth<{ token: string; user: User }>(
      "/auth/local/login",
      {
        method: "POST",
        headers: {
          "X-Auth-Type": "local",
        },
        body: JSON.stringify({ email, password }),
      }
    );

    if (!response.token) {
      throw new Error("No token received from server");
    }

    // Store the token
    localStorage.setItem(LOCAL_TOKEN_KEY, response.token);
    localStorage.setItem(AUTH_TYPE_KEY, "local");
    console.log("Stored local token and auth type");

    return response.user;
  }

  /**
   * Exchange Microsoft token for internal JWT
   */
  async exchangeMicrosoftToken(): Promise<string> {
    const response = await fetchWithAuth<{ token: string }>("/auth/exchange", {
      headers: {
        "X-Auth-Type": "microsoft",
      },
    });

    if (!response.token) {
      throw new Error("No token received from server");
    }

    return response.token;
  }

  /**
   * Clear auth tokens on logout
   */
  clearTokens(): void {
    localStorage.removeItem(LOCAL_TOKEN_KEY);
    localStorage.removeItem(AUTH_TYPE_KEY);
  }

  /**
   * Get current authenticated user info
   */
  async getCurrentUser(): Promise<User> {
    return fetchWithAuth<User>("/auth/exchange");
  }

  /**
   * Get authentication status and claims
   */
  async getAuthStatus(): Promise<AuthStatus> {
    return fetchWithAuth<AuthStatus>("/auth/status");
  }

  /**
   * Initialize admin user (bootstrap process)
   */
  async initializeAdmin(): Promise<User> {
    return fetchWithAuth<User>("/adminsetup/initialize", { method: "POST" });
  }

  /**
   * Create user invitation (Admin only)
   */
  async createInvitation(
    invitation: CreateInvitationRequest
  ): Promise<UserInvitation> {
    return fetchWithAuth<UserInvitation>("/user-invitations/invite", {
      method: "POST",
      body: JSON.stringify(invitation),
    });
  }

  /**
   * Get all user invitations (Admin only)
   */
  async getAllInvitations(): Promise<UserInvitation[]> {
    return fetchWithAuth<UserInvitation[]>("/user-invitations");
  }

  /**
   * Get pending invitations
   */
  async getPendingInvitations(): Promise<UserInvitation[]> {
    return fetchWithAuth<UserInvitation[]>("/user-invitations/pending");
  }

  /**
   * Resend invitation (Admin only)
   */
  async resendInvitation(invitationId: string): Promise<UserInvitation> {
    return fetchWithAuth<UserInvitation>(
      `/user-invitations/${invitationId}/resend`,
      {
        method: "POST",
      }
    );
  }

  /**
   * Cancel invitation (Admin only)
   */
  async cancelInvitation(invitationId: string): Promise<void> {
    return fetchWithAuth<void>(`/user-invitations/${invitationId}`, {
      method: "DELETE",
    });
  }

  /**
   * Check if current user has specific role
   */
  hasRole(user: User | null, role: string | string[]): boolean {
    if (!user) return false;

    if (Array.isArray(role)) {
      return role.includes(user.role);
    }

    return user.role === role;
  }

  /**
   * Check if current user has any of the specified roles
   */
  hasAnyRole(user: User | null, roles: string[]): boolean {
    if (!user) return false;
    return roles.includes(user.role);
  }

  /**
   * Check if user is admin
   */
  isAdmin(user: User | null): boolean {
    return this.hasRole(user, "JTB_ADMIN");
  }

  /**
   * Check if user is finance officer
   */
  isFinanceOfficer(user: User | null): boolean {
    return this.hasRole(user, "FINANCE_OFFICER");
  }

  /**
   * Check if user is senior finance officer
   */
  isSeniorFinanceOfficer(user: User | null): boolean {
    return this.hasRole(user, "SENIOR_FINANCE_OFFICER");
  }

  /**
   * Check if user is payer
   */
  isPayer(user: User | null): boolean {
    return this.hasRole(user, "PAYER");
  }

  /**
   * Check if user can manage payments
   */
  canManagePayments(user: User | null): boolean {
    return this.hasAnyRole(user, [
      "ADMIN",
      "FINANCE_OFFICER",
      "SENIOR_FINANCE_OFFICER",
    ]);
  }

  /**
   * Check if user can approve payments
   */
  canApprovePayments(user: User | null): boolean {
    return this.hasAnyRole(user, ["ADMIN", "SENIOR_FINANCE_OFFICER"]);
  }

  /**
   * Check if user can acknowledge payments
   */
  canAcknowledgePayments(user: User | null): boolean {
    return this.hasAnyRole(user, ["JTB_ADMIN", "FINANCE_OFFICER"]);
  }

  /**
   * Get user display name
   */
  getUserDisplayName(user: User | null): string {
    if (!user) return "Unknown User";

    if (user.firstName && user.lastName) {
      return `${user.firstName} ${user.lastName}`;
    }

    if (user.firstName) {
      return user.firstName;
    }

    return user.email || "Unknown User";
  }

  /**
   * Get role display name
   */
  getRoleDisplayName(role: string): string {
    const roleNames: Record<string, string> = {
      JTB_ADMIN: "Administrator",
      FINANCE_OFFICER: "Finance Officer",
      SENIOR_FINANCE_OFFICER: "Senior Finance Officer",
      PAYER: "Payer",
    };

    return roleNames[role] || role;
  }

  /**
   * Get available roles for invitation
   */
  getAvailableRoles(): Array<{ value: string; label: string }> {
    return [
      { value: "FINANCE_OFFICER", label: "Finance Officer" },
      { value: "SENIOR_FINANCE_OFFICER", label: "Senior Finance Officer" },
      { value: "PAYER", label: "Payer" },
    ];
  }

  /**
   * Validate email format
   */
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Check if invitation is expired
   */
  isInvitationExpired(invitation: UserInvitation): boolean {
    const expiryDate = new Date(invitation.expiryDate);
    const now = new Date();
    return now > expiryDate;
  }

  /**
   * Format invitation expiry date
   */
  formatExpiryDate(invitation: UserInvitation): string {
    const expiryDate = new Date(invitation.expiryDate);
    return expiryDate.toLocaleDateString();
  }
}

// Export singleton instance
export const authService = new AuthService();
