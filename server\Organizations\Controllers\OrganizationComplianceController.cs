using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Final_E_Receipt.Organizations.Services;

namespace Final_E_Receipt.Organizations.Controllers
{
    [ApiController]
    [Route("api/organizations/{organizationId}/compliance")]
    [Authorize]
    public class OrganizationComplianceController : ControllerBase
    {
        private readonly OrganizationComplianceService _complianceService;
        private readonly ILogger<OrganizationComplianceController> _logger;

        public OrganizationComplianceController(
            OrganizationComplianceService complianceService,
            ILogger<OrganizationComplianceController> logger)
        {
            _complianceService = complianceService;
            _logger = logger;
        }

        [HttpGet("status")]
        public async Task<IActionResult> GetOrganizationComplianceStatus(string organizationId)
        {
            try
            {
                // Check if user has access to this organization's data
                var userOrganizationId = User.FindFirst("OrganizationId")?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                if (userRole != "JTB_ADMIN" && userRole != "FINANCE_OFFICER" && userRole != "SENIOR_FINANCE_OFFICER" &&
                    userOrganizationId != organizationId)
                {
                    return Forbid();
                }

                var status = await _complianceService.GetOrganizationComplianceStatus(organizationId);
                
                if (status == null)
                    return NotFound(new { message = "Organization not found" });

                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving compliance status for organization {OrganizationId}", organizationId);
                return StatusCode(500, new { message = "An error occurred while retrieving compliance status" });
            }
        }

        [HttpGet("summary")]
        public async Task<IActionResult> GetOrganizationComplianceSummary(string organizationId)
        {
            try
            {
                // Check access permissions
                var userOrganizationId = User.FindFirst("OrganizationId")?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                if (userRole != "JTB_ADMIN" && userRole != "FINANCE_OFFICER" && userRole != "SENIOR_FINANCE_OFFICER" &&
                    userOrganizationId != organizationId)
                {
                    return Forbid();
                }

                var summary = await _complianceService.GetOrganizationComplianceSummary(organizationId);
                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving compliance summary for organization {OrganizationId}", organizationId);
                return StatusCode(500, new { message = "An error occurred while retrieving compliance summary" });
            }
        }

        [HttpGet("requirements")]
        public async Task<IActionResult> GetCertificateRequirements(string organizationId)
        {
            try
            {
                // Check access permissions
                var userOrganizationId = User.FindFirst("OrganizationId")?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                if (userRole != "JTB_ADMIN" && userRole != "FINANCE_OFFICER" && userRole != "SENIOR_FINANCE_OFFICER" &&
                    userOrganizationId != organizationId)
                {
                    return Forbid();
                }

                var requirements = await _complianceService.GetCertificateRequirements(organizationId);
                return Ok(requirements);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving certificate requirements for organization {OrganizationId}", organizationId);
                return StatusCode(500, new { message = "An error occurred while retrieving certificate requirements" });
            }
        }
    }

    [ApiController]
    [Route("api/compliance")]
    [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER,JTB_ADMIN")]
    public class ComplianceOverviewController : ControllerBase
    {
        private readonly OrganizationComplianceService _complianceService;
        private readonly ILogger<ComplianceOverviewController> _logger;

        public ComplianceOverviewController(
            OrganizationComplianceService complianceService,
            ILogger<ComplianceOverviewController> logger)
        {
            _complianceService = complianceService;
            _logger = logger;
        }

        [HttpGet("organizations")]
        public async Task<IActionResult> GetAllOrganizationsComplianceStatus()
        {
            try
            {
                var statuses = await _complianceService.GetAllOrganizationsComplianceStatus();
                return Ok(statuses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all organizations compliance status");
                return StatusCode(500, new { message = "An error occurred while retrieving compliance status" });
            }
        }

        [HttpGet("alerts")]
        public async Task<IActionResult> GetComplianceAlerts()
        {
            try
            {
                var alerts = await _complianceService.GetOrganizationComplianceAlerts();
                return Ok(alerts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving compliance alerts");
                return StatusCode(500, new { message = "An error occurred while retrieving compliance alerts" });
            }
        }

        [HttpPost("organizations/{organizationId}/update-status")]
        public async Task<IActionResult> UpdateOrganizationComplianceStatus(string organizationId, [FromBody] UpdateComplianceStatusRequest request)
        {
            try
            {
                var success = await _complianceService.UpdateOrganizationComplianceStatus(
                    organizationId, 
                    request.CertificateId, 
                    request.Action);

                if (!success)
                    return BadRequest(new { message = "Failed to update compliance status" });

                return Ok(new { message = "Compliance status updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating compliance status for organization {OrganizationId}", organizationId);
                return StatusCode(500, new { message = "An error occurred while updating compliance status" });
            }
        }
    }

    public class UpdateComplianceStatusRequest
    {
        public string CertificateId { get; set; }
        public string Action { get; set; }
    }
}
