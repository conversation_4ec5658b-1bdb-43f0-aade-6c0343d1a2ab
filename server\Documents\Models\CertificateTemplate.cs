using System;
using System.Collections.Generic;

namespace Final_E_Receipt.Documents.Models
{
    public class CertificateTemplate
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string OrganizationId { get; set; }
        public string CertificateType { get; set; }
        public string TemplateFileName { get; set; }
        public string PreviewImagePath { get; set; }
        public PageOrientation Orientation { get; set; }
        public bool IsDefault { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        
        // Template styling
        public string FontFamily { get; set; } = "Arial";
        public float FontSize { get; set; } = 12f;
        public string PrimaryColor { get; set; } = "#000000";
        public string SecondaryColor { get; set; } = "#666666";
        
        // Text positions as JSON
        public string TextPositionsJson { get; set; }
    }

    public class AvailableTemplate
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string PreviewImageUrl { get; set; }
        public PageOrientation Orientation { get; set; }
        public bool IsDefault { get; set; }
        public string OrganizationName { get; set; }
        public string CertificateTypeName { get; set; }
    }

    public class TemplateSelectionRequest
    {
        public string OrganizationId { get; set; }
        public string CertificateType { get; set; }
        public string SelectedTemplateId { get; set; }
        public PageOrientation? PreferredOrientation { get; set; }
    }
}
