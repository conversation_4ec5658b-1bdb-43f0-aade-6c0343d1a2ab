import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useNotificationsApi } from '../hooks/api';
import { useAuth } from '../hooks/useAuth';
import { notificationService } from '../services/notificationService';
import type { Notification, NotificationStats } from '../hooks/api/useNotifications';

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  stats: NotificationStats | null;
  loading: boolean;
  refreshNotifications: () => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  archiveNotification: (id: string) => Promise<void>;
  // Notification service methods
  notifyPaymentDue: typeof notificationService.notifyPaymentDue;
  notifyPaymentOverdue: typeof notificationService.notifyPaymentOverdue;
  notifyPaymentApproved: typeof notificationService.notifyPaymentApproved;
  notifyPaymentScheduleCreated: typeof notificationService.notifyPaymentScheduleCreated;
  notifyPaymentScheduleUpdated: typeof notificationService.notifyPaymentScheduleUpdated;
  notifyPaymentScheduleDeleted: typeof notificationService.notifyPaymentScheduleDeleted;
  notifyBulkSchedulesImported: typeof notificationService.notifyBulkSchedulesImported;
  notifyReceiptGenerated: typeof notificationService.notifyReceiptGenerated;
  notifyCertificateExpiring: typeof notificationService.notifyCertificateExpiring;
  notifySystemAlert: typeof notificationService.notifySystemAlert;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const notificationsApi = useNotificationsApi();
  const {
    getUserNotifications,
    getUnreadNotifications,
    getNotificationStats,
    markAsRead: apiMarkAsRead,
    markMultipleAsRead,
    deleteNotification: apiDeleteNotification,
    archiveNotification: apiArchiveNotification,
    loading,
  } = notificationsApi;

  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [stats, setStats] = useState<NotificationStats | null>(null);

  // Initialize notification service with API
  useEffect(() => {
    notificationService.initialize(notificationsApi);
  }, [notificationsApi]);

  // Load notifications when user changes
  useEffect(() => {
    if (user?.id) {
      refreshNotifications();
      
      // Set up periodic refresh (every 2 minutes)
      const interval = setInterval(refreshNotifications, 120000);
      return () => clearInterval(interval);
    }
  }, [user?.id]);

  const refreshNotifications = async () => {
    if (!user?.id) return;

    try {
      // Load recent notifications (last 50)
      const notificationsResult = await getUserNotifications(user.id, {
        status: 'UNREAD',
      });
      
      if (notificationsResult) {
        setNotifications(notificationsResult);
      }

      // Load stats
      const statsResult = await getNotificationStats(user.id);
      if (statsResult) {
        setStats(statsResult);
        setUnreadCount(statsResult.unreadCount);
      }
    } catch (error) {
      console.error('Failed to refresh notifications:', error);
    }
  };

  const markAsRead = async (id: string) => {
    try {
      await apiMarkAsRead(id);
      
      // Update local state
      setNotifications(prev => prev.map(n => 
        n.id === id ? { ...n, status: 'READ' as const, readDate: new Date().toISOString() } : n
      ));
      
      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
      
      // Update stats
      if (stats) {
        setStats(prev => prev ? {
          ...prev,
          unreadCount: Math.max(0, prev.unreadCount - 1),
          readCount: prev.readCount + 1,
        } : null);
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    if (!user?.id || unreadCount === 0) return;

    try {
      const unreadIds = notifications
        .filter(n => n.status === 'UNREAD')
        .map(n => n.id);
      
      if (unreadIds.length > 0) {
        await markMultipleAsRead(unreadIds);
        
        // Update local state
        setNotifications(prev => prev.map(n => 
          n.status === 'UNREAD' ? { ...n, status: 'read' as const, readDate: new Date().toISOString() } : n
        ));
        
        // Update counts
        setUnreadCount(0);
        if (stats) {
          setStats(prev => prev ? {
            ...prev,
            unreadCount: 0,
            readCount: prev.readCount + unreadIds.length,
          } : null);
        }
      }
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  const deleteNotification = async (id: string) => {
    try {
      await apiDeleteNotification(id);
      
      // Update local state
      const deletedNotification = notifications.find(n => n.id === id);
      setNotifications(prev => prev.filter(n => n.id !== id));
      
      // Update counts if it was unread
      if (deletedNotification?.status === 'UNREAD') {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
      
      // Update stats
      if (stats) {
        setStats(prev => prev ? {
          ...prev,
          totalNotifications: Math.max(0, prev.totalNotifications - 1),
          unreadCount: deletedNotification?.status === 'UNREAD' ? Math.max(0, prev.unreadCount - 1) : prev.unreadCount,
          readCount: deletedNotification?.status === 'read' ? Math.max(0, prev.readCount - 1) : prev.readCount,
        } : null);
      }
    } catch (error) {
      console.error('Failed to delete notification:', error);
    }
  };

  const archiveNotification = async (id: string) => {
    try {
      await apiArchiveNotification(id);
      
      // Update local state
      const archivedNotification = notifications.find(n => n.id === id);
      setNotifications(prev => prev.filter(n => n.id !== id));
      
      // Update counts if it was unread
      if (archivedNotification?.status === 'UNREAD') {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
      
      // Update stats
      if (stats) {
        setStats(prev => prev ? {
          ...prev,
          archivedCount: prev.archivedCount + 1,
          unreadCount: archivedNotification?.status === 'UNREAD' ? Math.max(0, prev.unreadCount - 1) : prev.unreadCount,
          readCount: archivedNotification?.status === 'read' ? Math.max(0, prev.readCount - 1) : prev.readCount,
        } : null);
      }
    } catch (error) {
      console.error('Failed to archive notification:', error);
    }
  };

  const contextValue: NotificationContextType = {
    notifications,
    unreadCount,
    stats,
    loading,
    refreshNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    archiveNotification,
    // Notification service methods
    notifyPaymentDue: notificationService.notifyPaymentDue.bind(notificationService),
    notifyPaymentOverdue: notificationService.notifyPaymentOverdue.bind(notificationService),
    notifyPaymentApproved: notificationService.notifyPaymentApproved.bind(notificationService),
    notifyPaymentScheduleCreated: notificationService.notifyPaymentScheduleCreated.bind(notificationService),
    notifyPaymentScheduleUpdated: notificationService.notifyPaymentScheduleUpdated.bind(notificationService),
    notifyPaymentScheduleDeleted: notificationService.notifyPaymentScheduleDeleted.bind(notificationService),
    notifyBulkSchedulesImported: notificationService.notifyBulkSchedulesImported.bind(notificationService),
    notifyReceiptGenerated: notificationService.notifyReceiptGenerated.bind(notificationService),
    notifyCertificateExpiring: notificationService.notifyCertificateExpiring.bind(notificationService),
    notifySystemAlert: notificationService.notifySystemAlert.bind(notificationService),
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export default NotificationContext;
