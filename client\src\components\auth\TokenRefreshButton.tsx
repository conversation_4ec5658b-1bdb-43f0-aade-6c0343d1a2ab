import React, { useState } from 'react';
import { RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';

interface TokenRefreshButtonProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
}

const TokenRefreshButton: React.FC<TokenRefreshButtonProps> = ({
  className = '',
  size = 'md',
  showText = true,
}) => {
  const { refreshToken } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [refreshStatus, setRefreshStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      setRefreshStatus('idle');
      
      const success = await refreshToken();
      
      if (success) {
        setRefreshStatus('success');
        setTimeout(() => setRefreshStatus('idle'), 3000);
      } else {
        setRefreshStatus('error');
        setTimeout(() => setRefreshStatus('idle'), 5000);
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      setRefreshStatus('error');
      setTimeout(() => setRefreshStatus('idle'), 5000);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs';
      case 'lg':
        return 'px-4 py-2 text-base';
      default:
        return 'px-3 py-1.5 text-sm';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 12;
      case 'lg':
        return 18;
      default:
        return 14;
    }
  };

  const getStatusIcon = () => {
    if (isRefreshing) {
      return <RefreshCw size={getIconSize()} className="animate-spin" />;
    }
    
    switch (refreshStatus) {
      case 'success':
        return <CheckCircle size={getIconSize()} className="text-green-600" />;
      case 'error':
        return <AlertCircle size={getIconSize()} className="text-red-600" />;
      default:
        return <RefreshCw size={getIconSize()} />;
    }
  };

  const getStatusText = () => {
    if (isRefreshing) {
      return 'Refreshing...';
    }
    
    switch (refreshStatus) {
      case 'success':
        return 'Refreshed';
      case 'error':
        return 'Failed';
      default:
        return 'Refresh Token';
    }
  };

  const getButtonClasses = () => {
    const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
    
    let colorClasses = 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500';
    
    if (refreshStatus === 'success') {
      colorClasses = 'bg-green-100 text-green-700 hover:bg-green-200 focus:ring-green-500';
    } else if (refreshStatus === 'error') {
      colorClasses = 'bg-red-100 text-red-700 hover:bg-red-200 focus:ring-red-500';
    }
    
    return `${baseClasses} ${getSizeClasses()} ${colorClasses} ${className}`;
  };

  return (
    <button
      onClick={handleRefresh}
      disabled={isRefreshing}
      className={getButtonClasses()}
      title="Refresh authentication token"
    >
      <span className="mr-2">
        {getStatusIcon()}
      </span>
      {showText && (
        <span>{getStatusText()}</span>
      )}
    </button>
  );
};

export default TokenRefreshButton;
