import { useState, useCallback } from 'react';
import { apiService } from '../../services/apiService';

// Types for Notifications API
export interface Notification {
  id: string;
  userId: string;
  organizationId?: string;
  type: 'PAYMENT_DUE' | 'PAYMENT_OVERDUE' | 'PAYMENT_SCHEDULE_CREATED' | 'PAYMENT_SCHEDULE_UPDATED' | 'PAYMENT_SCHEDULE_DELETED' | 'BULK_SCHEDULES_IMPORTED' | 'CERTIFICATE_EXPIRING' | 'CERTIFICATE_EXPIRED' | 'SYSTEM_ALERT' | 'CUSTOM';
  title: string;
  message: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status: 'UNREAD' | 'READ' | 'ARCHIVED';
  relatedEntityId?: string; // Payment ID, Certificate ID, etc.
  relatedEntityType?: string; // 'PAYMENT', 'CERTIFICATE', etc.
  scheduledDate?: string;
  sentDate?: string;
  readDate?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface CreateNotificationDTO {
  userId?: string;
  organizationId?: string;
  type: string;
  title: string;
  message: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  relatedEntityId?: string;
  relatedEntityType?: string;
  scheduledDate?: string;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  type: string;
  subject: string;
  bodyTemplate: string;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
}

export interface CreateNotificationTemplateDTO {
  name: string;
  type: string;
  subject: string;
  bodyTemplate: string;
}

export interface NotificationFilters {
  userId?: string;
  organizationId?: string;
  type?: string;
  status?: string;
  priority?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface NotificationStats {
  totalNotifications: number;
  unreadCount: number;
  readCount: number;
  archivedCount: number;
  byPriority: {
    urgent: number;
    high: number;
    medium: number;
    low: number;
  };
  byType: Record<string, number>;
}

// Notifications API Hooks
export const useNotificationsApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Notification CRUD endpoints
  const getAllNotifications = useCallback((filters?: NotificationFilters) => 
    handleApiCall(() => apiService.get<Notification[]>('/Notification', filters)), [handleApiCall]);

  const getNotificationById = useCallback((id: string) => 
    handleApiCall(() => apiService.get<Notification>(`/Notification/${id}`)), [handleApiCall]);

  const createNotification = useCallback((data: CreateNotificationDTO) => 
    handleApiCall(() => apiService.post<Notification>('/Notification', data)), [handleApiCall]);

  const updateNotification = useCallback((id: string, data: Partial<Notification>) => 
    handleApiCall(() => apiService.put<Notification>(`/Notification/${id}`, data)), [handleApiCall]);

  const deleteNotification = useCallback((id: string) => 
    handleApiCall(() => apiService.delete(`/Notification/${id}`)), [handleApiCall]);

  // Notification status management
  const markAsRead = useCallback((id: string) => 
    handleApiCall(() => apiService.put(`/Notification/${id}/read`)), [handleApiCall]);

  const markAsUnread = useCallback((id: string) => 
    handleApiCall(() => apiService.put(`/Notification/${id}/unread`)), [handleApiCall]);

  const archiveNotification = useCallback((id: string) => 
    handleApiCall(() => apiService.put(`/Notification/${id}/archive`)), [handleApiCall]);

  const unarchiveNotification = useCallback((id: string) => 
    handleApiCall(() => apiService.put(`/Notification/${id}/unarchive`)), [handleApiCall]);

  // Bulk operations
  const markMultipleAsRead = useCallback((ids: string[]) =>
    handleApiCall(() => apiService.post('/notification/bulk-action', { notificationIds: ids, action: 'READ' })), [handleApiCall]);

  const archiveMultiple = useCallback((ids: string[]) =>
    handleApiCall(() => apiService.post('/notification/bulk-action', { notificationIds: ids, action: 'ARCHIVE' })), [handleApiCall]);

  const deleteMultiple = useCallback((ids: string[]) =>
    handleApiCall(() => apiService.post('/notification/bulk-action', { notificationIds: ids, action: 'DELETE' })), [handleApiCall]);

  // User-specific notifications
  const getUserNotifications = useCallback((userId: string, filters?: NotificationFilters) =>
    handleApiCall(() => apiService.get<Notification[]>(`/notification/user/${userId}`, filters)), [handleApiCall]);

  const getUnreadNotifications = useCallback((userId: string, limit: number = 10) =>
    handleApiCall(() => apiService.get<Notification[]>(`/notification/user/${userId}/unread?limit=${limit}`)), [handleApiCall]);

  const getNotificationStats = useCallback((userId: string) =>
    handleApiCall(() => apiService.get<NotificationStats>(`/notification/user/${userId}/stats`)), [handleApiCall]);

  // Organization notifications
  const getOrganizationNotifications = useCallback((organizationId: string, filters?: NotificationFilters) => 
    handleApiCall(() => apiService.get<Notification[]>(`/Notification/organization/${organizationId}`, filters)), [handleApiCall]);

  // System notifications
  const sendSystemNotification = useCallback((data: CreateNotificationDTO) => 
    handleApiCall(() => apiService.post('/Notification/system', data)), [handleApiCall]);

  const broadcastNotification = useCallback((data: CreateNotificationDTO & { userIds?: string[]; organizationIds?: string[] }) =>
    handleApiCall(() => apiService.post('/Notification/broadcast', data)), [handleApiCall]);

  // Bulk notification actions
  const bulkMarkAsRead = useCallback((notificationIds: string[]) =>
    handleApiCall(() => apiService.post('/Notification/bulk-action', {
      notificationIds,
      action: 'READ'
    })), [handleApiCall]);

  const bulkArchive = useCallback((notificationIds: string[]) =>
    handleApiCall(() => apiService.post('/Notification/bulk-action', {
      notificationIds,
      action: 'ARCHIVE'
    })), [handleApiCall]);

  const bulkDelete = useCallback((notificationIds: string[]) =>
    handleApiCall(() => apiService.post('/Notification/bulk-action', {
      notificationIds,
      action: 'DELETE'
    })), [handleApiCall]);

  const bulkAction = useCallback((notificationIds: string[], action: 'READ' | 'ARCHIVE' | 'DELETE') =>
    handleApiCall(() => apiService.post('/Notification/bulk-action', {
      notificationIds,
      action: action.toUpperCase()
    })), [handleApiCall]);

  return {
    loading,
    error,
    getAllNotifications,
    getNotificationById,
    createNotification,
    updateNotification,
    deleteNotification,
    markAsRead,
    markAsUnread,
    archiveNotification,
    unarchiveNotification,
    markMultipleAsRead,
    archiveMultiple,
    deleteMultiple,
    getUserNotifications,
    getUnreadNotifications,
    getNotificationStats,
    getOrganizationNotifications,
    sendSystemNotification,
    broadcastNotification,
    bulkMarkAsRead,
    bulkArchive,
    bulkDelete,
    bulkAction,
  };
};

// Notification Templates API Hooks
export const useNotificationTemplatesApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Notification Template endpoints
  const getAllNotificationTemplates = useCallback(() => 
    handleApiCall(() => apiService.get<NotificationTemplate[]>('/NotificationTemplate')), [handleApiCall]);

  const getNotificationTemplateById = useCallback((id: string) => 
    handleApiCall(() => apiService.get<NotificationTemplate>(`/NotificationTemplate/${id}`)), [handleApiCall]);

  const createNotificationTemplate = useCallback((data: CreateNotificationTemplateDTO) => 
    handleApiCall(() => apiService.post<NotificationTemplate>('/NotificationTemplate', data)), [handleApiCall]);

  const updateNotificationTemplate = useCallback((id: string, data: Partial<NotificationTemplate>) => 
    handleApiCall(() => apiService.put<NotificationTemplate>(`/NotificationTemplate/${id}`, data)), [handleApiCall]);

  const deleteNotificationTemplate = useCallback((id: string) => 
    handleApiCall(() => apiService.delete(`/NotificationTemplate/${id}`)), [handleApiCall]);

  const getActiveNotificationTemplates = useCallback(() => 
    handleApiCall(() => apiService.get<NotificationTemplate[]>('/NotificationTemplate/active')), [handleApiCall]);

  const getNotificationTemplatesByType = useCallback((type: string) => 
    handleApiCall(() => apiService.get<NotificationTemplate[]>(`/NotificationTemplate/type/${type}`)), [handleApiCall]);

  return {
    loading,
    error,
    getAllNotificationTemplates,
    getNotificationTemplateById,
    createNotificationTemplate,
    updateNotificationTemplate,
    deleteNotificationTemplate,
    getActiveNotificationTemplates,
    getNotificationTemplatesByType,
  };
};
