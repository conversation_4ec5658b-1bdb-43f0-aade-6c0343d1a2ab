using Microsoft.Extensions.DependencyInjection;
using Final_E_Receipt.Notifications.Services;

namespace Final_E_Receipt.Notifications.Configuration
{
    public static class NotificationServiceConfiguration
    {
        public static IServiceCollection AddNotificationServices(this IServiceCollection services)
        {
            // Register centralized email service
            services.AddScoped<CentralizedEmailService>();

            // Register core email infrastructure services
            services.AddScoped<EmailConfigurationService>();
            services.AddScoped<EmailTemplateService>();
            services.AddScoped<UserEmailService>();

            // Register notification management (for future enhancement)
            services.AddScoped<NotificationManagementService>();

            return services;
        }

        /// <summary>
        /// Add only the centralized email services (recommended for new projects)
        /// </summary>
        public static IServiceCollection AddCentralizedEmailServices(this IServiceCollection services)
        {
            // Register centralized email service
            services.AddScoped<CentralizedEmailService>();

            // Register core email infrastructure services
            services.AddScoped<EmailConfigurationService>();
            services.AddScoped<EmailTemplateService>();
            services.AddScoped<UserEmailService>();

            return services;
        }
    }
}
