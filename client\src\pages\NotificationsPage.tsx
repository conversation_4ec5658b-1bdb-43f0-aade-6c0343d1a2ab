import React, { useState } from 'react';
import { Bell, Settings, Archive, Trash2 } from 'lucide-react';
import NotificationCenter from '../components/Notifications/NotificationCenter';
import NotificationSettings from '../components/Notifications/NotificationSettings';
import { useAuth } from '../hooks/useAuth';

const NotificationsPage: React.FC = () => {
  const { hasRole } = useAuth();
  const [activeTab, setActiveTab] = useState<'notifications' | 'settings'>('notifications');

  // Check if user has access to notifications
  const canAccessNotifications = hasRole(['JTB_ADMIN', 'FINANCE_OFFICER', 'SENIOR_FINANCE_OFFICER', 'PAYER']);

  if (!canAccessNotifications) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">You don't have permission to access notifications.</p>
        </div>
      </div>
    );
  }

  const tabs = [
    {
      id: 'notifications',
      label: 'Notifications',
      icon: Bell,
      description: 'View and manage your notifications',
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      description: 'Configure notification preferences',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-2">
            <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
              <Bell className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Notifications</h1>
              <p className="text-gray-600">Stay updated with important system events</p>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-md mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-[#2aa45c] text-[#2aa45c]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon size={16} />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Descriptions */}
          <div className="px-6 py-3 bg-gray-50">
            {tabs.map((tab) => (
              activeTab === tab.id && (
                <p key={tab.id} className="text-sm text-gray-600">
                  {tab.description}
                </p>
              )
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-lg shadow-md">
          {activeTab === 'notifications' && (
            <div className="p-6">
              <NotificationCenter 
                isOpen={true} 
                onClose={() => {}} 
                className="relative bg-transparent shadow-none border-none"
              />
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="p-6">
              <NotificationSettings />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationsPage;
