﻿using Final_E_Receipt.Payments.DTOs;
using Final_E_Receipt.Payments.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Final_E_Receipt.Payments.Controllers
{
    [ApiController]
    [Route("api/payment-types")]
    [Authorize(Roles = "FINANCE_OFFICER")]
    public class PaymentTypeController : ControllerBase
    {
        private readonly PaymentTypeService _paymentTypeService;

        public PaymentTypeController(PaymentTypeService paymentTypeService)
        {
            _paymentTypeService = paymentTypeService;
        }

        [HttpPost]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> Create([FromBody] PaymentTypeCreateDto dto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var paymentType = await _paymentTypeService.CreatePaymentType(dto.Name, dto.Description, userId);
            return Ok(paymentType);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(string id)
        {
            var paymentType = await _paymentTypeService.GetPaymentTypeById(id);
            if (paymentType == null)
                return NotFound();

            return Ok(paymentType);
        }

        [HttpGet]
        public async Task<IActionResult> GetAll([FromQuery] bool? isActive = null)
        {
            var types = await _paymentTypeService.GetAllPaymentTypes(isActive);
            return Ok(types);
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> Update(string id, [FromBody] PaymentTypeUpdateDto dto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var updatedType = await _paymentTypeService.UpdatePaymentType(id, dto.Name, dto.Description, userId);

            if (updatedType == null)
                return NotFound();

            return Ok(updatedType);
        }

        [HttpPatch("{id}/status")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> UpdateStatus(string id, [FromBody] PaymentTypeStatusUpdateDto dto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var updatedType = await _paymentTypeService.TogglePaymentTypeStatus(id, dto.IsActive, userId);

            if (updatedType == null)
                return NotFound();

            return Ok(updatedType);
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> Delete(string id)
        {
            var success = await _paymentTypeService.DeletePaymentType(id);
            if (!success)
                return BadRequest("Payment type could not be deleted or is in use");

            return NoContent();
        }

        [HttpGet("summary")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetSummary(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate,
            [FromQuery] string organizationId = null)
        {
            var summary = await _paymentTypeService.GetPaymentTypeSummary(startDate, endDate, organizationId);
            return Ok(summary);
        }
    }

    public class PaymentTypeCreateDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class PaymentTypeUpdateDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class PaymentTypeStatusUpdateDto
    {
        public bool IsActive { get; set; }
    }

}
