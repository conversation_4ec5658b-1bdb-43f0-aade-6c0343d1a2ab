﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;

namespace Final_E_Receipt.Infrastructure.Swagger
{
    public class FileUploadOperationFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            // Check if this is a file upload endpoint
            var hasConsumesAttribute = context.MethodInfo.GetCustomAttributes<Microsoft.AspNetCore.Mvc.ConsumesAttribute>()
                .Any(attr => attr.ContentTypes.Contains("multipart/form-data"));

            if (!hasConsumesAttribute)
                return;

            var parameters = context.MethodInfo.GetParameters();
            var hasFileUpload = parameters.Any(p =>
                p.ParameterType == typeof(IFormFile) ||
                p.ParameterType == typeof(IFormFile[]) ||
                HasIFormFileProperty(p.ParameterType));

            if (hasFileUpload)
            {
                // Clear existing parameters that might cause conflicts
                operation.Parameters?.Clear();

                // Create form data schema
                var properties = new Dictionary<string, OpenApiSchema>();
                var requiredFields = new HashSet<string>();

                foreach (var parameter in parameters)
                {
                    var paramName = parameter.Name ?? "";

                    if (parameter.ParameterType == typeof(IFormFile))
                    {
                        properties[paramName] = new OpenApiSchema
                        {
                            Type = "string",
                            Format = "binary"
                        };

                        // Mark as required if parameter doesn't have default value
                        if (!parameter.HasDefaultValue)
                        {
                            requiredFields.Add(paramName);
                        }
                    }
                    else if (parameter.ParameterType == typeof(IFormFile[]))
                    {
                        properties[paramName] = new OpenApiSchema
                        {
                            Type = "array",
                            Items = new OpenApiSchema
                            {
                                Type = "string",
                                Format = "binary"
                            }
                        };

                        if (!parameter.HasDefaultValue)
                        {
                            requiredFields.Add(paramName);
                        }
                    }
                    else if (HasIFormFileProperty(parameter.ParameterType))
                    {
                        // Handle DTO with IFormFile properties
                        var dtoProperties = GetDtoProperties(parameter.ParameterType);
                        foreach (var prop in dtoProperties)
                        {
                            properties[prop.Key] = prop.Value;
                        }
                    }
                    else if (parameter.ParameterType == typeof(string))
                    {
                        properties[paramName] = new OpenApiSchema
                        {
                            Type = "string"
                        };

                        if (!parameter.HasDefaultValue)
                        {
                            requiredFields.Add(paramName);
                        }
                    }
                    else if (IsNullableType(parameter.ParameterType))
                    {
                        // Handle nullable types (like string?)
                        var underlyingType = Nullable.GetUnderlyingType(parameter.ParameterType) ?? parameter.ParameterType;
                        properties[paramName] = new OpenApiSchema
                        {
                            Type = GetOpenApiType(underlyingType)
                        };

                        // Nullable types are typically optional
                        if (!parameter.HasDefaultValue && !IsReferenceTypeOrNullable(parameter.ParameterType))
                        {
                            requiredFields.Add(paramName);
                        }
                    }
                    else
                    {
                        properties[paramName] = new OpenApiSchema
                        {
                            Type = GetOpenApiType(parameter.ParameterType)
                        };

                        if (!parameter.HasDefaultValue)
                        {
                            requiredFields.Add(paramName);
                        }
                    }
                }

                operation.RequestBody = new OpenApiRequestBody
                {
                    Content = new Dictionary<string, OpenApiMediaType>
                    {
                        ["multipart/form-data"] = new OpenApiMediaType
                        {
                            Schema = new OpenApiSchema
                            {
                                Type = "object",
                                Properties = properties,
                                Required = requiredFields
                            }
                        }
                    }
                };
            }
        }

        private bool HasIFormFileProperty(Type type)
        {
            return type.GetProperties().Any(p =>
                p.PropertyType == typeof(IFormFile) ||
                p.PropertyType == typeof(IFormFile[]));
        }

        private Dictionary<string, OpenApiSchema> GetDtoProperties(Type dtoType)
        {
            var properties = new Dictionary<string, OpenApiSchema>();

            foreach (var prop in dtoType.GetProperties())
            {
                var propName = prop.Name;

                if (prop.PropertyType == typeof(IFormFile))
                {
                    properties[propName] = new OpenApiSchema
                    {
                        Type = "string",
                        Format = "binary"
                    };
                }
                else if (prop.PropertyType == typeof(IFormFile[]))
                {
                    properties[propName] = new OpenApiSchema
                    {
                        Type = "array",
                        Items = new OpenApiSchema
                        {
                            Type = "string",
                            Format = "binary"
                        }
                    };
                }
                else if (prop.PropertyType == typeof(string))
                {
                    properties[propName] = new OpenApiSchema
                    {
                        Type = "string"
                    };
                }
                else
                {
                    properties[propName] = new OpenApiSchema
                    {
                        Type = "string"
                    };
                }
            }

            return properties;
        }

        private static bool IsNullableType(Type type)
        {
            return type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>);
        }

        private static bool IsReferenceTypeOrNullable(Type type)
        {
            return !type.IsValueType || IsNullableType(type);
        }

        private static string GetOpenApiType(Type type)
        {
            if (type == typeof(int) || type == typeof(long) || type == typeof(short))
                return "integer";
            if (type == typeof(float) || type == typeof(double) || type == typeof(decimal))
                return "number";
            if (type == typeof(bool))
                return "boolean";
            return "string";
        }
    }
}
