using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using Final_E_Receipt.Payments.Models;
using Final_E_Receipt.Files.Services;
using Final_E_Receipt.Files.Models;

namespace Final_E_Receipt.Payments.Services
{
    public class PaymentScheduleImportService
    {
        private readonly PaymentScheduleService _scheduleService;
        private readonly FileService _fileService;
        private readonly ILogger<PaymentScheduleImportService> _logger;

        public PaymentScheduleImportService(
            PaymentScheduleService scheduleService,
            FileService fileService,
            ILogger<PaymentScheduleImportService> logger)
        {
            _scheduleService = scheduleService;
            _fileService = fileService;
            _logger = logger;
            
            // Set EPPlus license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public async Task<PaymentScheduleImportResult> ImportFromExcel(
            string paymentProfileId,
            IFormFile excelFile,
            string uploadedBy,
            string organizationId)
        {
            var result = new PaymentScheduleImportResult();
            
            try
            {
                // Upload the Excel file first
                var uploadedFile = await _fileService.UploadFile(
                    excelFile,
                    "PAYMENT_SCHEDULE_IMPORT",
                    paymentProfileId,
                    uploadedBy,
                    organizationId,
                    "Payment schedule bulk import file",
                    "IMPORT"
                );

                result.UploadedFileId = uploadedFile.Id;
                result.FileName = uploadedFile.OriginalFileName;

                // Process the Excel file
                using var stream = excelFile.OpenReadStream();
                using var package = new ExcelPackage(stream);
                
                var worksheet = package.Workbook.Worksheets[0]; // First worksheet
                if (worksheet == null)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "Excel file must contain at least one worksheet";
                    return result;
                }

                // Validate headers
                var validationResult = ValidateExcelHeaders(worksheet);
                if (!validationResult.IsValid)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = validationResult.ErrorMessage;
                    return result;
                }

                // Parse rows
                var schedules = ParseExcelRows(worksheet, paymentProfileId, uploadedBy);
                result.TotalRows = schedules.Count;

                // Import schedules
                foreach (var schedule in schedules)
                {
                    try
                    {
                        var createdSchedule = await _scheduleService.CreatePaymentSchedule(schedule);
                        if (createdSchedule != null)
                        {
                            result.SuccessfulImports++;
                            result.ImportedSchedules.Add(createdSchedule);
                        }
                        else
                        {
                            result.FailedImports++;
                            result.Errors.Add($"Row {result.SuccessfulImports + result.FailedImports + 1}: Failed to create payment schedule");
                        }
                    }
                    catch (Exception ex)
                    {
                        result.FailedImports++;
                        result.Errors.Add($"Row {result.SuccessfulImports + result.FailedImports + 1}: {ex.Message}");
                        _logger.LogError(ex, "Error importing payment schedule row");
                    }
                }

                result.IsSuccess = result.SuccessfulImports > 0;
                
                _logger.LogInformation(
                    "Excel import completed. File: {FileName}, Total: {Total}, Success: {Success}, Failed: {Failed}",
                    result.FileName, result.TotalRows, result.SuccessfulImports, result.FailedImports);

                return result;
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"Error processing Excel file: {ex.Message}";
                _logger.LogError(ex, "Error during Excel import");
                return result;
            }
        }

        private ExcelValidationResult ValidateExcelHeaders(ExcelWorksheet worksheet)
        {
            var requiredHeaders = new[]
            {
                "OrganizationId",
                "OrganizationName", 
                "Amount",
                "DueDate"
            };

            var optionalHeaders = new[]
            {
                "Currency",
                "Description"
            };

            // Check if headers exist in first row
            for (int col = 1; col <= requiredHeaders.Length; col++)
            {
                var headerValue = worksheet.Cells[1, col].Value?.ToString()?.Trim();
                if (string.IsNullOrEmpty(headerValue) || !Array.Exists(requiredHeaders, h => h.Equals(headerValue, StringComparison.OrdinalIgnoreCase)))
                {
                    return new ExcelValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = $"Invalid or missing header in column {col}. Expected headers: {string.Join(", ", requiredHeaders)}"
                    };
                }
            }

            return new ExcelValidationResult { IsValid = true };
        }

        private List<PaymentSchedule> ParseExcelRows(ExcelWorksheet worksheet, string paymentProfileId, string createdBy)
        {
            var schedules = new List<PaymentSchedule>();
            var rowCount = worksheet.Dimension.Rows;

            for (int row = 2; row <= rowCount; row++) // Skip header row
            {
                try
                {
                    var organizationId = worksheet.Cells[row, 1].Value?.ToString()?.Trim();
                    var organizationName = worksheet.Cells[row, 2].Value?.ToString()?.Trim();
                    var amountText = worksheet.Cells[row, 3].Value?.ToString()?.Trim();
                    var dueDateText = worksheet.Cells[row, 4].Value?.ToString()?.Trim();
                    var currency = worksheet.Cells[row, 5].Value?.ToString()?.Trim() ?? "NGN";
                    var description = worksheet.Cells[row, 6].Value?.ToString()?.Trim();

                    // Skip empty rows
                    if (string.IsNullOrEmpty(organizationId) && string.IsNullOrEmpty(amountText))
                        continue;

                    // Validate required fields
                    if (string.IsNullOrEmpty(organizationId))
                        throw new ArgumentException("OrganizationId is required");

                    if (!decimal.TryParse(amountText, out var amount))
                        throw new ArgumentException($"Invalid amount: {amountText}");

                    if (!DateTime.TryParse(dueDateText, out var dueDate))
                        throw new ArgumentException($"Invalid due date: {dueDateText}");

                    var schedule = new PaymentSchedule
                    {
                        Id = Guid.NewGuid().ToString(),
                        PaymentProfileId = paymentProfileId,
                        OrganizationId = organizationId,
                        Amount = amount,
                        Currency = currency,
                        DueDate = dueDate,
                        Status = "PENDING",
                        CreatedAt = DateTime.Now,
                        CreatedBy = createdBy,
                        OrganizationName = organizationName // For display purposes
                    };

                    schedules.Add(schedule);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("Error parsing row {Row}: {Error}", row, ex.Message);
                    // Continue processing other rows
                }
            }

            return schedules;
        }

        public async Task<byte[]> GenerateExcelTemplate()
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Payment Schedules");

            // Add headers
            worksheet.Cells[1, 1].Value = "OrganizationId";
            worksheet.Cells[1, 2].Value = "OrganizationName";
            worksheet.Cells[1, 3].Value = "Amount";
            worksheet.Cells[1, 4].Value = "DueDate";
            worksheet.Cells[1, 5].Value = "Currency";
            worksheet.Cells[1, 6].Value = "Description";

            // Add sample data
            worksheet.Cells[2, 1].Value = "ORG001";
            worksheet.Cells[2, 2].Value = "Sample Organization Ltd";
            worksheet.Cells[2, 3].Value = 50000.00;
            worksheet.Cells[2, 4].Value = DateTime.Now.AddDays(30).ToString("yyyy-MM-dd");
            worksheet.Cells[2, 5].Value = "NGN";
            worksheet.Cells[2, 6].Value = "Annual license fee";

            // Format headers
            using (var range = worksheet.Cells[1, 1, 1, 6])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return package.GetAsByteArray();
        }
    }

    public class PaymentScheduleImportResult
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; }
        public string UploadedFileId { get; set; }
        public string FileName { get; set; }
        public int TotalRows { get; set; }
        public int SuccessfulImports { get; set; }
        public int FailedImports { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<PaymentSchedule> ImportedSchedules { get; set; } = new List<PaymentSchedule>();
    }

    public class ExcelValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
    }
}
