using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Final_E_Receipt.Reporting.Models;
using Final_E_Receipt.Compliance.Models;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;

namespace Final_E_Receipt.Reporting.Services
{
    public class ReportExcelService
    {
        private readonly ILogger<ReportExcelService> _logger;

        public ReportExcelService(ILogger<ReportExcelService> logger)
        {
            _logger = logger;
            // Set EPPlus license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        /// <summary>
        /// Generates Excel report for compliance dashboard
        /// </summary>
        public async Task<byte[]> GenerateComplianceDashboardExcel(ComplianceDashboard dashboard)
        {
            try
            {
                using var package = new ExcelPackage();
                
                // Summary Sheet
                var summarySheet = package.Workbook.Worksheets.Add("Dashboard Summary");
                await CreateDashboardSummarySheet(summarySheet, dashboard);
                
                // Certificate Types Sheet
                var typesSheet = package.Workbook.Worksheets.Add("Certificate Types");
                await CreateCertificateTypesSheet(typesSheet, dashboard.CertificateTypeDistribution);
                
                // Monthly Issuance Sheet
                var monthlySheet = package.Workbook.Worksheets.Add("Monthly Issuance");
                await CreateMonthlyIssuanceSheet(monthlySheet, dashboard.MonthlyIssuanceStats);
                
                // Expiring Certificates Sheet
                var expiringSheet = package.Workbook.Worksheets.Add("Expiring Certificates");
                await CreateExpiringCertificatesSheet(expiringSheet, dashboard.TopExpiringCertificates);

                return await Task.FromResult(package.GetAsByteArray());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating compliance dashboard Excel report");
                throw;
            }
        }

        /// <summary>
        /// Generates Excel report for organization compliance
        /// </summary>
        public async Task<byte[]> GenerateOrganizationComplianceExcel(ComplianceReport report)
        {
            try
            {
                using var package = new ExcelPackage();
                
                // Organization Summary Sheet
                var summarySheet = package.Workbook.Worksheets.Add("Organization Summary");
                await CreateOrganizationSummarySheet(summarySheet, report);
                
                // Certificates List Sheet
                var certificatesSheet = package.Workbook.Worksheets.Add("All Certificates");
                await CreateCertificatesListSheet(certificatesSheet, report.RecentCertificates);
                
                // Expiring Certificates Sheet
                var expiringSheet = package.Workbook.Worksheets.Add("Expiring Certificates");
                await CreateExpiringCertificatesSheet(expiringSheet, report.ExpiringCertificates);

                return await Task.FromResult(package.GetAsByteArray());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating organization compliance Excel report");
                throw;
            }
        }

        /// <summary>
        /// Generates Excel report for expiring certificates
        /// </summary>
        public async Task<byte[]> GenerateExpiringCertificatesExcel(ExpiringCertificatesReport report)
        {
            try
            {
                using var package = new ExcelPackage();
                
                // Expiring Certificates Sheet
                var expiringSheet = package.Workbook.Worksheets.Add("Expiring Certificates");
                await CreateExpiringCertificatesSheet(expiringSheet, report.ExpiringCertificates);
                
                // Summary by Type Sheet
                var typesSheet = package.Workbook.Worksheets.Add("Summary by Type");
                await CreateCertificateTypesSheet(typesSheet, report.ExpiringByType);
                
                // Summary by Organization Sheet
                var orgsSheet = package.Workbook.Worksheets.Add("Summary by Organization");
                await CreateOrganizationExpirySheet(orgsSheet, report.ExpiringByOrganization);

                return await Task.FromResult(package.GetAsByteArray());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating expiring certificates Excel report");
                throw;
            }
        }

        // Helper methods for creating sheets
        private async Task CreateDashboardSummarySheet(ExcelWorksheet sheet, ComplianceDashboard dashboard)
        {
            // Header
            sheet.Cells["A1"].Value = "Compliance Dashboard Summary";
            sheet.Cells["A1"].Style.Font.Size = 16;
            sheet.Cells["A1"].Style.Font.Bold = true;
            sheet.Cells["A1:B1"].Merge = true;

            sheet.Cells["A2"].Value = $"Generated: {dashboard.GeneratedDate:yyyy-MM-dd HH:mm}";
            sheet.Cells["A2:B2"].Merge = true;

            // Metrics
            var row = 4;
            sheet.Cells[$"A{row}"].Value = "Metric";
            sheet.Cells[$"B{row}"].Value = "Value";
            ApplyHeaderStyle(sheet.Cells[$"A{row}:B{row}"]);

            var metrics = new Dictionary<string, object>
            {
                { "Total Certificates", dashboard.TotalCertificates },
                { "Active Certificates", dashboard.ActiveCertificates },
                { "Expired Certificates", dashboard.ExpiredCertificates },
                { "Revoked Certificates", dashboard.RevokedCertificates },
                { "Certificates Expiring Soon", dashboard.CertificatesExpiringSoon },
                { "Issued This Month", dashboard.CertificatesIssuedThisMonth },
                { "Issued This Year", dashboard.CertificatesIssuedThisYear },
                { "Organizations with Active Certificates", dashboard.OrganizationsWithActiveCertificates }
            };

            foreach (var metric in metrics)
            {
                row++;
                sheet.Cells[$"A{row}"].Value = metric.Key;
                sheet.Cells[$"B{row}"].Value = metric.Value;
            }

            sheet.Cells[sheet.Dimension.Address].AutoFitColumns();
        }

        private async Task CreateCertificateTypesSheet(ExcelWorksheet sheet, Dictionary<string, int> typeDistribution)
        {
            // Header
            sheet.Cells["A1"].Value = "Certificate Types Distribution";
            sheet.Cells["A1"].Style.Font.Size = 14;
            sheet.Cells["A1"].Style.Font.Bold = true;
            sheet.Cells["A1:B1"].Merge = true;

            // Column headers
            sheet.Cells["A3"].Value = "Certificate Type";
            sheet.Cells["B3"].Value = "Count";
            ApplyHeaderStyle(sheet.Cells["A3:B3"]);

            var row = 3;
            foreach (var type in typeDistribution)
            {
                row++;
                sheet.Cells[$"A{row}"].Value = type.Key;
                sheet.Cells[$"B{row}"].Value = type.Value;
            }

            sheet.Cells[sheet.Dimension.Address].AutoFitColumns();
        }

        private async Task CreateMonthlyIssuanceSheet(ExcelWorksheet sheet, Dictionary<string, int> monthlyStats)
        {
            // Header
            sheet.Cells["A1"].Value = "Monthly Certificate Issuance";
            sheet.Cells["A1"].Style.Font.Size = 14;
            sheet.Cells["A1"].Style.Font.Bold = true;
            sheet.Cells["A1:B1"].Merge = true;

            // Column headers
            sheet.Cells["A3"].Value = "Month";
            sheet.Cells["B3"].Value = "Certificates Issued";
            ApplyHeaderStyle(sheet.Cells["A3:B3"]);

            var row = 3;
            foreach (var month in monthlyStats.OrderBy(kvp => kvp.Key))
            {
                row++;
                sheet.Cells[$"A{row}"].Value = month.Key;
                sheet.Cells[$"B{row}"].Value = month.Value;
            }

            sheet.Cells[sheet.Dimension.Address].AutoFitColumns();
        }

        private async Task CreateExpiringCertificatesSheet(ExcelWorksheet sheet, List<ComplianceCertificate> certificates)
        {
            // Header
            sheet.Cells["A1"].Value = "Expiring Certificates";
            sheet.Cells["A1"].Style.Font.Size = 14;
            sheet.Cells["A1"].Style.Font.Bold = true;
            sheet.Cells["A1:G1"].Merge = true;

            // Column headers
            var headers = new[] { "Certificate Number", "Organization", "Type", "Valid Until", "Days Until Expiry", "Status", "Issued Date" };
            for (int i = 0; i < headers.Length; i++)
            {
                sheet.Cells[3, i + 1].Value = headers[i];
            }
            ApplyHeaderStyle(sheet.Cells["A3:G3"]);

            var row = 3;
            foreach (var cert in certificates.OrderBy(c => c.ValidUntil))
            {
                row++;
                var daysUntilExpiry = (cert.ValidUntil - DateTime.Now).Days;
                
                sheet.Cells[$"A{row}"].Value = cert.CertificateNumber;
                sheet.Cells[$"B{row}"].Value = cert.OrganizationName;
                sheet.Cells[$"C{row}"].Value = cert.CertificateType;
                sheet.Cells[$"D{row}"].Value = cert.ValidUntil.ToString("yyyy-MM-dd");
                sheet.Cells[$"E{row}"].Value = daysUntilExpiry;
                sheet.Cells[$"F{row}"].Value = cert.Status;
                sheet.Cells[$"G{row}"].Value = cert.IssuedDate.ToString("yyyy-MM-dd");

                // Color code based on urgency
                if (daysUntilExpiry <= 7)
                {
                    sheet.Cells[$"A{row}:G{row}"].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    sheet.Cells[$"A{row}:G{row}"].Style.Fill.BackgroundColor.SetColor(Color.LightCoral);
                }
                else if (daysUntilExpiry <= 30)
                {
                    sheet.Cells[$"A{row}:G{row}"].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    sheet.Cells[$"A{row}:G{row}"].Style.Fill.BackgroundColor.SetColor(Color.LightYellow);
                }
            }

            sheet.Cells[sheet.Dimension.Address].AutoFitColumns();
        }

        private async Task CreateOrganizationSummarySheet(ExcelWorksheet sheet, ComplianceReport report)
        {
            // Header
            sheet.Cells["A1"].Value = $"Organization Compliance Report";
            sheet.Cells["A1"].Style.Font.Size = 16;
            sheet.Cells["A1"].Style.Font.Bold = true;
            sheet.Cells["A1:B1"].Merge = true;

            sheet.Cells["A2"].Value = $"Organization ID: {report.OrganizationId}";
            sheet.Cells["A3"].Value = $"Report Date: {report.ReportDate:yyyy-MM-dd HH:mm}";

            // Metrics
            var row = 5;
            sheet.Cells[$"A{row}"].Value = "Metric";
            sheet.Cells[$"B{row}"].Value = "Value";
            ApplyHeaderStyle(sheet.Cells[$"A{row}:B{row}"]);

            var metrics = new Dictionary<string, object>
            {
                { "Total Certificates", report.TotalCertificates },
                { "Active Certificates", report.ActiveCertificates },
                { "Expired Certificates", report.ExpiredCertificates },
                { "Revoked Certificates", report.RevokedCertificates },
                { "Issued This Year", report.CertificatesIssuedThisYear },
                { "Compliance Score", $"{report.ComplianceScore:F1}%" }
            };

            foreach (var metric in metrics)
            {
                row++;
                sheet.Cells[$"A{row}"].Value = metric.Key;
                sheet.Cells[$"B{row}"].Value = metric.Value;
            }

            sheet.Cells[sheet.Dimension.Address].AutoFitColumns();
        }

        private async Task CreateCertificatesListSheet(ExcelWorksheet sheet, IEnumerable<ComplianceCertificate> certificates)
        {
            // Header
            sheet.Cells["A1"].Value = "All Certificates";
            sheet.Cells["A1"].Style.Font.Size = 14;
            sheet.Cells["A1"].Style.Font.Bold = true;
            sheet.Cells["A1:H1"].Merge = true;

            // Column headers
            var headers = new[] { "Certificate Number", "Type", "Status", "Valid From", "Valid Until", "Issued Date", "Amount", "Currency" };
            for (int i = 0; i < headers.Length; i++)
            {
                sheet.Cells[3, i + 1].Value = headers[i];
            }
            ApplyHeaderStyle(sheet.Cells["A3:H3"]);

            var row = 3;
            foreach (var cert in certificates)
            {
                row++;
                sheet.Cells[$"A{row}"].Value = cert.CertificateNumber;
                sheet.Cells[$"B{row}"].Value = cert.CertificateType;
                sheet.Cells[$"C{row}"].Value = cert.Status;
                sheet.Cells[$"D{row}"].Value = cert.ValidFrom.ToString("yyyy-MM-dd");
                sheet.Cells[$"E{row}"].Value = cert.ValidUntil.ToString("yyyy-MM-dd");
                sheet.Cells[$"F{row}"].Value = cert.IssuedDate.ToString("yyyy-MM-dd");
                sheet.Cells[$"G{row}"].Value = cert.TotalAmount;
                sheet.Cells[$"H{row}"].Value = cert.Currency;
            }

            sheet.Cells[sheet.Dimension.Address].AutoFitColumns();
        }

        private async Task CreateOrganizationExpirySheet(ExcelWorksheet sheet, List<OrganizationExpiryInfo> orgExpiryInfo)
        {
            // Header
            sheet.Cells["A1"].Value = "Organizations with Expiring Certificates";
            sheet.Cells["A1"].Style.Font.Size = 14;
            sheet.Cells["A1"].Style.Font.Bold = true;
            sheet.Cells["A1:E1"].Merge = true;

            // Column headers
            var headers = new[] { "Organization", "Expiring Count", "Earliest Expiry", "Latest Expiry", "Organization ID" };
            for (int i = 0; i < headers.Length; i++)
            {
                sheet.Cells[3, i + 1].Value = headers[i];
            }
            ApplyHeaderStyle(sheet.Cells["A3:E3"]);

            var row = 3;
            foreach (var org in orgExpiryInfo)
            {
                row++;
                sheet.Cells[$"A{row}"].Value = org.OrganizationName;
                sheet.Cells[$"B{row}"].Value = org.ExpiringCount;
                sheet.Cells[$"C{row}"].Value = org.EarliestExpiry.ToString("yyyy-MM-dd");
                sheet.Cells[$"D{row}"].Value = org.LatestExpiry.ToString("yyyy-MM-dd");
                sheet.Cells[$"E{row}"].Value = org.OrganizationId;
            }

            sheet.Cells[sheet.Dimension.Address].AutoFitColumns();
        }

        private void ApplyHeaderStyle(ExcelRange range)
        {
            range.Style.Font.Bold = true;
            range.Style.Fill.PatternType = ExcelFillStyle.Solid;
            range.Style.Fill.BackgroundColor.SetColor(Color.LightGray);
            range.Style.Border.BorderAround(ExcelBorderStyle.Thin);
        }
    }
}
