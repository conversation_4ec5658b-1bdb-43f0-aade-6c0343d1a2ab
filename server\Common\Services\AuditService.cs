using Final_E_Receipt.Common.Models;
using Final_E_Receipt.Services;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using System.Text.Json;

namespace Final_E_Receipt.Common.Services
{
    public class AuditService
    {
        private readonly IDatabaseService _dbService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<AuditService> _logger;

        public AuditService(
            IDatabaseService dbService, 
            IHttpContextAccessor httpContextAccessor,
            ILogger<AuditService> logger)
        {
            _dbService = dbService;
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }

        /// <summary>
        /// Log an audit event
        /// </summary>
        public async Task LogAsync(string action, string entityType, string entityId, 
            object? oldValues = null, object? newValues = null, string? additionalDetails = null)
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                var user = httpContext?.User;

                var auditLog = new AuditLog
                {
                    UserId = user?.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "SYSTEM",
                    UserName = GetUserDisplayName(user),
                    UserEmail = user?.FindFirst(ClaimTypes.Email)?.Value ?? "<EMAIL>",
                    Action = action,
                    EntityType = entityType,
                    EntityId = entityId,
                    OldValues = oldValues != null ? JsonSerializer.Serialize(oldValues) : null,
                    NewValues = newValues != null ? JsonSerializer.Serialize(newValues) : null,
                    AdditionalDetails = additionalDetails,
                    IpAddress = GetClientIpAddress(httpContext),
                    UserAgent = httpContext?.Request.Headers["User-Agent"].ToString() ?? "Unknown",
                    OrganizationId = user?.FindFirst("OrganizationId")?.Value ?? "SYSTEM"
                };

                await _dbService.ExecuteAsync("CreateAuditLog", auditLog);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create audit log for action {Action} on {EntityType} {EntityId}", 
                    action, entityType, entityId);
            }
        }

        /// <summary>
        /// Log user login event
        /// </summary>
        public async Task LogLoginAsync(string userId, string userEmail, string userName, bool isSuccessful)
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                
                var auditLog = new AuditLog
                {
                    UserId = userId,
                    UserName = userName,
                    UserEmail = userEmail,
                    Action = isSuccessful ? AuditActions.USER_LOGIN : AuditActions.UNAUTHORIZED_ACCESS_ATTEMPT,
                    EntityType = EntityTypes.USER,
                    EntityId = userId,
                    AdditionalDetails = isSuccessful ? "Login successful" : "Login failed",
                    IpAddress = GetClientIpAddress(httpContext),
                    UserAgent = httpContext?.Request.Headers["User-Agent"].ToString() ?? "Unknown",
                    OrganizationId = "SYSTEM"
                };

                await _dbService.ExecuteAsync("CreateAuditLog", auditLog);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log login event for user {UserId}", userId);
            }
        }

        /// <summary>
        /// Get audit logs with filtering
        /// </summary>
        public async Task<List<AuditLog>> GetAuditLogsAsync(
            string? userId = null,
            string? action = null,
            string? entityType = null,
            string? organizationId = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int pageSize = 50,
            int pageNumber = 1)
        {
            var parameters = new
            {
                UserId = userId,
                Action = action,
                EntityType = entityType,
                OrganizationId = organizationId,
                StartDate = startDate,
                EndDate = endDate,
                PageSize = pageSize,
                Offset = (pageNumber - 1) * pageSize
            };

            var result = await _dbService.QueryAsync<AuditLog>("GetAuditLogs", parameters);
            return result.ToList();
        }

        /// <summary>
        /// Get audit logs for a specific entity
        /// </summary>
        public async Task<List<AuditLog>> GetEntityAuditLogsAsync(string entityType, string entityId)
        {
            var parameters = new
            {
                EntityType = entityType,
                EntityId = entityId
            };

            var result = await _dbService.QueryAsync<AuditLog>("GetEntityAuditLogs", parameters);
            return result.ToList();
        }

        /// <summary>
        /// Get audit statistics
        /// </summary>
        public async Task<object> GetAuditStatsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var parameters = new
            {
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow
            };

            var result = await _dbService.QueryAsync<dynamic>("GetAuditStats", parameters);
            return result;
        }

        private string GetUserDisplayName(ClaimsPrincipal? user)
        {
            if (user == null) return "SYSTEM";

            var firstName = user.FindFirst(ClaimTypes.GivenName)?.Value;
            var lastName = user.FindFirst(ClaimTypes.Surname)?.Value;
            var name = user.FindFirst(ClaimTypes.Name)?.Value;

            if (!string.IsNullOrEmpty(firstName) && !string.IsNullOrEmpty(lastName))
                return $"{firstName} {lastName}";

            return name ?? user.FindFirst(ClaimTypes.Email)?.Value ?? "Unknown User";
        }

        private string GetClientIpAddress(HttpContext? context)
        {
            if (context == null) return "Unknown";

            var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Connection.RemoteIpAddress?.ToString();

            return ipAddress ?? "Unknown";
        }
    }
}
