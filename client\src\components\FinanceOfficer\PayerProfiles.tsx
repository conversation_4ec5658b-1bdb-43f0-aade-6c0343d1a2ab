// import React, { useState } from 'react';
// import { Plus, Search, Eye, Receipt, Filter, X, CreditCard } from 'lucide-react';

// interface PaymentProfile {
//   id: number;
//   name: string;
//   description: string;
// }

// interface PayerProfile {
//   id: number;
//   name: string;
//   email: string;
//   phone: string;
//   address: string;
//   status: string;
//   outstanding: string;
//   paymentProfiles: number[]; // Array of payment profile IDs
// }

// interface ActionButtonProps {
//   children: React.ReactNode;
//   onClick?: () => void;
//   variant?: 'primary' | 'secondary' | 'danger';
//   size?: 'sm' | 'md';
// }

// interface PayerFormData {
//   name: string;
//   address: string;
//   phone: string;
//   email: string;
// }

// const ActionButton: React.FC<ActionButtonProps> = ({ children, onClick, variant = "primary", size = "md" }) => {
//   const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
//   const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
//   const variantClasses = {
//     primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
//     secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
//     danger: "bg-red-500 hover:bg-red-600 text-white"
//   };

//   return (
//     <button
//       onClick={onClick}
//       className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]}`}
//     >
//       {children}
//     </button>
//   );
// };

// const PayerProfiles: React.FC = () => {
//   // Sample payment profiles
//   const paymentProfiles: PaymentProfile[] = [
//     { id: 1, name: "Standard Invoice", description: "Net 30 payment terms" },
//     { id: 2, name: "Quick Pay", description: "Net 15 payment terms with 2% discount" },
//     { id: 3, name: "Immediate Payment", description: "Payment due on receipt" },
//     { id: 4, name: "Extended Terms", description: "Net 60 payment terms for large clients" },
//     { id: 5, name: "Subscription Billing", description: "Monthly recurring payments" }
//   ];

//   const [payerProfiles, setPayerProfiles] = useState<PayerProfile[]>([
//     { id: 1, name: "Acme Corporation", email: "<EMAIL>", phone: "******-0123", address: "123 Business St, New York, NY", status: "Active", outstanding: "₦2,500", paymentProfiles: [1, 2] },
//     { id: 2, name: "TechStart Inc", email: "<EMAIL>", phone: "******-0124", address: "456 Tech Ave, San Francisco, CA", status: "Active", outstanding: "₦1,200", paymentProfiles: [1, 3] },
//     { id: 3, name: "Global Solutions", email: "<EMAIL>", phone: "******-0125", address: "789 Global Blvd, Chicago, IL", status: "Active", outstanding: "₦0", paymentProfiles: [1, 4, 5] }
//   ]);
  
//   const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
//   const [isPaymentProfileModalOpen, setIsPaymentProfileModalOpen] = useState(false);
//   const [selectedPayerId, setSelectedPayerId] = useState<number | null>(null);
//   const [selectedPaymentProfiles, setSelectedPaymentProfiles] = useState<number[]>([]);
//   const [formData, setFormData] = useState<PayerFormData>({
//     name: '',
//     address: '',
//     phone: '',
//     email: '',
//   });

//   const resetForm = () => {
//     setFormData({
//       name: '',
//       address: '',
//       phone: '',
//       email: '',
//     });
//   };

//   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
//     const { name, value } = e.target;
//     setFormData(prev => ({
//       ...prev,
//       [name]: value
//     }));
//   };

//   const handleCreateProfile = () => {
//     if (!formData.name || !formData.email || !formData.phone || !formData.address) {
//       alert('Please fill in all required fields');
//       return;
//     }

//     const newProfile: PayerProfile = {
//       id: Math.max(...payerProfiles.map(p => p.id)) + 1,
//       name: formData.name,
//       email: formData.email,
//       phone: formData.phone,
//       address: formData.address,
//       status: 'Active',
//       outstanding: '₦0',
//       paymentProfiles: []
//     };
    
//     setPayerProfiles([...payerProfiles, newProfile]);
//     setIsCreateModalOpen(false);
//     resetForm();
//   };

//   const handleCancel = () => {
//     setIsCreateModalOpen(false);
//     resetForm();
//   };

//   const handleManagePaymentProfiles = (payerId: number) => {
//     const payer = payerProfiles.find(p => p.id === payerId);
//     if (payer) {
//       setSelectedPayerId(payerId);
//       setSelectedPaymentProfiles([...payer.paymentProfiles]);
//       setIsPaymentProfileModalOpen(true);
//     }
//   };

//   const handlePaymentProfileToggle = (profileId: number) => {
//     setSelectedPaymentProfiles(prev => 
//       prev.includes(profileId)
//         ? prev.filter(id => id !== profileId)
//         : [...prev, profileId]
//     );
//   };

//   const handleSavePaymentProfiles = () => {
//     if (selectedPayerId) {
//       setPayerProfiles(prev => 
//         prev.map(payer => 
//           payer.id === selectedPayerId
//             ? { ...payer, paymentProfiles: [...selectedPaymentProfiles] }
//             : payer
//         )
//       );
//     }
//     setIsPaymentProfileModalOpen(false);
//     setSelectedPayerId(null);
//     setSelectedPaymentProfiles([]);
//   };

//   const handleCancelPaymentProfiles = () => {
//     setIsPaymentProfileModalOpen(false);
//     setSelectedPayerId(null);
//     setSelectedPaymentProfiles([]);
//   };

//   const getPaymentProfileNames = (profileIds: number[]) => {
//     return profileIds
//       .map(id => paymentProfiles.find(p => p.id === id)?.name)
//       .filter(Boolean)
//       .join(', ');
//   };

//   return (
//     <div className="space-y-6">
//       <div className="flex justify-between items-center">
//         <h2 className="text-2xl font-bold text-[#045024]">Payer Profiles</h2>
//         <ActionButton onClick={() => setIsCreateModalOpen(true)}>
//           <Plus size={16} />
//           Create New Payer Profile
//         </ActionButton>
//       </div>
      
//       <div className="bg-white rounded-lg shadow-md p-4">
//         <div className="flex gap-4 mb-4">
//           <div className="flex-1">
//             <div className="relative">
//               <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
//               <input
//                 type="text"
//                 placeholder="Search payers..."
//                 className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//               />
//             </div>
//           </div>
//           <ActionButton variant="secondary">
//             <Filter size={16} />
//             Filter
//           </ActionButton>
//         </div>
        
//         <div className="overflow-x-auto">
//           <table className="w-full">
//             <thead className="bg-[#dddeda]">
//               <tr>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Organization</th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Contact</th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Address</th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Payment Profiles</th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Outstanding</th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Status</th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Actions</th>
//               </tr>
//             </thead>
//             <tbody className="divide-y divide-gray-200">
//               {payerProfiles.map(payer => (
//                 <tr key={payer.id} className="hover:bg-gray-50">
//                   <td className="px-6 py-4">
//                     <div className="font-medium text-gray-900">{payer.name}</div>
//                   </td>
//                   <td className="px-6 py-4">
//                     <div className="text-sm text-gray-700">{payer.email}</div>
//                     <div className="text-sm text-gray-500">{payer.phone}</div>
//                   </td>
//                   <td className="px-6 py-4">
//                     <div className="text-sm text-gray-700">{payer.address}</div>
//                   </td>
//                   <td className="px-6 py-4">
//                     <div className="text-sm text-gray-700">
//                       {payer.paymentProfiles.length > 0 ? (
//                         <div>
//                           <div className="font-medium text-gray-900">{payer.paymentProfiles.length} profile(s)</div>
//                           <div className="text-xs text-gray-500 truncate max-w-xs">
//                             {getPaymentProfileNames(payer.paymentProfiles)}
//                           </div>
//                         </div>
//                       ) : (
//                         <span className="text-gray-400 italic">No profiles assigned</span>
//                       )}
//                     </div>
//                   </td>
//                   <td className="px-6 py-4 whitespace-nowrap">
//                     <span className={`font-medium ${payer.outstanding === '$0' ? 'text-green-600' : 'text-red-600'}`}>
//                       {payer.outstanding}
//                     </span>
//                   </td>
//                   <td className="px-6 py-4 whitespace-nowrap">
//                     <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
//                       {payer.status}
//                     </span>
//                   </td>
//                   <td className="px-6 py-4 whitespace-nowrap">
//                     <div className="flex gap-2">
//                       <ActionButton variant="secondary" size="sm">
//                         <Eye size={14} />
//                         View
//                       </ActionButton>
//                       <ActionButton 
//                         variant="secondary" 
//                         size="sm"
//                         onClick={() => handleManagePaymentProfiles(payer.id)}
//                       >
//                         <CreditCard size={14} />
//                         Payment Profiles
//                       </ActionButton>
//                       <ActionButton variant="secondary" size="sm">
//                         <Receipt size={14} />
//                         Receipts
//                       </ActionButton>
//                     </div>
//                   </td>
//                 </tr>
//               ))}
//             </tbody>
//           </table>
//         </div>
//       </div>

//       {/* Create Payer Profile Modal */}
//       {isCreateModalOpen && (
//         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//           <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
//             <div className="flex justify-between items-center p-6 border-b">
//               <h3 className="text-lg font-semibold text-[#045024]">Create New Payer Profile</h3>
//               <button
//                 onClick={handleCancel}
//                 className="text-gray-400 hover:text-gray-600"
//               >
//                 <X size={20} />
//               </button>
//             </div>
            
//             <div className="p-6 space-y-4">
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-1">
//                   Organization Name *
//                 </label>
//                 <input
//                   type="text"
//                   name="name"
//                   value={formData.name}
//                   onChange={handleInputChange}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                   placeholder="Enter organization name"
//                 />
//               </div>
              
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-1">
//                   Address *
//                 </label>
//                 <textarea
//                   name="address"
//                   value={formData.address}
//                   onChange={handleInputChange}
//                   rows={3}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                   placeholder="Enter complete address"
//                 />
//               </div>
              
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-1">
//                   Email Address *
//                 </label>
//                 <input
//                   type="email"
//                   name="email"
//                   value={formData.email}
//                   onChange={handleInputChange}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                   placeholder="Enter email address"
//                 />
//               </div>
              
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-1">
//                   Phone Number *
//                 </label>
//                 <input
//                   type="tel"
//                   name="phone"
//                   value={formData.phone}
//                   onChange={handleInputChange}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                   placeholder="Enter phone number"
//                 />
//               </div>
//             </div>
            
//             <div className="flex justify-end gap-3 p-6 border-t">
//               <ActionButton variant="secondary" onClick={handleCancel}>
//                 Cancel
//               </ActionButton>
//               <ActionButton onClick={handleCreateProfile}>
//                 Create Profile
//               </ActionButton>
//             </div>
//           </div>
//         </div>
//       )}

//       {/* Payment Profile Assignment Modal */}
//       {isPaymentProfileModalOpen && (
//         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//           <div className="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
//             <div className="flex justify-between items-center p-6 border-b">
//               <h3 className="text-lg font-semibold text-[#045024]">
//                 Manage Payment Profiles
//                 {selectedPayerId && (
//                   <span className="text-sm font-normal text-gray-600 block">
//                     {payerProfiles.find(p => p.id === selectedPayerId)?.name}
//                   </span>
//                 )}
//               </h3>
//               <button
//                 onClick={handleCancelPaymentProfiles}
//                 className="text-gray-400 hover:text-gray-600"
//               >
//                 <X size={20} />
//               </button>
//             </div>
            
//             <div className="p-6">
//               <p className="text-sm text-gray-600 mb-4">
//                 Select the payment profiles to assign to this payer:
//               </p>
              
//               <div className="space-y-3 max-h-80 overflow-y-auto">
//                 {paymentProfiles.map(profile => (
//                   <label key={profile.id} className="flex items-start space-x-3 cursor-pointer">
//                     <input
//                       type="checkbox"
//                       checked={selectedPaymentProfiles.includes(profile.id)}
//                       onChange={() => handlePaymentProfileToggle(profile.id)}
//                       className="mt-1 h-4 w-4 text-[#2aa45c] focus:ring-[#2aa45c] border-gray-300 rounded"
//                     />
//                     <div className="flex-1">
//                       <div className="font-medium text-gray-900">{profile.name}</div>
//                       <div className="text-sm text-gray-500">{profile.description}</div>
//                     </div>
//                   </label>
//                 ))}
//               </div>
              
//               {selectedPaymentProfiles.length > 0 && (
//                 <div className="mt-4 p-3 bg-green-50 rounded-md">
//                   <p className="text-sm text-green-800">
//                     <strong>{selectedPaymentProfiles.length}</strong> payment profile(s) selected
//                   </p>
//                 </div>
//               )}
//             </div>
            
//             <div className="flex justify-end gap-3 p-6 border-t">
//               <ActionButton variant="secondary" onClick={handleCancelPaymentProfiles}>
//                 Cancel
//               </ActionButton>
//               <ActionButton onClick={handleSavePaymentProfiles}>
//                 Save Changes
//               </ActionButton>
//             </div>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };

// export default PayerProfiles;


import React, { useState, useMemo } from 'react';
import { Plus, Search, Eye, Receipt, Filter, X, CreditCard, ChevronLeft, ChevronRight } from 'lucide-react';

interface PaymentProfile {
  id: number;
  name: string;
  description: string;
}

interface PayerProfile {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  status: string;
  outstanding: string;
  paymentProfiles: number[]; // Array of payment profile IDs
}

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md';
  disabled?: boolean;
}

interface PayerFormData {
  name: string;
  address: string;
  phone: string;
  email: string;
}

const ActionButton: React.FC<ActionButtonProps> = ({ children, onClick, variant = "primary", size = "md", disabled = false }) => {
  const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white disabled:bg-gray-300 disabled:text-gray-500",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024] disabled:bg-gray-100 disabled:text-gray-400",
    danger: "bg-red-500 hover:bg-red-600 text-white disabled:bg-gray-300 disabled:text-gray-500"
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${disabled ? 'cursor-not-allowed' : ''}`}
    >
      {children}
    </button>
  );
};

const PayerProfiles: React.FC = () => {
  const ITEMS_PER_PAGE = 10;
  
  // Sample payment profiles
  const paymentProfiles: PaymentProfile[] = [
    { id: 1, name: "Standard Invoice", description: "Net 30 payment terms" },
    { id: 2, name: "Quick Pay", description: "Net 15 payment terms with 2% discount" },
    { id: 3, name: "Immediate Payment", description: "Payment due on receipt" },
    { id: 4, name: "Extended Terms", description: "Net 60 payment terms for large clients" },
    { id: 5, name: "Subscription Billing", description: "Monthly recurring payments" }
  ];

  // Extended sample data for pagination demonstration
  const generateSampleData = () => {
    const companies = [
      "Acme Corporation", "TechStart Inc", "Global Solutions", "DataFlow Systems", "Innovation Labs",
      "Future Tech", "Digital Dynamics", "Smart Solutions", "NextGen Corp", "CloudWorks",
      "StreamLine Inc", "ProActive Systems", "MetaVerse Ltd", "Quantum Computing", "BioTech Solutions",
      "EcoFriendly Corp", "GreenTech Industries", "SolarPower Inc", "WindEnergy Solutions", "HydroTech Systems",
      "SafeGuard Security", "CyberShield Inc", "DataProtect Corp", "SecureNet Solutions", "TrustWorth Systems",
      "FinTech Innovations", "PaymentHub Inc", "CreditFlow Corp", "BankingTech Solutions", "MoneyTransfer Inc",
      "LogisticsPro", "ShipFast Corp", "DeliveryExpress", "CargoSolutions Inc", "TransportTech Systems",
      "HealthTech Corp", "MedicalSolutions Inc", "PharmaFlow Systems", "BioMed Innovations", "HealthCare Tech",
      "EduTech Solutions", "LearningHub Inc", "SkillBuilder Corp", "TrainingTech Systems", "KnowledgeBase Inc",
      "RetailTech Corp", "ShopSmart Solutions", "EcommercePro Inc", "MarketPlace Systems", "SalesTech Innovations"
    ];

    const domains = ["com", "org", "net", "io", "co"];
    const cities = ["New York, NY", "San Francisco, CA", "Chicago, IL", "Austin, TX", "Seattle, WA", "Boston, MA", "Denver, CO", "Miami, FL"];
    const statuses = ["Active", "Pending", "Overdue"];
    
    return companies.map((company, index) => ({
      id: index + 1,
      name: company,
      email: `finance@${company.toLowerCase().replace(/\s+/g, '')}.${domains[index % domains.length]}`,
      phone: `******-${String(index + 100).padStart(4, '0')}`,
      address: `${123 + index} Business St, ${cities[index % cities.length]}`,
      status: statuses[index % statuses.length],
      outstanding: index % 5 === 0 ? "₦0" : `₦${(Math.random() * 5000 + 500).toFixed(0)}`,
      paymentProfiles: [1, 2, 3, 4, 5].slice(0, Math.floor(Math.random() * 3) + 1)
    }));
  };

  const [payerProfiles, setPayerProfiles] = useState<PayerProfile[]>(generateSampleData());
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isPaymentProfileModalOpen, setIsPaymentProfileModalOpen] = useState(false);
  const [selectedPayerId, setSelectedPayerId] = useState<number | null>(null);
  const [selectedPaymentProfiles, setSelectedPaymentProfiles] = useState<number[]>([]);
  const [formData, setFormData] = useState<PayerFormData>({
    name: '',
    address: '',
    phone: '',
    email: '',
  });

  // Filter and paginate data
  const filteredProfiles = useMemo(() => {
    return payerProfiles.filter(profile =>
      profile.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      profile.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      profile.phone.includes(searchTerm)
    );
  }, [payerProfiles, searchTerm]);

  const totalPages = Math.ceil(filteredProfiles.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const currentProfiles = filteredProfiles.slice(startIndex, endIndex);

  // Reset to first page when search changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      address: '',
      phone: '',
      email: '',
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCreateProfile = () => {
    if (!formData.name || !formData.email || !formData.phone || !formData.address) {
      alert('Please fill in all required fields');
      return;
    }

    const newProfile: PayerProfile = {
      id: Math.max(...payerProfiles.map(p => p.id)) + 1,
      name: formData.name,
      email: formData.email,
      phone: formData.phone,
      address: formData.address,
      status: 'Active',
      outstanding: '₦0',
      paymentProfiles: []
    };
    
    setPayerProfiles([...payerProfiles, newProfile]);
    setIsCreateModalOpen(false);
    resetForm();
  };

  const handleCancel = () => {
    setIsCreateModalOpen(false);
    resetForm();
  };

  const handleManagePaymentProfiles = (payerId: number) => {
    const payer = payerProfiles.find(p => p.id === payerId);
    if (payer) {
      setSelectedPayerId(payerId);
      setSelectedPaymentProfiles([...payer.paymentProfiles]);
      setIsPaymentProfileModalOpen(true);
    }
  };

  const handlePaymentProfileToggle = (profileId: number) => {
    setSelectedPaymentProfiles(prev => 
      prev.includes(profileId)
        ? prev.filter(id => id !== profileId)
        : [...prev, profileId]
    );
  };

  const handleSavePaymentProfiles = () => {
    if (selectedPayerId) {
      setPayerProfiles(prev => 
        prev.map(payer => 
          payer.id === selectedPayerId
            ? { ...payer, paymentProfiles: [...selectedPaymentProfiles] }
            : payer
        )
      );
    }
    setIsPaymentProfileModalOpen(false);
    setSelectedPayerId(null);
    setSelectedPaymentProfiles([]);
  };

  const handleCancelPaymentProfiles = () => {
    setIsPaymentProfileModalOpen(false);
    setSelectedPayerId(null);
    setSelectedPaymentProfiles([]);
  };

  const getPaymentProfileNames = (profileIds: number[]) => {
    return profileIds
      .map(id => paymentProfiles.find(p => p.id === id)?.name)
      .filter(Boolean)
      .join(', ');
  };

  // Pagination component
  const PaginationControls = () => {
    const getPageNumbers = () => {
      const pages = [];
      const maxVisiblePages = 7;
      
      if (totalPages <= maxVisiblePages) {
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        if (currentPage <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i);
          }
          pages.push('...');
          pages.push(totalPages);
        } else if (currentPage >= totalPages - 3) {
          pages.push(1);
          pages.push('...');
          for (let i = totalPages - 4; i <= totalPages; i++) {
            pages.push(i);
          }
        } else {
          pages.push(1);
          pages.push('...');
          for (let i = currentPage - 1; i <= currentPage + 1; i++) {
            pages.push(i);
          }
          pages.push('...');
          pages.push(totalPages);
        }
      }
      
      return pages;
    };

    return (
      <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
        <div className="flex flex-1 justify-between sm:hidden">
          <ActionButton 
            variant="secondary" 
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </ActionButton>
          <ActionButton 
            variant="secondary" 
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next
          </ActionButton>
        </div>
        <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-gray-700">
              Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
              <span className="font-medium">{Math.min(endIndex, filteredProfiles.length)}</span> of{' '}
              <span className="font-medium">{filteredProfiles.length}</span> results
            </p>
          </div>
          <div>
            <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              
              {getPageNumbers().map((page, index) => (
                <React.Fragment key={index}>
                  {page === '...' ? (
                    <span className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300 focus:outline-offset-0">
                      ...
                    </span>
                  ) : (
                    <button
                      onClick={() => handlePageChange(page as number)}
                      className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 ${
                        currentPage === page
                          ? 'z-10 bg-[#2aa45c] text-white focus-visible:outline-offset-2 focus-visible:outline-[#2aa45c]'
                          : 'text-gray-900'
                      }`}
                    >
                      {page}
                    </button>
                  )}
                </React.Fragment>
              ))}
              
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </nav>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-[#045024]">Payer Profiles</h2>
        <ActionButton onClick={() => setIsCreateModalOpen(true)}>
          <Plus size={16} />
          Create New Payer Profile
        </ActionButton>
      </div>
      
      <div className="bg-white rounded-lg shadow-md">
        <div className="p-4">
          <div className="flex gap-4 mb-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search payers..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                />
              </div>
            </div>
            <ActionButton variant="secondary">
              <Filter size={16} />
              Filter
            </ActionButton>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-[#dddeda]">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Organization</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Contact</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Address</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Payment Profiles</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Outstanding</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {currentProfiles.map(payer => (
                <tr key={payer.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="font-medium text-gray-900">{payer.name}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-700">{payer.email}</div>
                    <div className="text-sm text-gray-500">{payer.phone}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-700">{payer.address}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-700">
                      {payer.paymentProfiles.length > 0 ? (
                        <div>
                          <div className="font-medium text-gray-900">{payer.paymentProfiles.length} profile(s)</div>
                          <div className="text-xs text-gray-500 truncate max-w-xs">
                            {getPaymentProfileNames(payer.paymentProfiles)}
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400 italic">No profiles assigned</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`font-medium ${payer.outstanding === '₦0' ? 'text-green-600' : 'text-red-600'}`}>
                      {payer.outstanding}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      payer.status === 'Active' ? 'bg-green-100 text-green-800' :
                      payer.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {payer.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex gap-2">
                      <ActionButton variant="secondary" size="sm">
                        <Eye size={14} />
                        View
                      </ActionButton>
                      <ActionButton 
                        variant="secondary" 
                        size="sm"
                        onClick={() => handleManagePaymentProfiles(payer.id)}
                      >
                        <CreditCard size={14} />
                        Payment Profiles
                      </ActionButton>
                      <ActionButton variant="secondary" size="sm">
                        <Receipt size={14} />
                        Receipts
                      </ActionButton>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <PaginationControls />
      </div>

      {/* Create Payer Profile Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="flex justify-between items-center p-6 border-b">
              <h3 className="text-lg font-semibold text-[#045024]">Create New Payer Profile</h3>
              <button
                onClick={handleCancel}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Organization Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  placeholder="Enter organization name"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address *
                </label>
                <textarea
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  placeholder="Enter complete address"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  placeholder="Enter email address"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  placeholder="Enter phone number"
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-3 p-6 border-t">
              <ActionButton variant="secondary" onClick={handleCancel}>
                Cancel
              </ActionButton>
              <ActionButton onClick={handleCreateProfile}>
                Create Profile
              </ActionButton>
            </div>
          </div>
        </div>
      )}

      {/* Payment Profile Assignment Modal */}
      {isPaymentProfileModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
            <div className="flex justify-between items-center p-6 border-b">
              <h3 className="text-lg font-semibold text-[#045024]">
                Manage Payment Profiles
                {selectedPayerId && (
                  <span className="text-sm font-normal text-gray-600 block">
                    {payerProfiles.find(p => p.id === selectedPayerId)?.name}
                  </span>
                )}
              </h3>
              <button
                onClick={handleCancelPaymentProfiles}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="p-6">
              <p className="text-sm text-gray-600 mb-4">
                Select the payment profiles to assign to this payer:
              </p>
              
              <div className="space-y-3 max-h-80 overflow-y-auto">
                {paymentProfiles.map(profile => (
                  <label key={profile.id} className="flex items-start space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectedPaymentProfiles.includes(profile.id)}
                      onChange={() => handlePaymentProfileToggle(profile.id)}
                      className="mt-1 h-4 w-4 text-[#2aa45c] focus:ring-[#2aa45c] border-gray-300 rounded"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{profile.name}</div>
                      <div className="text-sm text-gray-500">{profile.description}</div>
                    </div>
                  </label>
                ))}
              </div>
              
              {selectedPaymentProfiles.length > 0 && (
                <div className="mt-4 p-3 bg-green-50 rounded-md">
                  <p className="text-sm text-green-800">
                    <strong>{selectedPaymentProfiles.length}</strong> payment profile(s) selected
                  </p>
                </div>
              )}
            </div>
            
            <div className="flex justify-end gap-3 p-6 border-t">
              <ActionButton variant="secondary" onClick={handleCancelPaymentProfiles}>
                Cancel
              </ActionButton>
              <ActionButton onClick={handleSavePaymentProfiles}>
                Save Changes
              </ActionButton>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PayerProfiles;