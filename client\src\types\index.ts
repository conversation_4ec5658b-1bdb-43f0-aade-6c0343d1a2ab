// src/types/index.ts

// User and Authentication Types
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  role: UserRole;
  organizationId?: string;
  isActive: boolean;
  createdAt: string;
  lastLogin?: string;
}

// export enum UserRole {
//   JTB_ADMIN = 'JTB_ADMIN',
//   FINANCE_OFFICER = 'FINANCE_OFFICER',
//   SENIOR_FINANCE_OFFICER = 'SENIOR_FINANCE_OFFICER',
//   PAYER = 'PAYER'
// }
export type UserRole = 'JTB_ADMIN' | 'FINANCE_OFFICER' | 'SENIOR_FINANCE_OFFICER' | 'PAYER' ;


export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  token: string | null;
}

// Organization/Payer Types
export interface Organization {
  id: string;
  name: string;
  address: string;
  email: string;
  phoneNumber: string;
  contactPerson?: string;
  isActive: boolean;
  createdAt: string;
  paymentProfiles: string[]; // Payment profile IDs
}

// Payment Types
export interface PaymentType {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  createdAt: string;
}

export interface PaymentProfile {
  id: string;
  name: string;
  description: string;
  paymentTypeId: string;
  paymentType?: PaymentType;
  year: number;
  dueDate: string;
  amount: number;
  isActive: boolean;
  createdAt: string;
  assignedPayers: string[]; // Organization IDs
  receiptTemplate?: string; // File path
  certificateTemplate?: string; // File path
  approvalStatus: ApprovalStatus;
}

// export enum ApprovalStatus {
//   DRAFT = 'DRAFT',
//   PENDING_APPROVAL = 'PENDING_APPROVAL',
//   APPROVED = 'APPROVED',
//   REJECTED = 'REJECTED'
// }
type ApprovalStatus = 'DRAFT' | 'PENDING_APPROVAL' | 'APPROVED' | 'REJECTED' ;

export interface Payment {
  id: string;
  paymentProfileId: string;
  paymentProfile?: PaymentProfile;
  payerId: string;
  payer?: Organization;
  amount: number;
  paymentDate: string;
  proofOfPayment?: string; // File path
  status: PaymentStatus;
  acknowledgmentDate?: string;
  approvalDate?: string;
  receiptId?: string;
  createdAt: string;
  notes?: string;
}

// export enum PaymentStatus {
//   PENDING = 'PENDING',
//   PROOF_UPLOADED = 'PROOF_UPLOADED',
//   ACKNOWLEDGED = 'ACKNOWLEDGED',
//   APPROVED = 'APPROVED',
//   REJECTED = 'REJECTED'
// }
type PaymentStatus = 'PENDING' | 'PROOF_UPLOADED' | 'ACKNOWLEDGED' | 'APPROVED' | 'REJECTED' ;

export interface Receipt {
  id: string;
  paymentId: string;
  payment?: Payment;
  receiptNumber: string;
  issuedDate: string;
  filePath: string;
  isRevoked: boolean;
  revokedDate?: string;
  revokedReason?: string;
  revokedBy?: string;
}

export interface ComplianceCertificate {
  id: string;
  payerId: string;
  payer?: Organization;
  paymentProfileId: string;
  paymentProfile?: PaymentProfile;
  certificateNumber: string;
  issuedDate: string;
  filePath: string;
  isActive: boolean;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Form Types
export interface CreatePaymentProfileForm {
  name: string;
  description: string;
  paymentTypeId: string;
  year: number;
  dueDate: string;
  amount: number;
  assignedPayers: string[];
  receiptTemplate?: File;
  certificateTemplate?: File;
}

export interface CreateOrganizationForm {
  name: string;
  address: string;
  email: string;
  phoneNumber: string;
  contactPerson?: string;
}

export interface ReportPaymentForm {
  paymentProfileIds: string[];
  totalAmount: number;
  paymentDate: string;
  proofOfPayment: File;
  notes?: string;
}

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  isRead: boolean;
  createdAt: string;
  actionUrl?: string;
}

// enum NotificationType {
//   PAYMENT_DUE = 'PAYMENT_DUE',
//   PAYMENT_RECEIVED = 'PAYMENT_RECEIVED',
//   PAYMENT_APPROVED = 'PAYMENT_APPROVED',
//   RECEIPT_ISSUED = 'RECEIPT_ISSUED',
//   CERTIFICATE_ISSUED = 'CERTIFICATE_ISSUED',
//   SYSTEM = 'SYSTEM'
// }
type NotificationType = "INFO" | "WARNING" | "ERROR";


// Table/List Types
export interface TableColumn<T> {
  key: keyof T;
  title: string;
  sortable?: boolean;
  render?: (value: any, record: T) => React.ReactNode;
}

export interface FilterOption {
  label: string;
  value: string;
}

export interface TableFilters {
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  [key: string]: any;
}