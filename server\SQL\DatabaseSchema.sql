-- Receipts Table
CREATE TABLE Receipts (
    Id NVARCHAR(50) PRIMARY KEY,
    PayerId NVARCHAR(50),
    PayerName NVARCHAR(100),
    PayerEmail NVARCHAR(255),
    ReceiptNumber NVARCHAR(50) UNIQUE NOT NULL,
    Amount DECIMAL(18, 2) NOT NULL,
    Currency NVARCHAR(10) NOT NULL DEFAULT 'NGN',
    PaymentDate DATETIME NOT NULL,
    PaymentMethod NVARCHAR(50) NOT NULL,
    Status NVARCHAR(20) NOT NULL,
    Description NVARCHAR(500),
    Category NVARCHAR(100),
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50),
    OrganizationId NVARCHAR(50),
    PaymentId NVARCHAR(50), -- Link to payment
    IsRevoked BIT NOT NULL DEFAULT 0,
    RevokedDate DATETIME NULL,
    RevokedReason NVARCHAR(500) NULL,
    RevokedBy NVARCHAR(50) NULL,
    NotificationSent BIT NOT NULL DEFAULT 0,
    NotificationSentDate DATETIME NULL
);

-- Payments Table
CREATE TABLE Payments (
    Id NVARCHAR(50) PRIMARY KEY,
    PayerId NVARCHAR(50),
    PayerName NVARCHAR(100),
    PayerEmail NVARCHAR(255),
    Amount DECIMAL(18, 2) NOT NULL,
    Currency NVARCHAR(10) NOT NULL DEFAULT 'NGN',
    TransactionReference NVARCHAR(50) UNIQUE NOT NULL,
    PaymentMethod NVARCHAR(50) NOT NULL,
    Status NVARCHAR(20) NOT NULL,
    Description NVARCHAR(500),
    Category NVARCHAR(100),
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CompletedAt DATETIME NULL,
    ReceiptId NVARCHAR(50),
    OrganizationId NVARCHAR(50)
);

-- Organizations Table
CREATE TABLE Organizations (
    Id NVARCHAR(50) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    Email NVARCHAR(255),
    PhoneNumber NVARCHAR(20),
    Address NVARCHAR(255),
    City NVARCHAR(100),
    State NVARCHAR(100),
    Country NVARCHAR(100),
    LogoUrl NVARCHAR(255),
    Website NVARCHAR(255),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50)
);

-- Stored Procedures for Receipts
CREATE PROCEDURE CreateReceipt
    @Id NVARCHAR(50),
    @PayerId NVARCHAR(50),
    @PayerName NVARCHAR(100),
    @PayerEmail NVARCHAR(255),
    @ReceiptNumber NVARCHAR(50),
    @Amount DECIMAL(18, 2),
    @Currency NVARCHAR(10),
    @PaymentDate DATETIME,
    @PaymentMethod NVARCHAR(50),
    @Status NVARCHAR(20),
    @Description NVARCHAR(500),
    @Category NVARCHAR(100),
    @CreatedBy NVARCHAR(50),
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    INSERT INTO Receipts (
        Id, PayerId, PayerName, PayerEmail, ReceiptNumber, Amount, Currency,
        PaymentDate, PaymentMethod, Status, Description, Category, CreatedBy, OrganizationId
    )
    VALUES (
        @Id, @PayerId, @PayerName, @PayerEmail, @ReceiptNumber, @Amount, @Currency,
        @PaymentDate, @PaymentMethod, @Status, @Description, @Category, @CreatedBy, @OrganizationId
    )
    
    SELECT * FROM Receipts WHERE Id = @Id
END;

CREATE PROCEDURE GetReceiptById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Receipts WHERE Id = @Id
END;

CREATE PROCEDURE GetReceiptsByPayer
    @PayerId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Receipts WHERE PayerId = @PayerId ORDER BY CreatedAt DESC
END;

CREATE PROCEDURE GetReceiptsByOrganization
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Receipts WHERE OrganizationId = @OrganizationId ORDER BY CreatedAt DESC
END;

-- Stored Procedures for Payments
CREATE PROCEDURE CreatePayment
    @Id NVARCHAR(50),
    @PayerId NVARCHAR(50),
    @PayerName NVARCHAR(100),
    @PayerEmail NVARCHAR(255),
    @Amount DECIMAL(18, 2),
    @Currency NVARCHAR(10),
    @TransactionReference NVARCHAR(50),
    @PaymentMethod NVARCHAR(50),
    @Status NVARCHAR(20),
    @Description NVARCHAR(500),
    @Category NVARCHAR(100),
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    INSERT INTO Payments (
        Id, PayerId, PayerName, PayerEmail, Amount, Currency, TransactionReference,
        PaymentMethod, Status, Description, Category, OrganizationId
    )
    VALUES (
        @Id, @PayerId, @PayerName, @PayerEmail, @Amount, @Currency, @TransactionReference,
        @PaymentMethod, @Status, @Description, @Category, @OrganizationId
    )
    
    SELECT * FROM Payments WHERE Id = @Id
END;

CREATE PROCEDURE UpdatePaymentStatus
    @Id NVARCHAR(50),
    @Status NVARCHAR(20),
    @CompletedAt DATETIME
AS
BEGIN
    UPDATE Payments
    SET Status = @Status, CompletedAt = @CompletedAt
    WHERE Id = @Id
    
    SELECT * FROM Payments WHERE Id = @Id
END;

CREATE PROCEDURE UpdatePaymentReceiptId
    @Id NVARCHAR(50),
    @ReceiptId NVARCHAR(50)
AS
BEGIN
    UPDATE Payments
    SET ReceiptId = @ReceiptId
    WHERE Id = @Id
    
    SELECT * FROM Payments WHERE Id = @Id
END;

CREATE PROCEDURE GetPaymentById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Payments WHERE Id = @Id
END;

CREATE PROCEDURE GetPaymentsByPayer
    @PayerId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Payments WHERE PayerId = @PayerId ORDER BY CreatedAt DESC
END;

-- Stored Procedures for Organizations
CREATE PROCEDURE CreateOrganization
    @Id NVARCHAR(50),
    @Name NVARCHAR(100),
    @Email NVARCHAR(255),
    @PhoneNumber NVARCHAR(20),
    @Address NVARCHAR(255),
    @City NVARCHAR(100),
    @State NVARCHAR(100),
    @Country NVARCHAR(100),
    @LogoUrl NVARCHAR(255),
    @Website NVARCHAR(255),
    @IsActive BIT,
    @CreatedBy NVARCHAR(50)
AS
BEGIN
    INSERT INTO Organizations (
        Id, Name, Email, PhoneNumber, Address, City, State, Country,
        LogoUrl, Website, IsActive, CreatedBy
    )
    VALUES (
        @Id, @Name, @Email, @PhoneNumber, @Address, @City, @State, @Country,
        @LogoUrl, @Website, @IsActive, @CreatedBy
    )
    
    SELECT * FROM Organizations WHERE Id = @Id
END;

CREATE PROCEDURE GetOrganizationById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM Organizations WHERE Id = @Id
END;

CREATE PROCEDURE GetAllOrganizations
AS
BEGIN
    SELECT * FROM Organizations ORDER BY Name
END;

CREATE PROCEDURE UpdateOrganization
    @Id NVARCHAR(50),
    @Name NVARCHAR(100),
    @Email NVARCHAR(255),
    @PhoneNumber NVARCHAR(20),
    @Address NVARCHAR(255),
    @City NVARCHAR(100),
    @State NVARCHAR(100),
    @Country NVARCHAR(100),
    @LogoUrl NVARCHAR(255),
    @Website NVARCHAR(255)
AS
BEGIN
    UPDATE Organizations
    SET 
        Name = @Name,
        Email = @Email,
        PhoneNumber = @PhoneNumber,
        Address = @Address,
        City = @City,
        State = @State,
        Country = @Country,
        LogoUrl = @LogoUrl,
        Website = @Website
    WHERE Id = @Id
    
    SELECT * FROM Organizations WHERE Id = @Id
END;

CREATE PROCEDURE UpdateOrganizationStatus
    @Id NVARCHAR(50),
    @IsActive BIT
AS
BEGIN
    UPDATE Organizations
    SET IsActive = @IsActive
    WHERE Id = @Id
    
    SELECT * FROM Organizations WHERE Id = @Id
END;

-- Stored Procedures for Reporting
CREATE PROCEDURE GetDashboardSummary
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT
        (SELECT COUNT(*) FROM Receipts WHERE OrganizationId = @OrganizationId) AS TotalReceipts,
        (SELECT ISNULL(SUM(Amount), 0) FROM Receipts WHERE OrganizationId = @OrganizationId) AS TotalAmount,
        (SELECT COUNT(*) FROM Payments WHERE OrganizationId = @OrganizationId AND Status = 'Completed') AS CompletedPayments,
        (SELECT COUNT(*) FROM Payments WHERE OrganizationId = @OrganizationId AND Status = 'Pending') AS PendingPayments,
        (SELECT COUNT(*) FROM Payments WHERE OrganizationId = @OrganizationId AND Status = 'Failed') AS FailedPayments,
        (SELECT COUNT(*) FROM Users WHERE OrganizationId = @OrganizationId) AS TotalUsers,
        (SELECT COUNT(*) FROM Users WHERE Or

