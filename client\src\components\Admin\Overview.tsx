import React from 'react';
import {
  Users,
  Settings,
  CheckCircle,
  Clock,
  Shield,
  UserPlus,
  Mail,
  Activity
} from 'lucide-react';
import EmailConfigQuickView from './EmailConfigQuickView';

interface StatsCardProps {
  title: string;
  value: string;
  icon: React.ComponentType<any>;
  trend?: string;
  color?: string;
}

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md';
  disabled?: boolean;
}

interface AuditLog {
  id: number;
  user: string;
  action: string;
  details: string;
  timestamp: string;
  ip: string;
}

interface OverviewProps {
  auditLogs?: AuditLog[];
  setActiveTab?: (tab: string) => void;
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, icon: Icon, trend, color = "bg-[#2aa45c]" }) => (
  <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-[#2aa45c]">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className="text-2xl font-bold text-[#045024] mt-1">{value}</p>
        {trend && <p className="text-sm text-[#2aa45c] mt-1">{trend}</p>}
      </div>
      <div className={`${color} p-3 rounded-full`}>
        <Icon className="text-white" size={24} />
      </div>
    </div>
  </div>
);

const ActionButton: React.FC<ActionButtonProps> = ({ children, onClick, variant = "primary", size = "md", disabled = false }) => {
  const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white"
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {children}
    </button>
  );
};

// Default audit logs for when none are provided
const defaultAuditLogs: AuditLog[] = [
  {
    id: 1,
    user: "John Doe",
    action: "User logged in",
    details: "Successful login from dashboard",
    timestamp: "2 minutes ago",
    ip: "***********"
  },
  {
    id: 2,
    user: "Jane Smith",
    action: "User created",
    details: "New user account created",
    timestamp: "15 minutes ago",
    ip: "***********"
  },
  {
    id: 3,
    user: "Admin",
    action: "Settings updated",
    details: "System configuration changed",
    timestamp: "1 hour ago",
    ip: "***********"
  },
  {
    id: 4,
    user: "Mike Johnson",
    action: "Password reset",
    details: "User requested password reset",
    timestamp: "2 hours ago",
    ip: "***********"
  }
];

const Overview: React.FC<OverviewProps> = ({ auditLogs = defaultAuditLogs, setActiveTab }) => {
  const handleTabChange = (tab: string) => {
    if (setActiveTab) {
      setActiveTab(tab);
    } else {
      console.log(`Navigate to ${tab}`);
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard title="Total Users" value="156" icon={Users} trend="+12 this month" />
        <StatsCard title="Active Users" value="134" icon={CheckCircle} trend="+8 this week" color="bg-green-500" />
        <StatsCard title="Pending Invitations" value="15" icon={Clock} trend="+7 today" color="bg-orange-500" />
        <StatsCard title="System Admins" value="8" icon={Shield} color="bg-blue-500" />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-[#045024] mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-3">
            <ActionButton onClick={() => handleTabChange('create-user')}>
              <UserPlus size={16} />
              Invite User
            </ActionButton>
            <ActionButton onClick={() => handleTabChange('user-list')}>
              <Users size={16} />
              Manage Users
            </ActionButton>
            <ActionButton onClick={() => handleTabChange('invitations')}>
              <Mail size={16} />
              Track Invitations
            </ActionButton>
            <ActionButton onClick={() => handleTabChange('settings')}>
              <Settings size={16} />
              System Settings
            </ActionButton>
          </div>
        </div>

        {/* Email Configuration Quick View */}
        <EmailConfigQuickView
          organizationId="SYSTEM"
          showActions={true}
        />

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-[#045024] mb-4">Recent Activity</h3>
          <div className="space-y-3">
            {auditLogs.slice(0, 4).map(log => (
              <div key={log.id} className="flex items-start gap-3 p-3 bg-[#dddeda] bg-opacity-30 rounded-lg">
                <Activity className="text-[#2aa45c] mt-0.5" size={16} />
                <div className="flex-1">
                  <p className="text-sm text-gray-800">{log.action} by {log.user}</p>
                  <p className="text-xs text-gray-500 mt-1">{log.timestamp}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Overview;