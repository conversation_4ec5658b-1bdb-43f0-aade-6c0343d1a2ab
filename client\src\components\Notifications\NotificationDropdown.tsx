import React, { forwardRef } from 'react';
import { 
  Bell, 
  Check, 
  ExternalLink, 
  Settings,
  DollarSign,
  Shield,
  AlertCircle,
  Receipt,
  Clock
} from 'lucide-react';
import { useNotificationsApi } from '../../hooks/api';
import type { Notification } from '../../hooks/api/useNotifications';

interface NotificationDropdownProps {
  notifications: Notification[];
  unreadCount: number;
  onNotificationUpdate: () => void;
  onClose: () => void;
  loading?: boolean;
}

const NotificationDropdown = forwardRef<HTMLDivElement, NotificationDropdownProps>(({
  notifications,
  unreadCount,
  onNotificationUpdate,
  onClose,
  loading = false,
}, ref) => {
  const { markAsRead } = useNotificationsApi();

  const handleMarkAsRead = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    await markAsRead(notificationId);
    onNotificationUpdate();
  };

  const handleNotificationClick = async (notification: Notification) => {
    // Mark as read if unread
    if (notification.status === 'UNREAD') {
      await markAsRead(notification.id);
      onNotificationUpdate();
    }

    // Navigate to related entity if available
    if (notification.relatedEntityId && notification.relatedEntityType) {
      const entityType = notification.relatedEntityType.toLowerCase();
      let path = '';
      
      switch (entityType) {
        case 'payment':
          path = `/payments/${notification.relatedEntityId}`;
          break;
        case 'receipt':
          path = `/receipts/${notification.relatedEntityId}`;
          break;
        case 'certificate':
          path = `/compliance/certificates/${notification.relatedEntityId}`;
          break;
        case 'organization':
          path = `/organizations/${notification.relatedEntityId}`;
          break;
        default:
          return;
      }
      
      // Navigate to the path (you might want to use your router here)
      window.location.href = path;
    }
    
    onClose();
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'PAYMENT_DUE':
      case 'PAYMENT_OVERDUE':
        return <DollarSign size={16} className="text-orange-500" />;
      case 'PAYMENT_SCHEDULE_CREATED':
        return <Clock size={16} className="text-blue-500" />;
      case 'PAYMENT_SCHEDULE_UPDATED':
        return <Clock size={16} className="text-yellow-500" />;
      case 'PAYMENT_SCHEDULE_DELETED':
        return <Clock size={16} className="text-red-500" />;
      case 'BULK_SCHEDULES_IMPORTED':
        return <DollarSign size={16} className="text-green-500" />;
      case 'CERTIFICATE_EXPIRING':
      case 'CERTIFICATE_EXPIRED':
        return <Shield size={16} className="text-red-500" />;
      case 'SYSTEM_ALERT':
        return <AlertCircle size={16} className="text-red-500" />;
      case 'RECEIPT_GENERATED':
        return <Receipt size={16} className="text-green-500" />;
      default:
        return <Bell size={16} className="text-blue-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'border-l-red-500';
      case 'HIGH':
        return 'border-l-orange-500';
      case 'MEDIUM':
        return 'border-l-yellow-500';
      default:
        return 'border-l-blue-500';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <div
      ref={ref}
      className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden"
    >
      {/* Header */}
      <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bell size={16} className="text-gray-600" />
            <span className="font-medium text-gray-900">Notifications</span>
            {unreadCount > 0 && (
              <span className="bg-red-100 text-red-800 text-xs font-medium px-2 py-0.5 rounded-full">
                {unreadCount}
              </span>
            )}
          </div>
          <button
            onClick={() => {
              // Navigate to full notification center
              window.location.href = '/notifications';
              onClose();
            }}
            className="text-[#2aa45c] hover:text-[#076934] text-sm font-medium flex items-center space-x-1"
          >
            <span>View All</span>
            <ExternalLink size={12} />
          </button>
        </div>
      </div>

      {/* Notifications List */}
      <div className="max-h-80 overflow-y-auto">
        {loading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#2aa45c]"></div>
          </div>
        ) : notifications.length === 0 ? (
          <div className="text-center p-8">
            <Bell className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">No new notifications</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                onClick={() => handleNotificationClick(notification)}
                className={`
                  px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors border-l-4
                  ${notification.status === 'UNREAD' ? 'bg-blue-50' : 'bg-white'}
                  ${getPriorityColor(notification.priority)}
                `}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-0.5">
                    {getNotificationIcon(notification.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className={`text-sm ${notification.status === 'UNREAD' ? 'font-medium text-gray-900' : 'text-gray-700'}`}>
                          {notification.title}
                        </p>
                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                          {notification.message}
                        </p>
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs text-gray-500">
                            {formatTimeAgo(notification.createdAt)}
                          </span>
                          {notification.priority === 'URGENT' && (
                            <span className="text-xs bg-red-100 text-red-800 px-2 py-0.5 rounded-full">
                              Urgent
                            </span>
                          )}
                        </div>
                      </div>
                      
                      {notification.status === 'UNREAD' && (
                        <button
                          onClick={(e) => handleMarkAsRead(notification.id, e)}
                          className="ml-2 text-gray-400 hover:text-green-600 flex-shrink-0"
                          title="Mark as read"
                        >
                          <Check size={14} />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {notifications.length > 0 && (
        <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <button
              onClick={() => {
                // Navigate to notification settings
                window.location.href = '/settings/notifications';
                onClose();
              }}
              className="text-gray-600 hover:text-gray-900 text-sm flex items-center space-x-1"
            >
              <Settings size={14} />
              <span>Settings</span>
            </button>
            
            {unreadCount > 0 && (
              <button
                onClick={async () => {
                  // Mark all as read functionality would go here
                  // For now, just refresh
                  onNotificationUpdate();
                }}
                className="text-[#2aa45c] hover:text-[#076934] text-sm font-medium"
              >
                Mark all read
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
});

NotificationDropdown.displayName = 'NotificationDropdown';

export default NotificationDropdown;
