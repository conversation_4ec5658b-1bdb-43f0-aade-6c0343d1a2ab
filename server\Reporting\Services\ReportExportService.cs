using System.Text;
using Final_E_Receipt.Reporting.Models;

namespace Final_E_Receipt.Reporting.Services
{
    public class ReportExportService
    {
        private readonly ReportingService _reportingService;

        public ReportExportService(ReportingService reportingService)
        {
            _reportingService = reportingService;
        }

        public async Task<byte[]> ExportMonthlyRevenue(string organizationId, int year, string format)
        {
            var data = await _reportingService.GetMonthlyRevenue(organizationId, year);
            
            switch (format.ToUpper())
            {
                case "CSV":
                    return ExportMonthlyRevenueToCSV(data);
                case "EXCEL":
                    return ExportMonthlyRevenueToExcel(data);
                case "PDF":
                    return ExportMonthlyRevenueToPDF(data);
                default:
                    throw new ArgumentException("Unsupported export format");
            }
        }

        public async Task<byte[]> ExportPaymentMethodSummary(string organizationId, string format)
        {
            var data = await _reportingService.GetPaymentMethodSummary(organizationId);
            
            switch (format.ToUpper())
            {
                case "CSV":
                    return ExportPaymentMethodSummaryToCSV(data);
                case "EXCEL":
                    return ExportPaymentMethodSummaryToExcel(data);
                case "PDF":
                    return ExportPaymentMethodSummaryToPDF(data);
                default:
                    throw new ArgumentException("Unsupported export format");
            }
        }

        public async Task<byte[]> ExportCategorySummary(string organizationId, string format)
        {
            var data = await _reportingService.GetCategorySummary(organizationId);
            
            switch (format.ToUpper())
            {
                case "CSV":
                    return ExportCategorySummaryToCSV(data);
                case "EXCEL":
                    return ExportCategorySummaryToExcel(data);
                case "PDF":
                    return ExportCategorySummaryToPDF(data);
                default:
                    throw new ArgumentException("Unsupported export format");
            }
        }

        public async Task<byte[]> ExportDailyRevenue(string organizationId, DateTime startDate, DateTime endDate, string format)
        {
            var data = await _reportingService.GetDailyRevenue(organizationId, startDate, endDate);
            
            switch (format.ToUpper())
            {
                case "CSV":
                    return ExportDailyRevenueToCSV(data);
                case "EXCEL":
                    return ExportDailyRevenueToExcel(data);
                case "PDF":
                    return ExportDailyRevenueToPDF(data);
                default:
                    throw new ArgumentException("Unsupported export format");
            }
        }

        public async Task<byte[]> ExportTopPayers(string organizationId, int limit, string format)
        {
            var data = await _reportingService.GetTopPayers(organizationId, limit);
            
            switch (format.ToUpper())
            {
                case "CSV":
                    return ExportTopPayersToCSV(data);
                case "EXCEL":
                    return ExportTopPayersToExcel(data);
                case "PDF":
                    return ExportTopPayersToPDF(data);
                default:
                    throw new ArgumentException("Unsupported export format");
            }
        }

        #region CSV Export Methods
        private byte[] ExportMonthlyRevenueToCSV(List<MonthlyRevenue> data)
        {
            var csv = new StringBuilder();
            csv.AppendLine("Month,Year,TotalAmount,ReceiptCount");
            
            foreach (var item in data)
            {
                csv.AppendLine($"{item.Month},{item.Year},{item.TotalAmount},{item.ReceiptCount}");
            }
            
            return Encoding.UTF8.GetBytes(csv.ToString());
        }

        private byte[] ExportPaymentMethodSummaryToCSV(List<PaymentMethodSummary> data)
        {
            var csv = new StringBuilder();
            csv.AppendLine("PaymentMethod,Count,TotalAmount");
            
            foreach (var item in data)
            {
                csv.AppendLine($"{item.PaymentMethod},{item.Count},{item.TotalAmount}");
            }
            
            return Encoding.UTF8.GetBytes(csv.ToString());
        }

        private byte[] ExportCategorySummaryToCSV(List<CategorySummary> data)
        {
            var csv = new StringBuilder();
            csv.AppendLine("Category,Count,TotalAmount");
            
            foreach (var item in data)
            {
                csv.AppendLine($"{item.Category},{item.Count},{item.TotalAmount}");
            }
            
            return Encoding.UTF8.GetBytes(csv.ToString());
        }

        private byte[] ExportDailyRevenueToCSV(List<DailyRevenue> data)
        {
            var csv = new StringBuilder();
            csv.AppendLine("Date,TotalAmount,ReceiptCount");
            
            foreach (var item in data)
            {
                csv.AppendLine($"{item.Date:yyyy-MM-dd},{item.TotalAmount},{item.ReceiptCount}");
            }
            
            return Encoding.UTF8.GetBytes(csv.ToString());
        }

        private byte[] ExportTopPayersToCSV(List<TopPayer> data)
        {
            var csv = new StringBuilder();
            csv.AppendLine("PayerId,PayerName,PayerEmail,TransactionCount,TotalAmount");
            
            foreach (var item in data)
            {
                csv.AppendLine($"{item.PayerId},{item.PayerName},{item.PayerEmail},{item.TransactionCount},{item.TotalAmount}");
            }
            
            return Encoding.UTF8.GetBytes(csv.ToString());
        }
        #endregion

        #region Excel Export Methods
        // In a real implementation, you would use a library like EPPlus or NPOI
        // For simplicity, we'll just return the CSV data for now
        private byte[] ExportMonthlyRevenueToExcel(List<MonthlyRevenue> data)
        {
            // Placeholder for Excel export implementation
            return ExportMonthlyRevenueToCSV(data);
        }

        private byte[] ExportPaymentMethodSummaryToExcel(List<PaymentMethodSummary> data)
        {
            // Placeholder for Excel export implementation
            return ExportPaymentMethodSummaryToCSV(data);
        }

        private byte[] ExportCategorySummaryToExcel(List<CategorySummary> data)
        {
            // Placeholder for Excel export implementation
            return ExportCategorySummaryToCSV(data);
        }

        private byte[] ExportDailyRevenueToExcel(List<DailyRevenue> data)
        {
            // Placeholder for Excel export implementation
            return ExportDailyRevenueToCSV(data);
        }

        private byte[] ExportTopPayersToExcel(List<TopPayer> data)
        {
            // Placeholder for Excel export implementation
            return ExportTopPayersToCSV(data);
        }
        #endregion

        #region PDF Export Methods
        // In a real implementation, you would use a library like iTextSharp or PdfSharp
        // For simplicity, we'll just return the CSV data for now
        private byte[] ExportMonthlyRevenueToPDF(List<MonthlyRevenue> data)
        {
            // Placeholder for PDF export implementation
            return ExportMonthlyRevenueToCSV(data);
        }

        private byte[] ExportPaymentMethodSummaryToPDF(List<PaymentMethodSummary> data)
        {
            // Placeholder for PDF export implementation
            return ExportPaymentMethodSummaryToCSV(data);
        }

        private byte[] ExportCategorySummaryToPDF(List<CategorySummary> data)
        {
            // Placeholder for PDF export implementation
            return ExportCategorySummaryToCSV(data);
        }

        private byte[] ExportDailyRevenueToPDF(List<DailyRevenue> data)
        {
            // Placeholder for PDF export implementation
            return ExportDailyRevenueToCSV(data);
        }

        private byte[] ExportTopPayersToPDF(List<TopPayer> data)
        {
            // Placeholder for PDF export implementation
            return ExportTopPayersToCSV(data);
        }
        #endregion
    }
}