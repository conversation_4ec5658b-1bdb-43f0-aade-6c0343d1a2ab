using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Final_E_Receipt.Compliance.Services;
using Final_E_Receipt.Compliance.Models;
using Final_E_Receipt.Compliance.DTOs;
using Final_E_Receipt.Reporting.Models;
using Final_E_Receipt.Services;
using Microsoft.Extensions.Logging;
using Dapper;

namespace Final_E_Receipt.Reporting.Services
{
    public class ComplianceReportingService
    {
        private readonly ComplianceCertificateService _certificateService;
        private readonly IDatabaseService _dbService;
        private readonly ILogger<ComplianceReportingService> _logger;

        public ComplianceReportingService(
            ComplianceCertificateService certificateService,
            IDatabaseService dbService,
            ILogger<ComplianceReportingService> logger)
        {
            _certificateService = certificateService;
            _dbService = dbService;
            _logger = logger;
        }

        /// <summary>
        /// Gets comprehensive compliance report for an organization using optimized SQL
        /// </summary>
        public async Task<ComplianceReport> GetOrganizationComplianceReport(string organizationId)
        {
            try
            {
                var parameters = new { OrganizationId = organizationId };
                var summaryResult = await _dbService.QueryFirstOrDefaultAsync<dynamic>(
                    "GetOrganizationComplianceSummary", parameters);

                if (summaryResult == null)
                    return new ComplianceReport { OrganizationId = organizationId, ReportDate = DateTime.Now };

                var recentCertificates = await _certificateService.GetComplianceCertificatesByOrganization(organizationId, 1, 10);
                var expiringCertificates = await _certificateService.GetExpiringCertificates(30);
                var orgExpiringCertificates = expiringCertificates.Where(c => c.OrganizationId == organizationId).ToList();

                var report = new ComplianceReport
                {
                    OrganizationId = organizationId,
                    ReportDate = DateTime.Now,
                    TotalCertificates = (int)summaryResult.TotalCertificates,
                    ActiveCertificates = (int)summaryResult.ActiveCertificates,
                    ExpiredCertificates = (int)summaryResult.ExpiredCertificates,
                    RevokedCertificates = (int)summaryResult.RevokedCertificates,
                    CertificatesIssuedThisYear = (int)summaryResult.CertificatesIssuedThisYear,
                    ComplianceScore = (double)summaryResult.ComplianceScore,
                    CertificatesByType = await GetCertificateTypeDistributionForOrganization(organizationId),
                    ExpiringCertificates = orgExpiringCertificates,
                    RecentCertificates = recentCertificates
                };

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating compliance report for organization {OrganizationId}", organizationId);
                return new ComplianceReport { OrganizationId = organizationId, ReportDate = DateTime.Now };
            }
        }

        /// <summary>
        /// Gets system-wide compliance dashboard data using optimized SQL
        /// </summary>
        public async Task<ComplianceDashboard> GetComplianceDashboard()
        {
            try
            {
                var summaryResult = await _dbService.QueryFirstOrDefaultAsync<dynamic>(
                    "GetComplianceDashboardSummary", new { });

                if (summaryResult == null)
                    return new ComplianceDashboard { GeneratedDate = DateTime.Now };

                var typeDistribution = await GetCertificateTypeDistribution();
                var monthlyStats = await GetMonthlyIssuanceStatsOptimized();
                var expiringCertificates = await _certificateService.GetExpiringCertificates(30);
                var recentCertificates = await GetRecentlyIssuedCertificates(10);

                var dashboard = new ComplianceDashboard
                {
                    GeneratedDate = DateTime.Now,
                    TotalCertificates = (int)summaryResult.TotalCertificates,
                    ActiveCertificates = (int)summaryResult.ActiveCertificates,
                    ExpiredCertificates = (int)summaryResult.ExpiredCertificates,
                    RevokedCertificates = (int)summaryResult.RevokedCertificates,
                    CertificatesExpiringSoon = (int)summaryResult.CertificatesExpiringSoon,
                    CertificatesIssuedThisMonth = (int)summaryResult.CertificatesIssuedThisMonth,
                    CertificatesIssuedThisYear = (int)summaryResult.CertificatesIssuedThisYear,
                    OrganizationsWithActiveCertificates = (int)summaryResult.OrganizationsWithActiveCertificates,
                    CertificateTypeDistribution = typeDistribution,
                    MonthlyIssuanceStats = monthlyStats,
                    TopExpiringCertificates = expiringCertificates.Take(10).ToList(),
                    RecentlyIssuedCertificates = recentCertificates
                };

                return dashboard;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating compliance dashboard");
                return new ComplianceDashboard { GeneratedDate = DateTime.Now };
            }
        }

        /// <summary>
        /// Gets certificate issuance statistics
        /// </summary>
        public async Task<CertificateIssuanceStats> GetCertificateIssuanceStats(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var searchDto = new CertificateSearchDTO 
                { 
                    PageSize = 10000 
                };
                var certificates = await _certificateService.SearchComplianceCertificates(searchDto);
                
                var filteredCertificates = certificates.Where(c => 
                    c.IssuedDate >= fromDate && c.IssuedDate <= toDate).ToList();

                var stats = new CertificateIssuanceStats
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    TotalIssued = filteredCertificates.Count,
                    IssuedByType = GroupCertificatesByType(filteredCertificates),
                    IssuedByMonth = GetMonthlyIssuanceStats(filteredCertificates),
                    AverageIssuanceTime = CalculateAverageIssuanceTime(filteredCertificates),
                    TopIssuingOrganizations = GetTopIssuingOrganizations(filteredCertificates, 10)
                };

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating certificate issuance stats");
                return new CertificateIssuanceStats { FromDate = fromDate, ToDate = toDate };
            }
        }

        /// <summary>
        /// Gets expiring certificates report
        /// </summary>
        public async Task<ExpiringCertificatesReport> GetExpiringCertificatesReport(int daysFromNow = 30)
        {
            try
            {
                var expiringCertificates = await _certificateService.GetExpiringCertificates(daysFromNow);
                
                var report = new ExpiringCertificatesReport
                {
                    ReportDate = DateTime.Now,
                    DaysFromNow = daysFromNow,
                    TotalExpiringCertificates = expiringCertificates.Count,
                    ExpiringCertificates = expiringCertificates,
                    ExpiringByType = GroupCertificatesByType(expiringCertificates),
                    ExpiringByOrganization = expiringCertificates
                        .GroupBy(c => new { c.OrganizationId, c.OrganizationName })
                        .Select(g => new OrganizationExpiryInfo
                        {
                            OrganizationId = g.Key.OrganizationId,
                            OrganizationName = g.Key.OrganizationName,
                            ExpiringCount = g.Count(),
                            EarliestExpiry = g.Min(c => c.ValidUntil),
                            LatestExpiry = g.Max(c => c.ValidUntil)
                        })
                        .OrderBy(o => o.EarliestExpiry)
                        .ToList()
                };

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating expiring certificates report");
                return new ExpiringCertificatesReport { ReportDate = DateTime.Now, DaysFromNow = daysFromNow };
            }
        }

        /// <summary>
        /// Gets compliance metrics for reporting dashboard
        /// </summary>
        public async Task<ComplianceMetrics> GetComplianceMetrics()
        {
            try
            {
                var dashboard = await GetComplianceDashboard();
                
                return new ComplianceMetrics
                {
                    TotalCertificates = dashboard.TotalCertificates,
                    ActiveCertificates = dashboard.ActiveCertificates,
                    ComplianceRate = dashboard.TotalCertificates > 0 ? 
                        (double)dashboard.ActiveCertificates / dashboard.TotalCertificates * 100 : 0,
                    ExpiryRate = dashboard.TotalCertificates > 0 ? 
                        (double)dashboard.CertificatesExpiringSoon / dashboard.TotalCertificates * 100 : 0,
                    IssuanceGrowth = CalculateIssuanceGrowth(dashboard.MonthlyIssuanceStats),
                    OrganizationCompliance = dashboard.OrganizationsWithActiveCertificates
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating compliance metrics");
                return new ComplianceMetrics();
            }
        }

        // Helper methods for optimized data access
        private async Task<Dictionary<string, int>> GetCertificateTypeDistribution()
        {
            try
            {
                var results = await _dbService.QueryAsync<dynamic>(
                    "GetCertificateTypeDistribution", new { });

                return results.ToDictionary(
                    row => (string)row.CertificateType,
                    row => (int)row.CertificateCount
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting certificate type distribution");
                return new Dictionary<string, int>();
            }
        }

        private async Task<Dictionary<string, int>> GetCertificateTypeDistributionForOrganization(string organizationId)
        {
            try
            {
                var searchDto = new CertificateSearchDTO { OrganizationId = organizationId, PageSize = 1000 };
                var certificates = await _certificateService.SearchComplianceCertificates(searchDto);
                return GroupCertificatesByType(certificates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting certificate type distribution for organization {OrganizationId}", organizationId);
                return new Dictionary<string, int>();
            }
        }

        private async Task<Dictionary<string, int>> GetMonthlyIssuanceStatsOptimized()
        {
            try
            {
                var parameters = new
                {
                    StartDate = DateTime.Now.AddMonths(-12),
                    EndDate = DateTime.Now
                };

                var results = await _dbService.QueryAsync<dynamic>(
                    "GetMonthlyIssuanceStats", parameters);

                return results.ToDictionary(
                    row => (string)row.YearMonth,
                    row => (int)row.CertificatesIssued
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting monthly issuance stats");
                return new Dictionary<string, int>();
            }
        }

        private async Task<List<ComplianceCertificate>> GetRecentlyIssuedCertificates(int count)
        {
            try
            {
                var searchDto = new CertificateSearchDTO
                {
                    PageSize = count,
                    PageNumber = 1
                };
                var certificates = await _certificateService.SearchComplianceCertificates(searchDto);
                return certificates.Where(c => c.IssuedDate >= DateTime.Now.AddDays(-7))
                    .OrderByDescending(c => c.IssuedDate)
                    .Take(count)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recently issued certificates");
                return new List<ComplianceCertificate>();
            }
        }

        // Helper methods
        private double CalculateComplianceScore(List<ComplianceCertificate> certificates)
        {
            if (!certificates.Any()) return 0;

            var activeCertificates = certificates.Count(c => !c.IsRevoked && c.ValidUntil > DateTime.Now);
            return (double)activeCertificates / certificates.Count * 100;
        }

        private Dictionary<string, int> GroupCertificatesByType(List<ComplianceCertificate> certificates)
        {
            return certificates
                .GroupBy(c => c.CertificateType)
                .ToDictionary(g => g.Key, g => g.Count());
        }

        private Dictionary<string, int> GetMonthlyIssuanceStats(List<ComplianceCertificate> certificates)
        {
            return certificates
                .GroupBy(c => c.IssuedDate.ToString("yyyy-MM"))
                .ToDictionary(g => g.Key, g => g.Count());
        }

        private double CalculateAverageIssuanceTime(List<ComplianceCertificate> certificates)
        {
            // This would calculate average time from application to issuance
            // For now, return a placeholder
            return 5.5; // days
        }

        private List<OrganizationIssuanceInfo> GetTopIssuingOrganizations(List<ComplianceCertificate> certificates, int count)
        {
            return certificates
                .GroupBy(c => new { c.OrganizationId, c.OrganizationName })
                .Select(g => new OrganizationIssuanceInfo
                {
                    OrganizationId = g.Key.OrganizationId,
                    OrganizationName = g.Key.OrganizationName,
                    CertificateCount = g.Count(),
                    LatestIssuance = g.Max(c => c.IssuedDate)
                })
                .OrderByDescending(o => o.CertificateCount)
                .Take(count)
                .ToList();
        }

        private double CalculateIssuanceGrowth(Dictionary<string, int> monthlyStats)
        {
            if (monthlyStats.Count < 2) return 0;

            var sortedMonths = monthlyStats.OrderBy(kvp => kvp.Key).ToList();
            var currentMonth = sortedMonths.Last().Value;
            var previousMonth = sortedMonths[sortedMonths.Count - 2].Value;

            if (previousMonth == 0) return currentMonth > 0 ? 100 : 0;
            return ((double)(currentMonth - previousMonth) / previousMonth) * 100;
        }
    }

    // Supporting classes for reporting
    public class ComplianceReport
    {
        public string OrganizationId { get; set; }
        public DateTime ReportDate { get; set; }
        public int TotalCertificates { get; set; }
        public int ActiveCertificates { get; set; }
        public int ExpiredCertificates { get; set; }
        public int RevokedCertificates { get; set; }
        public int CertificatesIssuedThisYear { get; set; }
        public double ComplianceScore { get; set; }
        public Dictionary<string, int> CertificatesByType { get; set; } = new Dictionary<string, int>();
        public List<ComplianceCertificate> ExpiringCertificates { get; set; } = new List<ComplianceCertificate>();
        public IEnumerable<ComplianceCertificate> RecentCertificates { get; set; } = new List<ComplianceCertificate>();
    }

    public class ComplianceDashboard
    {
        public DateTime GeneratedDate { get; set; }
        public int TotalCertificates { get; set; }
        public int ActiveCertificates { get; set; }
        public int ExpiredCertificates { get; set; }
        public int RevokedCertificates { get; set; }
        public int CertificatesExpiringSoon { get; set; }
        public int CertificatesIssuedThisMonth { get; set; }
        public int CertificatesIssuedThisYear { get; set; }
        public int OrganizationsWithActiveCertificates { get; set; }
        public Dictionary<string, int> CertificateTypeDistribution { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> MonthlyIssuanceStats { get; set; } = new Dictionary<string, int>();
        public List<ComplianceCertificate> TopExpiringCertificates { get; set; } = new List<ComplianceCertificate>();
        public List<ComplianceCertificate> RecentlyIssuedCertificates { get; set; } = new List<ComplianceCertificate>();
    }

    public class CertificateIssuanceStats
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalIssued { get; set; }
        public Dictionary<string, int> IssuedByType { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> IssuedByMonth { get; set; } = new Dictionary<string, int>();
        public double AverageIssuanceTime { get; set; }
        public List<OrganizationIssuanceInfo> TopIssuingOrganizations { get; set; } = new List<OrganizationIssuanceInfo>();
    }

    public class ExpiringCertificatesReport
    {
        public DateTime ReportDate { get; set; }
        public int DaysFromNow { get; set; }
        public int TotalExpiringCertificates { get; set; }
        public List<ComplianceCertificate> ExpiringCertificates { get; set; } = new List<ComplianceCertificate>();
        public Dictionary<string, int> ExpiringByType { get; set; } = new Dictionary<string, int>();
        public List<OrganizationExpiryInfo> ExpiringByOrganization { get; set; } = new List<OrganizationExpiryInfo>();
    }

    public class ComplianceMetrics
    {
        public int TotalCertificates { get; set; }
        public int ActiveCertificates { get; set; }
        public double ComplianceRate { get; set; }
        public double ExpiryRate { get; set; }
        public double IssuanceGrowth { get; set; }
        public int OrganizationCompliance { get; set; }
    }

    public class OrganizationExpiryInfo
    {
        public string OrganizationId { get; set; }
        public string OrganizationName { get; set; }
        public int ExpiringCount { get; set; }
        public DateTime EarliestExpiry { get; set; }
        public DateTime LatestExpiry { get; set; }
    }

    public class OrganizationIssuanceInfo
    {
        public string OrganizationId { get; set; }
        public string OrganizationName { get; set; }
        public int CertificateCount { get; set; }
        public DateTime LatestIssuance { get; set; }
    }
}

