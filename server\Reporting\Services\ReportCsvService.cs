using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Final_E_Receipt.Reporting.Models;
using Microsoft.Extensions.Logging;
using CsvHelper;
using CsvHelper.Configuration;

namespace Final_E_Receipt.Reporting.Services
{
    public class ReportCsvService
    {
        private readonly ILogger<ReportCsvService> _logger;

        public ReportCsvService(ILogger<ReportCsvService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Generates CSV for payment history report
        /// </summary>
        public async Task<byte[]> GeneratePaymentHistoryCsv(PaymentHistoryReport report)
        {
            try
            {
                using var memoryStream = new MemoryStream();
                using var writer = new StreamWriter(memoryStream, Encoding.UTF8);
                using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

                // Configure CSV settings
                csv.Context.Configuration.HasHeaderRecord = true;

                // Write header
                csv.WriteField("Payment ID");
                csv.WriteField("Transaction Reference");
                csv.WriteField("Payer Name");
                csv.WriteField("Payer Email");
                csv.WriteField("Amount");
                csv.WriteField("Currency");
                csv.WriteField("Payment Method");
                csv.WriteField("Status");
                csv.WriteField("Description");
                csv.WriteField("Category");
                csv.WriteField("Created Date");
                csv.WriteField("Completed Date");
                csv.WriteField("Receipt ID");
                csv.NextRecord();

                // Write data
                foreach (var payment in report.Payments)
                {
                    csv.WriteField(payment.PaymentId);
                    csv.WriteField(payment.TransactionReference);
                    csv.WriteField(payment.PayerName);
                    csv.WriteField(payment.PayerEmail);
                    csv.WriteField(payment.Amount.ToString("F2"));
                    csv.WriteField(payment.Currency);
                    csv.WriteField(payment.PaymentMethod);
                    csv.WriteField(payment.Status);
                    csv.WriteField(payment.Description);
                    csv.WriteField(payment.Category);
                    csv.WriteField(payment.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));
                    csv.WriteField(payment.CompletedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "");
                    csv.WriteField(payment.ReceiptId ?? "");
                    csv.NextRecord();
                }

                // Write summary section
                csv.NextRecord();
                csv.WriteField("SUMMARY");
                csv.NextRecord();
                csv.WriteField("Total Payments");
                csv.WriteField(report.Summary.TotalPayments.ToString());
                csv.NextRecord();
                csv.WriteField("Total Amount");
                csv.WriteField(report.Summary.TotalAmount.ToString("F2"));
                csv.NextRecord();
                csv.WriteField("Completed Payments");
                csv.WriteField(report.Summary.CompletedPayments.ToString());
                csv.NextRecord();
                csv.WriteField("Completed Amount");
                csv.WriteField(report.Summary.CompletedAmount.ToString("F2"));
                csv.NextRecord();
                csv.WriteField("Pending Payments");
                csv.WriteField(report.Summary.PendingPayments.ToString());
                csv.NextRecord();
                csv.WriteField("Failed Payments");
                csv.WriteField(report.Summary.FailedPayments.ToString());
                csv.NextRecord();
                csv.WriteField("Success Rate");
                csv.WriteField(report.Summary.SuccessRate.ToString("F1") + "%");

                await writer.FlushAsync();
                return memoryStream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating payment history CSV");
                throw;
            }
        }

        /// <summary>
        /// Generates CSV for outstanding balances report
        /// </summary>
        public async Task<byte[]> GenerateOutstandingBalancesCsv(OutstandingBalancesReport report)
        {
            try
            {
                using var memoryStream = new MemoryStream();
                using var writer = new StreamWriter(memoryStream, Encoding.UTF8);
                using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

                // Write header
                csv.WriteField("Organization ID");
                csv.WriteField("Organization Name");
                csv.WriteField("Payment Schedule ID");
                csv.WriteField("Description");
                csv.WriteField("Due Date");
                csv.WriteField("Original Amount");
                csv.WriteField("Paid Amount");
                csv.WriteField("Outstanding Amount");
                csv.WriteField("Days Overdue");
                csv.WriteField("Is Overdue");
                csv.WriteField("Urgency Level");
                csv.WriteField("Payment Profile");
                csv.NextRecord();

                // Write data
                foreach (var balance in report.OutstandingBalances)
                {
                    csv.WriteField(balance.OrganizationId);
                    csv.WriteField(balance.OrganizationName);
                    csv.WriteField(balance.PaymentScheduleId);
                    csv.WriteField(balance.Description);
                    csv.WriteField(balance.DueDate.ToString("yyyy-MM-dd"));
                    csv.WriteField(balance.OriginalAmount.ToString("F2"));
                    csv.WriteField(balance.PaidAmount.ToString("F2"));
                    csv.WriteField(balance.OutstandingAmount.ToString("F2"));
                    csv.WriteField(balance.DaysOverdue.ToString());
                    csv.WriteField(balance.IsOverdue ? "Yes" : "No");
                    csv.WriteField(balance.UrgencyLevel);
                    csv.WriteField(balance.PaymentProfileName);
                    csv.NextRecord();
                }

                // Write aging analysis
                csv.NextRecord();
                csv.WriteField("AGING ANALYSIS");
                csv.NextRecord();
                csv.WriteField("Age Range");
                csv.WriteField("Count");
                csv.WriteField("Total Amount");
                csv.WriteField("Percentage");
                csv.NextRecord();

                foreach (var bucket in report.AgingAnalysis)
                {
                    csv.WriteField(bucket.AgeRange);
                    csv.WriteField(bucket.Count.ToString());
                    csv.WriteField(bucket.TotalAmount.ToString("F2"));
                    csv.WriteField(bucket.Percentage.ToString("F1") + "%");
                    csv.NextRecord();
                }

                // Write summary
                csv.NextRecord();
                csv.WriteField("SUMMARY");
                csv.NextRecord();
                csv.WriteField("Total Outstanding");
                csv.WriteField(report.TotalOutstanding.ToString("F2"));
                csv.NextRecord();
                csv.WriteField("Total Overdue");
                csv.WriteField(report.TotalOverdue.ToString("F2"));
                csv.NextRecord();
                csv.WriteField("Overdue Percentage");
                csv.WriteField(report.OverduePercentage.ToString("F1") + "%");

                await writer.FlushAsync();
                return memoryStream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating outstanding balances CSV");
                throw;
            }
        }

        /// <summary>
        /// Generates CSV for revoked receipts report
        /// </summary>
        public async Task<byte[]> GenerateRevokedReceiptsCsv(RevokedReceiptsReport report)
        {
            try
            {
                using var memoryStream = new MemoryStream();
                using var writer = new StreamWriter(memoryStream, Encoding.UTF8);
                using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

                // Write header
                csv.WriteField("Receipt ID");
                csv.WriteField("Receipt Number");
                csv.WriteField("Payer Name");
                csv.WriteField("Payer Email");
                csv.WriteField("Amount");
                csv.WriteField("Currency");
                csv.WriteField("Payment Method");
                csv.WriteField("Original Date");
                csv.WriteField("Revoked Date");
                csv.WriteField("Revoked By");
                csv.WriteField("Revoked Reason");
                csv.WriteField("Reason Category");
                csv.WriteField("Days Between Issue and Revocation");
                csv.WriteField("Payment ID");
                csv.NextRecord();

                // Write data
                foreach (var receipt in report.RevokedReceipts)
                {
                    csv.WriteField(receipt.ReceiptId);
                    csv.WriteField(receipt.ReceiptNumber);
                    csv.WriteField(receipt.PayerName);
                    csv.WriteField(receipt.PayerEmail);
                    csv.WriteField(receipt.Amount.ToString("F2"));
                    csv.WriteField(receipt.Currency);
                    csv.WriteField(receipt.PaymentMethod);
                    csv.WriteField(receipt.OriginalDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    csv.WriteField(receipt.RevokedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    csv.WriteField(receipt.RevokedBy);
                    csv.WriteField(receipt.RevokedReason);
                    csv.WriteField(receipt.ReasonCategory ?? "");
                    csv.WriteField(receipt.DaysBetweenIssueAndRevocation.ToString());
                    csv.WriteField(receipt.PaymentId ?? "");
                    csv.NextRecord();
                }

                // Write reason summary
                csv.NextRecord();
                csv.WriteField("REVOCATION REASONS SUMMARY");
                csv.NextRecord();
                csv.WriteField("Reason");
                csv.WriteField("Count");
                csv.NextRecord();

                foreach (var reason in report.ReasonSummary)
                {
                    csv.WriteField(reason.Key);
                    csv.WriteField(reason.Value.ToString());
                    csv.NextRecord();
                }

                // Write summary
                csv.NextRecord();
                csv.WriteField("SUMMARY");
                csv.NextRecord();
                csv.WriteField("Total Revoked");
                csv.WriteField(report.TotalRevoked.ToString());
                csv.NextRecord();
                csv.WriteField("Total Amount");
                csv.WriteField(report.TotalAmount.ToString("F2"));
                csv.NextRecord();
                csv.WriteField("Unique Organizations");
                csv.WriteField(report.UniqueOrganizations.ToString());

                await writer.FlushAsync();
                return memoryStream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating revoked receipts CSV");
                throw;
            }
        }

        /// <summary>
        /// Generates CSV for compliance status report
        /// </summary>
        public async Task<byte[]> GenerateComplianceStatusCsv(ComplianceStatusReport report)
        {
            try
            {
                using var memoryStream = new MemoryStream();
                using var writer = new StreamWriter(memoryStream, Encoding.UTF8);
                using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

                // Write header
                csv.WriteField("Organization ID");
                csv.WriteField("Organization Name");
                csv.WriteField("Is Compliant");
                csv.WriteField("Compliance Score");
                csv.WriteField("Compliance Level");
                csv.WriteField("Active Certificates");
                csv.WriteField("Expired Certificates");
                csv.WriteField("Expiring Certificates");
                csv.WriteField("Last Certificate Issued");
                csv.WriteField("Next Expiry Date");
                csv.WriteField("Required Actions");
                csv.NextRecord();

                // Write data
                foreach (var org in report.Organizations)
                {
                    csv.WriteField(org.OrganizationId);
                    csv.WriteField(org.OrganizationName);
                    csv.WriteField(org.IsCompliant ? "Yes" : "No");
                    csv.WriteField(org.ComplianceScore.ToString("F1"));
                    csv.WriteField(org.ComplianceLevel);
                    csv.WriteField(org.ActiveCertificates.ToString());
                    csv.WriteField(org.ExpiredCertificates.ToString());
                    csv.WriteField(org.ExpiringCertificates.ToString());
                    csv.WriteField(org.LastCertificateIssued?.ToString("yyyy-MM-dd") ?? "");
                    csv.WriteField(org.NextExpiryDate?.ToString("yyyy-MM-dd") ?? "");
                    csv.WriteField(string.Join("; ", org.RequiredActions));
                    csv.NextRecord();
                }

                // Write summary
                csv.NextRecord();
                csv.WriteField("COMPLIANCE SUMMARY");
                csv.NextRecord();
                csv.WriteField("Total Organizations");
                csv.WriteField(report.Summary.TotalOrganizations.ToString());
                csv.NextRecord();
                csv.WriteField("Compliant Organizations");
                csv.WriteField(report.Summary.CompliantOrganizations.ToString());
                csv.NextRecord();
                csv.WriteField("Non-Compliant Organizations");
                csv.WriteField(report.Summary.NonCompliantOrganizations.ToString());
                csv.NextRecord();
                csv.WriteField("Overall Compliance Rate");
                csv.WriteField(report.Summary.OverallComplianceRate.ToString("F1") + "%");
                csv.NextRecord();
                csv.WriteField("Organizations with Expiring Certificates");
                csv.WriteField(report.Summary.OrganizationsWithExpiringCertificates.ToString());

                await writer.FlushAsync();
                return memoryStream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating compliance status CSV");
                throw;
            }
        }

        /// <summary>
        /// Generates a generic CSV from any collection of objects
        /// </summary>
        public async Task<byte[]> GenerateGenericCsv<T>(IEnumerable<T> data, string[] headers = null)
        {
            try
            {
                using var memoryStream = new MemoryStream();
                using var writer = new StreamWriter(memoryStream, Encoding.UTF8);
                using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

                // Write records
                await csv.WriteRecordsAsync(data);
                await writer.FlushAsync();
                
                return memoryStream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating generic CSV");
                throw;
            }
        }
    }
}
