import React, { useState } from 'react';
import { Plus, Edit, X } from 'lucide-react';

interface PaymentType {
  id: number;
  name: string;
  description: string;
  status: 'Active' | 'Inactive';
}

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: (e?: React.MouseEvent<HTMLButtonElement>) => void;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md';
  disabled?: boolean;
}

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText: string;
  confirmVariant?: 'primary' | 'danger';
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  title, 
  message, 
  confirmText, 
  confirmVariant = 'primary' 
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="fixed inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-[#045024]">{title}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={20} />
          </button>
        </div>
        
        <p className="text-gray-600 mb-6">{message}</p>
        
        <div className="flex gap-3 justify-end">
          <ActionButton onClick={onClose} variant="secondary">
            Cancel
          </ActionButton>
          <ActionButton onClick={onConfirm} variant={confirmVariant}>
            {confirmText}
          </ActionButton>
        </div>
      </div>
    </div>
  );
};

interface PaymentTypeFormProps {
  initialData?: Partial<PaymentType>;
  onSubmit: (data: { name: string; description: string }) => void;
  onCancel: () => void;
  isEdit?: boolean;
}

const ActionButton: React.FC<ActionButtonProps> = ({ 
  children, 
  onClick, 
  variant = "primary", 
  size = "md", 
  disabled = false 
}) => {
  const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: disabled 
      ? "bg-gray-400 text-gray-200 cursor-not-allowed" 
      : "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: disabled 
      ? "bg-gray-200 text-gray-400 cursor-not-allowed" 
      : "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: disabled 
      ? "bg-gray-400 text-gray-200 cursor-not-allowed" 
      : "bg-red-500 hover:bg-red-600 text-white"
  };

  return (
    <button
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]}`}
    >
      {children}
    </button>
  );
};

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="fixed inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-[#045024]">{title}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={20} />
          </button>
        </div>
        {children}
      </div>
    </div>
  );
};

const PaymentTypeForm: React.FC<PaymentTypeFormProps> = ({ 
  initialData, 
  onSubmit, 
  onCancel, 
  isEdit = false 
}) => {
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    description: initialData?.description || ''
  });

  const [errors, setErrors] = useState<{ name?: string; description?: string }>({});

  const validateForm = () => {
    const newErrors: { name?: string; description?: string } = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
          Name *
        </label>
        <input
          type="text"
          id="name"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleSubmit()}
          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] ${
            errors.name ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="Enter payment type name"
        />
        {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
          Description *
        </label>
        <textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          rows={3}
          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#2aa45c] ${
            errors.description ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="Enter payment type description"
        />
        {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
      </div>

      <div className="flex gap-3 pt-4">
        <ActionButton onClick={handleSubmit} variant="primary">
          {isEdit ? 'Update Payment Type' : 'Create Payment Type'}
        </ActionButton>
        <ActionButton onClick={onCancel} variant="secondary">
          Cancel
        </ActionButton>
      </div>
    </div>
  );
};

const PaymentTypes: React.FC = () => {
  const [paymentTypes, setPaymentTypes] = useState<PaymentType[]>([
    { id: 1, name: "Annual License Fee", description: "Yearly licensing payment", status: "Active" },
    { id: 2, name: "Compliance Fee", description: "Regulatory compliance payment", status: "Active" },
    { id: 3, name: "Registration Fee", description: "Initial registration payment", status: "Inactive" }
  ]);

  const [modals, setModals] = useState({
    create: false,
    edit: false,
    confirmation: false
  });

  const [editingPaymentType, setEditingPaymentType] = useState<PaymentType | null>(null);
  const [confirmationAction, setConfirmationAction] = useState<{
    type: 'activate' | 'deactivate';
    paymentType: PaymentType;
  } | null>(null);

  const openCreateModal = () => {
    setModals(prev => ({ ...prev, create: true }));
  };

  const openEditModal = (paymentType: PaymentType) => {
    setEditingPaymentType(paymentType);
    setModals(prev => ({ ...prev, edit: true }));
  };

  const closeModals = () => {
    setModals({ create: false, edit: false, confirmation: false });
    setEditingPaymentType(null);
    setConfirmationAction(null);
  };

  const handleCreatePaymentType = (data: { name: string; description: string }) => {
    const newPaymentType: PaymentType = {
      id: Math.max(...paymentTypes.map(p => p.id)) + 1,
      name: data.name,
      description: data.description,
      status: 'Active'
    };
    
    setPaymentTypes(prev => [...prev, newPaymentType]);
    closeModals();
  };

  const handleEditPaymentType = (data: { name: string; description: string }) => {
    if (!editingPaymentType) return;
    
    setPaymentTypes(prev => prev.map(p => 
      p.id === editingPaymentType.id 
        ? { ...p, name: data.name, description: data.description }
        : p
    ));
    closeModals();
  };

  const handleStatusChange = (paymentType: PaymentType, action: 'activate' | 'deactivate') => {
    setConfirmationAction({ type: action, paymentType });
    setModals(prev => ({ ...prev, confirmation: true }));
  };

  const confirmStatusChange = () => {
    if (!confirmationAction) return;
    
    const { type, paymentType } = confirmationAction;
    const newStatus = type === 'activate' ? 'Active' : 'Inactive';
    
    setPaymentTypes(prev => prev.map(p => 
      p.id === paymentType.id ? { ...p, status: newStatus as 'Active' | 'Inactive' } : p
    ));
    
    closeModals();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-[#045024]">Payment Types</h2>
        <ActionButton onClick={openCreateModal}>
          <Plus size={16} />
          Create New Payment Type
        </ActionButton>
      </div>
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-[#dddeda]">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Name</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Description</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-[#045024] uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {paymentTypes.map(type => (
                <tr key={type.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">{type.name}</td>
                  <td className="px-6 py-4 text-gray-700">{type.description}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      type.status === 'Active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {type.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex gap-2">
                      <ActionButton 
                        variant="secondary" 
                        size="sm"
                        onClick={() => openEditModal(type)}
                      >
                        <Edit size={14} />
                        Edit
                      </ActionButton>
                      
                      {type.status === 'Active' ? (
                        <ActionButton 
                          variant="danger" 
                          size="sm"
                          onClick={() => handleStatusChange(type, 'deactivate')}
                        >
                          <X size={14} />
                          Deactivate
                        </ActionButton>
                      ) : (
                        <ActionButton 
                          variant="primary" 
                          size="sm"
                          onClick={() => handleStatusChange(type, 'activate')}
                        >
                          <Plus size={14} />
                          Activate
                        </ActionButton>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={modals.confirmation}
        onClose={closeModals}
        onConfirm={confirmStatusChange}
        title={confirmationAction?.type === 'activate' ? 'Activate Payment Type' : 'Deactivate Payment Type'}
        message={
          confirmationAction?.type === 'activate'
            ? `Are you sure you want to activate "${confirmationAction?.paymentType.name}"? This payment type will become available for use.`
            : `Are you sure you want to deactivate "${confirmationAction?.paymentType.name}"? This payment type will no longer be available for new transactions.`
        }
        confirmText={confirmationAction?.type === 'activate' ? 'Activate' : 'Deactivate'}
        confirmVariant={confirmationAction?.type === 'activate' ? 'primary' : 'danger'}
      />

      {/* Create Modal */}
      <Modal
        isOpen={modals.create}
        onClose={closeModals}
        title="Create New Payment Type"
      >
        <PaymentTypeForm
          onSubmit={handleCreatePaymentType}
          onCancel={closeModals}
        />
      </Modal>

      {/* Edit Modal */}
      <Modal
        isOpen={modals.edit}
        onClose={closeModals}
        title="Edit Payment Type"
      >
        {editingPaymentType && (
          <PaymentTypeForm
            initialData={editingPaymentType}
            onSubmit={handleEditPaymentType}
            onCancel={closeModals}
            isEdit={true}
          />
        )}
      </Modal>
    </div>
  );
};

export default PaymentTypes;