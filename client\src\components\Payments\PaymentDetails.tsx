import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  ArrowLeft,
  DollarSign,
  Calendar,
  Building2,
  User,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  AlertCircle,
  FileText,
  Activity,
} from 'lucide-react';
import { usePaymentApi, useOrganizationApi } from '../../hooks/api';
import { useAuth } from '../../contexts/AuthContext';
import type { Payment, Organization } from '../../hooks/api';

interface PaymentDetailsProps {
  payment: Payment;
  setActiveTab: (tab: string) => void;
  setSelectedPayment: (payment: Payment | null) => void;
  onPaymentUpdated?: () => void;
}

const PaymentDetails: React.FC<PaymentDetailsProps> = ({
  payment,
  setActiveTab,
  setSelectedPayment,
  onPaymentUpdated,
}) => {
  const { 
    loading, 
    error, 
    acknowledgePayment, 
    approvePayment, 
    completePayment, 
    rejectPayment 
  } = usePaymentApi();
  const { getAllOrganizations } = useOrganizationApi();
  const { user } = useAuth();
  
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [activeSection, setActiveSection] = useState<'overview' | 'activity'>('overview');
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');

  // Role-based permissions (Updated to match backend)
  const canAcknowledgePayments = user?.role === 'FINANCE_OFFICER';
  const canApprovePayments = user?.role === 'SENIOR_FINANCE_OFFICER';
  const canCompletePayments = ['FINANCE_OFFICER', 'SENIOR_FINANCE_OFFICER'].includes(user?.role || '');
  const canRejectPayments = ['FINANCE_OFFICER', 'SENIOR_FINANCE_OFFICER'].includes(user?.role || '');

  useEffect(() => {
    loadOrganization();
  }, [payment.organizationId]);

  const loadOrganization = async () => {
    const result = await getAllOrganizations();
    if (result) {
      const org = result.find(o => o.id === payment.organizationId);
      setOrganization(org || null);
    }
  };



  const handleBack = () => {
    setSelectedPayment(null);
    setActiveTab('payments');
  };

  const handleAcknowledge = async () => {
    const result = await acknowledgePayment(payment.id);
    if (result) {
      onPaymentUpdated?.();
    }
  };

  const handleApprove = async () => {
    const result = await approvePayment(payment.id);
    if (result) {
      onPaymentUpdated?.();
    }
  };

  const handleComplete = async () => {
    const result = await completePayment(payment.id);
    if (result) {
      onPaymentUpdated?.();
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      return;
    }

    const result = await rejectPayment(payment.id, { reason: rejectReason });
    if (result) {
      setShowRejectModal(false);
      setRejectReason('');
      onPaymentUpdated?.();
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'Pending': { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      'Proof_Uploaded': { color: 'bg-blue-100 text-blue-800', icon: Eye },
      'Acknowledged': { color: 'bg-blue-100 text-blue-800', icon: CheckCircle },
      'Approved': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      'Completed': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      'Rejected': { color: 'bg-red-100 text-red-800', icon: XCircle },
      'Failed': { color: 'bg-red-100 text-red-800', icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>
        <Icon className="w-4 h-4 mr-2" />
        {status}
      </span>
    );
  };

  const getAvailableActions = () => {
    const actions = [];

    switch (payment.status) {
      case 'Proof_Uploaded':
        if (canAcknowledgePayments) {
          actions.push({
            label: 'Acknowledge',
            action: handleAcknowledge,
            color: 'bg-blue-600 hover:bg-blue-700',
            icon: Eye,
          });
        }
        break;

      case 'Acknowledged':
        if (canApprovePayments) {
          actions.push({
            label: 'Approve',
            action: handleApprove,
            color: 'bg-green-600 hover:bg-green-700',
            icon: CheckCircle,
          });
        }
        break;

      case 'Approved':
        if (canCompletePayments) {
          actions.push({
            label: 'Complete',
            action: handleComplete,
            color: 'bg-green-600 hover:bg-green-700',
            icon: CheckCircle,
          });
        }
        break;
    }

    // Reject action available for proof uploaded, acknowledged, and approved payments
    if (['Proof_Uploaded', 'Acknowledged', 'Approved'].includes(payment.status) && canRejectPayments) {
      actions.push({
        label: 'Reject',
        action: () => setShowRejectModal(true),
        color: 'bg-red-600 hover:bg-red-700',
        icon: XCircle,
      });
    }

    return actions;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleBack}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <ArrowLeft size={24} />
          </button>
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
              <CreditCard className="h-6 w-6 text-[#2aa45c]" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-[#045024]">{payment.transactionReference}</h2>
              <div className="flex items-center space-x-2">
                {getStatusBadge(payment.status)}
                <span className="text-gray-500">•</span>
                <span className="text-sm text-gray-500">
                  Created {new Date(payment.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {getAvailableActions().map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              disabled={loading}
              className={`${action.color} text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2 disabled:opacity-50`}
            >
              <action.icon size={16} />
              <span>{action.label}</span>
            </button>
          ))}

        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={20} />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: CreditCard },
            { id: 'activity', label: 'Activity', icon: Activity },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveSection(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeSection === tab.id
                  ? 'border-[#2aa45c] text-[#2aa45c]'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content Sections */}
      {activeSection === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Payment Information */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Transaction Reference</label>
                  <p className="mt-1 text-sm text-gray-900">{payment.transactionReference}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Payment Method</label>
                  <p className="mt-1 text-sm text-gray-900">{payment.paymentMethod}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Amount</label>
                  <div className="flex items-center mt-1">
                    <DollarSign className="h-4 w-4 text-gray-400 mr-1" />
                    <p className="text-sm font-medium text-gray-900">
                      {payment.amount.toLocaleString()} {payment.currency}
                    </p>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Created Date</label>
                  <div className="flex items-center mt-1">
                    <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                    <p className="text-sm text-gray-900">
                      {new Date(payment.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                {payment.category && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Category</label>
                    <p className="mt-1 text-sm text-gray-900">{payment.category}</p>
                  </div>
                )}
                {payment.description && (
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-500">Description</label>
                    <p className="mt-1 text-sm text-gray-900">{payment.description}</p>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Payer Information</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
                    <User className="h-5 w-5 text-[#2aa45c]" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{payment.payerName}</p>
                    <p className="text-sm text-gray-500">{payment.payerEmail}</p>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Payer ID</label>
                  <p className="mt-1 text-sm text-gray-900">{payment.payerId}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Organization</h3>
              {organization ? (
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
                    <Building2 className="h-5 w-5 text-[#2aa45c]" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{organization.name}</p>
                    <p className="text-sm text-gray-500">{organization.email}</p>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-gray-500">Loading organization details...</p>
              )}
            </div>
          </div>

          {/* Status and Dates */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Status Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Current Status</label>
                  <div className="mt-1">
                    {getStatusBadge(payment.status)}
                  </div>
                </div>
                
                {payment.acknowledgedBy && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Acknowledged By</label>
                    <p className="mt-1 text-sm text-gray-900">{payment.acknowledgedBy}</p>
                    {payment.acknowledgedDate && (
                      <p className="text-xs text-gray-500">
                        {new Date(payment.acknowledgedDate).toLocaleString()}
                      </p>
                    )}
                  </div>
                )}

                {payment.approvedBy && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Approved By</label>
                    <p className="mt-1 text-sm text-gray-900">{payment.approvedBy}</p>
                    {payment.approvedDate && (
                      <p className="text-xs text-gray-500">
                        {new Date(payment.approvedDate).toLocaleString()}
                      </p>
                    )}
                  </div>
                )}

                {payment.completedAt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Completed Date</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(payment.completedAt).toLocaleString()}
                    </p>
                  </div>
                )}

                {payment.rejectedBy && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Rejected By</label>
                    <p className="mt-1 text-sm text-gray-900">{payment.rejectedBy}</p>
                    {payment.rejectedDate && (
                      <p className="text-xs text-gray-500">
                        {new Date(payment.rejectedDate).toLocaleString()}
                      </p>
                    )}
                    {payment.rejectedReason && (
                      <div className="mt-2">
                        <label className="block text-xs font-medium text-gray-500">Reason</label>
                        <p className="text-sm text-red-600">{payment.rejectedReason}</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">System Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Created By</label>
                  <p className="mt-1 text-sm text-gray-900">{payment.createdBy}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Created Date</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(payment.createdAt).toLocaleDateString()}
                  </p>
                </div>
                {payment.updatedAt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Last Updated</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(payment.updatedAt).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeSection === 'activity' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Activity</h3>
          <div className="text-center py-8">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Activity tracking coming soon</p>
          </div>
        </div>
      )}

      {/* Reject Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <XCircle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4 text-center">Reject Payment</h3>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reason for rejection *
                </label>
                <textarea
                  value={rejectReason}
                  onChange={(e) => setRejectReason(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  placeholder="Please provide a reason for rejecting this payment..."
                />
              </div>
              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowRejectModal(false)}
                  className="flex-1 px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300"
                >
                  Cancel
                </button>
                <button
                  onClick={handleReject}
                  disabled={!rejectReason.trim() || loading}
                  className="flex-1 px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
                >
                  {loading ? 'Rejecting...' : 'Reject Payment'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentDetails;
