using System;
using System.Threading.Tasks;
using Final_E_Receipt.Documents.Services;

namespace Final_E_Receipt.Documents.Examples
{
    /// <summary>
    /// Example showing how the dynamic template system works for any organization
    /// </summary>
    public class DynamicTemplateExample
    {
        private readonly CertificateTemplateService _templateService;
        private readonly BrandedDocumentService _documentService;

        public DynamicTemplateExample(
            CertificateTemplateService templateService,
            BrandedDocumentService documentService)
        {
            _templateService = templateService;
            _documentService = documentService;
        }

        /// <summary>
        /// Example: Get available templates for Joint Revenue Board
        /// </summary>
        public async Task GetJointRevenueBoardTemplatesExample()
        {
            // Works with any organization ID from database
            var organizationId = "jrb-001"; // Joint Revenue Board
            var certificateType = "ANNUAL_LICENSE";

            // Get available templates dynamically
            var templates = await _templateService.GetAvailableTemplates(organizationId, certificateType);

            Console.WriteLine($"Available templates for {organizationId}:");
            foreach (var template in templates)
            {
                Console.WriteLine($"- {template.Name} ({template.Orientation})");
                Console.WriteLine($"  ID: {template.Id}");
                Console.WriteLine($"  Default: {template.IsDefault}");
                Console.WriteLine($"  Preview: {template.PreviewImageUrl}");
                Console.WriteLine();
            }

            /*
            Expected output (if templates exist):
            Available templates for jrb-001:
            - Annual License (Landscape) (Landscape)
              ID: jrb-001-annual_license-landscape
              Default: True
              Preview: /templates/previews/jrb-001-annual_license-landscape-preview.png

            - Annual License (Portrait) (Portrait)
              ID: jrb-001-annual_license-portrait
              Default: False
              Preview: /templates/previews/jrb-001-annual_license-portrait-preview.png
            */
        }

        /// <summary>
        /// Example: Works with any organization ID
        /// </summary>
        public async Task WorksWithAnyOrganizationExample()
        {
            // Examples with different organization IDs
            var organizations = new[]
            {
                "jrb-001",      // Joint Revenue Board
                "revenue-123",  // Another revenue organization
                "tax-office-456", // Tax office
                "customs-789"   // Customs department
            };

            foreach (var orgId in organizations)
            {
                Console.WriteLine($"\n=== Templates for {orgId} ===");
                
                var templates = await _templateService.GetAvailableTemplates(orgId, "TAX_CLEARANCE");
                
                if (templates.Count > 0)
                {
                    Console.WriteLine("Organization-specific templates found:");
                    foreach (var template in templates)
                    {
                        Console.WriteLine($"- {template.Name}");
                    }
                }
                else
                {
                    Console.WriteLine("No organization-specific templates. Will use default templates.");
                }
            }
        }

        /// <summary>
        /// Example: Template file structure for any organization
        /// </summary>
        public void ShowTemplateFileStructure()
        {
            Console.WriteLine("Template File Structure (Dynamic):");
            Console.WriteLine();
            Console.WriteLine("wwwroot/templates/");
            Console.WriteLine("├── receipts/");
            Console.WriteLine("│   ├── {organizationId}-receipt-template.png");
            Console.WriteLine("│   └── default-receipt-template.png");
            Console.WriteLine("└── certificates/");
            Console.WriteLine("    ├── {organizationId}-annual_license-template.png          # Portrait");
            Console.WriteLine("    ├── {organizationId}-annual_license-landscape-template.png # Landscape");
            Console.WriteLine("    ├── {organizationId}-tax_clearance-template.png");
            Console.WriteLine("    ├── {organizationId}-quarterly_compliance-template.png");
            Console.WriteLine("    └── default-certificate-template.png");
            Console.WriteLine();
            Console.WriteLine("Examples for Joint Revenue Board (jrb-001):");
            Console.WriteLine("├── jrb-001-receipt-template.png");
            Console.WriteLine("├── jrb-001-annual_license-template.png");
            Console.WriteLine("├── jrb-001-annual_license-landscape-template.png");
            Console.WriteLine("├── jrb-001-tax_clearance-template.png");
            Console.WriteLine("└── jrb-001-quarterly_compliance-template.png");
            Console.WriteLine();
            Console.WriteLine("Examples for another organization (org-123):");
            Console.WriteLine("├── org-123-receipt-template.png");
            Console.WriteLine("├── org-123-annual_license-template.png");
            Console.WriteLine("└── org-123-tax_clearance-template.png");
        }

        /// <summary>
        /// Example: How template selection works
        /// </summary>
        public async Task TemplateSelectionWorkflowExample()
        {
            var organizationId = "jrb-001";
            var certificateType = "ANNUAL_LICENSE";

            Console.WriteLine("Template Selection Workflow:");
            Console.WriteLine("1. User selects certificate type: ANNUAL_LICENSE");
            Console.WriteLine("2. System checks for available templates...");

            var templates = await _templateService.GetAvailableTemplates(organizationId, certificateType);

            Console.WriteLine($"3. Found {templates.Count} templates:");
            foreach (var template in templates)
            {
                var defaultText = template.IsDefault ? " [DEFAULT]" : "";
                Console.WriteLine($"   - {template.Name}{defaultText}");
            }

            Console.WriteLine("4. User selects preferred template");
            Console.WriteLine("5. System generates certificate with selected template");

            // Example of generating with specific template
            if (templates.Count > 0)
            {
                var selectedTemplate = templates[0];
                Console.WriteLine($"6. Selected: {selectedTemplate.Name}");
                Console.WriteLine($"   Template ID: {selectedTemplate.Id}");
                Console.WriteLine($"   Orientation: {selectedTemplate.Orientation}");
            }
        }

        /// <summary>
        /// Example: Fallback behavior when no templates exist
        /// </summary>
        public async Task FallbackBehaviorExample()
        {
            var nonExistentOrgId = "unknown-org-999";
            var certificateType = "ANNUAL_LICENSE";

            Console.WriteLine($"Testing fallback for non-existent organization: {nonExistentOrgId}");

            var templates = await _templateService.GetAvailableTemplates(nonExistentOrgId, certificateType);

            Console.WriteLine($"Available templates: {templates.Count}");
            foreach (var template in templates)
            {
                Console.WriteLine($"- {template.Name} (Fallback: {template.OrganizationName == "Default"})");
            }

            Console.WriteLine();
            Console.WriteLine("System behavior:");
            Console.WriteLine("1. Looks for organization-specific templates");
            Console.WriteLine("2. If none found, provides default templates");
            Console.WriteLine("3. User can still select template orientation");
            Console.WriteLine("4. Certificate generation uses default-certificate-template.png");
        }
    }
}

/*
KEY BENEFITS OF DYNAMIC SYSTEM:

✅ No Hardcoded Organizations
- Works with any organization ID from database
- No code changes needed for new organizations

✅ File-Based Template Discovery
- Automatically detects available templates
- Supports multiple orientations per certificate type

✅ Intelligent Defaults
- Annual licenses default to landscape
- Tax clearances default to portrait
- Fallback to default templates when needed

✅ Scalable Architecture
- Add new organization: just add template files
- Add new certificate type: no code changes
- Template positions standardized across all organizations

✅ User Choice with Fallbacks
- Users can select from available templates
- System provides sensible defaults
- Graceful degradation when templates missing

TEMPLATE FILE NAMING CONVENTION:
- Receipts: {organizationId}-receipt-template.png
- Certificates: {organizationId}-{certificateType}-template.png
- Landscape: {organizationId}-{certificateType}-landscape-template.png
- Default: default-certificate-template.png
*/
