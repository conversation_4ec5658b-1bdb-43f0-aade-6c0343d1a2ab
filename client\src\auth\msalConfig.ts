import { PublicClientApplication, LogLevel } from "@azure/msal-browser";

// MSAL configuration - following Microsoft's official pattern
export const msalConfig = {
  auth: {
    clientId: import.meta.env.VITE_AZURE_AD_CLIENT_ID || "faa92dd9-5270-4024-8e1f-f2960dd20c6c",
    authority: `https://login.microsoftonline.com/${import.meta.env.VITE_AZURE_AD_TENANT_ID || "338f11d3-2f98-4d8a-a630-e67350f5ede4"}`,
    redirectUri: window.location.origin,
    postLogoutRedirectUri: window.location.origin,
    navigateToLoginRequestUrl: true,
  },
  cache: {
    cacheLocation: "sessionStorage",
    storeAuthStateInCookie: false,
  },
  system: {
    loggerOptions: {
      loggerCallback: (level: LogLevel, message: string, containsPii: boolean) => {
        if (containsPii) return;
        if (level === LogLevel.Error) {
          console.error(message);
        }
      },
      logLevel: LogLevel.Error,
    },
  },
};

// Login request - following Microsoft's pattern
export const loginRequest = {
  scopes: [
    "openid",
    "profile",
    "email",
    "api://faa92dd9-5270-4024-8e1f-f2960dd20c6c/access_as_user"
  ],
};

// MSAL instance
export const msalInstance = new PublicClientApplication(msalConfig);
