import React, { useState, useEffect, useCallback } from 'react';
import {
  CreditCard,
  Save,
  X,
  DollarSign,
  Building2,
  FileText,
  AlertCircle,
  CheckCircle,
  User,
} from 'lucide-react';
import { usePaymentApi, useOrganizationApi } from '../../hooks/api';
import { useAuth } from '../../contexts/AuthContext';
import type { CreatePaymentDTO, Organization } from '../../hooks/api';

interface CreatePaymentProps {
  setActiveTab: (tab: string) => void;
  onPaymentCreated?: () => void;
}

const CreatePayment: React.FC<CreatePaymentProps> = ({
  setActiveTab,
  onPaymentCreated,
}) => {
  const { loading, error, createPayment } = usePaymentApi();
  const { getAllOrganizations } = useOrganizationApi();
  const { user } = useAuth();

  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [formData, setFormData] = useState<CreatePaymentDTO>({
    payerId: '',
    payerName: '',
    payerEmail: '',
    amount: 0,
    currency: 'NGN',
    paymentMethod: 'BANK_TRANSFER',
    description: '',
    paymentTypeId: '',
    organizationId: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showSuccess, setShowSuccess] = useState(false);

  // Role-based permissions (Updated to match backend)
  const canCreatePayments = user?.role === 'JTB_ADMIN';

  useEffect(() => {
    if (!canCreatePayments) {
      setActiveTab('payments');
      return;
    }

    loadOrganizations();
  }, [canCreatePayments, setActiveTab, loadOrganizations]);

  const loadOrganizations = useCallback(async () => {
    const result = await getAllOrganizations();
    if (result) {
      setOrganizations(result.filter(org => org.isActive));
    }
  }, [getAllOrganizations]);



  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.organizationId) {
      errors.organizationId = 'Organization is required';
    }

    if (!formData.payerId.trim()) {
      errors.payerId = 'Payer ID is required';
    }

    if (!formData.payerName.trim()) {
      errors.payerName = 'Payer name is required';
    }

    if (!formData.payerEmail.trim()) {
      errors.payerEmail = 'Payer email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.payerEmail)) {
      errors.payerEmail = 'Please enter a valid email address';
    }

    if (!formData.amount || formData.amount <= 0) {
      errors.amount = 'Amount must be greater than 0';
    }

    if (!formData.paymentMethod) {
      errors.paymentMethod = 'Payment method is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'amount' ? parseFloat(value) || 0 : value,
    }));

    // Clear error for this field when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const result = await createPayment(formData);
    if (result) {
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        onPaymentCreated?.();
        setActiveTab('payments');
      }, 2000);
    }
  };

  const handleCancel = () => {
    setActiveTab('payments');
  };

  if (!canCreatePayments) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">You don't have permission to create payments</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="h-8 w-8 rounded-lg bg-[#2aa45c] bg-opacity-10 flex items-center justify-center">
            <CreditCard className="h-5 w-5 text-[#2aa45c]" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-[#045024]">Create Payment</h2>
            <p className="text-gray-600">Add a new payment to the system</p>
          </div>
        </div>
        <button
          onClick={handleCancel}
          className="text-gray-500 hover:text-gray-700 transition-colors"
        >
          <X size={24} />
        </button>
      </div>

      {/* Success Message */}
      {showSuccess && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircle className="text-green-500 mr-2" size={20} />
            <span className="text-green-700">Payment created successfully!</span>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={20} />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Form */}
      <div className="bg-white rounded-lg shadow-md">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Payer Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Payer Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="payerId" className="block text-sm font-medium text-gray-700 mb-2">
                  Payer ID *
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    id="payerId"
                    name="payerId"
                    value={formData.payerId}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                      formErrors.payerId ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Enter payer ID"
                  />
                </div>
                {formErrors.payerId && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.payerId}</p>
                )}
              </div>

              <div>
                <label htmlFor="payerName" className="block text-sm font-medium text-gray-700 mb-2">
                  Payer Name *
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    id="payerName"
                    name="payerName"
                    value={formData.payerName}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                      formErrors.payerName ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Enter payer name"
                  />
                </div>
                {formErrors.payerName && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.payerName}</p>
                )}
              </div>

              <div>
                <label htmlFor="payerEmail" className="block text-sm font-medium text-gray-700 mb-2">
                  Payer Email *
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="email"
                    id="payerEmail"
                    name="payerEmail"
                    value={formData.payerEmail}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                      formErrors.payerEmail ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="<EMAIL>"
                  />
                </div>
                {formErrors.payerEmail && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.payerEmail}</p>
                )}
              </div>

              <div>
                <label htmlFor="organizationId" className="block text-sm font-medium text-gray-700 mb-2">
                  Organization *
                </label>
                <div className="relative">
                  <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <select
                    id="organizationId"
                    name="organizationId"
                    value={formData.organizationId}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                      formErrors.organizationId ? 'border-red-300' : 'border-gray-300'
                    }`}
                  >
                    <option value="">Select Organization</option>
                    {organizations.map((org) => (
                      <option key={org.id} value={org.id}>
                        {org.name}
                      </option>
                    ))}
                  </select>
                </div>
                {formErrors.organizationId && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.organizationId}</p>
                )}
              </div>

            </div>
          </div>

          {/* Payment Details */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

              <div>
                <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 mb-2">
                  Payment Method *
                </label>
                <div className="relative">
                  <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <select
                    id="paymentMethod"
                    name="paymentMethod"
                    value={formData.paymentMethod}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  >
                    <option value="BANK_TRANSFER">Bank Transfer</option>
                    <option value="CREDIT_CARD">Credit Card</option>
                    <option value="DEBIT_CARD">Debit Card</option>
                    <option value="CASH">Cash</option>
                    <option value="CHECK">Check</option>
                    <option value="ONLINE">Online Payment</option>
                  </select>
                </div>
                {formErrors.paymentMethod && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.paymentMethod}</p>
                )}
              </div>

              <div>
                <label htmlFor="paymentTypeId" className="block text-sm font-medium text-gray-700 mb-2">
                  Payment Type
                </label>
                <div className="relative">
                  <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    id="paymentTypeId"
                    name="paymentTypeId"
                    value={formData.paymentTypeId}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                    placeholder="Payment Type ID"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Amount and Currency Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Amount and Currency</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                  Amount *
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="number"
                    id="amount"
                    name="amount"
                    value={formData.amount}
                    onChange={handleInputChange}
                    step="0.01"
                    min="0"
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent ${
                      formErrors.amount ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="0.00"
                  />
                </div>
                {formErrors.amount && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.amount}</p>
                )}
              </div>

              <div>
                <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-2">
                  Currency *
                </label>
                <select
                  id="currency"
                  name="currency"
                  value={formData.currency}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                >
                  <option value="NGN">NGN (Nigerian Naira)</option>
                  <option value="USD">USD (US Dollar)</option>
                  <option value="EUR">EUR (Euro)</option>
                  <option value="GBP">GBP (British Pound)</option>
                </select>
              </div>
            </div>
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
              placeholder="Enter payment description or notes"
            />
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="bg-[#2aa45c] text-white px-6 py-2 rounded-lg hover:bg-[#076934] transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Creating...</span>
                </>
              ) : (
                <>
                  <Save size={16} />
                  <span>Create Payment</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreatePayment;
