import { useState, useCallback } from 'react';
import { apiService } from '../../services/apiService';

// Types for Files API
export interface FileInfo {
  id: string;
  fileName: string;
  originalFileName: string;
  fileSize: number;
  mimeType: string;
  uploadedBy: string;
  uploadedAt: string;
  organizationId?: string;
  paymentId?: string;
  receiptId?: string;
  tags?: string[];
  description?: string;
  isActive: boolean;
}

export interface UploadFileDTO {
  file: File;
  organizationId?: string;
  paymentId?: string;
  receiptId?: string;
  tags?: string[];
  description?: string;
}

export interface FileSearchFilters {
  organizationId?: string;
  paymentId?: string;
  receiptId?: string;
  mimeType?: string;
  uploadedBy?: string;
  uploadedDateFrom?: string;
  uploadedDateTo?: string;
  tags?: string[];
}

export interface BulkUploadResult {
  successCount: number;
  failureCount: number;
  uploadedFiles: FileInfo[];
  errors: Array<{
    fileName: string;
    error: string;
  }>;
}

// Files API Hooks
export const useFilesApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // File CRUD endpoints
  const getAllFiles = useCallback(() => 
    handleApiCall(() => apiService.get<FileInfo[]>('/File')), [handleApiCall]);

  const getFileById = useCallback((id: string) => 
    handleApiCall(() => apiService.get<FileInfo>(`/File/${id}`)), [handleApiCall]);

  const deleteFile = useCallback((id: string) => 
    handleApiCall(() => apiService.delete(`/File/${id}`)), [handleApiCall]);

  // File upload with progress tracking
  const uploadFile = useCallback(async (uploadData: UploadFileDTO): Promise<FileInfo | null> => {
    try {
      setLoading(true);
      setError(null);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('file', uploadData.file);
      
      if (uploadData.organizationId) formData.append('organizationId', uploadData.organizationId);
      if (uploadData.paymentId) formData.append('paymentId', uploadData.paymentId);
      if (uploadData.receiptId) formData.append('receiptId', uploadData.receiptId);
      if (uploadData.description) formData.append('description', uploadData.description);
      if (uploadData.tags) {
        uploadData.tags.forEach(tag => formData.append('tags', tag));
      }

      // Custom fetch with progress tracking
      const response = await fetch(`${apiService.baseURL}/File/upload`, {
        method: 'POST',
        headers: await apiService.getAuthHeaders(),
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      setUploadProgress(100);
      return result;
    } catch (err: any) {
      setError(err.message || 'Upload failed');
      return null;
    } finally {
      setLoading(false);
      setTimeout(() => setUploadProgress(0), 1000); // Reset progress after 1 second
    }
  }, []);

  // Bulk file upload
  const uploadMultipleFiles = useCallback(async (files: File[], organizationId?: string): Promise<BulkUploadResult | null> => {
    try {
      setLoading(true);
      setError(null);
      setUploadProgress(0);

      const formData = new FormData();
      files.forEach(file => formData.append('files', file));
      if (organizationId) formData.append('organizationId', organizationId);

      const response = await fetch(`${apiService.baseURL}/File/upload-multiple`, {
        method: 'POST',
        headers: await apiService.getAuthHeaders(),
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Bulk upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      setUploadProgress(100);
      return result;
    } catch (err: any) {
      setError(err.message || 'Bulk upload failed');
      return null;
    } finally {
      setLoading(false);
      setTimeout(() => setUploadProgress(0), 1000);
    }
  }, []);

  // File download
  const downloadFile = useCallback(async (id: string): Promise<Blob | null> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${apiService.baseURL}/File/${id}/download`, {
        method: 'GET',
        headers: await apiService.getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`Download failed: ${response.statusText}`);
      }

      return await response.blob();
    } catch (err: any) {
      setError(err.message || 'Download failed');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // File filtering and search
  const getFilesByOrganization = useCallback((organizationId: string) => 
    handleApiCall(() => apiService.get<FileInfo[]>(`/File/organization/${organizationId}`)), [handleApiCall]);

  const getFilesByPayment = useCallback((paymentId: string) => 
    handleApiCall(() => apiService.get<FileInfo[]>(`/File/payment/${paymentId}`)), [handleApiCall]);

  const getFilesByReceipt = useCallback((receiptId: string) => 
    handleApiCall(() => apiService.get<FileInfo[]>(`/File/receipt/${receiptId}`)), [handleApiCall]);

  const searchFiles = useCallback((filters: FileSearchFilters) => 
    handleApiCall(() => apiService.post<FileInfo[]>('/File/search', filters)), [handleApiCall]);

  // File metadata management
  const updateFileMetadata = useCallback((id: string, metadata: Partial<Pick<FileInfo, 'tags' | 'description'>>) => 
    handleApiCall(() => apiService.put<FileInfo>(`/File/${id}/metadata`, metadata)), [handleApiCall]);

  const getFileMetadata = useCallback((id: string) => 
    handleApiCall(() => apiService.get<FileInfo>(`/File/${id}/metadata`)), [handleApiCall]);

  return {
    loading,
    error,
    uploadProgress,
    getAllFiles,
    getFileById,
    uploadFile,
    uploadMultipleFiles,
    downloadFile,
    deleteFile,
    getFilesByOrganization,
    getFilesByPayment,
    getFilesByReceipt,
    searchFiles,
    updateFileMetadata,
    getFileMetadata,
  };
};
