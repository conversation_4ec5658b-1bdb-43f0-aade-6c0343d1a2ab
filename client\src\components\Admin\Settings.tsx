import React, { useState, useEffect } from "react";
import { FileText, Download, Filter } from "lucide-react";

const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:5248/api";

interface AuditLog {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  action: string;
  entityType: string;
  entityId: string;
  oldValues?: string;
  newValues?: string;
  additionalDetails?: string;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
  organizationId: string;
}

interface AuditFilters {
  userId?: string;
  action?: string;
  entityType?: string;
  organizationId?: string;
  startDate?: string;
  endDate?: string;
  pageSize: number;
  pageNumber: number;
}

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: "primary" | "secondary" | "danger";
  size?: "sm" | "md";
  disabled?: boolean;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  children,
  onClick,
  variant = "primary",
  size = "md",
  disabled = false,
}) => {
  const baseClasses =
    "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white",
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${
        disabled ? "opacity-50 cursor-not-allowed" : ""
      }`}
    >
      {children}
    </button>
  );
};

const UserSettings: React.FC = () => {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<AuditFilters>({
    pageSize: 50,
    pageNumber: 1,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [availableActions, setAvailableActions] = useState<string[]>([]);
  const [availableEntityTypes, setAvailableEntityTypes] = useState<string[]>(
    []
  );

  // Fetch audit logs from API
  const fetchAuditLogs = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("auth_token");

      const queryParams = new URLSearchParams();
      if (filters.userId) queryParams.append("userId", filters.userId);
      if (filters.action) queryParams.append("action", filters.action);
      if (filters.entityType)
        queryParams.append("entityType", filters.entityType);
      if (filters.organizationId)
        queryParams.append("organizationId", filters.organizationId);
      if (filters.startDate) queryParams.append("startDate", filters.startDate);
      if (filters.endDate) queryParams.append("endDate", filters.endDate);
      queryParams.append("pageSize", filters.pageSize.toString());
      queryParams.append("pageNumber", filters.pageNumber.toString());

      const response = await fetch(`${API_BASE_URL}/Audit?${queryParams}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch audit logs: ${response.status}`);
      }

      const data = await response.json();
      setAuditLogs(data);
      setError(null);
    } catch (err) {
      console.error("Error fetching audit logs:", err);
      setError(
        err instanceof Error ? err.message : "Failed to load audit logs"
      );
    } finally {
      setLoading(false);
    }
  };

  // Fetch audit metadata (actions and entity types)
  const fetchAuditMetadata = async () => {
    try {
      const token = localStorage.getItem("auth_token");

      const response = await fetch(`${API_BASE_URL}/Audit/metadata`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const metadata = await response.json();
        setAvailableActions(metadata.actions?.map((a: any) => a.value) || []);
        setAvailableEntityTypes(
          metadata.entityTypes?.map((e: any) => e.value) || []
        );
      }
    } catch (err) {
      console.error("Error fetching audit metadata:", err);
    }
  };

  // Export audit logs
  const handleExportLogs = async () => {
    try {
      const token = localStorage.getItem("auth_token");

      const queryParams = new URLSearchParams();
      if (filters.userId) queryParams.append("userId", filters.userId);
      if (filters.action) queryParams.append("action", filters.action);
      if (filters.entityType)
        queryParams.append("entityType", filters.entityType);
      if (filters.organizationId)
        queryParams.append("organizationId", filters.organizationId);
      if (filters.startDate) queryParams.append("startDate", filters.startDate);
      if (filters.endDate) queryParams.append("endDate", filters.endDate);

      const response = await fetch(
        `${API_BASE_URL}/Audit/export?${queryParams}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to export audit logs");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `audit_logs_${new Date().toISOString().split("T")[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error("Error exporting audit logs:", err);
      setError("Failed to export audit logs");
    }
  };

  // Apply filters
  const handleApplyFilters = () => {
    setFilters((prev) => ({ ...prev, pageNumber: 1 }));
    fetchAuditLogs();
  };

  // Clear filters
  const handleClearFilters = () => {
    setFilters({
      pageSize: 50,
      pageNumber: 1,
    });
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  // Get action badge color
  const getActionBadgeColor = (action: string) => {
    if (action.includes("LOGIN") || action.includes("CREATED"))
      return "bg-green-100 text-green-800";
    if (action.includes("UPDATED") || action.includes("CHANGED"))
      return "bg-blue-100 text-blue-800";
    if (action.includes("DELETED") || action.includes("REVOKED"))
      return "bg-red-100 text-red-800";
    return "bg-gray-100 text-gray-800";
  };

  useEffect(() => {
    fetchAuditLogs();
    fetchAuditMetadata();
  }, []);

  useEffect(() => {
    if (filters.pageNumber > 1 || Object.keys(filters).length > 2) {
      fetchAuditLogs();
    }
  }, [filters]);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-[#045024] mb-4">
            Reports Assignment
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select User
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent">
                <option>John Doe (Admin)</option>
                <option>Jane Smith (Manager)</option>
                <option>Sarah Wilson (Manager)</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Available Reports
              </label>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {[
                  "Payment Summary Report",
                  "User Activity Report",
                  "Financial Overview",
                  "Audit Trail Report",
                  "System Performance",
                ].map((report) => (
                  <label key={report} className="flex items-center">
                    <input type="checkbox" className="mr-2" />
                    <span className="text-sm">{report}</span>
                  </label>
                ))}
              </div>
            </div>
            <ActionButton>
              <FileText size={16} />
              Assign Reports
            </ActionButton>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-[#045024]">
            System Audit Logs
          </h3>
          <div className="flex items-center gap-2">
            <ActionButton
              size="sm"
              variant="secondary"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter size={16} />
              Filters
            </ActionButton>
            <ActionButton
              size="sm"
              variant="secondary"
              onClick={handleExportLogs}
            >
              <Download size={16} />
              Export Logs
            </ActionButton>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  User ID
                </label>
                <input
                  type="text"
                  value={filters.userId || ""}
                  onChange={(e) =>
                    setFilters((prev) => ({ ...prev, userId: e.target.value }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                  placeholder="Filter by user ID"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Action
                </label>
                <select
                  value={filters.action || ""}
                  onChange={(e) =>
                    setFilters((prev) => ({ ...prev, action: e.target.value }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                >
                  <option value="">All Actions</option>
                  {availableActions.map((action) => (
                    <option key={action} value={action}>
                      {action}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Entity Type
                </label>
                <select
                  value={filters.entityType || ""}
                  onChange={(e) =>
                    setFilters((prev) => ({
                      ...prev,
                      entityType: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                >
                  <option value="">All Entity Types</option>
                  {availableEntityTypes.map((entityType) => (
                    <option key={entityType} value={entityType}>
                      {entityType}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  value={filters.startDate || ""}
                  onChange={(e) =>
                    setFilters((prev) => ({
                      ...prev,
                      startDate: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  value={filters.endDate || ""}
                  onChange={(e) =>
                    setFilters((prev) => ({ ...prev, endDate: e.target.value }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Page Size
                </label>
                <select
                  value={filters.pageSize}
                  onChange={(e) =>
                    setFilters((prev) => ({
                      ...prev,
                      pageSize: parseInt(e.target.value),
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                >
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
              </div>
            </div>
            <div className="flex items-center gap-2 mt-4">
              <ActionButton size="sm" onClick={handleApplyFilters}>
                Apply Filters
              </ActionButton>
              <ActionButton
                size="sm"
                variant="secondary"
                onClick={handleClearFilters}
              >
                Clear Filters
              </ActionButton>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <div className="text-gray-500">Loading audit logs...</div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {/* Audit Logs Table */}
        {!loading && !error && (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    User
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Action
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Entity
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Details
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    IP Address
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Timestamp
                  </th>
                </tr>
              </thead>
              <tbody>
                {auditLogs.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="py-8 text-center text-gray-500">
                      No audit logs found
                    </td>
                  </tr>
                ) : (
                  auditLogs.map((log) => (
                    <tr
                      key={log.id}
                      className="border-b border-gray-100 hover:bg-gray-50"
                    >
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium">{log.userName}</div>
                          <div className="text-sm text-gray-500">
                            {log.userEmail}
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getActionBadgeColor(
                            log.action
                          )}`}
                        >
                          {log.action}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium">{log.entityType}</div>
                          <div className="text-sm text-gray-500">
                            {log.entityId}
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-600 max-w-xs truncate">
                        {log.additionalDetails || "No details"}
                      </td>
                      <td className="py-3 px-4 text-sm">{log.ipAddress}</td>
                      <td className="py-3 px-4 text-sm text-gray-500">
                        {formatTimestamp(log.timestamp)}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {auditLogs.length > 0 && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-gray-500">
              Showing {auditLogs.length} results (Page {filters.pageNumber})
            </div>
            <div className="flex items-center gap-2">
              <ActionButton
                size="sm"
                variant="secondary"
                disabled={filters.pageNumber === 1}
                onClick={() =>
                  setFilters((prev) => ({
                    ...prev,
                    pageNumber: prev.pageNumber - 1,
                  }))
                }
              >
                Previous
              </ActionButton>
              <ActionButton
                size="sm"
                variant="secondary"
                disabled={auditLogs.length < filters.pageSize}
                onClick={() =>
                  setFilters((prev) => ({
                    ...prev,
                    pageNumber: prev.pageNumber + 1,
                  }))
                }
              >
                Next
              </ActionButton>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserSettings;
