import { useState, useCallback } from 'react';
import { apiService } from '../../services/apiService';

// Types for Payment API (Updated to match backend)
export interface Payment {
  id: string;
  payerId: string;
  payerName: string;
  payerEmail: string;
  amount: number;
  currency: string;
  transactionReference: string;
  paymentMethod: string;
  status: string; // Pending, Proof_Uploaded, Acknowledged, Approved, Rejected, Completed, Failed
  description?: string;
  paymentTypeId?: string;
  createdAt: string;
  completedAt?: string;
  receiptId?: string;
  organizationId: string;
  paymentScheduleId?: string;
  proofFileId?: string;
  proofFileStatus?: string;
  acknowledgedBy?: string;
  acknowledgedDate?: string;
  acknowledgedNotes?: string;
  approvedBy?: string;
  approvedDate?: string;
  approvedNotes?: string;
  rejectedBy?: string;
  rejectedDate?: string;
  rejectedReason?: string;
  rejectedNotes?: string;
}

export interface CreatePaymentDTO {
  payerId: string;
  payerName: string;
  payerEmail: string;
  amount: number;
  currency?: string;
  paymentMethod: string;
  description?: string;
  paymentTypeId?: string;
  organizationId: string;
}

export interface PaymentProfile {
  id: string;
  name: string;
  description?: string;
  organizationId: string;
  paymentTypes: string[];
  isActive: boolean;
  createdBy: string;
  createdAt: string;
}

export interface CreatePaymentProfileDTO {
  name: string;
  description?: string;
  organizationId: string;
  paymentTypes: string[];
}

export interface PaymentSchedule {
  id: string;
  organizationId: string;
  paymentProfileId: string;
  name: string;
  frequency: 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY';
  amount: number;
  startDate: string;
  endDate?: string;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
}

export interface CreatePaymentScheduleDTO {
  organizationId: string;
  paymentProfileId: string;
  name: string;
  frequency: 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY';
  amount: number;
  startDate: string;
  endDate?: string;
}

// Payment Approval DTOs (matching backend)
export interface UpdatePaymentStatusDTO {
  status: string;
}

export interface AcknowledgePaymentDTO {
  notes?: string;
}

export interface ApprovePaymentDTO {
  notes?: string;
}

export interface RejectPaymentDTO {
  reason: string;
  notes?: string;
}

// Payment API Hooks
export const usePaymentApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Payment CRUD endpoints (Updated to match backend)
  const getAllPayments = useCallback(() =>
    handleApiCall(() => apiService.get<Payment[]>('/payments')), [handleApiCall]);

  const getPaymentById = useCallback((id: string) =>
    handleApiCall(() => apiService.get<Payment>(`/payments/${id}`)), [handleApiCall]);

  const createPayment = useCallback((data: CreatePaymentDTO) =>
    handleApiCall(() => apiService.post<Payment>('/payments', data)), [handleApiCall]);

  const updatePaymentStatus = useCallback((id: string, data: UpdatePaymentStatusDTO) =>
    handleApiCall(() => apiService.put<Payment>(`/payments/${id}/status`, data)), [handleApiCall]);

  const completePayment = useCallback((id: string) =>
    handleApiCall(() => apiService.put<Payment>(`/payments/${id}/complete`)), [handleApiCall]);

  // Payment approval workflow (Updated to match backend)
  const acknowledgePayment = useCallback((id: string, data?: AcknowledgePaymentDTO) =>
    handleApiCall(() => apiService.post(`/payment-approval/${id}/acknowledge`, data || {})), [handleApiCall]);

  const approvePayment = useCallback((id: string, data?: ApprovePaymentDTO) =>
    handleApiCall(() => apiService.post(`/payment-approval/${id}/approve`, data || {})), [handleApiCall]);

  const rejectPayment = useCallback((id: string, data: RejectPaymentDTO) =>
    handleApiCall(() => apiService.post(`/payment-approval/${id}/reject`, data)), [handleApiCall]);

  // Payment filtering and queries (Updated to match backend)
  const getPaymentsByPayer = useCallback((payerId: string) =>
    handleApiCall(() => apiService.get<Payment[]>(`/payments/payer/${payerId}`)), [handleApiCall]);

  const getPaymentsByOrganization = useCallback((organizationId: string) =>
    handleApiCall(() => apiService.get<Payment[]>(`/payments/organization/${organizationId}`)), [handleApiCall]);

  const getPaymentsByStatus = useCallback((status: string) =>
    handleApiCall(() => apiService.get<Payment[]>(`/payments/status/${status}`)), [handleApiCall]);

  // Payment approval queries
  const getPaymentsPendingAcknowledgment = useCallback((organizationId?: string) =>
    handleApiCall(() => apiService.get<Payment[]>('/payment-approval/pending-acknowledgment', organizationId ? { organizationId } : {})), [handleApiCall]);

  const getPaymentsPendingApproval = useCallback((organizationId?: string) =>
    handleApiCall(() => apiService.get<Payment[]>('/payment-approval/pending-approval', organizationId ? { organizationId } : {})), [handleApiCall]);

  const getPaymentApprovalHistory = useCallback((paymentId: string) =>
    handleApiCall(() => apiService.get<Payment>(`/payment-approval/${paymentId}/history`)), [handleApiCall]);

  return {
    loading,
    error,
    getAllPayments,
    getPaymentById,
    createPayment,
    updatePaymentStatus,
    completePayment,
    acknowledgePayment,
    approvePayment,
    rejectPayment,
    getPaymentsByPayer,
    getPaymentsByOrganization,
    getPaymentsByStatus,
    getPaymentsPendingAcknowledgment,
    getPaymentsPendingApproval,
    getPaymentApprovalHistory,
  };
};

// Payment Profile API Hooks
export const usePaymentProfileApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Payment Profile endpoints
  const getAllPaymentProfiles = useCallback(() =>
    handleApiCall(() => apiService.get<PaymentProfile[]>('/PaymentProfile')), [handleApiCall]);

  const getPaymentProfileById = useCallback((id: string) =>
    handleApiCall(() => apiService.get<PaymentProfile>(`/PaymentProfile/${id}`)), [handleApiCall]);

  const createPaymentProfile = useCallback((data: CreatePaymentProfileDTO) =>
    handleApiCall(() => apiService.post<PaymentProfile>('/PaymentProfile', data)), [handleApiCall]);

  const updatePaymentProfile = useCallback((id: string, data: Partial<PaymentProfile>) =>
    handleApiCall(() => apiService.put<PaymentProfile>(`/PaymentProfile/${id}`, data)), [handleApiCall]);

  const deletePaymentProfile = useCallback((id: string) =>
    handleApiCall(() => apiService.delete(`/PaymentProfile/${id}`)), [handleApiCall]);

  const getPaymentProfilesByOrganization = useCallback((organizationId: string) =>
    handleApiCall(() => apiService.get<PaymentProfile[]>(`/PaymentProfile/organization/${organizationId}`)), [handleApiCall]);

  return {
    loading,
    error,
    getAllPaymentProfiles,
    getPaymentProfileById,
    createPaymentProfile,
    updatePaymentProfile,
    deletePaymentProfile,
    getPaymentProfilesByOrganization,
  };
};

// Payment Schedule API Hooks
export const usePaymentScheduleApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Payment Schedule endpoints
  const getAllPaymentSchedules = useCallback(() =>
    handleApiCall(() => apiService.get<PaymentSchedule[]>('/PaymentSchedule')), [handleApiCall]);

  const getPaymentScheduleById = useCallback((id: string) =>
    handleApiCall(() => apiService.get<PaymentSchedule>(`/PaymentSchedule/${id}`)), [handleApiCall]);

  const createPaymentSchedule = useCallback((data: CreatePaymentScheduleDTO) =>
    handleApiCall(() => apiService.post<PaymentSchedule>('/PaymentSchedule', data)), [handleApiCall]);

  const updatePaymentSchedule = useCallback((id: string, data: Partial<PaymentSchedule>) =>
    handleApiCall(() => apiService.put<PaymentSchedule>(`/PaymentSchedule/${id}`, data)), [handleApiCall]);

  const deletePaymentSchedule = useCallback((id: string) =>
    handleApiCall(() => apiService.delete(`/PaymentSchedule/${id}`)), [handleApiCall]);

  const getPaymentSchedulesByOrganization = useCallback((organizationId: string) =>
    handleApiCall(() => apiService.get<PaymentSchedule[]>(`/PaymentSchedule/organization/${organizationId}`)), [handleApiCall]);

  const generateSchedulePayments = useCallback((scheduleId: string) =>
    handleApiCall(() => apiService.post(`/PaymentSchedule/${scheduleId}/generate`)), [handleApiCall]);

  return {
    loading,
    error,
    getAllPaymentSchedules,
    getPaymentScheduleById,
    createPaymentSchedule,
    updatePaymentSchedule,
    deletePaymentSchedule,
    getPaymentSchedulesByOrganization,
    generateSchedulePayments,
  };
};
