using System;

namespace Final_E_Receipt.Payments.Models
{
    public class Payment
    {
        public string Id { get; set; }
        public string PayerId { get; set; }
        public string PayerName { get; set; }
        public string PayerEmail { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "NGN";
        public string TransactionReference { get; set; }
        public string PaymentMethod { get; set; }
        public string Status { get; set; } // Pending, Proof_Uploaded, Acknowledged, Approved, Rejected, Completed, Failed
        public string Description { get; set; }
        public string PaymentTypeId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string ReceiptId { get; set; }
        public string OrganizationId { get; set; }
        public string ProofFileId { get; set; }

        // Approval Workflow Fields
        public string AcknowledgedBy { get; set; }      // Finance Officer who acknowledged the payment
        public DateTime? AcknowledgedDate { get; set; } // When payment was acknowledged
        public string AcknowledgedNotes { get; set; }  // Notes from Finance Officer

        public string ApprovedBy { get; set; }          // Senior Finance Officer who approved the payment
        public DateTime? ApprovedDate { get; set; }     // When payment was approved
        public string ApprovalNotes { get; set; }       // Notes from Senior Finance Officer

        public string RejectedBy { get; set; }          // User who rejected the payment
        public DateTime? RejectedDate { get; set; }     // When payment was rejected
        public string RejectionReason { get; set; }     // Reason for rejection

        // Payment Schedule Link
        public string PaymentScheduleId { get; set; }   // Link to payment schedule if applicable
    }
}



