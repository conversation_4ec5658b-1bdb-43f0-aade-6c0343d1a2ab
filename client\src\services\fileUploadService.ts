// File Upload Service
const API_BASE_URL = 'http://localhost:5000/api';

export interface FileUploadResponse {
  id: string;
  fileName: string;
  originalFileName: string;
  contentType: string;
  fileSize: number;
  relatedEntityType: string;
  relatedEntityId: string;
  createdAt: string;
  description?: string;
  category?: string;
  isScanned: boolean;
  scanResult?: string;
}

export interface FileListItem {
  id: string;
  fileName: string;
  originalFileName: string;
  contentType: string;
  fileSize: number;
  relatedEntityType: string;
  relatedEntityId: string;
  createdAt: string;
  description?: string;
  category?: string;
  isScanned: boolean;
  scanResult?: string;
  uploadedBy: string;
}

class FileUploadService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('authToken');
    return {
      'Authorization': `Bearer ${token}`,
    };
  }

  async uploadPaymentProof(paymentId: string, file: File, description?: string): Promise<FileUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    if (description) {
      formData.append('description', description);
    }

    const response = await fetch(`${API_BASE_URL}/payments/${paymentId}/proof/upload`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to upload payment proof');
    }

    return response.json();
  }

  async getPaymentProofFiles(paymentId: string): Promise<FileListItem[]> {
    const response = await fetch(`${API_BASE_URL}/payments/${paymentId}/proof`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get payment proof files');
    }

    return response.json();
  }

  async downloadPaymentProof(paymentId: string, fileId: string): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/payments/${paymentId}/proof/${fileId}/download`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to download payment proof');
    }

    return response.blob();
  }

  async deletePaymentProof(paymentId: string, fileId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/payments/${paymentId}/proof/${fileId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete payment proof');
    }
  }

  async validatePaymentProof(paymentId: string, fileId: string, isValid: boolean, comments?: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/payments/${paymentId}/proof/${fileId}/validate`, {
      method: 'POST',
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        isValid,
        comments,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to validate payment proof');
    }
  }

  async uploadFile(file: File, relatedEntityType: string, relatedEntityId: string, description?: string, category?: string): Promise<FileUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('relatedEntityType', relatedEntityType);
    formData.append('relatedEntityId', relatedEntityId);
    if (description) {
      formData.append('description', description);
    }
    if (category) {
      formData.append('category', category);
    }

    const response = await fetch(`${API_BASE_URL}/file/upload`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to upload file');
    }

    return response.json();
  }

  async getFilesByEntity(entityType: string, entityId: string): Promise<FileListItem[]> {
    const response = await fetch(`${API_BASE_URL}/file/entity/${entityType}/${entityId}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get files');
    }

    return response.json();
  }

  async downloadFile(fileId: string): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/file/download/${fileId}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to download file');
    }

    return response.blob();
  }

  async deleteFile(fileId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/file/${fileId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete file');
    }
  }

  // Utility methods
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  isValidFileType(file: File): boolean {
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    return allowedTypes.includes(file.type);
  }

  isValidFileSize(file: File, maxSizeInMB: number = 10): boolean {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return file.size <= maxSizeInBytes;
  }

  validateFile(file: File): { isValid: boolean; error?: string } {
    if (!this.isValidFileType(file)) {
      return {
        isValid: false,
        error: 'Invalid file type. Please upload PDF, JPG, PNG, DOC, or DOCX files only.'
      };
    }

    if (!this.isValidFileSize(file)) {
      return {
        isValid: false,
        error: 'File size exceeds 10MB limit.'
      };
    }

    return { isValid: true };
  }
}

export const fileUploadService = new FileUploadService();
