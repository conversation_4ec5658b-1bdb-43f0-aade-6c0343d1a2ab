-- Compliance Certificates Table
CREATE TABLE ComplianceCertificates (
    Id NVARCHAR(50) PRIMARY KEY,
    CertificateNumber NVARCHAR(100) UNIQUE NOT NULL,
    OrganizationId NVARCHAR(50) NOT NULL,
    OrganizationName NVARCHAR(255) NOT NULL,
    PaymentProfileId NVARCHAR(50) NOT NULL,
    PaymentProfileName NVARCHAR(255) NOT NULL,
    CertificateType NVARCHAR(100) NOT NULL,
    Status NVARCHAR(50) NOT NULL DEFAULT 'PENDING',
    TotalAmount DECIMAL(18,2) NOT NULL,
    Currency NVARCHAR(10) NOT NULL DEFAULT 'NGN',
    ValidFrom DATETIME NOT NULL,
    ValidUntil DATETIME NOT NULL,
    IssuedDate DATETIME NOT NULL DEFAULT GETDATE(),
    IssuedBy NVARCHAR(50) NOT NULL,
    Description NVARCHAR(1000),
    Terms NVARCHAR(2000),
    CertificatePdfFileId NVARCHAR(50),
    IsRevoked BIT NOT NULL DEFAULT 0,
    RevokedDate DATETIME NULL,
    RevokedBy NVARCHAR(50) NULL,
    RevokedReason NVARCHAR(500) NULL,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50) NOT NULL,
    UpdatedAt DATETIME NULL,
    UpdatedBy NVARCHAR(50) NULL,
    ComplianceYear NVARCHAR(10),
    CompliancePeriod NVARCHAR(20),
    RegulatoryBody NVARCHAR(255),
    LicenseCategory NVARCHAR(100),
    Notes NVARCHAR(1000)
);

-- Create indexes for better performance
CREATE INDEX IX_ComplianceCertificates_Organization ON ComplianceCertificates(OrganizationId);
CREATE INDEX IX_ComplianceCertificates_PaymentProfile ON ComplianceCertificates(PaymentProfileId);
CREATE INDEX IX_ComplianceCertificates_Type ON ComplianceCertificates(CertificateType);
CREATE INDEX IX_ComplianceCertificates_Status ON ComplianceCertificates(Status);
CREATE INDEX IX_ComplianceCertificates_ValidPeriod ON ComplianceCertificates(ValidFrom, ValidUntil);
CREATE INDEX IX_ComplianceCertificates_ComplianceYear ON ComplianceCertificates(ComplianceYear, CompliancePeriod);

-- Stored Procedures for Compliance Certificates
CREATE PROCEDURE CreateComplianceCertificate
    @Id NVARCHAR(50),
    @CertificateNumber NVARCHAR(100),
    @OrganizationId NVARCHAR(50),
    @OrganizationName NVARCHAR(255),
    @PaymentProfileId NVARCHAR(50),
    @PaymentProfileName NVARCHAR(255),
    @CertificateType NVARCHAR(100),
    @TotalAmount DECIMAL(18,2),
    @Currency NVARCHAR(10),
    @ValidFrom DATETIME,
    @ValidUntil DATETIME,
    @IssuedBy NVARCHAR(50),
    @Description NVARCHAR(1000),
    @Terms NVARCHAR(2000),
    @CreatedBy NVARCHAR(50),
    @ComplianceYear NVARCHAR(10),
    @CompliancePeriod NVARCHAR(20),
    @RegulatoryBody NVARCHAR(255),
    @LicenseCategory NVARCHAR(100),
    @Notes NVARCHAR(1000)
AS
BEGIN
    INSERT INTO ComplianceCertificates (
        Id, CertificateNumber, OrganizationId, OrganizationName, PaymentProfileId, PaymentProfileName,
        CertificateType, TotalAmount, Currency, ValidFrom, ValidUntil, IssuedBy, Description, Terms,
        CreatedBy, ComplianceYear, CompliancePeriod, RegulatoryBody, LicenseCategory, Notes
    )
    VALUES (
        @Id, @CertificateNumber, @OrganizationId, @OrganizationName, @PaymentProfileId, @PaymentProfileName,
        @CertificateType, @TotalAmount, @Currency, @ValidFrom, @ValidUntil, @IssuedBy, @Description, @Terms,
        @CreatedBy, @ComplianceYear, @CompliancePeriod, @RegulatoryBody, @LicenseCategory, @Notes
    )
    
    SELECT * FROM ComplianceCertificates WHERE Id = @Id
END;

CREATE PROCEDURE GetComplianceCertificateById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM ComplianceCertificates WHERE Id = @Id
END;

CREATE PROCEDURE GetComplianceCertificatesByOrganization
    @OrganizationId NVARCHAR(50),
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT * FROM ComplianceCertificates 
    WHERE OrganizationId = @OrganizationId
    ORDER BY CreatedAt DESC
    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END;

CREATE PROCEDURE SearchComplianceCertificates
    @OrganizationId NVARCHAR(50) = NULL,
    @CertificateType NVARCHAR(100) = NULL,
    @Status NVARCHAR(50) = NULL,
    @ComplianceYear NVARCHAR(10) = NULL,
    @CompliancePeriod NVARCHAR(20) = NULL,
    @ValidFromStart DATETIME = NULL,
    @ValidFromEnd DATETIME = NULL,
    @ValidUntilStart DATETIME = NULL,
    @ValidUntilEnd DATETIME = NULL,
    @IsRevoked BIT = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT * FROM ComplianceCertificates 
    WHERE (@OrganizationId IS NULL OR OrganizationId = @OrganizationId)
    AND (@CertificateType IS NULL OR CertificateType = @CertificateType)
    AND (@Status IS NULL OR Status = @Status)
    AND (@ComplianceYear IS NULL OR ComplianceYear = @ComplianceYear)
    AND (@CompliancePeriod IS NULL OR CompliancePeriod = @CompliancePeriod)
    AND (@ValidFromStart IS NULL OR ValidFrom >= @ValidFromStart)
    AND (@ValidFromEnd IS NULL OR ValidFrom <= @ValidFromEnd)
    AND (@ValidUntilStart IS NULL OR ValidUntil >= @ValidUntilStart)
    AND (@ValidUntilEnd IS NULL OR ValidUntil <= @ValidUntilEnd)
    AND (@IsRevoked IS NULL OR IsRevoked = @IsRevoked)
    ORDER BY CreatedAt DESC
    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END;

CREATE PROCEDURE UpdateComplianceCertificateStatus
    @Id NVARCHAR(50),
    @Status NVARCHAR(50),
    @UpdatedBy NVARCHAR(50)
AS
BEGIN
    UPDATE ComplianceCertificates 
    SET Status = @Status, UpdatedAt = GETDATE(), UpdatedBy = @UpdatedBy
    WHERE Id = @Id
    
    SELECT * FROM ComplianceCertificates WHERE Id = @Id
END;

CREATE PROCEDURE UpdateComplianceCertificatePdfFile
    @Id NVARCHAR(50),
    @CertificatePdfFileId NVARCHAR(50),
    @UpdatedBy NVARCHAR(50)
AS
BEGIN
    UPDATE ComplianceCertificates 
    SET CertificatePdfFileId = @CertificatePdfFileId, 
        Status = 'GENERATED',
        UpdatedAt = GETDATE(), 
        UpdatedBy = @UpdatedBy
    WHERE Id = @Id
    
    SELECT * FROM ComplianceCertificates WHERE Id = @Id
END;

CREATE PROCEDURE RevokeComplianceCertificate
    @Id NVARCHAR(50),
    @RevokedBy NVARCHAR(50),
    @RevokedReason NVARCHAR(500)
AS
BEGIN
    UPDATE ComplianceCertificates 
    SET IsRevoked = 1, 
        RevokedDate = GETDATE(), 
        RevokedBy = @RevokedBy, 
        RevokedReason = @RevokedReason,
        Status = 'REVOKED',
        UpdatedAt = GETDATE(),
        UpdatedBy = @RevokedBy
    WHERE Id = @Id
    
    SELECT * FROM ComplianceCertificates WHERE Id = @Id
END;

CREATE PROCEDURE GetComplianceCertificatesByPaymentProfile
    @PaymentProfileId NVARCHAR(50),
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT * FROM ComplianceCertificates 
    WHERE PaymentProfileId = @PaymentProfileId
    ORDER BY CreatedAt DESC
    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END;

CREATE PROCEDURE GetExpiringCertificates
    @DaysFromNow INT = 30
AS
BEGIN
    DECLARE @ExpiryDate DATETIME = DATEADD(DAY, @DaysFromNow, GETDATE())
    
    SELECT * FROM ComplianceCertificates 
    WHERE ValidUntil <= @ExpiryDate 
    AND IsRevoked = 0 
    AND Status IN ('GENERATED', 'ISSUED')
    ORDER BY ValidUntil ASC
END;

-- Function to generate certificate number
CREATE FUNCTION GenerateCertificateNumber(
    @CertificateType NVARCHAR(100),
    @ComplianceYear NVARCHAR(10),
    @OrganizationId NVARCHAR(50)
)
RETURNS NVARCHAR(100)
AS
BEGIN
    DECLARE @TypePrefix NVARCHAR(10)
    DECLARE @Counter INT
    DECLARE @CertificateNumber NVARCHAR(100)
    
    -- Set prefix based on certificate type
    SET @TypePrefix = CASE 
        WHEN @CertificateType = 'ANNUAL_LICENSE' THEN 'AL'
        WHEN @CertificateType = 'QUARTERLY_COMPLIANCE' THEN 'QC'
        WHEN @CertificateType = 'TAX_CLEARANCE' THEN 'TC'
        ELSE 'CC'
    END
    
    -- Get next counter for this type and year
    SELECT @Counter = ISNULL(MAX(CAST(RIGHT(CertificateNumber, 4) AS INT)), 0) + 1
    FROM ComplianceCertificates 
    WHERE CertificateType = @CertificateType 
    AND ComplianceYear = @ComplianceYear
    
    -- Format: AL-2024-0001
    SET @CertificateNumber = @TypePrefix + '-' + @ComplianceYear + '-' + RIGHT('0000' + CAST(@Counter AS NVARCHAR), 4)
    
    RETURN @CertificateNumber
END;

-- Update certificate PDF file ID
CREATE PROCEDURE UpdateCertificatePdfFileId
    @Id NVARCHAR(50),
    @CertificatePdfFileId NVARCHAR(50)
AS
BEGIN
    UPDATE ComplianceCertificates
    SET CertificatePdfFileId = @CertificatePdfFileId,
        UpdatedAt = GETDATE()
    WHERE Id = @Id

    SELECT * FROM ComplianceCertificates WHERE Id = @Id
END;
