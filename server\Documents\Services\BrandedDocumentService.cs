using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Threading.Tasks;
using Final_E_Receipt.Documents.Models;
using Final_E_Receipt.Receipts.Models;
using Final_E_Receipt.Compliance.Models;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Documents.Services
{
    public class BrandedDocumentService
    {
        private readonly string _templateBasePath = "wwwroot/templates";
        private readonly CertificateTemplateService _templateService;
        private readonly ILogger<BrandedDocumentService> _logger;

        public BrandedDocumentService(
            CertificateTemplateService templateService,
            ILogger<BrandedDocumentService> logger)
        {
            _templateService = templateService;
            _logger = logger;
        }

        /// <summary>
        /// Generate branded receipt from image template
        /// </summary>
        public async Task<byte[]> GenerateReceipt(Receipt receipt)
        {
            try
            {
                var config = GetReceiptConfiguration(receipt.OrganizationId);
                return await GenerateDocument(receipt, config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating receipt for {ReceiptId}", receipt.Id);
                throw;
            }
        }

        /// <summary>
        /// Generate branded compliance certificate from image template
        /// </summary>
        public async Task<byte[]> GenerateComplianceCertificate(ComplianceCertificate certificate)
        {
            try
            {
                var config = GetCertificateConfiguration(certificate.OrganizationId, certificate.CertificateType);
                return await GenerateDocument(certificate, config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating certificate for {CertificateId}", certificate.Id);
                throw;
            }
        }

        /// <summary>
        /// Generate branded compliance certificate with specific template selection
        /// </summary>
        public async Task<byte[]> GenerateComplianceCertificateWithTemplate(ComplianceCertificate certificate, string templateId)
        {
            try
            {
                // Try to get user-selected template configuration
                var config = await _templateService.GetTemplateConfiguration(templateId, certificate.OrganizationId, certificate.CertificateType);

                // Fallback to automatic selection if template not found
                if (config == null)
                {
                    _logger.LogWarning("Template {TemplateId} not found, falling back to automatic selection", templateId);
                    config = GetCertificateConfiguration(certificate.OrganizationId, certificate.CertificateType);
                }

                return await GenerateDocument(certificate, config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating certificate with template {TemplateId} for {CertificateId}", templateId, certificate.Id);
                throw;
            }
        }

        /// <summary>
        /// Core document generation method - shared by both receipts and certificates
        /// </summary>
        private async Task<byte[]> GenerateDocument(object documentData, DocumentConfiguration config)
        {
            if (!File.Exists(config.TemplatePath))
            {
                throw new FileNotFoundException($"Template not found: {config.TemplatePath}");
            }

            using (var templateImage = Image.FromFile(config.TemplatePath))
            {
                var outputImage = new Bitmap(templateImage);
                using (var graphics = Graphics.FromImage(outputImage))
                {
                    // Apply document-specific settings
                    graphics.TextRenderingHint = config.TextRenderingHint;
                    graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

                    var font = new Font(config.FontFamily, config.FontSize, config.FontStyle);
                    var brush = new SolidBrush(config.TextColor);

                    // Get text data using the configured mapper
                    var textData = config.DataMapper(documentData);

                    // Overlay text based on positions
                    foreach (var (key, value) in textData)
                    {
                        if (config.TextPositions.ContainsKey(key) && !string.IsNullOrEmpty(value))
                        {
                            var position = config.TextPositions[key];

                            // Apply any rotation if needed
                            if (config.TextRotation != 0)
                            {
                                graphics.TranslateTransform(position.X, position.Y);
                                graphics.RotateTransform(config.TextRotation);
                                graphics.DrawString(value, font, brush, 0, 0);
                                graphics.ResetTransform();
                            }
                            else
                            {
                                graphics.DrawString(value, font, brush, position);
                            }
                        }
                    }

                    font.Dispose();
                    brush.Dispose();
                }

                // Convert to PDF with document-specific settings
                return await ConvertImageToPdf(outputImage, config.PdfSettings);
            }
        }

        /// <summary>
        /// Convert image to PDF bytes
        /// </summary>
        private async Task<byte[]> ConvertImageToPdf(Image image, PdfSettings settings)
        {
            // For now, we'll save as high-quality PNG and return as bytes
            // In production, you'd use a PDF library like iTextSharp or PdfSharp
            using (var stream = new MemoryStream())
            {
                // Save as high-quality image
                var encoder = ImageCodecInfo.GetImageEncoders()[1]; // PNG encoder
                var encoderParams = new EncoderParameters(1);
                encoderParams.Param[0] = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, settings.Quality);

                image.Save(stream, encoder, encoderParams);

                // TODO: Convert to actual PDF using library like iTextSharp
                // For now, return the image bytes
                return await Task.FromResult(stream.ToArray());
            }
        }

        /// <summary>
        /// Get receipt configuration - JRB uses one template for all receipts
        /// </summary>
        private DocumentConfiguration GetReceiptConfiguration(string organizationId)
        {
            // JRB uses one generalized receipt template for all tax services
            var templatePath = Path.Combine(_templateBasePath, "receipts", "jrb-receipt-template.png");
            if (!File.Exists(templatePath))
                templatePath = Path.Combine(_templateBasePath, "receipts", "default-receipt-template.png");

            // Standard configuration for all JRB receipts
            return new DocumentConfiguration
            {
                TemplatePath = templatePath,
                FontFamily = "Arial",
                FontSize = 12f,
                TextColor = Color.Black,
                PdfSettings = new PdfSettings
                {
                    Orientation = PageOrientation.Portrait,
                    PageSize = PageSize.A4
                },
                TextPositions = GetReceiptTextPositions(),
                DataMapper = (data) => MapReceiptData((Receipt)data)
            };
        }

        /// <summary>
        /// Get standard receipt text positions (same for all organizations)
        /// </summary>
        private Dictionary<string, PointF> GetReceiptTextPositions()
        {
            return new Dictionary<string, PointF>
            {
                { "ReceiptNumber", new PointF(400, 120) },
                { "PayerName", new PointF(200, 200) },
                { "PayerEmail", new PointF(200, 230) },
                { "Amount", new PointF(450, 350) },
                { "PaymentDate", new PointF(200, 260) },
                { "PaymentMethod", new PointF(200, 290) },
                { "Description", new PointF(200, 320) },
                { "IssuedDate", new PointF(200, 400) }
            };
        }

        /// <summary>
        /// Get certificate configuration - JRB uses generalized templates for all certificate types
        /// </summary>
        private DocumentConfiguration GetCertificateConfiguration(string organizationId, string certificateType)
        {
            // JRB uses generalized certificate templates based on orientation
            var orientation = GetCertificateOrientation(certificateType);

            string templatePath;
            if (orientation == PageOrientation.Landscape)
            {
                templatePath = Path.Combine(_templateBasePath, "certificates", "jrb-certificate-landscape-template.png");
            }
            else
            {
                templatePath = Path.Combine(_templateBasePath, "certificates", "jrb-certificate-portrait-template.png");
            }

            // Fallback to default if JRB template doesn't exist
            if (!File.Exists(templatePath))
                templatePath = Path.Combine(_templateBasePath, "certificates", "default-certificate-template.png");

            // Standard configuration for all JRB certificates
            return new DocumentConfiguration
            {
                TemplatePath = templatePath,
                FontFamily = "Arial",
                FontSize = 13f,
                FontStyle = FontStyle.Bold,
                TextColor = Color.Black,
                PdfSettings = new PdfSettings
                {
                    Orientation = orientation,
                    PageSize = PageSize.A4
                },
                TextPositions = GetCertificateTextPositions(orientation),
                DataMapper = (data) => MapCertificateData((ComplianceCertificate)data)
            };
        }

        /// <summary>
        /// Get certificate orientation based on certificate type
        /// </summary>
        private PageOrientation GetCertificateOrientation(string certificateType)
        {
            return certificateType switch
            {
                "ANNUAL_LICENSE" => PageOrientation.Landscape, // Wider format for licenses
                "TAX_CLEARANCE" => PageOrientation.Portrait,   // Standard format for clearances
                "QUARTERLY_COMPLIANCE" => PageOrientation.Portrait,
                "PAYMENT_COMPLIANCE" => PageOrientation.Portrait,
                _ => PageOrientation.Landscape // Default to landscape
            };
        }

        /// <summary>
        /// Get certificate text positions based on orientation
        /// </summary>
        private Dictionary<string, PointF> GetCertificateTextPositions(PageOrientation orientation)
        {
            if (orientation == PageOrientation.Landscape)
            {
                // Landscape positions (wider layout)
                return new Dictionary<string, PointF>
                {
                    { "CertificateNumber", new PointF(600, 150) },
                    { "OrganizationName", new PointF(300, 250) },
                    { "CertificateType", new PointF(300, 300) },
                    { "TotalAmount", new PointF(500, 400) },
                    { "ValidFrom", new PointF(200, 450) },
                    { "ValidUntil", new PointF(500, 450) },
                    { "IssuedDate", new PointF(300, 500) },
                    { "RegulatoryBody", new PointF(300, 550) },
                    { "ComplianceYear", new PointF(600, 200) },
                    { "PaymentProfileName", new PointF(300, 350) }
                };
            }
            else
            {
                // Portrait positions (taller layout)
                return new Dictionary<string, PointF>
                {
                    { "CertificateNumber", new PointF(400, 120) },
                    { "OrganizationName", new PointF(200, 220) },
                    { "CertificateType", new PointF(200, 270) },
                    { "TotalAmount", new PointF(400, 350) },
                    { "ValidFrom", new PointF(200, 400) },
                    { "ValidUntil", new PointF(400, 400) },
                    { "IssuedDate", new PointF(200, 450) },
                    { "RegulatoryBody", new PointF(200, 500) },
                    { "ComplianceYear", new PointF(400, 170) },
                    { "PaymentProfileName", new PointF(200, 320) }
                };
            }
        }

        /// <summary>
        /// Get default receipt configuration
        /// </summary>
        private DocumentConfiguration GetDefaultReceiptConfiguration(string templatePath)
        {
            return new DocumentConfiguration
            {
                TemplatePath = templatePath,
                FontFamily = "Arial",
                FontSize = 12f,
                TextColor = Color.Black,
                PdfSettings = new PdfSettings
                {
                    Orientation = PageOrientation.Portrait,
                    PageSize = PageSize.A4
                },
                TextPositions = new Dictionary<string, PointF>
                {
                    { "ReceiptNumber", new PointF(400, 120) },
                    { "PayerName", new PointF(200, 200) },
                    { "PayerEmail", new PointF(200, 230) },
                    { "Amount", new PointF(450, 350) },
                    { "PaymentDate", new PointF(200, 260) },
                    { "PaymentMethod", new PointF(200, 290) },
                    { "Description", new PointF(200, 320) },
                    { "IssuedDate", new PointF(200, 400) }
                },
                DataMapper = (data) => MapReceiptData((Receipt)data)
            };
        }

        /// <summary>
        /// Get default certificate configuration
        /// </summary>
        private DocumentConfiguration GetDefaultCertificateConfiguration(string templatePath)
        {
            return new DocumentConfiguration
            {
                TemplatePath = templatePath,
                FontFamily = "Arial",
                FontSize = 13f,
                FontStyle = FontStyle.Bold,
                TextColor = Color.Black,
                PdfSettings = new PdfSettings
                {
                    Orientation = PageOrientation.Landscape,
                    PageSize = PageSize.A4
                },
                TextPositions = new Dictionary<string, PointF>
                {
                    { "CertificateNumber", new PointF(600, 150) },
                    { "OrganizationName", new PointF(300, 250) },
                    { "CertificateType", new PointF(300, 300) },
                    { "TotalAmount", new PointF(500, 400) },
                    { "ValidFrom", new PointF(200, 450) },
                    { "ValidUntil", new PointF(500, 450) },
                    { "IssuedDate", new PointF(300, 500) },
                    { "RegulatoryBody", new PointF(300, 550) },
                    { "ComplianceYear", new PointF(600, 200) },
                    { "PaymentProfileName", new PointF(300, 350) }
                },
                DataMapper = (data) => MapCertificateData((ComplianceCertificate)data)
            };
        }

        /// <summary>
        /// Map receipt data to text dictionary
        /// </summary>
        private Dictionary<string, string> MapReceiptData(Receipt receipt)
        {
            return new Dictionary<string, string>
            {
                { "ReceiptNumber", receipt.ReceiptNumber ?? "" },
                { "PayerName", receipt.PayerName ?? "" },
                { "PayerEmail", receipt.PayerEmail ?? "" },
                { "Amount", $"{receipt.Currency} {receipt.Amount:N2}" },
                { "PaymentDate", receipt.PaymentDate.ToString("dd/MM/yyyy") },
                { "PaymentMethod", receipt.PaymentMethod ?? "" },
                { "Description", receipt.Description ?? "" },
                { "IssuedDate", DateTime.Now.ToString("dd/MM/yyyy") }
            };
        }

        /// <summary>
        /// Map certificate data to text dictionary
        /// </summary>
        private Dictionary<string, string> MapCertificateData(ComplianceCertificate certificate)
        {
            return new Dictionary<string, string>
            {
                { "CertificateNumber", certificate.CertificateNumber ?? "" },
                { "OrganizationName", certificate.OrganizationName ?? "" },
                { "CertificateType", FormatCertificateType(certificate.CertificateType) },
                { "TotalAmount", $"{certificate.Currency} {certificate.TotalAmount:N2}" },
                { "ValidFrom", certificate.ValidFrom.ToString("MMMM dd, yyyy") },
                { "ValidUntil", certificate.ValidUntil.ToString("MMMM dd, yyyy") },
                { "IssuedDate", certificate.IssuedDate.ToString("MMMM dd, yyyy") },
                { "RegulatoryBody", certificate.RegulatoryBody ?? "" },
                { "ComplianceYear", certificate.ComplianceYear ?? "" },
                { "PaymentProfileName", certificate.PaymentProfileName ?? "" }
            };
        }

        /// <summary>
        /// Format certificate type for display
        /// </summary>
        private string FormatCertificateType(string certificateType)
        {
            return certificateType switch
            {
                "ANNUAL_LICENSE" => "Annual Business License",
                "TAX_CLEARANCE" => "Tax Clearance Certificate",
                "QUARTERLY_COMPLIANCE" => "Quarterly Compliance Certificate",
                "PAYMENT_COMPLIANCE" => "Payment Compliance Certificate",
                _ => certificateType?.Replace("_", " ") ?? ""
            };
        }
    }
}
