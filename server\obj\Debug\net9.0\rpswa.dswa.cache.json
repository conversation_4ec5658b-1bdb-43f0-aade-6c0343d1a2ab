{"GlobalPropertiesHash": "gxs3/j3Lwdb5ZnJmfe+Rm0SMFATZUsbovhuRx87sruA=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["64Byb1zY7X95+nM32INwEA1JaYMlGRSnAzRyQWNKevA=", "suE6M5myDke1NI2BSTez4scuq/mVVNuQPBgXLH6ORQk=", "E4qiC8a4n8h5XfDbOZP2CcOHadxlHeiF98mcy3hklxE=", "x+SVjGKoL01YrV7z/KeAq6FrwF6Ly+Yq2u5116ttVpE=", "fjVSELZXEveGoe32LysPrFl7l8OvAJDuh7X1iPtoka8="], "CachedAssets": {"64Byb1zY7X95+nM32INwEA1JaYMlGRSnAzRyQWNKevA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\wwwroot\\templates\\DUMMY_EXAMPLE.md", "SourceId": "Final_E_Receipt", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\wwwroot\\", "BasePath": "_content/Final_E_Receipt", "RelativePath": "templates/DUMMY_EXAMPLE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f3wj77cful", "Integrity": "xXNXiuwo8a6lEJcgphsMfsxdF0+78P1dorCfrD2P8Eg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\templates\\DUMMY_EXAMPLE.md", "FileLength": 7746, "LastWriteTime": "2025-07-15T17:05:56.9062301+00:00"}, "suE6M5myDke1NI2BSTez4scuq/mVVNuQPBgXLH6ORQk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\wwwroot\\templates\\README.md", "SourceId": "Final_E_Receipt", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\wwwroot\\", "BasePath": "_content/Final_E_Receipt", "RelativePath": "templates/README#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wxenofpedg", "Integrity": "ZG/0yH04ybRy9Sk4l9+YGjA572AilkYPS7PgGIVrD7M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\templates\\README.md", "FileLength": 3154, "LastWriteTime": "2025-07-15T17:13:18.6895011+00:00"}, "E4qiC8a4n8h5XfDbOZP2CcOHadxlHeiF98mcy3hklxE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\wwwroot\\templates\\TEMPLATE_FILE_NAMES.md", "SourceId": "Final_E_Receipt", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Final_E_Receipt\\server\\wwwroot\\", "BasePath": "_content/Final_E_Receipt", "RelativePath": "templates/TEMPLATE_FILE_NAMES#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2srma2b3wk", "Integrity": "3se5Lov0YzvhlGRyHgzmnoSGn3wSO+UGSTCf9zZG14I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\templates\\TEMPLATE_FILE_NAMES.md", "FileLength": 4332, "LastWriteTime": "2025-07-15T17:13:51.5634214+00:00"}}, "CachedCopyCandidates": {}}