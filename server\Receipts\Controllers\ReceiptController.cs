using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Final_E_Receipt.Receipts.Models;
using Final_E_Receipt.Receipts.Services;
using Final_E_Receipt.Receipts.DTOs;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Final_E_Receipt.Receipts.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReceiptController : ControllerBase
    {
        private readonly ReceiptService _receiptService;

        public ReceiptController(ReceiptService receiptService)
        {
            _receiptService = receiptService;
        }

        [HttpPost]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> CreateReceipt([FromBody] CreateReceiptDTO dto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            var receipt = new Receipt
            {
                PayerId = dto.PayerId,
                PayerName = dto.PayerName,
                PayerEmail = dto.PayerEmail,
                Amount = dto.Amount,
                Currency = dto.Currency,
                PaymentDate = dto.PaymentDate,
                PaymentMethod = dto.PaymentMethod,
                Status = "Completed",
                Description = dto.Description,
                Category = dto.Category,
                CreatedBy = userId,
                OrganizationId = dto.OrganizationId
            };

            var createdReceipt = await _receiptService.CreateReceipt(receipt);
            
            if (createdReceipt == null)
                return BadRequest(new { message = "Failed to create receipt" });
                
            return CreatedAtAction(nameof(GetReceiptById), new { id = createdReceipt.Id }, createdReceipt);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetReceiptById(string id)
        {
            var receipt = await _receiptService.GetReceiptById(id);
            
            if (receipt == null)
                return NotFound(new { message = "Receipt not found" });
                
            return Ok(receipt);
        }

        [HttpGet("payer/{payerId}")]
        public async Task<IActionResult> GetReceiptsByPayer(string payerId)
        {
            var receipts = await _receiptService.GetReceiptsByPayer(payerId);
            return Ok(receipts);
        }

        [HttpGet("organization/{organizationId}")]
        public async Task<IActionResult> GetReceiptsByOrganization(string organizationId)
        {
            var receipts = await _receiptService.GetReceiptsByOrganization(organizationId);
            return Ok(receipts);
        }

        [HttpGet("organization/{organizationId}/daterange")]
        public async Task<IActionResult> GetReceiptsByDateRange(
            string organizationId, 
            [FromQuery] DateTime startDate, 
            [FromQuery] DateTime endDate)
        {
            var receipts = await _receiptService.GetReceiptsByDateRange(organizationId, startDate, endDate);
            return Ok(receipts);
        }

        [HttpPut("{id}/revoke")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> RevokeReceipt(string id, [FromBody] RevokeReceiptDTO dto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            var revokedReceipt = await _receiptService.RevokeReceipt(id, dto.RevokedReason, userId);
            
            if (revokedReceipt == null)
                return NotFound(new { message = "Receipt not found" });
                
            return Ok(revokedReceipt);
        }

        [HttpPut("{id}/notification")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> MarkNotificationSent(string id)
        {
            var updatedReceipt = await _receiptService.MarkNotificationSent(id);
            
            if (updatedReceipt == null)
                return NotFound(new { message = "Receipt not found" });
                
            return Ok(updatedReceipt);
        }

        [HttpPost("search")]
        public async Task<IActionResult> SearchReceipts(
            [FromQuery] string organizationId, 
            [FromBody] SearchReceiptsDTO dto)
        {
            if (string.IsNullOrEmpty(dto.SearchTerm) && !dto.StartDate.HasValue && !dto.EndDate.HasValue)
                return BadRequest(new { message = "At least one search parameter is required" });
                
            if (!string.IsNullOrEmpty(dto.SearchTerm))
            {
                var searchResults = await _receiptService.SearchReceipts(organizationId, dto.SearchTerm);
                return Ok(searchResults);
            }
            else if (dto.StartDate.HasValue && dto.EndDate.HasValue)
            {
                var dateRangeResults = await _receiptService.GetReceiptsByDateRange(
                    organizationId, 
                    dto.StartDate.Value, 
                    dto.EndDate.Value);
                return Ok(dateRangeResults);
            }
            
            return BadRequest(new { message = "Invalid search parameters" });
        }
    }
}