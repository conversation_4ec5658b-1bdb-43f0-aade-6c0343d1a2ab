# 📧 SMTP Configuration Guide for Email Notifications

## 🎯 Overview

This guide shows you how to configure SMTP settings for the email-only payment schedule notification system.

## 🔧 Step 1: Add SMTP Configuration to appsettings.json

Add this configuration to your `appsettings.json` file:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "your-connection-string"
  },
  "EmailConfiguration": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "your-app-password",
    "EnableSsl": true,
    "FromEmail": "<EMAIL>",
    "FromName": "Payment Management System"
  }
}
```

## 📧 SMTP Provider Configurations

### **Gmail SMTP**
```json
{
  "EmailConfiguration": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "your-app-password",
    "EnableSsl": true,
    "FromEmail": "<EMAIL>",
    "FromName": "Payment Management System"
  }
}
```

**⚠️ Important for Gmail:**
1. Enable 2-Factor Authentication on your Gmail account
2. Generate an "App Password" (not your regular password)
3. Use the App Password in the `SmtpPassword` field

### **Outlook/Office 365 SMTP**
```json
{
  "EmailConfiguration": {
    "SmtpServer": "smtp-mail.outlook.com",
    "SmtpPort": 587,
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "your-password",
    "EnableSsl": true,
    "FromEmail": "<EMAIL>",
    "FromName": "Payment Management System"
  }
}
```

### **Custom SMTP Server**
```json
{
  "EmailConfiguration": {
    "SmtpServer": "mail.yourcompany.com",
    "SmtpPort": 587,
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "your-smtp-password",
    "EnableSsl": true,
    "FromEmail": "<EMAIL>",
    "FromName": "Payment Management System"
  }
}
```

### **SendGrid SMTP**
```json
{
  "EmailConfiguration": {
    "SmtpServer": "smtp.sendgrid.net",
    "SmtpPort": 587,
    "SmtpUsername": "apikey",
    "SmtpPassword": "your-sendgrid-api-key",
    "EnableSsl": true,
    "FromEmail": "<EMAIL>",
    "FromName": "Payment Management System"
  }
}
```

## 🔧 Step 2: Register Services in Startup.cs

Add this to your `Startup.cs` or `Program.cs`:

```csharp
// In ConfigureServices method (Startup.cs) or builder.Services (Program.cs)
services.AddNotificationServices();

// Or register manually:
services.AddScoped<CentralizedEmailService>();
services.AddScoped<UnifiedNotificationService>();
services.AddScoped<UserEmailService>();
services.AddScoped<EmailConfigurationService>();
services.AddScoped<EmailTemplateService>();
```

## 🔧 Step 3: Create Default Email Configuration in Database

Run this SQL to create a default email configuration:

```sql
-- Insert default email configuration
INSERT INTO EmailConfigurations (
    Id, 
    OrganizationId, 
    SmtpServer, 
    SmtpPort, 
    SmtpUsername, 
    SmtpPassword, 
    EnableSsl, 
    IsDefault, 
    SenderName, 
    FromEmail,
    CreatedAt,
    CreatedBy
)
VALUES (
    NEWID(),
    'your-organization-id', -- Replace with actual organization ID
    'smtp.gmail.com',
    587,
    '<EMAIL>',
    'your-app-password',
    1, -- EnableSsl = true
    1, -- IsDefault = true
    'Payment Management System',
    '<EMAIL>',
    GETDATE(),
    'SYSTEM'
);
```

## 🧪 Step 4: Test Email Configuration

Create a simple test endpoint to verify email is working:

```csharp
[ApiController]
[Route("api/[controller]")]
public class TestEmailController : ControllerBase
{
    private readonly NotificationService _notificationService;

    public TestEmailController(NotificationService notificationService)
    {
        _notificationService = notificationService;
    }

    [HttpPost("test-payment-schedule-email")]
    public async Task<IActionResult> TestPaymentScheduleEmail([FromBody] TestEmailRequest request)
    {
        var dto = new PaymentScheduleNotificationDTO
        {
            PaymentScheduleId = "test-123",
            PayerUserId = request.UserId,
            OrganizationId = request.OrganizationId,
            Amount = 50000,
            DueDate = DateTime.Now.AddDays(30),
            PaymentProfileName = "Test Payment Profile",
            Currency = "NGN"
        };

        var success = await _notificationService.SendPaymentScheduleCreatedNotification(dto);
        
        return Ok(new { success, message = success ? "Email sent successfully" : "Failed to send email" });
    }
}

public class TestEmailRequest
{
    public string UserId { get; set; }
    public string OrganizationId { get; set; }
}
```

## 🔍 Step 5: Troubleshooting

### **Common Issues:**

#### **1. Authentication Failed**
- **Gmail:** Make sure you're using an App Password, not your regular password
- **Outlook:** Check if 2FA is enabled and use the correct credentials
- **Custom SMTP:** Verify username/password with your email provider

#### **2. Connection Timeout**
- Check if the SMTP server and port are correct
- Verify firewall settings allow outbound connections on the SMTP port
- Try different ports (25, 465, 587)

#### **3. SSL/TLS Issues**
- Try `EnableSsl: false` for testing (not recommended for production)
- Some servers require different SSL configurations

#### **4. Email Not Delivered**
- Check spam/junk folders
- Verify the "From" email address is valid
- Check email provider logs

### **Testing Commands:**

```bash
# Test SMTP connection using telnet
telnet smtp.gmail.com 587

# Test with PowerShell
Test-NetConnection -ComputerName smtp.gmail.com -Port 587
```

## 📊 Step 6: Monitoring & Logging

Add logging to monitor email delivery:

```csharp
// In your appsettings.json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Final_E_Receipt.Notifications": "Debug" // Enable detailed notification logging
    }
  }
}
```

## 🚀 Step 7: Production Considerations

### **Security:**
- Use environment variables for sensitive data:
  ```json
  {
    "EmailConfiguration": {
      "SmtpPassword": "${SMTP_PASSWORD}"
    }
  }
  ```

### **Performance:**
- Email sending is async (fire-and-forget)
- Failed emails are logged but don't block main operations
- Consider implementing retry logic for failed emails

### **Compliance:**
- Ensure email content complies with your organization's policies
- Add unsubscribe links if required
- Consider GDPR/privacy requirements

## ✅ Quick Setup Checklist

- [ ] Add SMTP configuration to appsettings.json
- [ ] Register notification services in DI container
- [ ] Create default email configuration in database
- [ ] Test email sending with test endpoint
- [ ] Integrate notification calls into PaymentScheduleService
- [ ] Verify emails are delivered to users
- [ ] Monitor logs for any errors

## 🎯 Next Steps

Once email notifications are working:
1. **Monitor email delivery** - Check logs and user feedback
2. **Add more notification types** - Payment due, overdue reminders
3. **Enhance email templates** - Add branding, better formatting
4. **Add in-app notifications** - Use the full notification system we built

---

**Your email-only payment schedule notification system is now ready for production!** 🎉
