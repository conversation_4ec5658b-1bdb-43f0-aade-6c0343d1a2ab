-- COMPLETE DATABASE SCHEMA FOR PAYMENT MANAGEMENT SYSTEM
-- This file contains ALL tables required for the system to function properly

-- ===== CORE SYSTEM TABLES =====

-- Organizations Table (from main schema)
CREATE TABLE Organizations (
    Id NVARCHAR(50) PRIMARY KEY,
    Name NVARCHAR(255) NOT NULL,
    Email NVARCHAR(255),
    PhoneNumber NVARCHAR(20),
    Address NVARCHAR(500),
    Contact<PERSON>erson NVARCHAR(100),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    UpdatedAt DATETIME NULL
);

-- Users Table (from Authentication)
CREATE TABLE Users (
    Id NVARCHAR(50) PRIMARY KEY,
    FirstName NVARCHAR(100),
    LastName NVARCHAR(100),
    Email NVARCHAR(255) UNIQUE NOT NULL,
    PhoneNumber NVARCHAR(20),
    Role NVARCHAR(50) NOT NULL,
    OrganizationId NVARCHAR(50),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    LastLogin DATETIME NULL,
    AzureAdObjectId NVARCHAR(100) UNIQUE,
    FOREIGN KEY (OrganizationId) REFERENCES Organizations(Id)
);

-- ===== PAYMENT SYSTEM TABLES =====

-- Payment Profiles Table (MISSING from main schema)
CREATE TABLE PaymentProfiles (
    Id NVARCHAR(50) PRIMARY KEY,
    Name NVARCHAR(255) NOT NULL,
    Description NVARCHAR(1000),
    Category NVARCHAR(100),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50),
    UpdatedAt DATETIME NULL,
    UpdatedBy NVARCHAR(50) NULL,
    FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
);

-- Payment Schedules Table (MISSING from main schema)
CREATE TABLE PaymentSchedules (
    Id NVARCHAR(50) PRIMARY KEY,
    OrganizationId NVARCHAR(50) NOT NULL,
    PaymentProfileId NVARCHAR(50) NOT NULL,
    Description NVARCHAR(500) NOT NULL,
    Amount DECIMAL(18,2) NOT NULL,
    Currency NVARCHAR(10) NOT NULL DEFAULT 'NGN',
    DueDate DATETIME NOT NULL,
    Status NVARCHAR(50) NOT NULL DEFAULT 'PENDING', -- PENDING, PAID, OVERDUE, CANCELLED
    PaymentId NVARCHAR(50) NULL, -- Link to actual payment when paid
    PaidAmount DECIMAL(18,2) NULL,
    PaidDate DATETIME NULL,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50),
    UpdatedAt DATETIME NULL,
    UpdatedBy NVARCHAR(50) NULL,
    FOREIGN KEY (OrganizationId) REFERENCES Organizations(Id),
    FOREIGN KEY (PaymentProfileId) REFERENCES PaymentProfiles(Id),
    FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
);

-- Payments Table (from main schema but needs enhancement)
CREATE TABLE Payments (
    Id NVARCHAR(50) PRIMARY KEY,
    PayerId NVARCHAR(50),
    PayerName NVARCHAR(100),
    PayerEmail NVARCHAR(255),
    Amount DECIMAL(18,2) NOT NULL,
    Currency NVARCHAR(10) NOT NULL DEFAULT 'NGN',
    PaymentMethod NVARCHAR(50),
    TransactionReference NVARCHAR(100),
    Status NVARCHAR(50) NOT NULL, -- Pending, Proof_Uploaded, Acknowledged, Approved, Rejected, Completed, Failed
    Description NVARCHAR(500),
    Category NVARCHAR(100),
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CompletedAt DATETIME NULL,
    OrganizationId NVARCHAR(50),
    ReceiptId NVARCHAR(50), -- Link to receipt
    PaymentScheduleId NVARCHAR(50), -- Link to payment schedule
    ProofFileId NVARCHAR(50), -- Link to payment proof file

    -- Approval Workflow Fields
    AcknowledgedBy NVARCHAR(50) NULL, -- Finance Officer who acknowledged the payment
    AcknowledgedDate DATETIME NULL, -- When payment was acknowledged
    AcknowledgmentNotes NVARCHAR(1000) NULL, -- Notes from Finance Officer

    ApprovedBy NVARCHAR(50) NULL, -- Senior Finance Officer who approved the payment
    ApprovedDate DATETIME NULL, -- When payment was approved
    ApprovalNotes NVARCHAR(1000) NULL, -- Notes from Senior Finance Officer

    RejectedBy NVARCHAR(50) NULL, -- User who rejected the payment
    RejectedDate DATETIME NULL, -- When payment was rejected
    RejectionReason NVARCHAR(1000) NULL, -- Reason for rejection

    FOREIGN KEY (OrganizationId) REFERENCES Organizations(Id),
    FOREIGN KEY (PaymentScheduleId) REFERENCES PaymentSchedules(Id),
    FOREIGN KEY (AcknowledgedBy) REFERENCES Users(Id),
    FOREIGN KEY (ApprovedBy) REFERENCES Users(Id),
    FOREIGN KEY (RejectedBy) REFERENCES Users(Id)
);

-- ===== RECEIPT SYSTEM TABLES =====

-- Receipts Table (from Receipts SQL but needs to be in main schema)
CREATE TABLE Receipts (
    Id NVARCHAR(50) PRIMARY KEY,
    PayerId NVARCHAR(50),
    PayerName NVARCHAR(100),
    PayerEmail NVARCHAR(255),
    ReceiptNumber NVARCHAR(50) UNIQUE NOT NULL,
    Amount DECIMAL(18,2) NOT NULL,
    Currency NVARCHAR(10) NOT NULL DEFAULT 'NGN',
    PaymentMethod NVARCHAR(50),
    PaymentDate DATETIME NOT NULL,
    Description NVARCHAR(500),
    Category NVARCHAR(100),
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50),
    OrganizationId NVARCHAR(50),
    PaymentId NVARCHAR(50), -- Link to payment
    IsRevoked BIT NOT NULL DEFAULT 0,
    RevokedDate DATETIME NULL,
    RevokedReason NVARCHAR(500) NULL,
    RevokedBy NVARCHAR(50) NULL,
    ReasonCategory NVARCHAR(50) NULL, -- MISSING FIELD for enhanced reporting
    NotificationSent BIT NOT NULL DEFAULT 0,
    NotificationSentDate DATETIME NULL,
    FOREIGN KEY (OrganizationId) REFERENCES Organizations(Id),
    FOREIGN KEY (PaymentId) REFERENCES Payments(Id),
    FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    FOREIGN KEY (RevokedBy) REFERENCES Users(Id)
);

-- ===== COMPLIANCE SYSTEM TABLES =====

-- Compliance Certificates Table (from Compliance SQL)
CREATE TABLE ComplianceCertificates (
    Id NVARCHAR(50) PRIMARY KEY,
    CertificateNumber NVARCHAR(100) UNIQUE NOT NULL,
    OrganizationId NVARCHAR(50) NOT NULL,
    OrganizationName NVARCHAR(255) NOT NULL,
    PaymentProfileId NVARCHAR(50) NOT NULL,
    PaymentProfileName NVARCHAR(255) NOT NULL,
    CertificateType NVARCHAR(100) NOT NULL,
    Status NVARCHAR(50) NOT NULL DEFAULT 'PENDING',
    TotalAmount DECIMAL(18,2) NOT NULL,
    Currency NVARCHAR(10) NOT NULL DEFAULT 'NGN',
    ValidFrom DATETIME NOT NULL,
    ValidUntil DATETIME NOT NULL,
    IssuedDate DATETIME NOT NULL DEFAULT GETDATE(),
    IssuedBy NVARCHAR(50) NOT NULL,
    Description NVARCHAR(1000),
    Terms NVARCHAR(2000),
    CertificatePdfFileId NVARCHAR(50),
    IsRevoked BIT NOT NULL DEFAULT 0,
    RevokedDate DATETIME NULL,
    RevokedBy NVARCHAR(50) NULL,
    RevokedReason NVARCHAR(500) NULL,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50) NOT NULL,
    UpdatedAt DATETIME NULL,
    UpdatedBy NVARCHAR(50) NULL,
    ComplianceYear NVARCHAR(10),
    CompliancePeriod NVARCHAR(20),
    RegulatoryBody NVARCHAR(255),
    LicenseCategory NVARCHAR(100),
    Notes NVARCHAR(1000),
    FOREIGN KEY (OrganizationId) REFERENCES Organizations(Id),
    FOREIGN KEY (PaymentProfileId) REFERENCES PaymentProfiles(Id),
    FOREIGN KEY (IssuedBy) REFERENCES Users(Id),
    FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
);

-- ===== FILE SYSTEM TABLES =====

-- File Uploads Table (from Files SQL)
CREATE TABLE FileUploads (
    Id NVARCHAR(50) PRIMARY KEY,
    FileName NVARCHAR(255) NOT NULL,
    OriginalFileName NVARCHAR(255) NOT NULL,
    FilePath NVARCHAR(500) NOT NULL,
    ContentType NVARCHAR(100) NOT NULL,
    FileSize BIGINT NOT NULL,
    FileHash NVARCHAR(64) NOT NULL,
    UploadedBy NVARCHAR(50) NOT NULL,
    OrganizationId NVARCHAR(50),
    RelatedEntityType NVARCHAR(50), -- PAYMENT, RECEIPT, CERTIFICATE
    RelatedEntityId NVARCHAR(50),
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    IsActive BIT NOT NULL DEFAULT 1,
    IsScanned BIT NOT NULL DEFAULT 0,
    ScanResult NVARCHAR(100),
    ScannedAt DATETIME NULL,
    Description NVARCHAR(500),
    Category NVARCHAR(100),
    FOREIGN KEY (UploadedBy) REFERENCES Users(Id),
    FOREIGN KEY (OrganizationId) REFERENCES Organizations(Id)
);

-- ===== NOTIFICATION SYSTEM TABLES =====

-- Email Templates Table (from Notifications SQL)
CREATE TABLE EmailTemplates (
    Id NVARCHAR(50) PRIMARY KEY,
    OrganizationId NVARCHAR(50) NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500),
    Subject NVARCHAR(255) NOT NULL,
    BodyContent NVARCHAR(MAX) NOT NULL,
    TemplateContent NVARCHAR(MAX) NOT NULL, -- MISSING FIELD used in compliance notifications
    Type NVARCHAR(50) NOT NULL, -- RECEIPT_NOTIFICATION, PAYMENT_REMINDER, CERTIFICATE_ISSUED, etc.
    IsDefault BIT NOT NULL DEFAULT 0,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50),
    UpdatedAt DATETIME NULL,
    UpdatedBy NVARCHAR(50) NULL,
    FOREIGN KEY (OrganizationId) REFERENCES Organizations(Id),
    FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
);

-- Email Configurations Table (from Notifications SQL)
CREATE TABLE EmailConfigurations (
    Id NVARCHAR(50) PRIMARY KEY,
    OrganizationId NVARCHAR(50) NOT NULL,
    SmtpServer NVARCHAR(255) NOT NULL,
    SmtpPort INT NOT NULL,
    SmtpUsername NVARCHAR(255) NOT NULL,
    SmtpPassword NVARCHAR(255) NOT NULL,
    EnableSsl BIT NOT NULL DEFAULT 1,
    IsDefault BIT NOT NULL DEFAULT 0,
    SenderName NVARCHAR(100) NOT NULL,
    FromEmail NVARCHAR(255) NOT NULL, -- MISSING FIELD used in compliance notifications
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50),
    UpdatedAt DATETIME NULL,
    UpdatedBy NVARCHAR(50) NULL,
    FOREIGN KEY (OrganizationId) REFERENCES Organizations(Id),
    FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
);

-- ===== AUTHENTICATION TABLES =====

-- User Invitations Table (from Authentication SQL)
CREATE TABLE UserInvitations (
    Id NVARCHAR(50) PRIMARY KEY,
    Email NVARCHAR(255) NOT NULL,
    Role NVARCHAR(50) NOT NULL,
    OrganizationId NVARCHAR(50) NOT NULL, -- Organization assigned during invitation
    InvitedDate DATETIME NOT NULL DEFAULT GETDATE(),
    Status NVARCHAR(20) NOT NULL DEFAULT 'Pending', -- Pending, Accepted, Cancelled, Expired
    InvitedBy NVARCHAR(50) NOT NULL,
    ExpiryDate DATETIME NOT NULL,
    AcceptedDate DATETIME NULL, -- When invitation was accepted
    FOREIGN KEY (InvitedBy) REFERENCES Users(Id),
    FOREIGN KEY (OrganizationId) REFERENCES Organizations(Id)
);

-- ===== INDEXES FOR PERFORMANCE =====

-- Organizations indexes
CREATE INDEX IX_Organizations_Name ON Organizations(Name);
CREATE INDEX IX_Organizations_IsActive ON Organizations(IsActive);

-- Users indexes
CREATE INDEX IX_Users_Email ON Users(Email);
CREATE INDEX IX_Users_Role ON Users(Role);
CREATE INDEX IX_Users_Organization ON Users(OrganizationId);

-- Payment Profiles indexes
CREATE INDEX IX_PaymentProfiles_Name ON PaymentProfiles(Name);
CREATE INDEX IX_PaymentProfiles_Category ON PaymentProfiles(Category);

-- Payment Schedules indexes
CREATE INDEX IX_PaymentSchedules_Organization ON PaymentSchedules(OrganizationId);
CREATE INDEX IX_PaymentSchedules_PaymentProfile ON PaymentSchedules(PaymentProfileId);
CREATE INDEX IX_PaymentSchedules_DueDate ON PaymentSchedules(DueDate);
CREATE INDEX IX_PaymentSchedules_Status ON PaymentSchedules(Status);

-- Payments indexes
CREATE INDEX IX_Payments_Organization ON Payments(OrganizationId);
CREATE INDEX IX_Payments_Status ON Payments(Status);
CREATE INDEX IX_Payments_CreatedAt ON Payments(CreatedAt);
CREATE INDEX IX_Payments_PaymentSchedule ON Payments(PaymentScheduleId);
CREATE INDEX IX_Payments_AcknowledgedBy ON Payments(AcknowledgedBy);
CREATE INDEX IX_Payments_AcknowledgedDate ON Payments(AcknowledgedDate);
CREATE INDEX IX_Payments_ApprovedBy ON Payments(ApprovedBy);
CREATE INDEX IX_Payments_ApprovedDate ON Payments(ApprovedDate);
CREATE INDEX IX_Payments_RejectedBy ON Payments(RejectedBy);
CREATE INDEX IX_Payments_RejectedDate ON Payments(RejectedDate);

-- Receipts indexes
CREATE INDEX IX_Receipts_Organization ON Receipts(OrganizationId);
CREATE INDEX IX_Receipts_PaymentId ON Receipts(PaymentId);
CREATE INDEX IX_Receipts_IsRevoked ON Receipts(IsRevoked);
CREATE INDEX IX_Receipts_RevokedDate ON Receipts(RevokedDate);

-- Compliance Certificates indexes
CREATE INDEX IX_ComplianceCertificates_Organization ON ComplianceCertificates(OrganizationId);
CREATE INDEX IX_ComplianceCertificates_PaymentProfile ON ComplianceCertificates(PaymentProfileId);
CREATE INDEX IX_ComplianceCertificates_Type ON ComplianceCertificates(CertificateType);
CREATE INDEX IX_ComplianceCertificates_Status ON ComplianceCertificates(Status);
CREATE INDEX IX_ComplianceCertificates_ValidPeriod ON ComplianceCertificates(ValidFrom, ValidUntil);
CREATE INDEX IX_ComplianceCertificates_ComplianceYear ON ComplianceCertificates(ComplianceYear, CompliancePeriod);

-- File Uploads indexes
CREATE INDEX IX_FileUploads_RelatedEntity ON FileUploads(RelatedEntityType, RelatedEntityId);
CREATE INDEX IX_FileUploads_Organization ON FileUploads(OrganizationId);
CREATE INDEX IX_FileUploads_UploadedBy ON FileUploads(UploadedBy);
CREATE INDEX IX_FileUploads_CreatedAt ON FileUploads(CreatedAt);

-- Email Templates indexes
CREATE INDEX IX_EmailTemplates_Organization ON EmailTemplates(OrganizationId);
CREATE INDEX IX_EmailTemplates_Type ON EmailTemplates(Type);

-- Email Configurations indexes
CREATE INDEX IX_EmailConfigurations_Organization ON EmailConfigurations(OrganizationId);
CREATE INDEX IX_EmailConfigurations_IsDefault ON EmailConfigurations(IsDefault);
