-- <PERSON><PERSON>t to find your organization information for template naming

-- 1. Get all organizations in the system
SELECT 
    Id as OrganizationId,
    Name as OrganizationName,
    Code as OrganizationCode,
    Type as OrganizationType,
    CreatedAt,
    IsActive
FROM Organizations
ORDER BY CreatedAt DESC;

-- 2. If you know the organization name, search for it
SELECT 
    Id as OrganizationId,
    Name as OrganizationName
FROM Organizations 
WHERE Name LIKE '%Joint Revenue%' 
   OR Name LIKE '%Revenue Board%'
   OR Name LIKE '%JRB%';

-- 3. Get the most recently created organization (likely yours)
SELECT TOP 1
    Id as OrganizationId,
    Name as OrganizationName,
    Code as OrganizationCode
FROM Organizations
WHERE IsActive = 1
ORDER BY CreatedAt DESC;

-- 4. Check if any organizations exist
SELECT COUNT(*) as TotalOrganizations FROM Organizations;

-- 5. If no organizations exist, you may need to create one first
-- Example insert (adjust values as needed):
/*
INSERT INTO Organizations (Id, Name, Code, Type, IsActive, CreatedAt, CreatedBy)
VALUES (
    'jrb-001',                    -- Use this ID for your templates
    'Joint Revenue Board',        -- Your organization name
    'JRB',                       -- Short code
    'GOVERNMENT',                -- Organization type
    1,                           -- Active
    GETDATE(),                   -- Created now
    'SYSTEM'                     -- Created by system
);
*/

-- 6. After running the query, use the OrganizationId for your template file names
-- Example: If OrganizationId = 'jrb-001', then your template files should be:
-- jrb-001-receipt-template.png
-- jrb-001-annual_license-template.png
-- jrb-001-tax_clearance-template.png
-- etc.
