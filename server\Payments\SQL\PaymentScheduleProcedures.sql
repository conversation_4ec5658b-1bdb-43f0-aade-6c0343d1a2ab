-- Payment Schedule Management Stored Procedures

-- Create a new payment schedule
CREATE PROCEDURE CreatePaymentSchedule
    @Id NVARCHAR(50),
    @OrganizationId NVARCHAR(50),
    @PaymentProfileId NVARCHAR(50),
    @Description NVARCHAR(500),
    @Amount DECIMAL(18,2),
    @Currency NVARCHAR(10),
    @DueDate DATETIME,
    @CreatedBy NVARCHAR(50)
AS
BEGIN
    INSERT INTO PaymentSchedules (
        Id, OrganizationId, PaymentProfileId, Description, Amount, Currency, DueDate, CreatedBy
    )
    VALUES (
        @Id, @OrganizationId, @PaymentProfileId, @Description, @Amount, @Currency, @DueDate, @CreatedBy
    )
    
    SELECT * FROM PaymentSchedules WHERE Id = @Id
END;

-- Get payment schedule by ID
CREATE PROCEDURE GetPaymentScheduleById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM PaymentSchedules WHERE Id = @Id
END;

-- Get payment schedules by organization
CREATE PROCEDURE GetPaymentSchedulesByOrganization
    @OrganizationId NVARCHAR(50),
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT * FROM PaymentSchedules 
    WHERE OrganizationId = @OrganizationId
    ORDER BY DueDate ASC
    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END;

-- Get payment schedules by payment profile
CREATE PROCEDURE GetPaymentSchedulesByPaymentProfile
    @PaymentProfileId NVARCHAR(50),
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT * FROM PaymentSchedules 
    WHERE PaymentProfileId = @PaymentProfileId
    ORDER BY DueDate ASC
    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END;

-- Update payment schedule
CREATE PROCEDURE UpdatePaymentSchedule
    @Id NVARCHAR(50),
    @Description NVARCHAR(500),
    @Amount DECIMAL(18,2),
    @Currency NVARCHAR(10),
    @DueDate DATETIME,
    @UpdatedBy NVARCHAR(50)
AS
BEGIN
    UPDATE PaymentSchedules 
    SET Description = @Description,
        Amount = @Amount,
        Currency = @Currency,
        DueDate = @DueDate,
        UpdatedAt = GETDATE(),
        UpdatedBy = @UpdatedBy
    WHERE Id = @Id
    
    SELECT * FROM PaymentSchedules WHERE Id = @Id
END;

-- Mark payment schedule as paid
CREATE PROCEDURE MarkPaymentScheduleAsPaid
    @Id NVARCHAR(50),
    @PaymentId NVARCHAR(50),
    @PaidAmount DECIMAL(18,2),
    @UpdatedBy NVARCHAR(50)
AS
BEGIN
    UPDATE PaymentSchedules 
    SET Status = 'PAID',
        PaymentId = @PaymentId,
        PaidAmount = @PaidAmount,
        PaidDate = GETDATE(),
        UpdatedAt = GETDATE(),
        UpdatedBy = @UpdatedBy
    WHERE Id = @Id
    
    SELECT * FROM PaymentSchedules WHERE Id = @Id
END;

-- Get overdue payment schedules
CREATE PROCEDURE GetOverduePaymentSchedules
    @OrganizationId NVARCHAR(50) = NULL,
    @DaysOverdue INT = 0
AS
BEGIN
    DECLARE @OverdueDate DATETIME = DATEADD(DAY, -@DaysOverdue, GETDATE())
    
    SELECT * FROM PaymentSchedules 
    WHERE Status != 'PAID' 
    AND DueDate < @OverdueDate
    AND (@OrganizationId IS NULL OR OrganizationId = @OrganizationId)
    ORDER BY DueDate ASC
END;

-- Get upcoming payment schedules
CREATE PROCEDURE GetUpcomingPaymentSchedules
    @OrganizationId NVARCHAR(50) = NULL,
    @DaysAhead INT = 30
AS
BEGIN
    DECLARE @FutureDate DATETIME = DATEADD(DAY, @DaysAhead, GETDATE())
    
    SELECT * FROM PaymentSchedules 
    WHERE Status != 'PAID' 
    AND DueDate BETWEEN GETDATE() AND @FutureDate
    AND (@OrganizationId IS NULL OR OrganizationId = @OrganizationId)
    ORDER BY DueDate ASC
END;

-- Delete payment schedule
CREATE PROCEDURE DeletePaymentSchedule
    @Id NVARCHAR(50)
AS
BEGIN
    DELETE FROM PaymentSchedules WHERE Id = @Id
END;

-- Get payment schedule statistics
CREATE PROCEDURE GetPaymentScheduleStatistics
    @OrganizationId NVARCHAR(50) = NULL,
    @PaymentProfileId NVARCHAR(50) = NULL
AS
BEGIN
    SELECT 
        COUNT(*) as TotalSchedules,
        SUM(CASE WHEN Status = 'PAID' THEN 1 ELSE 0 END) as PaidSchedules,
        SUM(CASE WHEN Status = 'PENDING' THEN 1 ELSE 0 END) as PendingSchedules,
        SUM(CASE WHEN Status = 'OVERDUE' THEN 1 ELSE 0 END) as OverdueSchedules,
        SUM(Amount) as TotalAmount,
        SUM(CASE WHEN Status = 'PAID' THEN PaidAmount ELSE 0 END) as TotalPaidAmount,
        SUM(CASE WHEN Status != 'PAID' THEN Amount ELSE 0 END) as TotalOutstandingAmount
    FROM PaymentSchedules
    WHERE (@OrganizationId IS NULL OR OrganizationId = @OrganizationId)
    AND (@PaymentProfileId IS NULL OR PaymentProfileId = @PaymentProfileId)
END;

-- Bulk create payment schedules
CREATE PROCEDURE BulkCreatePaymentSchedules
    @OrganizationId NVARCHAR(50),
    @PaymentProfileId NVARCHAR(50),
    @CreatedBy NVARCHAR(50)
AS
BEGIN
    -- This procedure would be used for bulk import
    -- Implementation depends on specific bulk import requirements
    SELECT 'Bulk create procedure - implementation needed' as Message
END;

-- Update payment schedule status
CREATE PROCEDURE UpdatePaymentScheduleStatus
    @Id NVARCHAR(50),
    @Status NVARCHAR(50),
    @UpdatedBy NVARCHAR(50)
AS
BEGIN
    UPDATE PaymentSchedules 
    SET Status = @Status,
        UpdatedAt = GETDATE(),
        UpdatedBy = @UpdatedBy
    WHERE Id = @Id
    
    SELECT * FROM PaymentSchedules WHERE Id = @Id
END;

-- Search payment schedules
CREATE PROCEDURE SearchPaymentSchedules
    @OrganizationId NVARCHAR(50) = NULL,
    @PaymentProfileId NVARCHAR(50) = NULL,
    @Status NVARCHAR(50) = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @MinAmount DECIMAL(18,2) = NULL,
    @MaxAmount DECIMAL(18,2) = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT * FROM PaymentSchedules 
    WHERE (@OrganizationId IS NULL OR OrganizationId = @OrganizationId)
    AND (@PaymentProfileId IS NULL OR PaymentProfileId = @PaymentProfileId)
    AND (@Status IS NULL OR Status = @Status)
    AND (@StartDate IS NULL OR DueDate >= @StartDate)
    AND (@EndDate IS NULL OR DueDate <= @EndDate)
    AND (@MinAmount IS NULL OR Amount >= @MinAmount)
    AND (@MaxAmount IS NULL OR Amount <= @MaxAmount)
    ORDER BY DueDate ASC
    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END;

-- Get payment schedules with organization and profile details
CREATE PROCEDURE GetPaymentSchedulesWithDetails
    @OrganizationId NVARCHAR(50) = NULL,
    @PaymentProfileId NVARCHAR(50) = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    SELECT 
        ps.*,
        o.Name as OrganizationName,
        pp.Name as PaymentProfileName,
        pp.Category as PaymentProfileCategory
    FROM PaymentSchedules ps
    LEFT JOIN Organizations o ON ps.OrganizationId = o.Id
    LEFT JOIN PaymentProfiles pp ON ps.PaymentProfileId = pp.Id
    WHERE (@OrganizationId IS NULL OR ps.OrganizationId = @OrganizationId)
    AND (@PaymentProfileId IS NULL OR ps.PaymentProfileId = @PaymentProfileId)
    ORDER BY ps.DueDate ASC
    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY
END;
