import React from 'react';
import { UserProfileProps } from '../../types/auth';
import { authService } from '../../services/authService';

export const UserProfile: React.FC<UserProfileProps> = ({
  user,
  showRole = true,
  showOrganization = false,
  showLastLogin = false,
  className = '',
}) => {
  const displayName = authService.getUserDisplayName(user);
  const roleDisplayName = authService.getRoleDisplayName(user.role);

  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <div className="flex items-center space-x-4">
        {/* Avatar */}
        <div className="flex-shrink-0">
          <div className="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center">
            <span className="text-white font-medium text-lg">
              {displayName.charAt(0).toUpperCase()}
            </span>
          </div>
        </div>

        {/* User Info */}
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-medium text-gray-900 truncate">
            {displayName}
          </h3>
          <p className="text-sm text-gray-500 truncate">
            {user.email}
          </p>
          {showRole && (
            <p className="text-sm text-blue-600 font-medium">
              {roleDisplayName}
            </p>
          )}
        </div>

        {/* Status Indicator */}
        <div className="flex-shrink-0">
          <div className={`h-3 w-3 rounded-full ${user.isActive ? 'bg-green-400' : 'bg-red-400'}`} />
        </div>
      </div>

      {/* Additional Info */}
      {(showOrganization || showLastLogin) && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
            {showOrganization && user.organizationId && (
              <div>
                <dt className="text-sm font-medium text-gray-500">Organization</dt>
                <dd className="text-sm text-gray-900">{user.organizationId}</dd>
              </div>
            )}
            {showLastLogin && user.lastLogin && (
              <div>
                <dt className="text-sm font-medium text-gray-500">Last Login</dt>
                <dd className="text-sm text-gray-900">
                  {new Date(user.lastLogin).toLocaleDateString()}
                </dd>
              </div>
            )}
            <div>
              <dt className="text-sm font-medium text-gray-500">Member Since</dt>
              <dd className="text-sm text-gray-900">
                {new Date(user.createdAt).toLocaleDateString()}
              </dd>
            </div>
            {user.phoneNumber && (
              <div>
                <dt className="text-sm font-medium text-gray-500">Phone</dt>
                <dd className="text-sm text-gray-900">{user.phoneNumber}</dd>
              </div>
            )}
          </dl>
        </div>
      )}
    </div>
  );
};
