import { useNotificationsApi } from '../hooks/api';
import type { CreateNotificationDTO } from '../hooks/api/useNotifications';

export interface NotificationTrigger {
  type: string;
  title: string;
  message: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  relatedEntityId?: string;
  relatedEntityType?: string;
  userId?: string;
  organizationId?: string;
}

class NotificationService {
  private notificationsApi: ReturnType<typeof useNotificationsApi> | null = null;

  // Initialize with the notifications API hook
  initialize(api: ReturnType<typeof useNotificationsApi>) {
    this.notificationsApi = api;
  }

  // Payment-related notifications
  async notifyPaymentDue(paymentId: string, payerUserId: string, organizationId: string, amount: number, dueDate: string) {
    const notification: NotificationTrigger = {
      type: 'PAYMENT_DUE',
      title: 'Payment Due Soon',
      message: `Payment of ₦${amount.toLocaleString()} is due on ${new Date(dueDate).toLocaleDateString()}`,
      priority: 'HIGH',
      relatedEntityId: paymentId,
      relatedEntityType: 'PAYMENT',
      userId: payerUserId,
      organizationId,
    };

    return this.sendNotification(notification);
  }

  async notifyPaymentOverdue(paymentId: string, payerUserId: string, organizationId: string, amount: number, daysPastDue: number) {
    const notification: NotificationTrigger = {
      type: 'PAYMENT_OVERDUE',
      title: 'Payment Overdue',
      message: `Payment of ₦${amount.toLocaleString()} is ${daysPastDue} days overdue`,
      priority: 'URGENT',
      relatedEntityId: paymentId,
      relatedEntityType: 'PAYMENT',
      userId: payerUserId,
      organizationId,
    };

    return this.sendNotification(notification);
  }

  async notifyPaymentApproved(paymentId: string, payerUserId: string, organizationId: string, amount: number) {
    const notification: NotificationTrigger = {
      type: 'PAYMENT_APPROVED',
      title: 'Payment Approved',
      message: `Your payment of ₦${amount.toLocaleString()} has been approved`,
      priority: 'MEDIUM',
      relatedEntityId: paymentId,
      relatedEntityType: 'PAYMENT',
      userId: payerUserId,
      organizationId,
    };

    return this.sendNotification(notification);
  }

  async notifyPaymentRejected(paymentId: string, payerUserId: string, organizationId: string, amount: number, reason?: string) {
    const notification: NotificationTrigger = {
      type: 'PAYMENT_REJECTED',
      title: 'Payment Rejected',
      message: `Your payment of ₦${amount.toLocaleString()} has been rejected${reason ? `: ${reason}` : ''}`,
      priority: 'HIGH',
      relatedEntityId: paymentId,
      relatedEntityType: 'PAYMENT',
      userId: payerUserId,
      organizationId,
    };

    return this.sendNotification(notification);
  }

  // Payment Schedule notifications
  async notifyPaymentScheduleCreated(scheduleId: string, payerUserId: string, organizationId: string, amount: number, dueDate: string, paymentProfileName: string) {
    const notification: NotificationTrigger = {
      type: 'PAYMENT_SCHEDULE_CREATED',
      title: 'New Payment Schedule Created',
      message: `A payment of ₦${amount.toLocaleString()} has been scheduled for ${new Date(dueDate).toLocaleDateString()} under ${paymentProfileName}`,
      priority: 'MEDIUM',
      relatedEntityId: scheduleId,
      relatedEntityType: 'PAYMENT_SCHEDULE',
      userId: payerUserId,
      organizationId,
    };

    return this.sendNotification(notification);
  }

  async notifyPaymentScheduleUpdated(scheduleId: string, payerUserId: string, organizationId: string, amount: number, dueDate: string, changes: string) {
    const notification: NotificationTrigger = {
      type: 'PAYMENT_SCHEDULE_UPDATED',
      title: 'Payment Schedule Updated',
      message: `Your payment schedule for ₦${amount.toLocaleString()} due ${new Date(dueDate).toLocaleDateString()} has been updated: ${changes}`,
      priority: 'MEDIUM',
      relatedEntityId: scheduleId,
      relatedEntityType: 'PAYMENT_SCHEDULE',
      userId: payerUserId,
      organizationId,
    };

    return this.sendNotification(notification);
  }

  async notifyPaymentScheduleDeleted(scheduleId: string, payerUserId: string, organizationId: string, amount: number, dueDate: string, reason?: string) {
    const notification: NotificationTrigger = {
      type: 'PAYMENT_SCHEDULE_DELETED',
      title: 'Payment Schedule Cancelled',
      message: `Your payment schedule for ₦${amount.toLocaleString()} due ${new Date(dueDate).toLocaleDateString()} has been cancelled${reason ? `: ${reason}` : ''}`,
      priority: 'HIGH',
      relatedEntityId: scheduleId,
      relatedEntityType: 'PAYMENT_SCHEDULE',
      userId: payerUserId,
      organizationId,
    };

    return this.sendNotification(notification);
  }

  async notifyBulkSchedulesImported(organizationId: string, paymentProfileName: string, successCount: number, totalCount: number, userIds: string[]) {
    const notification: NotificationTrigger = {
      type: 'BULK_SCHEDULES_IMPORTED',
      title: 'Payment Schedules Imported',
      message: `${successCount} of ${totalCount} payment schedules have been imported for ${paymentProfileName}`,
      priority: 'MEDIUM',
      relatedEntityType: 'PAYMENT_PROFILE',
      organizationId,
    };

    // Send to all affected payers
    const promises = userIds.map(userId =>
      this.sendNotification({ ...notification, userId })
    );

    return Promise.all(promises);
  }

  // Receipt-related notifications
  async notifyReceiptGenerated(receiptId: string, payerUserId: string, organizationId: string, receiptNumber: string) {
    const notification: NotificationTrigger = {
      type: 'RECEIPT_GENERATED',
      title: 'Receipt Generated',
      message: `Receipt ${receiptNumber} has been generated and is ready for download`,
      priority: 'MEDIUM',
      relatedEntityId: receiptId,
      relatedEntityType: 'RECEIPT',
      userId: payerUserId,
      organizationId,
    };

    return this.sendNotification(notification);
  }

  async notifyReceiptRevoked(receiptId: string, payerUserId: string, organizationId: string, receiptNumber: string, reason: string) {
    const notification: NotificationTrigger = {
      type: 'RECEIPT_REVOKED',
      title: 'Receipt Revoked',
      message: `Receipt ${receiptNumber} has been revoked. Reason: ${reason}`,
      priority: 'HIGH',
      relatedEntityId: receiptId,
      relatedEntityType: 'RECEIPT',
      userId: payerUserId,
      organizationId,
    };

    return this.sendNotification(notification);
  }

  // Compliance-related notifications
  async notifyCertificateExpiring(certificateId: string, organizationId: string, certificateType: string, daysUntilExpiry: number) {
    const notification: NotificationTrigger = {
      type: 'CERTIFICATE_EXPIRING',
      title: 'Certificate Expiring Soon',
      message: `${certificateType} certificate expires in ${daysUntilExpiry} days`,
      priority: daysUntilExpiry <= 7 ? 'URGENT' : daysUntilExpiry <= 30 ? 'HIGH' : 'MEDIUM',
      relatedEntityId: certificateId,
      relatedEntityType: 'CERTIFICATE',
      organizationId,
    };

    return this.sendNotification(notification);
  }

  async notifyCertificateExpired(certificateId: string, organizationId: string, certificateType: string) {
    const notification: NotificationTrigger = {
      type: 'CERTIFICATE_EXPIRED',
      title: 'Certificate Expired',
      message: `${certificateType} certificate has expired and needs renewal`,
      priority: 'URGENT',
      relatedEntityId: certificateId,
      relatedEntityType: 'CERTIFICATE',
      organizationId,
    };

    return this.sendNotification(notification);
  }

  async notifyCertificateRevoked(certificateId: string, organizationId: string, certificateType: string, reason: string) {
    const notification: NotificationTrigger = {
      type: 'CERTIFICATE_REVOKED',
      title: 'Certificate Revoked',
      message: `${certificateType} certificate has been revoked. Reason: ${reason}`,
      priority: 'URGENT',
      relatedEntityId: certificateId,
      relatedEntityType: 'CERTIFICATE',
      organizationId,
    };

    return this.sendNotification(notification);
  }

  // System notifications
  async notifySystemAlert(title: string, message: string, priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' = 'MEDIUM', userIds?: string[], organizationIds?: string[]) {
    const notification: NotificationTrigger = {
      type: 'SYSTEM_ALERT',
      title,
      message,
      priority,
    };

    if (userIds && userIds.length > 0) {
      // Send to specific users
      const promises = userIds.map(userId => 
        this.sendNotification({ ...notification, userId })
      );
      return Promise.all(promises);
    } else if (organizationIds && organizationIds.length > 0) {
      // Send to specific organizations
      const promises = organizationIds.map(organizationId => 
        this.sendNotification({ ...notification, organizationId })
      );
      return Promise.all(promises);
    } else {
      // Broadcast to all users
      return this.broadcastNotification(notification);
    }
  }

  // File upload notifications
  async notifyFileUploadSuccess(fileName: string, userId: string, organizationId: string, relatedEntityId?: string, relatedEntityType?: string) {
    const notification: NotificationTrigger = {
      type: 'FILE_UPLOADED',
      title: 'File Uploaded Successfully',
      message: `File "${fileName}" has been uploaded successfully`,
      priority: 'LOW',
      relatedEntityId,
      relatedEntityType,
      userId,
      organizationId,
    };

    return this.sendNotification(notification);
  }

  async notifyFileUploadFailed(fileName: string, userId: string, organizationId: string, error: string) {
    const notification: NotificationTrigger = {
      type: 'FILE_UPLOAD_FAILED',
      title: 'File Upload Failed',
      message: `Failed to upload "${fileName}": ${error}`,
      priority: 'MEDIUM',
      userId,
      organizationId,
    };

    return this.sendNotification(notification);
  }

  // User management notifications
  async notifyUserInvited(invitedUserId: string, organizationId: string, inviterName: string, role: string) {
    const notification: NotificationTrigger = {
      type: 'USER_INVITED',
      title: 'Welcome to the System',
      message: `You have been invited by ${inviterName} as ${role}`,
      priority: 'MEDIUM',
      userId: invitedUserId,
      organizationId,
    };

    return this.sendNotification(notification);
  }

  async notifyRoleChanged(userId: string, organizationId: string, newRole: string, changedBy: string) {
    const notification: NotificationTrigger = {
      type: 'ROLE_CHANGED',
      title: 'Role Updated',
      message: `Your role has been changed to ${newRole} by ${changedBy}`,
      priority: 'MEDIUM',
      userId,
      organizationId,
    };

    return this.sendNotification(notification);
  }

  // Private helper methods
  private async sendNotification(notification: NotificationTrigger) {
    if (!this.notificationsApi) {
      console.error('NotificationService not initialized');
      return null;
    }

    const notificationData: CreateNotificationDTO = {
      type: notification.type,
      title: notification.title,
      message: notification.message,
      priority: notification.priority,
      relatedEntityId: notification.relatedEntityId,
      relatedEntityType: notification.relatedEntityType,
      userId: notification.userId,
      organizationId: notification.organizationId,
    };

    return this.notificationsApi.createNotification(notificationData);
  }

  private async broadcastNotification(notification: NotificationTrigger) {
    if (!this.notificationsApi) {
      console.error('NotificationService not initialized');
      return null;
    }

    const notificationData = {
      type: notification.type,
      title: notification.title,
      message: notification.message,
      priority: notification.priority,
      relatedEntityId: notification.relatedEntityId,
      relatedEntityType: notification.relatedEntityType,
    };

    return this.notificationsApi.broadcastNotification(notificationData);
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
export default notificationService;
