import { Mail, Clock, Trash2, Loader2 } from "lucide-react";
import { useState, useEffect } from "react";
import { API_BASE_URL } from "../../config/api";

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: "primary" | "secondary" | "danger";
  size?: "sm" | "md";
  disabled?: boolean;
}

interface Invitation {
  id: string;
  email: string;
  role: string;
  organizationId: string;
  organizationName: string | null;
  invitedDate: string;
  status: string;
  expiryDate: string;
  acceptedDate: string | null;
  invitedBy: string | null;
  invitedByName: string | null;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  children,
  onClick,
  variant = "primary",
  size = "md",
  disabled = false,
}) => {
  const baseClasses =
    "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white",
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${
        disabled ? "opacity-50 cursor-not-allowed" : ""
      }`}
    >
      {children}
    </button>
  );
};

const UserInvitations = () => {
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [resendingId, setResendingId] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  useEffect(() => {
    const fetchPendingInvitations = async () => {
      try {
        const token = localStorage.getItem("auth_token");
        const response = await fetch(
          `${API_BASE_URL}/user-invitations/pending`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch pending invitations");
        }

        const data = await response.json();
        setInvitations(data);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "An unknown error occurred"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchPendingInvitations();
  }, []);

  const handleResendInvitation = async (id: string) => {
    try {
      setResendingId(id);
      const token = localStorage.getItem("auth_token");
      const response = await fetch(
        `${API_BASE_URL}/user-invitations/${id}/resend`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to resend invitation");
      }

      // Refresh the list after successful resend
      const updatedInvitations = invitations.map((inv) =>
        inv.id === id ? { ...inv, invitedDate: new Date().toISOString() } : inv
      );
      setInvitations(updatedInvitations);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to resend invitation"
      );
    } finally {
      setResendingId(null);
    }
  };

  const handleDeleteInvitation = async (id: string) => {
    try {
      setDeletingId(id);
      const token = localStorage.getItem("auth_token");
      const response = await fetch(`${API_BASE_URL}/user-invitations/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to delete invitation");
      }

      // Remove the deleted invitation from the list
      setInvitations(invitations.filter((inv) => inv.id !== id));
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to delete invitation"
      );
    } finally {
      setDeletingId(null);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const formatRole = (role: string) => {
    const roleMap: Record<string, string> = {
      admin: "Admin",
      payer: "Payer",
      finance_officer: "Finance Officer",
      senior_finance_officer: "Senior Finance Officer",
    };
    return roleMap[role] || role;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="animate-spin text-[#2aa45c]" size={32} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 text-red-800 p-4 rounded-lg">
        Error: {error}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-[#045024]">
            Invitation Status
          </h2>
        </div>

        {invitations.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No pending invitations found
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Email
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Role
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Organization
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Invited Date
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Status
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {invitations.map((invitation) => (
                  <tr key={invitation.id} className="border-b border-gray-100">
                    <td className="py-3 px-4">{invitation.email}</td>
                    <td className="py-3 px-4">{formatRole(invitation.role)}</td>
                    <td className="py-3 px-4">{invitation.organizationId}</td>
                    <td className="py-3 px-4">
                      {formatDate(invitation.invitedDate)}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Clock className="text-orange-500" size={16} />
                        <span className="text-sm">{invitation.status}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <ActionButton
                          size="sm"
                          onClick={() => handleResendInvitation(invitation.id)}
                          disabled={resendingId === invitation.id}
                        >
                          {resendingId === invitation.id ? (
                            <Loader2 className="animate-spin" size={14} />
                          ) : (
                            <Mail size={14} />
                          )}
                          Resend
                        </ActionButton>
                        <ActionButton
                          size="sm"
                          variant="danger"
                          onClick={() => handleDeleteInvitation(invitation.id)}
                          disabled={deletingId === invitation.id}
                        >
                          {deletingId === invitation.id ? (
                            <Loader2 className="animate-spin" size={14} />
                          ) : (
                            <Trash2 size={14} />
                          )}
                          Cancel
                        </ActionButton>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserInvitations;
