import React, { useState, useEffect } from 'react';
import { useEmailConfigApi, type EmailConfiguration, type CreateEmailConfigurationDTO } from '../hooks/api';

const EmailConfigurationPage: React.FC = () => {
  const {
    loading,
    error,
    createEmailConfig,
    getEmailConfigsByOrganization,
    getDefaultEmailConfig,
    updateEmailConfig,
    deleteEmailConfig,
  } = useEmailConfigApi();

  const [emailConfigs, setEmailConfigs] = useState<EmailConfiguration[]>([]);
  const [defaultConfig, setDefaultConfig] = useState<EmailConfiguration | null>(null);
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<string>('SYSTEM');
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Form state for creating/editing email configuration
  const [formData, setFormData] = useState<CreateEmailConfigurationDTO>({
    organizationId: 'SYSTEM',
    smtpServer: '',
    port: 587,
    username: '',
    password: '',
    enableSsl: true,
    isDefault: false,
    senderName: '',
    senderEmail: '',
  });

  // Load email configurations for an organization
  const loadEmailConfigs = async (organizationId: string) => {
    if (!organizationId) return;

    console.log('Loading email configs for organization:', organizationId);

    const configs = await getEmailConfigsByOrganization(organizationId);
    console.log('Loaded configs:', configs);
    if (configs) {
      setEmailConfigs(configs);
    }

    const defaultConf = await getDefaultEmailConfig(organizationId);
    console.log('Default config:', defaultConf);
    if (defaultConf) {
      setDefaultConfig(defaultConf);
    }
  };

  // Load configs on component mount
  useEffect(() => {
    if (selectedOrganizationId) {
      loadEmailConfigs(selectedOrganizationId);
    }
  }, [selectedOrganizationId]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const result = await createEmailConfig(formData);
    if (result) {
      alert('Email configuration created successfully!');
      setShowCreateForm(false);
      // Reload configurations
      if (selectedOrganizationId) {
        await loadEmailConfigs(selectedOrganizationId);
      }
      // Reset form
      setFormData({
        organizationId: selectedOrganizationId || 'SYSTEM',
        smtpServer: '',
        port: 587,
        username: '',
        password: '',
        enableSsl: true,
        isDefault: false,
        senderName: '',
        senderEmail: '',
      });
    }
  };

  // Handle delete configuration
  const handleDelete = async (configId: string) => {
    if (window.confirm('Are you sure you want to delete this email configuration?')) {
      const result = await deleteEmailConfig(configId);
      if (result) {
        alert('Email configuration deleted successfully!');
        if (selectedOrganizationId) {
          await loadEmailConfigs(selectedOrganizationId);
        }
      }
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Email Configuration Management</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          Error: {error}
        </div>
      )}

      {/* Organization Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Organization
        </label>
        <div className="flex gap-4">
          <input
            type="text"
            value={selectedOrganizationId}
            onChange={(e) => setSelectedOrganizationId(e.target.value)}
            placeholder="Enter Organization ID"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={() => loadEmailConfigs(selectedOrganizationId)}
            disabled={loading || !selectedOrganizationId}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Load Configs'}
          </button>
        </div>
      </div>

      {/* Create New Configuration Button */}
      <div className="mb-6">
        <button
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
        >
          {showCreateForm ? 'Cancel Create Configuration' : 'Create New Configuration'}
        </button>
      </div>

      {/* Create Form */}
      {showCreateForm && (
        <div className="bg-gray-50 p-6 rounded-lg mb-6">
          <h2 className="text-xl font-semibold mb-4">Create Email Configuration</h2>
          <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Organization ID
              </label>
              <input
                type="text"
                value={formData.organizationId}
                onChange={(e) => setFormData({ ...formData, organizationId: e.target.value })}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                SMTP Server
              </label>
              <input
                type="text"
                value={formData.smtpServer}
                onChange={(e) => setFormData({ ...formData, smtpServer: e.target.value })}
                placeholder="smtp.gmail.com"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Port
              </label>
              <input
                type="number"
                value={formData.port}
                onChange={(e) => setFormData({ ...formData, port: parseInt(e.target.value) })}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Username
              </label>
              <input
                type="text"
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <input
                type="password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sender Name
              </label>
              <input
                type="text"
                value={formData.senderName}
                onChange={(e) => setFormData({ ...formData, senderName: e.target.value })}
                placeholder="Payment Management System"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sender Email
              </label>
              <input
                type="email"
                value={formData.senderEmail}
                onChange={(e) => setFormData({ ...formData, senderEmail: e.target.value })}
                placeholder="<EMAIL>"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="enableSsl"
                checked={formData.enableSsl}
                onChange={(e) => setFormData({ ...formData, enableSsl: e.target.checked })}
                className="mr-2"
              />
              <label htmlFor="enableSsl" className="text-sm font-medium text-gray-700">
                Enable SSL
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isDefault"
                checked={formData.isDefault}
                onChange={(e) => setFormData({ ...formData, isDefault: e.target.checked })}
                className="mr-2"
              />
              <label htmlFor="isDefault" className="text-sm font-medium text-gray-700">
                Set as Default
              </label>
            </div>

            <div className="md:col-span-2">
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Create Configuration'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Default Configuration Display */}
      {defaultConfig && (
        <div className="bg-blue-50 p-4 rounded-lg mb-6">
          <h2 className="text-lg font-semibold text-blue-800 mb-2">Default Configuration</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            <p><strong>SMTP Server:</strong> {defaultConfig.smtpServer}</p>
            <p><strong>Port:</strong> {defaultConfig.port}</p>
            <p><strong>Username:</strong> {defaultConfig.username}</p>
            <p><strong>Sender:</strong> {defaultConfig.senderName} &lt;{defaultConfig.senderEmail}&gt;</p>
            <p><strong>SSL Enabled:</strong> {defaultConfig.enableSsl ? 'Yes' : 'No'}</p>
          </div>
        </div>
      )}

      {/* Email Configurations List */}
      {emailConfigs.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold mb-4">Email Configurations</h2>
          <div className="grid gap-4">
            {emailConfigs.map((config) => (
              <div key={config.id} className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium">
                    {config.senderName} {config.isDefault && <span className="text-blue-600">(Default)</span>}
                  </h3>
                  <button
                    onClick={() => handleDelete(config.id)}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Delete
                  </button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
                  <p><strong>SMTP:</strong> {config.smtpServer}:{config.port}</p>
                  <p><strong>Username:</strong> {config.username}</p>
                  <p><strong>Sender Email:</strong> {config.senderEmail}</p>
                  <p><strong>SSL:</strong> {config.enableSsl ? 'Enabled' : 'Disabled'}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No configurations message */}
      {selectedOrganizationId && emailConfigs.length === 0 && !loading && (
        <div className="text-center py-8 text-gray-500">
          No email configurations found for this organization.
        </div>
      )}
    </div>
  );
};

export default EmailConfigurationPage;
