using System.ComponentModel.DataAnnotations;

namespace Final_E_Receipt.Notifications.DTOs
{
    //public class NotificationFiltersDTO
    //{
    //    public string Status { get; set; } // UNREAD, READ, ARCHIVED
    //    public string Type { get; set; }
    //    public string Priority { get; set; }
    //    public DateTime? FromDate { get; set; }
    //    public DateTime? ToDate { get; set; }
    //    public int PageNumber { get; set; } = 1;
    //    public int PageSize { get; set; } = 50;
    //}

    public class NotificationBulkActionDTO
    {
        [Required]
        public string[] NotificationIds { get; set; }
        
        [Required]
        public string Action { get; set; } // READ, ARCHIVE, DELETE
    }

    public class NotificationBulkMarkAsReadDTO
    {
        [Required]
        public string[] NotificationIds { get; set; }
    }
}





