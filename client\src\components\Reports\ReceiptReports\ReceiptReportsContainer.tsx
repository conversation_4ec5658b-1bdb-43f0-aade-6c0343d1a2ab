import React, { useState } from 'react';
import { Receipt, Ban, BarChart3 } from 'lucide-react';
import RevokedReceiptsReport from './RevokedReceiptsReport';

const ReceiptReportsContainer: React.FC = () => {
  const [activeReport, setActiveReport] = useState<'revoked' | 'analytics' | 'audit'>('revoked');

  const reportTabs = [
    {
      id: 'revoked',
      label: 'Revoked Receipts',
      icon: Ban,
      description: 'Audit trail for all revoked receipts with detailed analysis',
    },
    {
      id: 'analytics',
      label: 'Receipt Analytics',
      icon: BarChart3,
      description: 'Receipt generation statistics and trends',
    },
    {
      id: 'audit',
      label: 'Audit Trail',
      icon: Receipt,
      description: 'Complete receipt audit history and changes',
    },
  ];

  const renderActiveReport = () => {
    switch (activeReport) {
      case 'revoked':
        return <RevokedReceiptsReport />;
      case 'analytics':
        return (
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="text-center py-8">
              <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Receipt analytics coming soon</p>
            </div>
          </div>
        );
      case 'audit':
        return (
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="text-center py-8">
              <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Receipt audit trail coming soon</p>
            </div>
          </div>
        );
      default:
        return <RevokedReceiptsReport />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="h-10 w-10 rounded-lg bg-purple-100 flex items-center justify-center">
          <Receipt className="h-5 w-5 text-purple-600" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Receipt Reports</h1>
          <p className="text-gray-600">Receipt analytics, audit trails, and compliance reporting</p>
        </div>
      </div>

      {/* Report Navigation */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {reportTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveReport(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeReport === tab.id
                    ? 'border-[#2aa45c] text-[#2aa45c]'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon size={16} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Descriptions */}
        <div className="px-6 py-3 bg-gray-50">
          {reportTabs.map((tab) => (
            activeReport === tab.id && (
              <p key={tab.id} className="text-sm text-gray-600">
                {tab.description}
              </p>
            )
          ))}
        </div>
      </div>

      {/* Active Report Content */}
      {renderActiveReport()}
    </div>
  );
};

export default ReceiptReportsContainer;
