# JRB Dummy Test Data Guide

## 🎯 Purpose
This dummy data file creates a complete test environment for the JRB Payment Management System with realistic sample data across all tables.

## 📁 Files Created
- `server/SQL/DummyTestData.sql` - Complete dummy data for all tables

## 🚀 How to Deploy

### Step 1: Deploy Database Schema
```sql
-- Deploy the complete database schema first
server/SQL/CompleteDatabaseSchema.sql
```

### Step 2: Deploy Stored Procedures
```sql
-- Deploy all stored procedures (in any order)
server/Authentication/SQL/AuthenticationProcedures.sql
server/Organizations/SQL/OrganizationProcedures.sql
server/Payments/SQL/PaymentProcedures.sql
server/Payments/SQL/PaymentProfileProcedures.sql
server/Payments/SQL/PaymentScheduleProcedures.sql
server/Receipts/SQL/ReceiptProcedures.sql
server/Files/SQL/FileUploadProcedures.sql
server/Notifications/SQL/NotificationProcedures.sql
server/Compliance/SQL/ComplianceCertificateProcedures.sql
server/Reporting/SQL/ComplianceReportingProcedures.sql
server/Reporting/SQL/EnhancedReportingProcedures.sql
```

### Step 3: Insert Dummy Data
```sql
-- Insert all test data
server/SQL/DummyTestData.sql
```

## 📊 What's Included

### 🏢 Organizations (3 taxpaying companies)
- **ABC Manufacturing Ltd** - Manufacturing company
- **XYZ Trading Company** - Trading business  
- **Tech Solutions Nigeria** - Technology company

### 👥 Users (8 test users)
- **1 Admin**: Sarah Johnson (<EMAIL>)
- **2 Finance Officers**: Michael Okonkwo, Grace Adebola
- **1 Senior Finance Officer**: Dr. Ahmed Ibrahim
- **3 Payers**: One from each organization
- **1 Pending Invitation**: New finance officer

### 💰 Payment Profiles (3 tax services)
- **Annual Business License** - ₦50,000-₦75,000
- **Corporate Income Tax** - ₦180,000-₦320,000
- **Quarterly VAT Returns** - ₦32,000-₦58,000

### 📅 Payment Schedules (9 schedules)
- **3 PAID** - Annual licenses (all organizations)
- **3 PENDING** - Corporate income tax (all organizations)
- **2 PAID** - Q1 VAT (ABC Manufacturing, Tech Solutions)
- **1 OVERDUE** - Q1 VAT (XYZ Trading)

### 💳 Payments (5 completed payments)
- **3 Annual License payments** - All approved and completed
- **2 VAT payments** - All approved and completed
- **All payments have approval workflow** - Acknowledged by Finance Officers, Approved by Senior Finance Officer

### 🧾 Receipts (5 generated receipts)
- **Auto-generated for all completed payments**
- **Receipt numbers**: JRB-RCP-2024-001 to JRB-RCP-2024-005
- **All receipts sent via email**

### 🏆 Compliance Certificates (3 certificates)
- **3 Annual License certificates** - Auto-generated for completed annual license payments
- **Certificate numbers**: AL-2024-1001 to AL-2024-1003
- **All certificates valid for 2024**

### 📁 File Uploads (6 files)
- **3 Payment proof files** - Bank transfers and online payment screenshots
- **2 Receipt PDF files** - Generated receipt documents
- **1 Certificate PDF file** - Generated certificate document

### 📧 Email Templates (3 templates)
- **Receipt Notification** - For payment receipts
- **Certificate Issued** - For compliance certificates
- **Payment Reminder** - For upcoming payments

### ⚙️ Email Configurations (2 configs)
- **Primary SMTP** - smtp.jrb.gov.ng (default)
- **Backup SMTP** - Gmail backup

## 🧪 Test Scenarios

### Scenario 1: Complete Payment Workflow
1. **Login as Payer** (<EMAIL>)
2. **View Payment Schedules** - See pending Corporate Income Tax
3. **Make Payment** - Upload proof, submit payment
4. **Login as Finance Officer** - Acknowledge payment
5. **Login as Senior Finance Officer** - Approve payment
6. **Verify Receipt Generation** - Check auto-generated receipt
7. **Check Certificate** - If all profile payments complete

### Scenario 2: Certificate Management
1. **Login as Admin/Finance Officer**
2. **View Certificates** - See issued annual license certificates
3. **Download Certificate PDFs** - Test PDF generation
4. **Revoke Certificate** - Test revocation workflow
5. **Generate New Certificate** - Manual certificate creation

### Scenario 3: Reporting and Analytics
1. **Payment History Report** - View all payments by organization
2. **Outstanding Balances** - See pending payments
3. **Compliance Status** - Check certificate status
4. **Revenue Analytics** - View payment trends

### Scenario 4: User Management
1. **Login as Admin**
2. **View Pending Invitations** - See <EMAIL>
3. **Send New Invitation** - Invite new payer
4. **Manage User Roles** - Update user permissions

## 🔍 Verification Queries

### Check Data Integrity
```sql
-- Verify all data was inserted
SELECT 'Organizations' as TableName, COUNT(*) as RecordCount FROM Organizations
UNION ALL
SELECT 'Users', COUNT(*) FROM Users
UNION ALL
SELECT 'PaymentProfiles', COUNT(*) FROM PaymentProfiles
UNION ALL
SELECT 'PaymentSchedules', COUNT(*) FROM PaymentSchedules
UNION ALL
SELECT 'Payments', COUNT(*) FROM Payments
UNION ALL
SELECT 'Receipts', COUNT(*) FROM Receipts
UNION ALL
SELECT 'ComplianceCertificates', COUNT(*) FROM ComplianceCertificates
UNION ALL
SELECT 'FileUploads', COUNT(*) FROM FileUploads
UNION ALL
SELECT 'EmailTemplates', COUNT(*) FROM EmailTemplates
UNION ALL
SELECT 'UserInvitations', COUNT(*) FROM UserInvitations;
```

### Check Payment Workflow
```sql
-- Verify payment approval workflow
SELECT 
    p.Id,
    p.PayerName,
    p.Amount,
    p.Status,
    p.AcknowledgedBy,
    p.ApprovedBy,
    r.ReceiptNumber
FROM Payments p
LEFT JOIN Receipts r ON p.ReceiptId = r.Id
WHERE p.Status = 'Completed';
```

### Check Certificate Generation
```sql
-- Verify certificate generation
SELECT 
    cc.CertificateNumber,
    cc.OrganizationName,
    cc.CertificateType,
    cc.TotalAmount,
    cc.Status,
    cc.ValidFrom,
    cc.ValidUntil
FROM ComplianceCertificates cc
ORDER BY cc.IssuedDate DESC;
```

## 🎯 Login Credentials

### JRB Staff
- **Admin**: <EMAIL> (Azure AD: azure-admin-001)
- **Finance Officer 1**: <EMAIL> (Azure AD: azure-fo-001)
- **Finance Officer 2**: <EMAIL> (Azure AD: azure-fo-002)
- **Senior Finance Officer**: <EMAIL> (Azure AD: azure-sfo-001)

### Organization Payers
- **ABC Manufacturing**: <EMAIL> (Azure AD: azure-payer-001)
- **XYZ Trading**: <EMAIL> (Azure AD: azure-payer-002)
- **Tech Solutions**: <EMAIL> (Azure AD: azure-payer-003)

## 📈 Expected Results

After deploying this dummy data, you should be able to:

1. **✅ Login with different user roles**
2. **✅ View payment schedules and make payments**
3. **✅ Process payment approval workflow**
4. **✅ Generate and view receipts**
5. **✅ Generate and manage certificates**
6. **✅ Test file upload functionality**
7. **✅ Send email notifications**
8. **✅ Run reporting queries**
9. **✅ Test user invitation system**
10. **✅ Verify template generation system**

## 🚨 Important Notes

1. **Azure AD Integration**: The AzureAdObjectId values are dummy - replace with real Azure AD object IDs for actual testing
2. **Email Passwords**: SMTP passwords are placeholders - use real encrypted passwords
3. **File Paths**: File paths are examples - ensure actual files exist for full testing
4. **Template Testing**: Create the JRB template files mentioned in the template guide
5. **Date Calculations**: Uses DATEADD for relative dates - adjust as needed

This dummy data provides a complete, realistic test environment for the JRB Payment Management System! 🚀
