using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Final_E_Receipt.Reporting.Services;
using Final_E_Receipt.Reporting.DTOs;
using System.Security.Claims;
using System;
using System.Threading.Tasks;
using Final_E_Receipt.Reporting.Models;

namespace Final_E_Receipt.Reporting.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReportingController : ControllerBase
    {
        private readonly ReportingService _reportingService;
        private readonly ReportExportService _exportService;
        private readonly ReportExcelService _excelService;
        private readonly ReportCsvService _csvService;
        private readonly ILogger<ReportingController> _logger;

        public ReportingController(
            ReportingService reportingService,
            ReportExportService exportService,
            ReportExcelService excelService,
            ReportCsvService csvService,
            ILogger<ReportingController> logger)
        {
            _reportingService = reportingService;
            _exportService = exportService;
            _excelService = excelService;
            _csvService = csvService;
            _logger = logger;
        }

        [HttpGet("dashboard-summary/{organizationId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetDashboardSummary(string organizationId)
        {
            var summary = await _reportingService.GetDashboardSummary(organizationId);
            return Ok(summary);
        }

        [HttpGet("monthly-revenue/{organizationId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetMonthlyRevenue(string organizationId, [FromQuery] YearDTO dto)
        {
            var year = dto.Year > 0 ? dto.Year : DateTime.Now.Year;
            var revenue = await _reportingService.GetMonthlyRevenue(organizationId, year);
            return Ok(revenue);
        }

        [HttpGet("payment-method-summary/{organizationId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetPaymentMethodSummary(string organizationId)
        {
            var summary = await _reportingService.GetPaymentMethodSummary(organizationId);
            return Ok(summary);
        }

        [HttpGet("category-summary/{organizationId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetCategorySummary(string organizationId)
        {
            var summary = await _reportingService.GetCategorySummary(organizationId);
            return Ok(summary);
        }

        [HttpGet("daily-revenue/{organizationId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetDailyRevenue(string organizationId, [FromQuery] DateRangeDTO dto)
        {
            var revenue = await _reportingService.GetDailyRevenue(organizationId, dto.StartDate, dto.EndDate);
            return Ok(revenue);
        }

        [HttpGet("top-payers/{organizationId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetTopPayers(string organizationId, [FromQuery] TopPayersDTO dto)
        {
            var topPayers = await _reportingService.GetTopPayers(organizationId, dto.Limit);
            return Ok(topPayers);
        }

        [HttpGet("year-over-year/{organizationId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetYearOverYearComparison(string organizationId, [FromQuery] YearComparisonDTO dto)
        {
            var currentYear = dto.CurrentYear > 0 ? dto.CurrentYear : DateTime.Now.Year;
            var previousYear = dto.PreviousYear > 0 ? dto.PreviousYear : currentYear - 1;
            
            var comparison = await _reportingService.GetYearOverYearComparison(organizationId, currentYear, previousYear);
            return Ok(comparison);
        }

        [HttpPost("export/{organizationId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> ExportReport(string organizationId, [FromBody] ExportReportDTO dto)
        {
            byte[] fileContent;
            string fileName;
            string contentType;
            
            switch (dto.ReportType.ToLower())
            {
                case "monthly":
                    var year = dto.Year ?? DateTime.Now.Year;
                    fileContent = await _exportService.ExportMonthlyRevenue(organizationId, year, dto.ExportFormat);
                    fileName = $"monthly_revenue_{year}";
                    break;
                
                case "daily":
                    if (!dto.StartDate.HasValue || !dto.EndDate.HasValue)
                        return BadRequest(new { message = "Start date and end date are required for daily revenue report" });
                    
                    fileContent = await _exportService.ExportDailyRevenue(organizationId, dto.StartDate.Value, dto.EndDate.Value, dto.ExportFormat);
                    fileName = $"daily_revenue_{dto.StartDate.Value:yyyyMMdd}_to_{dto.EndDate.Value:yyyyMMdd}";
                    break;
                
                case "paymentmethod":
                    fileContent = await _exportService.ExportPaymentMethodSummary(organizationId, dto.ExportFormat);
                    fileName = "payment_method_summary";
                    break;
                
                case "category":
                    fileContent = await _exportService.ExportCategorySummary(organizationId, dto.ExportFormat);
                    fileName = "category_summary";
                    break;
                
                case "toppayers":
                    fileContent = await _exportService.ExportTopPayers(organizationId, 10, dto.ExportFormat);
                    fileName = "top_payers";
                    break;
                
                default:
                    return BadRequest(new { message = "Invalid report type" });
            }
            
            switch (dto.ExportFormat.ToUpper())
            {
                case "CSV":
                    contentType = "text/csv";
                    fileName += ".csv";
                    break;
                case "EXCEL":
                    contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    fileName += ".xlsx";
                    break;
                case "PDF":
                    contentType = "application/pdf";
                    fileName += ".pdf";
                    break;
                default:
                    return BadRequest(new { message = "Invalid export format" });
            }
            
            return File(fileContent, contentType, fileName);
        }

        // ===== ENHANCED REPORTING ENDPOINTS =====

        /// <summary>
        /// Gets payment history by organization with filtering and sorting
        /// </summary>
        [HttpPost("payment-history")]
        public async Task<IActionResult> GetPaymentHistory([FromBody] PaymentHistoryFilter filter)
        {
            try
            {
                // Check access permissions
                var userOrganizationId = User.FindFirst("OrganizationId")?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                if (userRole != "JTB_ADMIN" && userRole != "FINANCE_OFFICER" && userRole != "SENIOR_FINANCE_OFFICER" &&
                    userOrganizationId != filter.OrganizationId)
                {
                    return Forbid();
                }

                var report = await _reportingService.GetPaymentHistoryByOrganization(filter);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving payment history for organization {OrganizationId}", filter.OrganizationId);
                return StatusCode(500, new { message = "An error occurred while retrieving payment history" });
            }
        }

        /// <summary>
        /// Gets outstanding balances report with aging analysis
        /// </summary>
        [HttpPost("outstanding-balances")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER,JTB_ADMIN")]
        public async Task<IActionResult> GetOutstandingBalances([FromBody] OutstandingBalancesFilter filter)
        {
            try
            {
                var report = await _reportingService.GetOutstandingBalancesReport(filter);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving outstanding balances report");
                return StatusCode(500, new { message = "An error occurred while retrieving outstanding balances" });
            }
        }

        /// <summary>
        /// Gets revoked receipts report with reasons and audit trail
        /// </summary>
        [HttpPost("revoked-receipts")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER,JTB_ADMIN")]
        public async Task<IActionResult> GetRevokedReceipts([FromBody] RevokedReceiptsFilter filter)
        {
            try
            {
                var report = await _reportingService.GetRevokedReceiptsReport(filter);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving revoked receipts report");
                return StatusCode(500, new { message = "An error occurred while retrieving revoked receipts" });
            }
        }

        /// <summary>
        /// Exports payment history report in specified format
        /// </summary>
        [HttpPost("payment-history/export")]
        public async Task<IActionResult> ExportPaymentHistory([FromBody] PaymentHistoryExportRequest request)
        {
            try
            {
                // Check access permissions
                var userOrganizationId = User.FindFirst("OrganizationId")?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                if (userRole != "JTB_ADMIN" && userRole != "FINANCE_OFFICER" && userRole != "SENIOR_FINANCE_OFFICER" &&
                    userOrganizationId != request.Filter.OrganizationId)
                {
                    return Forbid();
                }

                var report = await _reportingService.GetPaymentHistoryByOrganization(request.Filter);
                byte[] fileContent;
                string contentType;
                string fileName = $"PaymentHistory_{request.Filter.OrganizationId}_{DateTime.Now:yyyyMMdd}";

                switch (request.Format.ToUpper())
                {
                    case "CSV":
                        fileContent = await _csvService.GeneratePaymentHistoryCsv(report);
                        contentType = "text/csv";
                        fileName += ".csv";
                        break;
                    case "EXCEL":
                        // Create a simple Excel version for payment history
                        fileContent = await _csvService.GeneratePaymentHistoryCsv(report); // TODO: Implement Excel version
                        contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                        fileName += ".xlsx";
                        break;
                    default:
                        return BadRequest(new { message = "Unsupported export format. Use CSV or EXCEL." });
                }

                return File(fileContent, contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting payment history");
                return StatusCode(500, new { message = "An error occurred while exporting payment history" });
            }
        }

        /// <summary>
        /// Exports outstanding balances report in specified format
        /// </summary>
        [HttpPost("outstanding-balances/export")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER,JTB_ADMIN")]
        public async Task<IActionResult> ExportOutstandingBalances([FromBody] OutstandingBalancesExportRequest request)
        {
            try
            {
                var report = await _reportingService.GetOutstandingBalancesReport(request.Filter);
                byte[] fileContent;
                string contentType;
                string fileName = $"OutstandingBalances_{DateTime.Now:yyyyMMdd}";

                switch (request.Format.ToUpper())
                {
                    case "CSV":
                        fileContent = await _csvService.GenerateOutstandingBalancesCsv(report);
                        contentType = "text/csv";
                        fileName += ".csv";
                        break;
                    case "EXCEL":
                        fileContent = await _csvService.GenerateOutstandingBalancesCsv(report); // TODO: Implement Excel version
                        contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                        fileName += ".xlsx";
                        break;
                    default:
                        return BadRequest(new { message = "Unsupported export format. Use CSV or EXCEL." });
                }

                return File(fileContent, contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting outstanding balances");
                return StatusCode(500, new { message = "An error occurred while exporting outstanding balances" });
            }
        }

        /// <summary>
        /// Exports revoked receipts report in specified format
        /// </summary>
        [HttpPost("revoked-receipts/export")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER,JTB_ADMIN")]
        public async Task<IActionResult> ExportRevokedReceipts([FromBody] RevokedReceiptsExportRequest request)
        {
            try
            {
                var report = await _reportingService.GetRevokedReceiptsReport(request.Filter);
                byte[] fileContent;
                string contentType;
                string fileName = $"RevokedReceipts_{DateTime.Now:yyyyMMdd}";

                switch (request.Format.ToUpper())
                {
                    case "CSV":
                        fileContent = await _csvService.GenerateRevokedReceiptsCsv(report);
                        contentType = "text/csv";
                        fileName += ".csv";
                        break;
                    case "EXCEL":
                        fileContent = await _csvService.GenerateRevokedReceiptsCsv(report); // TODO: Implement Excel version
                        contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                        fileName += ".xlsx";
                        break;
                    default:
                        return BadRequest(new { message = "Unsupported export format. Use CSV or EXCEL." });
                }

                return File(fileContent, contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting revoked receipts");
                return StatusCode(500, new { message = "An error occurred while exporting revoked receipts" });
            }
        }
    }

    // ===== EXPORT REQUEST MODELS =====

    public class PaymentHistoryExportRequest
    {
        public PaymentHistoryFilter Filter { get; set; }
        public string Format { get; set; } = "CSV";
    }

    public class OutstandingBalancesExportRequest
    {
        public OutstandingBalancesFilter Filter { get; set; }
        public string Format { get; set; } = "CSV";
    }

    public class RevokedReceiptsExportRequest
    {
        public RevokedReceiptsFilter Filter { get; set; }
        public string Format { get; set; } = "CSV";
    }
}